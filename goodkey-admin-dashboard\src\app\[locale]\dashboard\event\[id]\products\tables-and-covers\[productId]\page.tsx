import type { Metadata } from 'next';
import ProductDetailClient from './ProductDetailClient';

export const metadata: Metadata = {
  title: 'Product Details | GOODKEY SHOW SERVICES LTD.',
  description: 'View detailed product information and ordering options',
};

export default async function ProductDetailPage(props: {
  params: Promise<{ id: string; productId: string }>;
}) {
  const params = await props.params;
  return <ProductDetailClient params={params} />;
}
