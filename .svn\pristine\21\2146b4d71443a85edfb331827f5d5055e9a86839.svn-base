﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportRows
    {
        public ExhibitorImportRows()
        {
            ExhibitorImportAppliedFixes = new HashSet<ExhibitorImportAppliedFixes>();
            ExhibitorImportValidationMessages = new HashSet<ExhibitorImportValidationMessages>();
        }

        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int RowNumber { get; set; }
        public string Status { get; set; }
        public string CompanyName { get; set; }
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string BoothNumbers { get; set; }
        public string ContactType { get; set; }
        public int? ResolvedCompanyId { get; set; }
        public int? ResolvedContactTypeId { get; set; }
        public string[] ResolvedBoothNumbersArray { get; set; }
        public bool? IsNewCompany { get; set; }
        public bool? IsNewContact { get; set; }
        public bool? IsDuplicate { get; set; }
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int? CreatedCompanyId { get; set; }
        public int? CreatedContactId { get; set; }
        public int? CreatedExhibitorId { get; set; }
        public int? CreatedUserId { get; set; }
        public DateTime? ProcessedAt { get; set; }

        public virtual Company CreatedCompany { get; set; }
        public virtual Contact CreatedContact { get; set; }
        public virtual ShowExhibitors CreatedExhibitor { get; set; }
        public virtual AuthUser CreatedUser { get; set; }
        public virtual Company ResolvedCompany { get; set; }
        public virtual ContactType ResolvedContactType { get; set; }
        public virtual ExhibitorImportSessions Session { get; set; }
        public virtual ICollection<ExhibitorImportAppliedFixes> ExhibitorImportAppliedFixes { get; set; }
        public virtual ICollection<ExhibitorImportValidationMessages> ExhibitorImportValidationMessages { get; set; }
    }
}