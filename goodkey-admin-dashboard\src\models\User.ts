import { BriefData } from './BriefData';
import { PermissionKey } from './Permission';

export interface UserData {
  username: string;
  email: string;
  name: string;
  role?: BriefData;
  permissions: PermissionKey[];
  menuItems: number[];
  isSuper?: boolean;
}
export interface UserBriefData extends UserData {
  name: string;
  id: number;
  role: BriefData;
  isActive: boolean;
  mobileNumber?: string;
  workPhoneNumber?: string;
  note?: string;
  isVerified: boolean;
  isArchived: boolean;
}

export interface UserDetail {
  id: number;
  statusId: number;
  salutationId: number;
  departmentId: number;
  firstName: string;
  lastName: string;
  verificationEmail: string;
  workEmail: string;
  mobileNumber: string;
  workPhoneNumber: string;
  hasAccount: boolean;
  roleId: number;
  isActive: boolean;
  isSuper: boolean;
  archive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface InviteUser {
  userId: number;
}
