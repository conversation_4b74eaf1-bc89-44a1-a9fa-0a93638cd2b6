"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_stata_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sql.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SQL\\\",\\\"name\\\":\\\"sql\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"((?<!@)@)\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"text.variable\\\"},{\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\]]*(\\\\\\\\])\\\",\\\"name\\\":\\\"text.bracketed\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(create(?:\\\\\\\\s+or\\\\\\\\s+replace)?)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)(['\\\\\\\"`]?)(\\\\\\\\w+)\\\\\\\\4\\\",\\\"name\\\":\\\"meta.create.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(drop)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.cascade.sql\\\"}},\\\"match\\\":\\\"(?i:\\\\\\\\s*(drop)\\\\\\\\s+(table)\\\\\\\\s+(\\\\\\\\w+)(\\\\\\\\s+cascade)?\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(alter)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)\\\",\\\"name\\\":\\\"meta.alter.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"9\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"11\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"12\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"13\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"14\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"15\\\":{\\\"name\\\":\\\"storage.type.sql\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# normal stuff, capture 1\\\\n\\\\t\\\\t\\\\t\\\\t \\\\\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\\\\\b\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# numeric suffix, capture 2 + 3i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(bit\\\\\\\\svarying|character\\\\\\\\s(?:varying)?|tinyint|var\\\\\\\\schar|float|interval)\\\\\\\\((\\\\\\\\d+)\\\\\\\\)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# optional numeric suffix, capture 4 + 5i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(char|number|varchar\\\\\\\\d?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, capture 6 + 7i + 8i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(numeric|decimal)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+),(\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 9, 10i, 11\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(times?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\swith(?:out)?\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 12, 13, 14i, 15\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(timestamp)(?:(s|tz))?\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\s(with|without)\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b((?:primary|foreign)\\\\\\\\s+key|references|on\\\\\\\\sdelete(\\\\\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.modifier.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(select(\\\\\\\\s+(all|distinct))?|insert\\\\\\\\s+(ignore\\\\\\\\s+)?into|update|delete|from|set|where|group\\\\\\\\s+by|or|like|and|union(\\\\\\\\s+all)?|having|order\\\\\\\\s+by|limit|cross\\\\\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\\\\\s+outer)?)\\\\\\\\s+join|natural(\\\\\\\\s+(inner|(left|right|full)(\\\\\\\\s+outer)?))?\\\\\\\\s+join)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(on|off|((is\\\\\\\\s+)?not\\\\\\\\s+)?null)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DDL.create.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bvalues\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(begin(\\\\\\\\s+work)?|start\\\\\\\\s+transaction|commit(\\\\\\\\s+work)?|rollback(\\\\\\\\s+work)?)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.LUW.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(grant(\\\\\\\\swith\\\\\\\\sgrant\\\\\\\\soption)?|revoke)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.authorization.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bin\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.data-integrity.sql\\\"},{\\\"match\\\":\\\"(?i:^\\\\\\\\s*(comment\\\\\\\\s+on\\\\\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\\\\\s+.*?\\\\\\\\s+(is)\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.other.object-comments.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\bAS\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.alias.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(DESC|ASC)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.order.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.star.sql\\\"},{\\\"match\\\":\\\"[!<>]?=|<>|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.sql\\\"},{\\\"match\\\":\\\"-|\\\\\\\\+|/\\\",\\\"name\\\":\\\"keyword.operator.math.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.concatenator.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.aggregate.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.analytic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.bitmanipulation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.conversion.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.collation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(collationproperty|tertiary_weights)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cryptographic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cursor.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cursor_status)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datetime.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datatype.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.expression.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(coalesce|nullif)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.globalvar.sql\\\"}},\\\"match\\\":\\\"(?<!@)@@(?i)\\\\\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.json.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.logical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(choose|iif|greatest|least)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.mathematical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.metadata.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.ranking.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(rank|dense_rank|ntile|row_number)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.rowset.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.security.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.string.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.system.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textimage.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(patindex|textptr|textvalid)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.database-name.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.table-name.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+?)\\\\\\\\.(\\\\\\\\w+)\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#regexps\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\\\\\\\\\s+or\\\\\\\\\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.sql\\\"}},\\\"comment\\\":\\\"Allow for special ↩ behavior\\\",\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.block.sql\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.sql\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[]},{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"regexps\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/(?=\\\\\\\\S.*/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\/\\\",\\\"name\\\":\\\"constant.character.escape.slash.sql\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"comment\\\":\\\"We should probably handle nested bracket pairs!?! -- Allan\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.modr.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]},\\\"string_escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.sql\\\"},\\\"string_interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(#\\\\\\\\{)([^\\\\\\\\}]*)(\\\\\\\\})\\\",\\\"name\\\":\\\"string.interpolated.sql\\\"},\\\"strings\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(N)?(')[^']*(')\\\",\\\"name\\\":\\\"string.quoted.single.sql\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.single.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(`)[^`\\\\\\\\\\\\\\\\]*(`)\\\",\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\"},{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(\\\\\\\")[^\\\\\\\"#]*(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sql\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.double.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},{\\\"begin\\\":\\\"%\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.other.quoted.brackets.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.sql\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/stata.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/stata.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sql_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sql.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Stata\\\",\\\"fileTypes\\\":[\\\"do\\\",\\\"ado\\\",\\\"mata\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"comment\\\":\\\"keywords that delimit flow conditionals\\\",\\\"match\\\":\\\"\\\\\\\\b(if|else if|else)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.scalar.stata\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(sca(lar|la|l)?(\\\\\\\\s+de(fine|fin|fi|f)?)?)\\\\\\\\s+(?!(drop|dir?|l(ist|is|i)?)\\\\\\\\s+)\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(mer(ge|g)?)\\\\\\\\s+(1|m|n)(:)(1|m|n)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"m|n\\\",\\\"name\\\":\\\"\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.key-value\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"m|n\\\",\\\"name\\\":\\\"\\\"}]}},\\\"end\\\":\\\"using\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(foreach)\\\\\\\\s+((?!in|of).+)\\\\\\\\s+(in|of var(list|lis|li|l)?|of new(list|lis|li|l)?|of num(list|lis|li|l)?)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(foreach)\\\\\\\\s+((?!in|of).+)\\\\\\\\s+(of loc(al|a)?|of glo(bal|ba|b)?)\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(forvalues|forvalue|forvalu|forval|forva|forv)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(=)\\\\\\\\s*([^\\\\\\\\{]+)\\\\\\\\s*|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"comment\\\":\\\"keywords that delimit loops\\\",\\\"match\\\":\\\"\\\\\\\\b(while|continue)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.stata\\\"}},\\\"comment\\\":\\\"keywords that haven't fit into other groups (yet).\\\",\\\"match\\\":\\\"\\\\\\\\b(as|ass|asse|asser|assert)\\\\\\\\b\\\"},{\\\"comment\\\":\\\"prefixes that require a colon\\\",\\\"match\\\":\\\"\\\\\\\\b(by(sort|sor|so|s)?|statsby|rolling|bootstrap|jackknife|permute|simulate|svy|mi est(imate|imat|ima|im|i)?|nestreg|stepwise|xi|fp|mfp|vers(ion|io|i)?)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.function.stata\\\"},{\\\"comment\\\":\\\"prefixes that don't need a colon\\\",\\\"match\\\":\\\"\\\\\\\\b(qui(etly|etl|et|e)?|n(oisily|oisil|oisi|ois|oi|o)?|cap(ture|tur|tu|t)?)\\\\\\\\b:?\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(pr(ogram|ogra|ogr|og|o)?)\\\\\\\\s+((di(r)?|drop|l(ist|is|i)?)\\\\\\\\s+)([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*(pr(ogram|ogra|ogr|og|o)?)\\\\\\\\s+(de(fine|fin|fi|f)?\\\\\\\\s+)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.function.stata\\\"}},\\\"end\\\":\\\"(?=,|\\\\\\\\n|/)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"entity.name.function.stata\\\"},{\\\"match\\\":\\\"[^A-za-z_0-9,\\\\\\\\n/ ]+\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}]},{\\\"captures\\\":{\\\"1\\\":\\\"keyword.functions.data.stata.test\\\"},\\\"match\\\":\\\"\\\\\\\\b(form(at|a)?)\\\\\\\\s*([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})*\\\\\\\\s*(%)(-)?(0)?([0-9]+)(.)([0-9]+)(e|f|g)(c)?\\\"},{\\\"include\\\":\\\"#braces-with-error\\\"},{\\\"begin\\\":\\\"(?=syntax)\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"syntax\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.functions.program.stata\\\"}},\\\"comment\\\":\\\"color before the comma\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(varlist|varname|newvarlist|newvarname|namelist|name|anything)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b((if|in|using|fweight|aweight|pweight|iweight))\\\\\\\\b(/)?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"(/)?(exp)\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]},{\\\"begin\\\":\\\",\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}},\\\"comment\\\":\\\"things to color after the comma\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"begin\\\":\\\"([^\\\\\\\\s\\\\\\\\[\\\\\\\\]]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"comment\\\":\\\"these are the names that become macros\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"comment\\\":\\\"color options with parentheses\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.stata\\\"}},\\\"comment\\\":\\\"the first word is often a type\\\",\\\"match\\\":\\\"\\\\\\\\b(integer|intege|integ|inte|int|real|string|strin|stri|str)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]},{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"comment\\\":\\\"one-word commands\\\",\\\"match\\\":\\\"\\\\\\\\b(sa(v|ve)|saveold|destring|tostring|u(se|s)?|note(s)?|form(at|a)?)\\\\\\\\b\\\"},{\\\"comment\\\":\\\"programming commands\\\",\\\"match\\\":\\\"\\\\\\\\b(exit|end)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.functions.data.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(replace)\\\\\\\\s+([^=]+)\\\\\\\\s*((==)|(=))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.stata\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved-names\\\"},{\\\"include\\\":\\\"#macro-local\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.assignment.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(g(enerate|enerat|enera|ener|ene|en|e)?|egen)\\\\\\\\s+((byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)\\\\\\\\s+)?([^=\\\\\\\\s]+)\\\\\\\\s*((==)|(=))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(set ty(pe|p)?)\\\\\\\\s+((byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)?\\\\\\\\s+)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[^`\\\\\\\\$]{81,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.compound.stata\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(bel|be|b)?)\\\\\\\\s+(var(iable|iabl|iab|ia|i)?)\\\\\\\\s+([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\\\\\\s+(`\\\\\\\")(.+)(\\\\\\\"')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[^`\\\\\\\\$]{81,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.stata\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(bel|be|b)?)\\\\\\\\s+(var(iable|iabl|iab|ia|i)?)\\\\\\\\s+([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31})\\\\\\\\s+(\\\\\\\")(.+)(\\\\\\\")\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(la(bel|be|b)?)\\\\\\\\s+(da(ta|t)?|var(iable|iabl|iab|ia|i)?|de(f|fi|fin|fine)?|val(ues|ue|u)?|di(r)?|l(ist|is|i)?|copy|drop|save|lang(uage|uag|ua|u)?)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(drop|keep)\\\\\\\\b(?!\\\\\\\\s+(if|in)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(if|in)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#operators\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.functions.data.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(drop|keep)\\\\\\\\s+(if|in)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*mata:?\\\\\\\\s*$\\\",\\\"comment\\\":\\\"won't match single-line Mata statements\\\",\\\"end\\\":\\\"^\\\\\\\\s*end\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.embedded.block.mata\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![^$\\\\\\\\s])(version|pragma|if|else|for|while|do|break|continue|goto|return)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.control.mata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.eltype.mata\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.orgtype.mata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(transmorphic|string|numeric|real|complex|(pointer(\\\\\\\\([^)]+\\\\\\\\))?))\\\\\\\\s+(matrix|vector|rowvector|colvector|scalar)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.mata\\\"},{\\\"comment\\\":\\\"need to end with whitespace character here or last group doesn't match\\\",\\\"match\\\":\\\"\\\\\\\\b(transmorphic|string|numeric|real|complex|(pointer(\\\\\\\\([^)]+\\\\\\\\))?))\\\\\\\\s\\\",\\\"name\\\":\\\"storage.type.eltype.mata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(matrix|vector|rowvector|colvector|scalar)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.orgtype.mata\\\"},{\\\"match\\\":\\\"\\\\\\\\!|\\\\\\\\+\\\\\\\\+|\\\\\\\\-\\\\\\\\-|\\\\\\\\&|\\\\\\\\'|\\\\\\\\?|\\\\\\\\\\\\\\\\|\\\\\\\\:\\\\\\\\:|\\\\\\\\,|\\\\\\\\.\\\\\\\\.|\\\\\\\\||\\\\\\\\=|\\\\\\\\=\\\\\\\\=|\\\\\\\\>\\\\\\\\=|\\\\\\\\<\\\\\\\\=|\\\\\\\\<|\\\\\\\\>|\\\\\\\\!\\\\\\\\=|\\\\\\\\#|\\\\\\\\+|\\\\\\\\-|\\\\\\\\*|\\\\\\\\^|\\\\\\\\/\\\",\\\"name\\\":\\\"keyword.operator.mata\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(odbc)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.flow.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"begin\\\":\\\"(exec?)(\\\\\\\\(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#commands-other\\\"}],\\\"repository\\\":{\\\"ascii-regex-character-class\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\*\\\\\\\\+\\\\\\\\?\\\\\\\\-\\\\\\\\.\\\\\\\\^\\\\\\\\$\\\\\\\\|\\\\\\\\[\\\\\\\\]\\\\\\\\(\\\\\\\\)\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.character-class.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"illegal.invalid.character-class.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"}},\\\"name\\\":\\\"constant.other.character-class.set.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-character-class\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\.)|.)\\\\\\\\-((\\\\\\\\\\\\\\\\.)|[^\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.other.character-class.range.stata\\\"}]}]},\\\"ascii-regex-functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexm with regular quotes i.e. \\\\\\\" \\\",\\\"match\\\":\\\"\\\\\\\\b(regexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexm with compound quotes\\\",\\\"match\\\":\\\"\\\\\\\\b(regexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexr with regular quotes i.e. \\\\\\\" \\\",\\\"match\\\":\\\"\\\\\\\\b(regexr)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*([^\\\\\\\\)]*)(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexr with compound quotes i.e. `\\\\\\\"text\\\\\\\"' \\\",\\\"match\\\":\\\"\\\\\\\\b(regexr)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*([^\\\\\\\\)]*)(\\\\\\\\))\\\"}]},\\\"ascii-regex-internals\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"comment\\\":\\\"matched when not a global, but must be ascii\\\",\\\"match\\\":\\\"\\\\\\\\$(?![a-zA-Z_\\\\\\\\{])\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"[\\\\\\\\?\\\\\\\\+\\\\\\\\*]\\\",\\\"name\\\":\\\"keyword.control.quantifier.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.control.or.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(?=\\\\\\\\?|\\\\\\\\*|\\\\\\\\+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"contentName\\\":\\\"invalid.illegal.regexm.stata\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}}},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-internals\\\"}]},{\\\"include\\\":\\\"#ascii-regex-character-class\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"comment\\\":\\\"NOTE: Error if I have .+ No idea why but it works fine it seems with just .\\\",\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.stata\\\"}]},\\\"braces-with-error\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\{)\\\\\\\\s*([^\\\\\\\\n]*)(?=\\\\\\\\n)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block.begin.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]+\\\",\\\"name\\\":\\\"illegal.invalid.name.stata\\\"}]}},\\\"comment\\\":\\\"correct with nothing else on the line but whitespace; before and after; before; after; correct\\\",\\\"end\\\":\\\"^\\\\\\\\s*(\\\\\\\\})\\\\\\\\s*$|^\\\\\\\\s*([^\\\\\\\\*\\\\\\\"\\\\\\\\}]+)\\\\\\\\s+(\\\\\\\\})\\\\\\\\s*([^\\\\\\\\*\\\\\\\"\\\\\\\\}/\\\\\\\\n]+)|^\\\\\\\\s*([^\\\\\\\"\\\\\\\\*\\\\\\\\}]+)\\\\\\\\s+(\\\\\\\\})|\\\\\\\\s*(\\\\\\\\})\\\\\\\\s*([^\\\\\\\"\\\\\\\\*\\\\\\\\}/\\\\\\\\n]+)|(\\\\\\\\})$\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"braces-without-error\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.block.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.block.end.stata\\\"}}}]},\\\"builtin_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(byte|int|long|float|double|str[1-9]?[0-9]?[0-9]?[0-9]?|strL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stata\\\"}]},\\\"builtin_variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(_b|_coef|_cons|_n|_N|_rc|_se)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.object.stata\\\"}]},\\\"commands-other\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Add on commands\\\",\\\"match\\\":\\\"\\\\\\\\b(reghdfe|ivreghdfe|ivreg2|outreg|gcollapse|gcontract|gegen|gisid|glevelsof|gquantiles)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"},{\\\"comment\\\":\\\"Built in commands\\\",\\\"match\\\":\\\"\\\\\\\\b(about|ac|acprplot|ado|adopath|adoupdate|alpha|ameans|an|ano|anov|anova|anova_terms|anovadef|aorder|ap|app|appe|appen|append|arch|arch_dr|arch_estat|arch_p|archlm|areg|areg_p|args|arima|arima_dr|arima_estat|arima_p|asmprobit|asmprobit_estat|asmprobit_lf|asmprobit_mfx__dlg|asmprobit_p|avplot|avplots|bcskew0|bgodfrey|binreg|bip0_lf|biplot|bipp_lf|bipr_lf|bipr_p|biprobit|bitest|bitesti|bitowt|blogit|bmemsize|boot|bootsamp|boxco_l|boxco_p|boxcox|boxcox_p|bprobit|br|break|brier|bro|brow|brows|browse|brr|brrstat|bs|bsampl_w|bsample|bsqreg|bstat|bstrap|ca|ca_estat|ca_p|cabiplot|camat|canon|canon_estat|canon_p|caprojection|cat|cc|cchart|cci|cd|censobs_table|centile|cf|char|chdir|checkdlgfiles|checkestimationsample|checkhlpfiles|checksum|chelp|ci|cii|cl|class|classutil|clear|cli|clis|clist|clog|clog_lf|clog_p|clogi|clogi_sw|clogit|clogit_lf|clogit_p|clogitp|clogl_sw|cloglog|clonevar|clslistarray|cluster|cluster_measures|cluster_stop|cluster_tree|cluster_tree_8|clustermat|cmdlog|cnr|cnre|cnreg|cnreg_p|cnreg_sw|cnsreg|codebook|collaps4|collapse|colormult_nb|colormult_nw|compare|compress|conf|confi|confir|confirm|conren|cons|const|constr|constra|constrai|constrain|constraint|contract|copy|copyright|copysource|cor|corc|corr|corr2data|corr_anti|corr_kmo|corr_smc|corre|correl|correla|correlat|correlate|corrgram|cou|coun|count|cprplot|crc|cret|cretu|cretur|creturn|cross|cs|cscript|cscript_log|csi|ct|ct_is|ctset|ctst_st|cttost|cumsp|cumul|cusum|cutil|d|datasig|datasign|datasigna|datasignat|datasignatu|datasignatur|datasignature|datetof|db|dbeta|de|dec|deco|decod|decode|deff|des|desc|descr|descri|describ|describe|dfbeta|dfgls|dfuller|di|di_g|dir|dirstats|dis|discard|disp|disp_res|disp_s|displ|displa|display|do|doe|doed|doedi|doedit|dotplot|dprobit|drawnorm|ds|ds_util|dstdize|duplicates|durbina|dwstat|dydx|ed|edi|edit|eivreg|emdef|en|enc|enco|encod|encode|eq|erase|ereg|ereg_lf|ereg_p|ereg_sw|ereghet|ereghet_glf|ereghet_glf_sh|ereghet_gp|ereghet_ilf|ereghet_ilf_sh|ereghet_ip|eret|eretu|eretur|ereturn|err|erro|error|est|est_cfexist|est_cfname|est_clickable|est_expand|est_hold|est_table|est_unhold|est_unholdok|estat|estat_default|estat_summ|estat_vce_only|esti|estimates|etodow|etof|etomdy|expand|expandcl|fac|fact|facto|factor|factor_estat|factor_p|factor_pca_rotated|factor_rotate|factormat|fcast|fcast_compute|fcast_graph|fdades|fdadesc|fdadescr|fdadescri|fdadescrib|fdadescribe|fdasav|fdasave|fdause|fh_st|file|filefilter|fillin|find_hlp_file|findfile|findit|fit|fl|fli|flis|flist|fpredict|frac_adj|frac_chk|frac_cox|frac_ddp|frac_dis|frac_dv|frac_in|frac_mun|frac_pp|frac_pq|frac_pv|frac_wgt|frac_xo|fracgen|fracplot|fracpoly|fracpred|fron_ex|fron_hn|fron_p|fron_tn|fron_tn2|frontier|ftodate|ftoe|ftomdy|ftowdate|gamhet_glf|gamhet_gp|gamhet_ilf|gamhet_ip|gamma|gamma_d2|gamma_p|gamma_sw|gammahet|gdi_hexagon|gdi_spokes|genrank|genstd|genvmean|gettoken|gladder|glim_l01|glim_l02|glim_l03|glim_l04|glim_l05|glim_l06|glim_l07|glim_l08|glim_l09|glim_l10|glim_l11|glim_l12|glim_lf|glim_mu|glim_nw1|glim_nw2|glim_nw3|glim_p|glim_v1|glim_v2|glim_v3|glim_v4|glim_v5|glim_v6|glim_v7|glm|glm_p|glm_sw|glmpred|glogit|glogit_p|gmeans|gnbre_lf|gnbreg|gnbreg_p|gomp_lf|gompe_sw|gomper_p|gompertz|gompertzhet|gomphet_glf|gomphet_glf_sh|gomphet_gp|gomphet_ilf|gomphet_ilf_sh|gomphet_ip|gphdot|gphpen|gphprint|gprefs|gprobi_p|gprobit|gr|gr7|gr_copy|gr_current|gr_db|gr_describe|gr_dir|gr_draw|gr_draw_replay|gr_drop|gr_edit|gr_editviewopts|gr_example|gr_example2|gr_export|gr_print|gr_qscheme|gr_query|gr_read|gr_rename|gr_replay|gr_save|gr_set|gr_setscheme|gr_table|gr_undo|gr_use|graph|grebar|greigen|grmeanby|gs_fileinfo|gs_filetype|gs_graphinfo|gs_stat|gsort|gwood|h|hareg|hausman|haver|he|heck_d2|heckma_p|heckman|heckp_lf|heckpr_p|heckprob|hel|help|hereg|hetpr_lf|hetpr_p|hetprob|hettest|hexdump|hilite|hist|histogram|hlogit|hlu|hmeans|hotel|hotelling|hprobit|hreg|hsearch|icd9|icd9_ff|icd9p|iis|impute|imtest|inbase|include|inf|infi|infil|infile|infix|inp|inpu|input|ins|insheet|insp|inspe|inspec|inspect|integ|inten|intreg|intreg_p|intrg2_ll|intrg_ll|intrg_ll2|ipolate|iqreg|ir|irf|irf_create|irfm|iri|is_svy|is_svysum|isid|istdize|ivprobit|ivprobit_p|ivreg|ivreg_footnote|ivtob_lf|ivtobit|ivtobit_p|jacknife|jknife|jkstat|joinby|kalarma1|kap|kapmeier|kappa|kapwgt|kdensity|ksm|ksmirnov|ktau|kwallis|labelbook|ladder|levelsof|leverage|lfit|lfit_p|li|lincom|line|linktest|lis|list|lloghet_glf|lloghet_glf_sh|lloghet_gp|lloghet_ilf|lloghet_ilf_sh|lloghet_ip|llogi_sw|llogis_p|llogist|llogistic|llogistichet|lnorm_lf|lnorm_sw|lnorma_p|lnormal|lnormalhet|lnormhet_glf|lnormhet_glf_sh|lnormhet_gp|lnormhet_ilf|lnormhet_ilf_sh|lnormhet_ip|lnskew0|loadingplot|(?<!\\\\\\\\.)log|logi|logis_lf|logistic|logistic_p|logit|logit_estat|logit_p|loglogs|logrank|loneway|lookfor|lookup|lowess|lpredict|lrecomp|lroc|lrtest|ls|lsens|lsens_x|lstat|ltable|ltriang|lv|lvr2plot|m|ma|mac|macr|macro|makecns|man|manova|manovatest|mantel|mark|markin|markout|marksample|mat|mat_capp|mat_order|mat_put_rr|mat_rapp|mata|mata_clear|mata_describe|mata_drop|mata_matdescribe|mata_matsave|mata_matuse|mata_memory|mata_mlib|mata_mosave|mata_rename|mata_which|matalabel|matcproc|matlist|matname|matr|matri|matrix|matrix_input__dlg|matstrik|mcc|mcci|md0_|md1_|md1debug_|md2_|md2debug_|mds|mds_estat|mds_p|mdsconfig|mdslong|mdsmat|mdsshepard|mdytoe|mdytof|me_derd|mean|means|median|memory|memsize|mfp|mfx|mhelp|mhodds|minbound|mixed_ll|mixed_ll_reparm|mkassert|mkdir|mkmat|mkspline|ml|ml_adjs|ml_bhhhs|ml_c_d|ml_check|ml_clear|ml_cnt|ml_debug|ml_defd|ml_e0|ml_e0_bfgs|ml_e0_cycle|ml_e0_dfp|ml_e0i|ml_e1|ml_e1_bfgs|ml_e1_bhhh|ml_e1_cycle|ml_e1_dfp|ml_e2|ml_e2_cycle|ml_ebfg0|ml_ebfr0|ml_ebfr1|ml_ebh0q|ml_ebhh0|ml_ebhr0|ml_ebr0i|ml_ecr0i|ml_edfp0|ml_edfr0|ml_edfr1|ml_edr0i|ml_eds|ml_eer0i|ml_egr0i|ml_elf|ml_elf_bfgs|ml_elf_bhhh|ml_elf_cycle|ml_elf_dfp|ml_elfi|ml_elfs|ml_enr0i|ml_enrr0|ml_erdu0|ml_erdu0_bfgs|ml_erdu0_bhhh|ml_erdu0_bhhhq|ml_erdu0_cycle|ml_erdu0_dfp|ml_erdu0_nrbfgs|ml_exde|ml_footnote|ml_geqnr|ml_grad0|ml_graph|ml_hbhhh|ml_hd0|ml_hold|ml_init|ml_inv|ml_log|ml_max|ml_mlout|ml_mlout_8|ml_model|ml_nb0|ml_opt|ml_p|ml_plot|ml_query|ml_rdgrd|ml_repor|ml_s_e|ml_score|ml_searc|ml_technique|ml_unhold|mleval|mlf_|mlmatbysum|mlmatsum|mlog|mlogi|mlogit|mlogit_footnote|mlogit_p|mlopts|mlsum|mlvecsum|mnl0_|mor|more|mov|move|mprobit|mprobit_lf|mprobit_p|mrdu0_|mrdu1_|mvdecode|mvencode|mvreg|mvreg_estat|nbreg|nbreg_al|nbreg_lf|nbreg_p|nbreg_sw|nestreg|net|newey|newey_p|news|nl|nlcom|nlcom_p|nlexp2|nlexp2a|nlexp3|nlgom3|nlgom4|nlinit|nllog3|nllog4|nlog_rd|nlogit|nlogit_p|nlogitgen|nlogittree|nlpred|nobreak|notes_dlg|nptrend|numlabel|numlist|old_ver|olo|olog|ologi|ologi_sw|ologit|ologit_p|ologitp|on|one|onew|onewa|oneway|op_colnm|op_comp|op_diff|op_inv|op_str|opr|opro|oprob|oprob_sw|oprobi|oprobi_p|oprobit|oprobitp|opts_exclusive|order|orthog|orthpoly|ou|out|outf|outfi|outfil|outfile|outs|outsh|outshe|outshee|outsheet|ovtest|pac|palette|parse_dissim|pause|pca|pca_display|pca_estat|pca_p|pca_rotate|pcamat|pchart|pchi|pcorr|pctile|pentium|pergram|personal|peto_st|pkcollapse|pkcross|pkequiv|pkexamine|pkshape|pksumm|plugin|pnorm|poisgof|poiss_lf|poiss_sw|poisso_p|poisson|poisson_estat|post|postclose|postfile|postutil|pperron|prais|prais_e|prais_e2|prais_p|predict|predictnl|preserve|print|prob|probi|probit|probit_estat|probit_p|proc_time|procoverlay|procrustes|procrustes_estat|procrustes_p|profiler|prop|proportion|prtest|prtesti|pwcorr|pwd|qs|qby|qbys|qchi|qladder|qnorm|qqplot|qreg|qreg_c|qreg_p|qreg_sw|qu|quadchk|quantile|que|quer|query|range|ranksum|ratio|rchart|rcof|recast|recode|reg|reg3|reg3_p|regdw|regr|regre|regre_p2|regres|regres_p|regress|regress_estat|regriv_p|remap|ren|rena|renam|rename|renpfix|repeat|reshape|restore|ret|retu|retur|return|rmdir|robvar|roccomp|rocf_lf|rocfit|rocgold|rocplot|roctab|rologit|rologit_p|rot|rota|rotat|rotate|rotatemat|rreg|rreg_p|ru|run|runtest|rvfplot|rvpplot|safesum|sample|sampsi|savedresults|sc|scatter|scm_mine|sco|scob_lf|scob_p|scobi_sw|scobit|scor|score|scoreplot|scoreplot_help|scree|screeplot|screeplot_help|sdtest|sdtesti|se|search|separate|seperate|serrbar|serset|set|set_defaults|sfrancia|sh|she|shel|shell|shewhart|signestimationsample|signrank|signtest|simul|sktest|sleep|slogit|slogit_d2|slogit_p|smooth|snapspan|so|sor|sort|spearman|spikeplot|spikeplt|spline_x|split|sqreg|sqreg_p|sret|sretu|sretur|sreturn|ssc|st|st_ct|st_hc|st_hcd|st_hcd_sh|st_is|st_issys|st_note|st_promo|st_set|st_show|st_smpl|st_subid|stack|stbase|stci|stcox|stcox_estat|stcox_fr|stcox_fr_ll|stcox_p|stcox_sw|stcoxkm|stcstat|stcurv|stcurve|stdes|stem|stepwise|stfill|stgen|stir|stjoin|stmc|stmh|stphplot|stphtest|stptime|strate|streg|streg_sw|streset|sts|stset|stsplit|stsum|sttocc|sttoct|stvary|su|suest|sum|summ|summa|summar|summari|summariz|summarize|sunflower|sureg|survcurv|survsum|svar|svar_p|svmat|svy_disp|svy_dreg|svy_est|svy_est_7|svy_estat|svy_get|svy_gnbreg_p|svy_head|svy_header|svy_heckman_p|svy_heckprob_p|svy_intreg_p|svy_ivreg_p|svy_logistic_p|svy_logit_p|svy_mlogit_p|svy_nbreg_p|svy_ologit_p|svy_oprobit_p|svy_poisson_p|svy_probit_p|svy_regress_p|svy_sub|svy_sub_7|svy_x|svy_x_7|svy_x_p|svydes|svygen|svygnbreg|svyheckman|svyheckprob|svyintreg|svyintrg|svyivreg|svylc|svylog_p|svylogit|svymarkout|svymean|svymlog|svymlogit|svynbreg|svyolog|svyologit|svyoprob|svyoprobit|svyopts|svypois|svypoisson|svyprobit|svyprobt|svyprop|svyratio|svyreg|svyreg_p|svyregress|svyset|svytab|svytest|svytotal|sw|swilk|symmetry|symmi|symplot|sysdescribe|sysdir|sysuse|szroeter|ta|tab|tab1|tab2|tab_or|tabd|tabdi|tabdis|tabdisp|tabi|table|tabodds|tabstat|tabu|tabul|tabula|tabulat|tabulate|te|tes|test|testnl|testparm|teststd|tetrachoric|time_it|timer|tis|tob|tobi|tobit|tobit_p|tobit_sw|token|tokeni|tokeniz|tokenize|total|translate|translator|transmap|treat_ll|treatr_p|treatreg|trim|trnb_cons|trnb_mean|trpoiss_d2|trunc_ll|truncr_p|truncreg|tsappend|tset|tsfill|tsline|tsline_ex|tsreport|tsrevar|tsrline|tsset|tssmooth|tsunab|ttest|ttesti|tut_chk|tut_wait|tutorial|tw|tware_st|two|twoway|twoway__fpfit_serset|twoway__function_gen|twoway__histogram_gen|twoway__ipoint_serset|twoway__ipoints_serset|twoway__kdensity_gen|twoway__lfit_serset|twoway__normgen_gen|twoway__pci_serset|twoway__qfit_serset|twoway__scatteri_serset|twoway__sunflower_gen|twoway_ksm_serset|ty|typ|type|typeof|unab|unabbrev|unabcmd|update|uselabel|var|var_mkcompanion|var_p|varbasic|varfcast|vargranger|varirf|varirf_add|varirf_cgraph|varirf_create|varirf_ctable|varirf_describe|varirf_dir|varirf_drop|varirf_erase|varirf_graph|varirf_ograph|varirf_rename|varirf_set|varirf_table|varlmar|varnorm|varsoc|varstable|varstable_w|varstable_w2|varwle|vec|vec_fevd|vec_mkphi|vec_p|vec_p_w|vecirf_create|veclmar|veclmar_w|vecnorm|vecnorm_w|vecrank|vecstable|verinst|vers|versi|versio|version|view|viewsource|vif|vwls|wdatetof|webdescribe|webseek|webuse|wh|whelp|whi|which|wilc_st|wilcoxon|win|wind|windo|window|winexec|wntestb|wntestq|xchart|xcorr|xi|xmlsav|xmlsave|xmluse|xpose|xsh|xshe|xshel|xshell|xt_iis|xt_tis|xtab_p|xtabond|xtbin_p|xtclog|xtcloglog|xtcloglog_d2|xtcloglog_pa_p|xtcloglog_re_p|xtcnt_p|xtcorr|xtdata|xtdes|xtfront_p|xtfrontier|xtgee|xtgee_elink|xtgee_estat|xtgee_makeivar|xtgee_p|xtgee_plink|xtgls|xtgls_p|xthaus|xthausman|xtht_p|xthtaylor|xtile|xtint_p|xtintreg|xtintreg_d2|xtintreg_p|xtivreg|xtline|xtline_ex|xtlogit|xtlogit_d2|xtlogit_fe_p|xtlogit_pa_p|xtlogit_re_p|xtmixed|xtmixed_estat|xtmixed_p|xtnb_fe|xtnb_lf|xtnbreg|xtnbreg_pa_p|xtnbreg_refe_p|xtpcse|xtpcse_p|xtpois|xtpoisson|xtpoisson_d2|xtpoisson_pa_p|xtpoisson_refe_p|xtpred|xtprobit|xtprobit_d2|xtprobit_re_p|xtps_fe|xtps_lf|xtps_ren|xtps_ren_8|xtrar_p|xtrc|xtrc_p|xtrchh|xtrefe_p|yx|yxview__barlike_draw|yxview_area_draw|yxview_bar_draw|yxview_dot_draw|yxview_dropline_draw|yxview_function_draw|yxview_iarrow_draw|yxview_ilabels_draw|yxview_normal_draw|yxview_pcarrow_draw|yxview_pcbarrow_draw|yxview_pccapsym_draw|yxview_pcscatter_draw|yxview_pcspike_draw|yxview_rarea_draw|yxview_rbar_draw|yxview_rbarm_draw|yxview_rcap_draw|yxview_rcapsym_draw|yxview_rconnected_draw|yxview_rline_draw|yxview_rscatter_draw|yxview_rspike_draw|yxview_spike_draw|yxview_sunflower_draw|zap_s|zinb|zinb_llf|zinb_plf|zip|zip_llf|zip_p|zip_plf|zt_ct_5|zt_hc_5|zt_hcd_5|zt_is_5|zt_iss_5|zt_sho_5|zt_smp_5|ztnb|ztnb_p|ztp|ztp_p|prtab|prchange|eststo|estout|esttab|estadd|estpost|ivregress|xtreg|xtreg_be|xtreg_fe|xtreg_ml|xtreg_pa_p|xtreg_re|xtregar|xtrere_p|xtset|xtsf_ll|xtsf_llti|xtsum|xttab|xttest0|xttobit|xttobit_p|xttrans)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.stata\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments-double-slash\\\"},{\\\"include\\\":\\\"#comments-star\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"comments-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\*/\\\\\\\\s+\\\\\\\\*[^\\\\\\\\n]*)|(\\\\\\\\*/(?!\\\\\\\\*))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.stata\\\"}},\\\"name\\\":\\\"comment.block.stata\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"this ends and restarts a comment block. but need to catch this so that it doesn't start _another_ level of comment blocks\\\",\\\"match\\\":\\\"\\\\\\\\*/\\\\\\\\*\\\"},{\\\"include\\\":\\\"#docblockr-comment\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"include\\\":\\\"#docstring\\\"}]}]},\\\"comments-double-slash\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^//|(?<=\\\\\\\\s)//)(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"}]}]},\\\"comments-star\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(\\\\\\\\*)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"comment\\\":\\\"TODO! need to except out the occasion that a * comes after a /// on the previous line. May be easiest to join with the comment.line.triple-slash.stata below\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.star.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"},{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line-continuation.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"comments-triple-slash\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^///|(?<=\\\\\\\\s)///)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.triple-slash.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblockr-comment\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#factorvariables\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(\\\\\\\\d+\\\\\\\\.\\\\\\\\d*(e[\\\\\\\\-\\\\\\\\+]?\\\\\\\\d+)?))(?=[^a-zA-Z_])\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"(?<=[^0-9a-zA-Z_])(?i:(\\\\\\\\.\\\\\\\\d+(e[\\\\\\\\-\\\\\\\\+]?\\\\\\\\d+)?))\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(\\\\\\\\d+e[\\\\\\\\-\\\\\\\\+]?\\\\\\\\d+))\\\",\\\"name\\\":\\\"constant.numeric.float.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.decimal.stata\\\"},{\\\"match\\\":\\\"(?<![\\\\\\\\w])(\\\\\\\\.(?![\\\\\\\\./]))(?![\\\\\\\\w])\\\",\\\"name\\\":\\\"constant.language.missing.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b_all\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.allvars.stata\\\"}]},\\\"docblockr-comment\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(@(error|ERROR|Error))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.docblockr.stata\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(@\\\\\\\\w+)\\\\\\\\b\\\"}]},\\\"docstring\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"'''\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"'''\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"name\\\":\\\"string.quoted.docstring.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"name\\\":\\\"string.quoted.docstring.stata\\\"}]},\\\"factorvariables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(i|c|o)\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\",\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(i?b)((\\\\\\\\d+)|n)\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(i?b)(\\\\\\\\()(#\\\\\\\\d+|first|last|freq)(\\\\\\\\))\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(i?o?)(\\\\\\\\d+)\\\\\\\\.(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.language.factorvars.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(i?o?)(\\\\\\\\()(.*?)(\\\\\\\\))(\\\\\\\\.)(?=[\\\\\\\\w&&[^0-9]]|\\\\\\\\([\\\\\\\\w&&[^0-9]])\\\"}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b((abbrev|abs|acos|acosh|asin|asinh|atan|atan2|atanh|autocode|betaden|binomial|binomialp|binomialtail|binormalbofd|byteorder|c|cauchy|cauchyden|cauchytail|Cdhms|ceil|char|chi2|chi2den|chi2tail|Chms|cholesky|chop|clip|clock|Clock|cloglog|Cmdyhms|cofC|Cofc|cofd|Cofd|coleqnumb|collatorlocale|collatorversion|colnfreeparms|colnumb|colsof|comb|cond|corr|cos|cosh|daily|date|day|det|dgammapda|dgammapdada|dgammapdadx|dgammapdx|dgammapdxdx|dhms|diag|diag0cnt|digamma|dofb|dofc|dofC|dofh|dofm|dofq|dofw|dofy|dow|doy|dunnettprob|e|el|epsdouble|epsfloat|exp|exponential|exponentialden|exponentialtail|F|Fden|fileexists|fileread|filereaderror|filewrite|float|floor|fmtwidth|Ftail|gammaden|gammap|gammaptail|get|hadamard|halfyear|halfyearly|hh|hhC|hms|hofd|hours|hypergeometric|hypergeometricp|I|ibeta|ibetatail|igaussian|igaussianden|igaussiantail|indexnot|inlist|inrange|int|inv|invbinomial|invbinomialtail|invcauchy|invcauchytail|invchi2|invchi2tail|invcloglog|invdunnettprob|invexponential|invexponentialtail|invF|invFtail|invgammap|invgammaptail|invibeta|invibetatail|invigaussian|invigaussiantail|invlaplace|invlaplacetail|invlogistic|invlogistictail|invlogit|invnbinomial|invnbinomialtail|invnchi2|invnchi2tail|invnF|invnFtail|invnibeta|invnormal|invnt|invnttail|invpoisson|invpoissontail|invsym|invt|invttail|invtukeyprob|invweibull|invweibullph|invweibullphtail|invweibulltail|irecode|issymmetric|itrim|J|laplace|laplaceden|laplacetail|length|ln|lncauchyden|lnfactorial|lngamma|lnigammaden|lnigaussianden|lniwishartden|lnlaplaceden|lnmvnormalden|lnnormal|lnnormalden|lnwishartden|log|log10|logistic|logisticden|logistictail|logit|lower|ltrim|matmissing|matrix|matuniform|max|maxbyte|maxdouble|maxfloat|maxint|maxlong|mdy|mdyhms|mi|min|minbyte|mindouble|minfloat|minint|minlong|minutes|missing|mm|mmC|mod|mofd|month|monthly|mreldif|msofhours|msofminutes|msofseconds|nbetaden|nbinomial|nbinomialp|nbinomialtail|nchi2|nchi2den|nchi2tail|nF|nFden|nFtail|nibeta|normal|normalden|npnchi2|npnF|npnt|nt|ntden|nttail|nullmat|plural|poisson|poissonp|poissontail|proper|qofd|quarter|quarterly|r|rbeta|rbinomial|rcauchy|rchi2|real|recode|regexs|reldif|replay|return|reverse|rexponential|rgamma|rhypergeometric|rigaussian|rlaplace|rlogistic|rnbinomial|rnormal|round|roweqnumb|rownfreeparms|rownumb|rowsof|rpoisson|rt|rtrim|runiform|runiformint|rweibull|rweibullph|s|scalar|seconds|sign|sin|sinh|smallestdouble|soundex|sqrt|ss|ssC|string|stritrim|strlen|strlower|strltrim|strmatch|strofreal|strpos|strproper|strreverse|strrpos|strrtrim|strtoname|strtrim|strupper|subinstr|subinword|substr|sum|sweep|t|tan|tanh|tc|tC|td|tden|th|tin|tm|tobytes|tq|trace|trigamma|trim|trunc|ttail|tukeyprob|tw|twithin|uchar|udstrlen|udsubstr|uisdigit|uisletter|upper|ustrcompare|ustrcompareex|ustrfix|ustrfrom|ustrinvalidcnt|ustrleft|ustrlen|ustrlower|ustrltrim|ustrnormalize|ustrpos|ustrregexs|ustrreverse|ustrright|ustrrpos|ustrrtrim|ustrsortkey|ustrsortkeyex|ustrtitle|ustrto|ustrtohex|ustrtoname|ustrtrim|ustrunescape|ustrupper|ustrword|ustrwordcount|usubinstr|usubstr|vec|vecdiag|week|weekly|weibull|weibullden|weibullph|weibullphden|weibullphtail|weibulltail|wofd|word|wordbreaklocale|wordcount|year|yearly|yh|ym|yofd|yq|yw)|([\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.custom.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"include\\\":\\\"#braces-without-error\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"}]},{\\\"include\\\":\\\"#ascii-regex-functions\\\"},{\\\"include\\\":\\\"#unicode-regex-functions\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#subscripts\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#macro-commands\\\"},{\\\"include\\\":\\\"#braces-without-error\\\"}]}]},\\\"macro-commands\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(loc(al|a)?)\\\\\\\\s+([\\\\\\\\w'`\\\\\\\\$\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}]+)\\\\\\\\s*(?=:|=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-extended-functions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(gl(obal|oba|ob|o)?)\\\\\\\\s+(?=[\\\\\\\\w`\\\\\\\\$])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\})|(?=\\\\\\\\\\\\\\\"|\\\\\\\\s|\\\\\\\\n|/|,|=)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved-names\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(loc(al|a)?)\\\\\\\\s+(\\\\\\\\+\\\\\\\\+|\\\\\\\\-\\\\\\\\-)?(?=[\\\\\\\\w`\\\\\\\\$])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\"|\\\\\\\\s|\\\\\\\\n|/|,|=)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(tempvar|tempname|tempfile)\\\\\\\\s*(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"include\\\":\\\"#macro-local-identifiers\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(ma(cro|cr|c)?)\\\\\\\\s+(drop|l(ist|is|i)?)\\\\\\\\s*(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.macro.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-extended-functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(properties)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(t(ype|yp|y)?|f(ormat|orma|orm|or|o)?|val(ue|u)?\\\\\\\\s+l(able|abl|ab|a)?|var(iable|iabl|iab|ia|i)?\\\\\\\\s+l(abel|abe|ab|a)?|data\\\\\\\\s+l(able|abl|ab|a)?|sort(edby|edb|ed|e)?|lab(el|e)?|maxlength|constraint|char)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(permname)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(adosubdir|dir|files?|dirs?|other|sysdir)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(env(ironment|ironmen|ironme|ironm|iron|iro|ir|i)?)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(all\\\\\\\\s+(globals|scalars|matrices)|((numeric|string)\\\\\\\\s+scalars))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list)\\\\\\\\s+(uniq|dups|sort|clean|retok(enize|eniz|eni|en|e)?|sizeof)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.list.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list)\\\\\\\\s+(\\\\\\\\w{1,32})\\\\\\\\s+(\\\\\\\\||&|\\\\\\\\-|===|==|in)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.quoted.double.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(list\\\\\\\\s+posof)\\\\\\\\s+(\\\\\\\")(\\\\\\\\w+)(\\\\\\\")\\\\\\\\s+(in)\\\\\\\\s+(\\\\\\\\w{1,32})\\\"},{\\\"match\\\":\\\"\\\\\\\\b(rown(ames|ame|am|a)?|coln(ames|ame|am|a)?|rowf(ullnames|ullname|ullnam|ullna|ulln|ull|ul|u)?|colf(ullnames|ullname|ullnam|ullna|ulln|ull|ul|u)?|roweq?|coleq?|rownumb|colnumb|roweqnumb|coleqnumb|rownfreeparms|colnfreeparms|rownlfs|colnlfs|rowsof|colsof|rowvarlist|colvarlist|rowlfnames|collfnames)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\b(tsnorm)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b((copy|(ud|u)?strlen)\\\\\\\\s+(loc(al|a)?|gl(obal|oba|ob|o)?))\\\\\\\\s+([^']+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"}},\\\"match\\\":\\\"\\\\\\\\b(word\\\\\\\\s+count)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"}},\\\"match\\\":\\\"(word|piece)\\\\\\\\s+([\\\\\\\\s`'\\\\\\\\w]+)\\\\\\\\s+(of)\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(subinstr\\\\\\\\s+(loc(al|a)?|gl(obal|oba|ob|o)?))\\\\\\\\s+(\\\\\\\\w{1,32})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"}},\\\"end\\\":\\\"(?=//|\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.macro.extendedfcn.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.class.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"match\\\":\\\"(count|coun|cou|co|c)(\\\\\\\\()(local|loca|loc|global|globa|glob|glo|gl)\\\\\\\\s+(\\\\\\\\w{1,32})(\\\\\\\\))\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"macro-global\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\$)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"begin\\\":\\\"[^\\\\\\\\w]\\\",\\\"end\\\":\\\"\\\\\\\\n|(?=})\\\",\\\"name\\\":\\\"comment.line.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,32}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\$\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\w)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}|_\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-global-escaped\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\\\\\\\\\\\\\\\\\$)(\\\\\\\\\\\\\\\\\\\\\\\\{)?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\\\\\\\\\\\\\\\\\})|(?=\\\\\\\\\\\\\\\"|\\\\\\\\s|\\\\\\\\n|/|,)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9_]]\\\\\\\\w{0,31}|_\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(`)(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(`)(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"}},\\\"contentName\\\":\\\"meta.macro-extended-function.stata\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-extended-functions\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"}]},{\\\"begin\\\":\\\"(`)(macval)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"contentName\\\":\\\"meta.macro-extended-function.stata\\\",\\\"end\\\":\\\"(\\\\\\\\))(')\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},{\\\"begin\\\":\\\"`(?!\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+|\\\\\\\\-\\\\\\\\-\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#comments-block\\\"},{\\\"begin\\\":\\\"[^\\\\\\\\w]\\\",\\\"end\\\":\\\"\\\\\\\\n|(?=')\\\",\\\"name\\\":\\\"comment.line.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local-escaped\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\`(?!\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"comment\\\":\\\"appropriately color macros that have embedded escaped `,', and $ characters for lazy evaluation\\\",\\\"end\\\":\\\"\\\\\\\\\\\\\\\\'|'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]}]},\\\"macro-local-identifiers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[^\\\\\\\\w'`\\\\\\\\$\\\\\\\\(\\\\\\\\)\\\\\\\\s]\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{32,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{1,31}\\\",\\\"name\\\":\\\"entity.name.type.class.stata\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"++ and -- must come first to support ligatures\\\",\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+|\\\\\\\\-\\\\\\\\-|\\\\\\\\+|\\\\\\\\-|\\\\\\\\*|\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"comment\\\":\\\"match division operator but not path separator\\\",\\\"match\\\":\\\"(?<![\\\\\\\\w.&&[^0-9]])/(?![\\\\\\\\w.&&[^0-9]]|$)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.stata\\\"},{\\\"comment\\\":\\\"match division operator but not path separator\\\",\\\"match\\\":\\\"(?<![\\\\\\\\w.&&[^0-9]])\\\\\\\\\\\\\\\\(?![\\\\\\\\w.&&[^0-9]]|$)\\\",\\\"name\\\":\\\"keyword.operator.matrix.addrow.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.graphcombine.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\&|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.stata\\\"},{\\\"match\\\":\\\"(?:<=|>=|:=|==|!=|~=|<|>|=|!!|!)\\\",\\\"name\\\":\\\"keyword.operator.comparison.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\(|\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.operator.parentheses.stata\\\"},{\\\"match\\\":\\\"(##|#)\\\",\\\"name\\\":\\\"keyword.operator.factor-variables.stata\\\"},{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.operator.format.stata\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"keyword.operator.delimiter.stata\\\"}]},\\\"reserved-names\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(_all|_b|byte|_coef|_cons|double|float|if|in|int|long|_n|_N|_pi|_pred|_rc|_skip|str[0-9]+|strL|using|with)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"[^\\\\\\\\w'`\\\\\\\\$\\\\\\\\(\\\\\\\\)\\\\\\\\s]\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"[0-9][\\\\\\\\w]{31,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\w{33,}\\\",\\\"name\\\":\\\"invalid.illegal.name.stata\\\"}]},\\\"string-compound\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"`\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"\\\\\\\"'|(?=\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"}},\\\"name\\\":\\\"string.quoted.double.compound.stata\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"This must come before #string-regular and #string-compound to accurately color `\\\\\\\"\\\\\\\"\\\\\\\"' in strings\\\",\\\"match\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.compound.stata\\\"},{\\\"comment\\\":\\\"see https://github.com/kylebarron/language-stata/issues/53\\\",\\\"match\\\":\\\"```(?=[^']*\\\\\\\")\\\",\\\"name\\\":\\\"meta.markdown.code.block.stata\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}]},\\\"string-regular\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!`)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\")(')?|(?=\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"}},\\\"name\\\":\\\"string.quoted.double.stata\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"see https://github.com/kylebarron/language-stata/issues/53\\\",\\\"match\\\":\\\"```(?=[^']*\\\\\\\")\\\",\\\"name\\\":\\\"meta.markdown.code.block.stata\\\"},{\\\"include\\\":\\\"#macro-local-escaped\\\"},{\\\"include\\\":\\\"#macro-global-escaped\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"}]}]},\\\"subscripts\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[\\\\\\\\w'])(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"}},\\\"comment\\\":\\\"highlight expressions, like [_n], when using subscripts on a variable\\\",\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"name\\\":\\\"meta.subscripts.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#builtin_variables\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#functions\\\"}]}]},\\\"unicode-regex-character-class\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[wWsSdD]|\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.character-class.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character-class.stata\\\"}},\\\"name\\\":\\\"constant.other.character-class.set.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-character-class\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.backslash.stata\\\"}},\\\"match\\\":\\\"((\\\\\\\\\\\\\\\\.)|.)\\\\\\\\-((\\\\\\\\\\\\\\\\.)|[^\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.other.character-class.range.stata\\\"}]}]},\\\"unicode-regex-functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexm with regular quotes i.e. \\\\\\\" \\\",\\\"match\\\":\\\"\\\\\\\\b(ustrregexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)([,0-9\\\\\\\\s]*)?\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexm with compound quotes\\\",\\\"match\\\":\\\"\\\\\\\\b(ustrregexm)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')([,0-9\\\\\\\\s]*)?\\\\\\\\s*(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"name\\\":\\\"invalid.illegal.punctuation.stata\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexr with regular quotes i.e. \\\\\\\" \\\",\\\"match\\\":\\\"\\\\\\\\b(ustrregexrf|ustrregexra)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"(')?)\\\\\\\\s*([^\\\\\\\\)]*)(\\\\\\\\))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.builtin.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.stata\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.stata\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.stata\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.variable.begin.stata\\\"},{\\\"include\\\":\\\"#string-compound\\\"},{\\\"include\\\":\\\"#string-regular\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"[\\\\\\\\w&&[^0-9]]\\\\\\\\w{0,31}\\\",\\\"name\\\":\\\"variable.parameter.function.stata\\\"},{\\\"include\\\":\\\"#comments-triple-slash\\\"},{\\\"include\\\":\\\"#constants\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.stata\\\"}},\\\"comment\\\":\\\"color regexr with compound quotes i.e. `\\\\\\\"text\\\\\\\"' \\\",\\\"match\\\":\\\"\\\\\\\\b(ustrregexrf|ustrregexra)(\\\\\\\\()([^,]+)(,)\\\\\\\\s*(`\\\\\\\")([^\\\\\\\"]+)(\\\\\\\"')\\\\\\\\s*([^\\\\\\\\)]*)(\\\\\\\\))\\\"}]},\\\"unicode-regex-internals\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[bBAZzG]|\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"comment\\\":\\\"matched when not a global\\\",\\\"match\\\":\\\"\\\\\\\\$(?![[\\\\\\\\w&&[^0-9_]][\\\\\\\\w]{0,31}|_[\\\\\\\\w]{1,31}\\\\\\\\{])\\\",\\\"name\\\":\\\"keyword.control.anchor.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[1-9][0-9]?\\\",\\\"name\\\":\\\"keyword.other.back-reference.stata\\\"},{\\\"match\\\":\\\"[?+*][?+]?|\\\\\\\\{(\\\\\\\\d+,\\\\\\\\d+|\\\\\\\\d+,|,\\\\\\\\d+|\\\\\\\\d+)\\\\\\\\}\\\\\\\\??\\\",\\\"name\\\":\\\"keyword.operator.quantifier.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.or.stata\\\"},{\\\"begin\\\":\\\"\\\\\\\\((?!\\\\\\\\?\\\\\\\\#|\\\\\\\\?=|\\\\\\\\?!|\\\\\\\\?<=|\\\\\\\\?<!)\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.operator.group.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?\\\\\\\\#\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.stata\\\"},{\\\"comment\\\":\\\"We are restrictive in what we allow to go after the comment character to avoid false positives, since the availability of comments depend on regexp flags.\\\",\\\"match\\\":\\\"(?<=^|\\\\\\\\s)#\\\\\\\\s[[a-zA-Z0-9,. \\\\\\\\t?!-:][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]*$\\\",\\\"name\\\":\\\"comment.line.number-sign.stata\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\\\\\\?[iLmsux]+\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.other.option-toggle.stata\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()((\\\\\\\\?=)|(\\\\\\\\?!)|(\\\\\\\\?<=)|(\\\\\\\\?<!))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.assertion.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.assertion.look-ahead.stata\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.assertion.negative-look-ahead.stata\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.assertion.look-behind.stata\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.assertion.negative-look-behind.stata\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.group.stata\\\"}},\\\"name\\\":\\\"meta.group.assertion.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?\\\\\\\\(([1-9][0-9]?|[a-zA-Z_][a-zA-Z_0-9]*)\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.stata\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.assertion.conditional.stata\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.section.back-reference.stata\\\"}},\\\"comment\\\":\\\"we can make this more sophisticated to match the | character that separates yes-pattern from no-pattern, but it's not really necessary.\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.assertion.conditional.stata\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unicode-regex-internals\\\"}]},{\\\"include\\\":\\\"#unicode-regex-character-class\\\"},{\\\"include\\\":\\\"#macro-local\\\"},{\\\"include\\\":\\\"#macro-global\\\"},{\\\"comment\\\":\\\"NOTE: Error if I have .+ No idea why but it works fine it seems with just .\\\",\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.stata\\\"}]}},\\\"scopeName\\\":\\\"source.stata\\\",\\\"embeddedLangs\\\":[\\\"sql\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._sql_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/stata.mjs\n"));

/***/ })

}]);