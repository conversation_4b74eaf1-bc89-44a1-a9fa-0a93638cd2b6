import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { getQueryClient } from '@/utils/query-client';
import ShowFacilityGeneralInfo from './components/show_facility_general_info';

export default async function ShowFacilityPage({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['Show Facility', { id: Number(id) }],
        queryFn: () => ShowLocationQuery.get(Number(id)),
      });
    }

    return (
      <div>
        <HydrationBoundary state={dehydrate(client)}>
          <ShowFacilityGeneralInfo
            id={Number.isNaN(Number(id)) ? undefined : Number(id)}
          />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/show-facility/add');
  }
}
