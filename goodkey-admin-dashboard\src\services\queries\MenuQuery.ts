import { MenuConfig, MenuItemBrief } from '@/models/MenuItem';
import fetcher from './fetcher';
import { urlToFile } from '@/utils/file-helper';
import { BriefData } from '@/models/BriefData';
import { MenuItem } from '@/models/Menu';
import { MenuItemData } from '@/schema/MenuItemSchema';
import { ItemStatus } from '@/models/ItemStatus';

const MenuQuery = {
  tags: ['menu-item'] as const,
  getAll: async () => fetcher<MenuItemBrief[]>(`menu/items`),
  getBrief: async () => fetcher<MenuItem[]>('MenuItem'),
  getSections: async () =>
    fetcher<(BriefData & { isDashboard?: boolean })[]>(`menu`),
  getBySection: async (sectionName: string) =>
    fetcher<MenuItemBrief[]>(`menu/ItemsBySection/${sectionName}`),
  getConfig: async (menuId: number) =>
    fetcher<MenuConfig>(`menu/config/${menuId}`),
  get: async (id: number) => {
    const data = await fetcher<MenuItemData & { imagePath?: string }>(
      `menu/items/${id}`,
    );
    if (data.imagePath) {
      const file = await urlToFile('/images' + data.imagePath);
      return {
        ...data,
        image: file ? [file] : [],
      } satisfies MenuItemData;
    } else return data satisfies MenuItemData;
  },
  update: (id: number) => async (data: MenuItemData) => {
    const formData = new FormData();
    if (data.image?.length) formData.append('image', data.image[0]);
    formData.append('name', data.name);
    if (data.description && data.description.length > 0)
      formData.append('description', data.description);
    if (data.metaDescription && data.metaDescription.length > 0)
      formData.append('metaDescription', data.metaDescription);
    if (data.keywords && data.keywords.length > 0)
      formData.append('keywords', data.keywords);
    if (data.url) formData.append('url', data.url);
    formData.append('displayOrder', data.displayOrder.toString());
    if (data.permissionKey)
      formData.append('permissionKey', data.permissionKey);
    if (data.iconName) formData.append('iconName', data.iconName);
    if (data.target) formData.append('target', data.target);
    if (data.parentId) formData.append('parentId', data.parentId);
    if (data.sectionId) formData.append('sectionId', data.sectionId);
    if (data.roleId) formData.append('roleId', data.roleId);
    if (data.level) formData.append('level', data.level);
    if (data.isStatic) formData.append('isStatic', data.isStatic.toString());
    if (data.isVisible) formData.append('isVisible', data.isVisible.toString());
    if (data.direction) formData.append('direction', data.direction.toString());
    if (data.isParent) formData.append('isParent', data.isParent.toString());
    if (data.isDashboard)
      formData.append('isDashboard', data.isDashboard.toString());

    data.menuIds?.forEach((i) => formData.append('menuIds', i));

    return fetcher<number>(
      `menu/items/${id}`,
      {
        method: 'PATCH',

        body: formData,
      },
      true,
    );
  },

  create: async (data: MenuItemData) => {
    const formData = new FormData();
    if (data.image?.length) formData.append('image', data.image[0]);
    formData.append('name', data.name);
    if (data.description && data.description.length > 0)
      formData.append('description', data.description);
    if (data.metaDescription && data.metaDescription.length > 0)
      formData.append('metaDescription', data.metaDescription);
    if (data.keywords && data.keywords.length > 0)
      formData.append('keywords', data.keywords);
    if (data.url) formData.append('url', data.url);
    formData.append('displayOrder', data.displayOrder.toString());
    if (data.permissionKey)
      formData.append('permissionKey', data.permissionKey);
    if (data.iconName) formData.append('iconName', data.iconName);
    if (data.target) formData.append('target', data.target);
    if (data.parentId) formData.append('parentId', data.parentId);
    if (data.sectionId) formData.append('sectionId', data.sectionId);
    if (data.roleId) formData.append('roleId', data.roleId);
    if (data.level) formData.append('level', data.level);
    if (data.isStatic) formData.append('isStatic', data.isStatic.toString());
    if (data.isVisible) formData.append('isVisible', data.isVisible.toString());
    if (data.direction) formData.append('direction', data.direction.toString());
    if (data.isParent) formData.append('isParent', data.isParent.toString());
    if (data.isDashboard)
      formData.append('isDashboard', data.isDashboard.toString());
    data.menuIds?.forEach((i) => formData.append('menuIds', i));
    return fetcher<number>(
      `menu/items`,
      {
        method: 'POST',

        body: formData,
      },
      true,
    );
  },

  switchArchive: (id: number) =>
    fetcher<number>(`menu/items/${id}/SwitchArchive`, {
      method: 'PATCH',
    }),
  switchPublic: (id: number) =>
    fetcher<number>(`menu/items/${id}/switchPublic`, {
      method: 'PATCH',
    }),
  getStatus: (id: number) =>
    fetcher<ItemStatus>(`menu/items/${id}/status`, { method: 'GET' }),
  MoveUp: (id: number) =>
    fetcher<boolean>(`menu/MoveUp/${id}`, {
      method: 'PATCH',
    }),
  MoveDown: (id: number) =>
    fetcher<boolean>(`menu/MoveDown/${id}`, {
      method: 'PATCH',
    }),
};
export default MenuQuery;
