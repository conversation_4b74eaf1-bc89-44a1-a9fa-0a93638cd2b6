'use client';

import { useQuery } from '@tanstack/react-query';
import { Spinner } from '@/components/ui/spinner';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

const ShowAllProducts = () => {
  const { data: warehouses, isLoading: loadingWarehouses } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
    select: (data) => data.filter((wh) => wh.isActive),
  });

  const { data, isLoading: loadingProducts } = useQuery({
    queryKey: ['products-all'],
    queryFn: () => OfferingRateQuery.getAll(1),
  });

  if (loadingWarehouses || loadingProducts) {
    return <Spinner />;
  }

  const warehouseCodes = warehouses && warehouses.map((w) => w.code);

  return (
    <div className="overflow-x-auto w-full">
      <div className="w-full border-collapse text-sm">
        <div className="bg-slate-100 text-slate-700 w-full">
          <div
            className="min-w-full grid gap-1 items-center border-b border-gray-200 font-semibold"
            style={{
              gridTemplateColumns: `425px repeat(${warehouseCodes && warehouseCodes.length}, 120px) auto`, // last for 'Discontinued'
            }}
          >
            <div className="text-left p-2">Product Name</div>
            {warehouseCodes &&
              warehouseCodes.map((code) => (
                <div key={code} className="text-center p-2">
                  <div>{code}</div>
                  <div className="text-xs">Qty × Price</div>
                </div>
              ))}
            <div className="text-center p-2">Discontinued</div>
          </div>
        </div>
        <div className="w-full">
          {data &&
            data.group.map((g) => (
              <div key={g.groupId}>
                <div className="p-2">
                  <Accordion type="single" collapsible className="space-y-1">
                    <AccordionItem value={g.groupId.toString()}>
                      <AccordionTrigger className="text-md font-medium hover:bg-gray-50 rounded-md">
                        {g.groupName}&nbsp;
                        <span className="text-pink-700 font-mono">
                          ({g.code})
                        </span>
                        <Badge
                          variant="secondary"
                          className="ml-2 text-xs text-primary"
                        >
                          {g.categories.length}
                        </Badge>
                      </AccordionTrigger>
                      <AccordionContent>
                        {g.categories.length > 0 ? (
                          g.categories.map((c) => (
                            <Accordion
                              key={c.categoryId}
                              type="single"
                              collapsible
                              className="space-y-1 pl-4"
                            >
                              <AccordionItem
                                value={c.categoryName + c.categoryId.toString()}
                              >
                                <AccordionTrigger className="text-sm font-medium hover:bg-gray-50 rounded-md">
                                  {c.categoryName}&nbsp;
                                  <span className="text-pink-700 font-mono">
                                    ({c.code})
                                  </span>
                                  <Badge
                                    variant="secondary"
                                    className="ml-2 text-xs text-primary"
                                  >
                                    {c.offerings.length}
                                  </Badge>
                                </AccordionTrigger>
                                <AccordionContent>
                                  {c.offerings.length > 0 ? (
                                    c.offerings.map((off) => (
                                      <Accordion
                                        key={off.id}
                                        type="single"
                                        collapsible
                                        className="space-y-1 pl-4"
                                      >
                                        <AccordionItem
                                          value={off && off.id.toString()}
                                        >
                                          <AccordionTrigger className="text-sm font-medium hover:bg-gray-50 rounded-md">
                                            <span>{off.name}</span>&nbsp;
                                            <span className="text-pink-700 font-mono">
                                              ({off.code})
                                            </span>
                                            <Badge
                                              variant="secondary"
                                              className="ml-2 text-xs text-primary"
                                            >
                                              {off.quantity || 0}
                                            </Badge>
                                          </AccordionTrigger>
                                          <AccordionContent>
                                            {off &&
                                            off.options &&
                                            off.options.length > 0 ? (
                                              off.options.map(
                                                (option, index) => (
                                                  <div
                                                    className="ml-6 border-l w-full"
                                                    key={option.id}
                                                  >
                                                    <div
                                                      style={{
                                                        gridTemplateColumns: `365px repeat(${warehouseCodes && warehouseCodes.length}, 120px) auto`, // last for 'Discontinued'
                                                      }}
                                                      className={`ml-2 grid items-center hover:bg-gray-50 ${
                                                        index !==
                                                        (off.options &&
                                                          off.options.length -
                                                            1)
                                                          ? 'border-b'
                                                          : ''
                                                      }`}
                                                    >
                                                      <div className="p-2 font-medium text-sm">
                                                        <span
                                                          className={
                                                            option.isDiscontinued
                                                              ? 'line-through text-gray-400'
                                                              : ''
                                                          }
                                                        >
                                                          {option.name}
                                                        </span>{' '}
                                                        <span className="font-mono text-pink-700">
                                                          ({option.code})
                                                        </span>
                                                        {/* ✅ Total Quantity Across Warehouses */}
                                                        <Badge
                                                          variant="secondary"
                                                          className="ml-2 text-xs text-primary"
                                                        >
                                                          {option.warehousePriceQuantities?.reduce(
                                                            (acc, u) =>
                                                              acc +
                                                              (u.quantity ?? 0),
                                                            0,
                                                          ) || 0}
                                                        </Badge>
                                                      </div>
                                                      {warehouseCodes &&
                                                        warehouseCodes.map(
                                                          (whCode) => {
                                                            const w =
                                                              option.warehousePriceQuantities?.find(
                                                                (q) =>
                                                                  warehouses.find(
                                                                    (wh) =>
                                                                      wh.code ===
                                                                      whCode,
                                                                  )?.id ===
                                                                    q.warehouseId ||
                                                                  0,
                                                              );
                                                            return (
                                                              <div
                                                                key={
                                                                  whCode +
                                                                  option.id
                                                                }
                                                                className="p-2 text-center"
                                                              >
                                                                <p
                                                                  className={`${option.isDiscontinued ? 'text-muted-foreground' : ''} ${w && w.quantity && w.quantity < 20 ? ' bg-orange-50' : (w ? w.quantity : 0) == 0 ? ' bg-red-50' : 'bg-gray-100'} bg-gray-100 px-2 py-1 border-none rounded-md`}
                                                                >
                                                                  <span>
                                                                    {w?.quantity ??
                                                                      0}
                                                                  </span>{' '}
                                                                  ×{' '}
                                                                  <span>
                                                                    $
                                                                    {(
                                                                      w?.unitPrice ??
                                                                      0
                                                                    ).toFixed(
                                                                      2,
                                                                    )}
                                                                  </span>
                                                                </p>
                                                              </div>
                                                            );
                                                          },
                                                        )}
                                                      <div className="p-2 text-center">
                                                        <span
                                                          className={cn(
                                                            'text-xs font-medium px-2 py-0.5 rounded-full inline-block whitespace-nowrap',
                                                            !option.isDiscontinued
                                                              ? 'text-green-700 bg-green-50 border border-green-200'
                                                              : 'text-red-700 bg-red-50 border border-red-200',
                                                          )}
                                                        >
                                                          {option.isDiscontinued
                                                            ? 'Discontinued'
                                                            : 'Active'}
                                                        </span>
                                                        {/* {option.isDiscontinued ? (
                                                    <Badge
                                                      variant="destructive"
                                                      className="text-xs"
                                                    >
                                                      Discontinued
                                                    </Badge>
                                                  ) : (
                                                    <Badge
                                                      variant="success"
                                                      className="text-xs"
                                                    >
                                                      Active
                                                    </Badge>
                                                  )} */}
                                                      </div>
                                                    </div>
                                                  </div>
                                                ),
                                              )
                                            ) : (
                                              <div>
                                                <div className="text-sm italic text-gray-500 p-2">
                                                  No property for this product.
                                                </div>
                                              </div>
                                            )}
                                          </AccordionContent>
                                        </AccordionItem>
                                      </Accordion>
                                    ))
                                  ) : (
                                    <div className="pl-4">
                                      <div className="text-sm italic text-gray-500 p-2">
                                        No products for this category.
                                      </div>
                                    </div>
                                  )}
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          ))
                        ) : (
                          <div className="pl-4">
                            <div className="text-sm italic text-gray-500 p-2">
                              No categories for this group.
                            </div>
                          </div>
                        )}
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ShowAllProducts;
