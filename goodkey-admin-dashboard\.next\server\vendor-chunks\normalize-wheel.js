/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/normalize-wheel";
exports.ids = ["vendor-chunks/normalize-wheel"];
exports.modules = {

/***/ "(ssr)/./node_modules/normalize-wheel/index.js":
/*!***********************************************!*\
  !*** ./node_modules/normalize-wheel/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./src/normalizeWheel.js */ \"(ssr)/./node_modules/normalize-wheel/src/normalizeWheel.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbm9ybWFsaXplLXdoZWVsL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLGlJQUFtRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxub3JtYWxpemUtd2hlZWxcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9zcmMvbm9ybWFsaXplV2hlZWwuanMnKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/normalize-wheel/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/normalize-wheel/src/ExecutionEnvironment.js":
/*!******************************************************************!*\
  !*** ./node_modules/normalize-wheel/src/ExecutionEnvironment.js ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
eval("/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\n\n\nvar canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n\n  canUseDOM: canUseDOM,\n\n  canUseWorkers: typeof Worker !== 'undefined',\n\n  canUseEventListeners:\n    canUseDOM && !!(window.addEventListener || window.attachEvent),\n\n  canUseViewport: canUseDOM && !!window.screen,\n\n  isInWorker: !canUseDOM // For now, this is true - might change in the future.\n\n};\n\nmodule.exports = ExecutionEnvironment;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/normalize-wheel/src/ExecutionEnvironment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js":
/*!******************************************************************!*\
  !*** ./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\n\nvar _mobile;\n\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);\n  var os    = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!(/Win64/.exec(uas));\n\n  if (agent) {\n    _ie = agent[1] ? parseFloat(agent[1]) : (\n          agent[5] ? parseFloat(agent[5]) : NaN);\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera   = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit  = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux   = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\n\nvar UserAgent_DEPRECATED = {\n\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function() {\n    return _populate() || _ie;\n  },\n\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function() {\n    return _populate() || (_ie_real_version > _ie);\n  },\n\n\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function() {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function() {\n    return _populate() || _firefox;\n  },\n\n\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function() {\n    return _populate() || _opera;\n  },\n\n\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function() {\n    return _populate() || _webkit;\n  },\n\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function() {\n    return UserAgent_DEPRECATED.webkit();\n  },\n\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome : function() {\n    return _populate() || _chrome;\n  },\n\n\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function() {\n    return _populate() || _windows;\n  },\n\n\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function() {\n    return _populate() || _osx;\n  },\n\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function() {\n    return _populate() || _linux;\n  },\n\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function() {\n    return _populate() || _iphone;\n  },\n\n  mobile: function() {\n    return _populate() || (_iphone || _ipad || _android || _mobile);\n  },\n\n  nativeApp: function() {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n\n  android: function() {\n    return _populate() || _android;\n  },\n\n  ipad: function() {\n    return _populate() || _ipad;\n  }\n};\n\nmodule.exports = UserAgent_DEPRECATED;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/normalize-wheel/src/isEventSupported.js":
/*!**************************************************************!*\
  !*** ./node_modules/normalize-wheel/src/isEventSupported.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\n\n\nvar ExecutionEnvironment = __webpack_require__(/*! ./ExecutionEnvironment */ \"(ssr)/./node_modules/normalize-wheel/src/ExecutionEnvironment.js\");\n\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature =\n    document.implementation &&\n    document.implementation.hasFeature &&\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (!ExecutionEnvironment.canUseDOM ||\n      capture && !('addEventListener' in document)) {\n    return false;\n  }\n\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\n\nmodule.exports = isEventSupported;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/normalize-wheel/src/isEventSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/normalize-wheel/src/normalizeWheel.js":
/*!************************************************************!*\
  !*** ./node_modules/normalize-wheel/src/normalizeWheel.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\n\n\nvar UserAgent_DEPRECATED = __webpack_require__(/*! ./UserAgent_DEPRECATED */ \"(ssr)/./node_modules/normalize-wheel/src/UserAgent_DEPRECATED.js\");\n\nvar isEventSupported = __webpack_require__(/*! ./isEventSupported */ \"(ssr)/./node_modules/normalize-wheel/src/isEventSupported.js\");\n\n\n// Reasonable defaults\nvar PIXEL_STEP  = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/ event) /*object*/ {\n  var sX = 0, sY = 0,       // spinX, spinY\n      pX = 0, pY = 0;       // pixelX, pixelY\n\n  // Legacy\n  if ('detail'      in event) { sY = event.detail; }\n  if ('wheelDelta'  in event) { sY = -event.wheelDelta / 120; }\n  if ('wheelDeltaY' in event) { sY = -event.wheelDeltaY / 120; }\n  if ('wheelDeltaX' in event) { sX = -event.wheelDeltaX / 120; }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n    sX = sY;\n    sY = 0;\n  }\n\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n\n  if ('deltaY' in event) { pY = event.deltaY; }\n  if ('deltaX' in event) { pX = event.deltaX; }\n\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {          // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {                             // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) { sX = (pX < 1) ? -1 : 1; }\n  if (pY && !sY) { sY = (pY < 1) ? -1 : 1; }\n\n  return { spinX  : sX,\n           spinY  : sY,\n           pixelX : pX,\n           pixelY : pY };\n}\n\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function() /*string*/ {\n  return (UserAgent_DEPRECATED.firefox())\n           ? 'DOMMouseScroll'\n           : (isEventSupported('wheel'))\n               ? 'wheel'\n               : 'mousewheel';\n};\n\nmodule.exports = normalizeWheel;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/normalize-wheel/src/normalizeWheel.js\n");

/***/ })

};
;