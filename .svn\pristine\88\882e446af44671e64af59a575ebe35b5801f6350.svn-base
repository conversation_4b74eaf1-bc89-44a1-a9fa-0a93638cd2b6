﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class OfferingProperty
    {
        public OfferingProperty()
        {
            OfferingAddOn = new HashSet<OfferingAddOn>();
            OfferingRate = new HashSet<OfferingRate>();
        }

        public int Id { get; set; }
        public int OfferingId { get; set; }
        public string Code { get; set; }
        public int? Property1Id { get; set; }
        public int? PropertyOption1Id { get; set; }
        public int? Property2Id { get; set; }
        public int? PropertyOption2Id { get; set; }
        public string SupplierItemNumber { get; set; }
        public string Image { get; set; }
        public bool? IsForSmOnly { get; set; }
        public bool? IsInternalOnly { get; set; }
        public bool? IsActive { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool? IsDiscontinued { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual Offering Offering { get; set; }
        public virtual Property Property1 { get; set; }
        public virtual Property Property2 { get; set; }
        public virtual PropertyOption PropertyOption1 { get; set; }
        public virtual PropertyOption PropertyOption2 { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<OfferingAddOn> OfferingAddOn { get; set; }
        public virtual ICollection<OfferingRate> OfferingRate { get; set; }
    }
}