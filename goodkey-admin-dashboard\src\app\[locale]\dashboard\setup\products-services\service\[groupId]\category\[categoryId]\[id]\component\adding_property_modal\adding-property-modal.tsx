'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Spinner } from '@/components/ui/spinner';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import Suspense from '@/components/ui/Suspense';
import OfferingQuery from '@/services/queries/OfferingQuery';
import CategoryQuery from '@/services/queries/CategoryQuery';
import {
  OfferingPropertyFormData,
  OfferingPropertySchema,
} from '@/schema/PropertyOptionSchema';
import Field from '@/components/ui/inputs/field';
import { getQueryClient } from '@/utils/query-client';
import { modal } from '@/components/ui/overlay';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';
import { PropertyDetail } from '@/models/Property';
import { useRouter } from 'next/navigation';

function FormContent({
  defaultValues,
  id,
  groupId,
  categoryId,
  isUpdate,
  propertyDetails,
}: {
  defaultValues?: OfferingPropertyFormData;
  id?: number;
  groupId: number;
  categoryId: number;
  isUpdate?: boolean;
  propertyDetails?: PropertyDetail[];
}) {
  const { toast } = useToast();

  // const { data: propertyDetails, isLoading: loadingProperties } = useQuery({
  //   queryKey: ['Category Property Details', { categoryId }],
  //   queryFn: () => CategoryQuery.getAllPropertyDetails(categoryId),
  //   enabled: !!categoryId,
  // });

  const form = useForm<OfferingPropertyFormData>({
    resolver: zodResolver(OfferingPropertySchema),
    defaultValues:
      defaultValues && defaultValues.property.length > 0
        ? defaultValues
        : {
            property:
              propertyDetails &&
              propertyDetails.map((o) => ({
                id: o.id,
                options: [],
              })),
          },
  });

  // useEffect(() => {
  //   if (!defaultValues && propertyDetails) {
  //     form.reset({
  //       property: propertyDetails.map((o) => ({
  //         id: Number(o.id),
  //         options: [],
  //       })),
  //     });
  //   } else if (defaultValues) {
  //     form.reset(defaultValues);
  //   }
  // }, [propertyDetails, defaultValues, form]);

  const { mutate, isPending } = useMutation({
    mutationFn: isUpdate
      ? OfferingQuery.updateOfferingPropertyLink(Number(id))
      : OfferingQuery.addOfferingPropertyLink(Number(id)),
    onSuccess: async () => {
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: OfferingQuery.tags,
        });
      }
      await getQueryClient().invalidateQueries({
        queryKey: ['Services', { groupType: 2 }],
      });
      await getQueryClient().invalidateQueries({
        queryKey: GroupTypeQuery.tags,
      });
      await getQueryClient().invalidateQueries({
        queryKey: ['Offering Property Details', { id }],
      });
      await getQueryClient().invalidateQueries({
        queryKey: ['Offering Property', { id }],
      });
      toast({
        title: 'Success',
        description: isUpdate
          ? 'Product property updated successfully.'
          : 'Product property created successfully.',
        variant: 'success',
      });
      modal.close();
    },
  });

  const { push } = useRouter();

  return (
    <Form {...form}>
      <ModalContainer
        title="Set Product Properties"
        description="Select options for each available property"
        onSubmit={form.handleSubmit((data) => {
          mutate(data);
        })}
      >
        <div className="flex flex-col mt-4">
          {propertyDetails && propertyDetails?.length ? (
            propertyDetails.map((property, index) => {
              return (
                <div key={property.id} className="flex flex-col gap-4">
                  <Field
                    control={form.control}
                    name={`property.${index}.options` as const}
                    label={property.name ?? `Property ${index + 1}`}
                    type={{
                      type: 'multiBadgeSelect',
                      props: {
                        options:
                          property.option?.map((p) => ({
                            label: p.name ?? '',
                            value: p.id.toString(),
                          })) || [],
                        placeholder: 'Select Options',
                      },
                    }}
                  />

                  {/* {index === 0 && <hr className="border-t border-muted mt-4" />} */}
                </div>
              );
            })
          ) : (
            <p className="text-center">
              No properties available for this product's category. Click on this{' '}
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  push(
                    `/dashboard/setup/products-services/category/${categoryId}/property`,
                  );
                  modal.close();
                }}
              >
                category
              </Button>{' '}
              to add properties.
            </p>
          )}
          {propertyDetails && propertyDetails?.length > 0 && (
            <div className="flex justify-end w-full gap-4 mt-4">
              <Button
                type="submit"
                disabled={isPending}
                iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
                iconProps={{ className: isPending ? 'animate-spin' : '' }}
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </ModalContainer>
    </Form>
  );
}

interface IAddingPropertyModal {
  groupId: number;
  categoryId: number;
  id: number;
  isUpdate?: boolean;
}

export default function AddingPropertyModal({
  id,
  groupId,
  categoryId,
  isUpdate,
}: IAddingPropertyModal) {
  const {
    data: property,
    isLoading,
    isPending,
  } = useQuery({
    queryKey: ['Offering Property', { id }],
    queryFn: () => OfferingQuery.getOfferingProperty(Number(id)),
    enabled: !!id,
    select: (res) => {
      return {
        property:
          res.property?.map((p) => ({
            id: Number(p.id),
            options: p.options?.map((o) => o.toString()) || [],
          })) || [],
      };
    },
  });

  const { data: propertyDetails, isLoading: loadingDetails } = useQuery({
    queryKey: ['category-selection', categoryId],
    queryFn: () => CategoryQuery.getAllPropertyDetails(categoryId),
    enabled: !!categoryId,
  });

  const isFormReady = !isLoading && !isPending && !loadingDetails;

  return (
    <Suspense isLoading={!isFormReady}>
      {isLoading && isPending ? (
        <Spinner />
      ) : (
        <FormContent
          defaultValues={property}
          id={id}
          groupId={groupId}
          categoryId={categoryId}
          isUpdate={isUpdate}
          propertyDetails={propertyDetails}
        />
      )}
    </Suspense>
  );
}
