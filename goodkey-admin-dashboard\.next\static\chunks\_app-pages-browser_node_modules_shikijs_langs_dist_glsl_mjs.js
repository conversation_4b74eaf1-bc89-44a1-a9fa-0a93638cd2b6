"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_glsl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/c.mjs":
/*!************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/c.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"C\\\",\\\"name\\\":\\\"c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional\\\"},{\\\"include\\\":\\\"#predefined_macros\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#switch_statement\\\"},{\\\"include\\\":\\\"#anon_pattern_1\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#anon_pattern_2\\\"},{\\\"include\\\":\\\"#anon_pattern_3\\\"},{\\\"include\\\":\\\"#anon_pattern_4\\\"},{\\\"include\\\":\\\"#anon_pattern_5\\\"},{\\\"include\\\":\\\"#anon_pattern_6\\\"},{\\\"include\\\":\\\"#anon_pattern_7\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#anon_pattern_range_1\\\"},{\\\"include\\\":\\\"#anon_pattern_range_2\\\"},{\\\"include\\\":\\\"#anon_pattern_range_3\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"},{\\\"include\\\":\\\"#anon_pattern_range_4\\\"},{\\\"include\\\":\\\"#anon_pattern_range_5\\\"},{\\\"include\\\":\\\"#anon_pattern_range_6\\\"},{\\\"include\\\":\\\"#anon_pattern_8\\\"},{\\\"include\\\":\\\"#anon_pattern_9\\\"},{\\\"include\\\":\\\"#anon_pattern_10\\\"},{\\\"include\\\":\\\"#anon_pattern_11\\\"},{\\\"include\\\":\\\"#anon_pattern_12\\\"},{\\\"include\\\":\\\"#anon_pattern_13\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#anon_pattern_range_7\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#anon_pattern_range_8\\\"},{\\\"include\\\":\\\"#anon_pattern_range_9\\\"},{\\\"include\\\":\\\"#anon_pattern_14\\\"},{\\\"include\\\":\\\"#anon_pattern_15\\\"}],\\\"repository\\\":{\\\"access-method\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*(?:(\\\\\\\\.)|(->))((?:(?:[a-zA-Z_][a-zA-Z_0-9]*)\\\\\\\\s*(?:(?:\\\\\\\\.)|(?:->)))*)\\\\\\\\s*([a-zA-Z_][a-zA-Z_0-9]*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},{\\\"match\\\":\\\"[a-zA-Z_][a-zA-Z_0-9]*\\\",\\\"name\\\":\\\"variable.object.c\\\"},{\\\"match\\\":\\\".+\\\",\\\"name\\\":\\\"everything.else.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.c\\\"}},\\\"name\\\":\\\"meta.function-call.member.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"anon_pattern_1\\\":{\\\"match\\\":\\\"\\\\\\\\b(break|continue|do|else|for|goto|if|_Pragma|return|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.c\\\"},\\\"anon_pattern_10\\\":{\\\"match\\\":\\\"\\\\\\\\b(int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stdint.c\\\"},\\\"anon_pattern_11\\\":{\\\"match\\\":\\\"\\\\\\\\b(noErr|kNilOptions|kInvalidID|kVariableLengthArray)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.mac-classic.c\\\"},\\\"anon_pattern_12\\\":{\\\"match\\\":\\\"\\\\\\\\b(AbsoluteTime|Boolean|Byte|ByteCount|ByteOffset|BytePtr|CompTimeValue|ConstLogicalAddress|ConstStrFileNameParam|ConstStringPtr|Duration|Fixed|FixedPtr|Float32|Float32Point|Float64|Float80|Float96|FourCharCode|Fract|FractPtr|Handle|ItemCount|LogicalAddress|OptionBits|OSErr|OSStatus|OSType|OSTypePtr|PhysicalAddress|ProcessSerialNumber|ProcessSerialNumberPtr|ProcHandle|Ptr|ResType|ResTypePtr|ShortFixed|ShortFixedPtr|SignedByte|SInt16|SInt32|SInt64|SInt8|Size|StrFileName|StringHandle|StringPtr|TimeBase|TimeRecord|TimeScale|TimeValue|TimeValue64|UInt16|UInt32|UInt64|UInt8|UniChar|UniCharCount|UniCharCountPtr|UniCharPtr|UnicodeScalarValue|UniversalProcHandle|UniversalProcPtr|UnsignedFixed|UnsignedFixedPtr|UnsignedWide|UTF16Char|UTF32Char|UTF8Char)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.mac-classic.c\\\"},\\\"anon_pattern_13\\\":{\\\"match\\\":\\\"\\\\\\\\b([A-Za-z0-9_]+_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.posix-reserved.c\\\"},\\\"anon_pattern_14\\\":{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.c\\\"},\\\"anon_pattern_15\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.c\\\"},\\\"anon_pattern_2\\\":{\\\"match\\\":\\\"typedef\\\",\\\"name\\\":\\\"keyword.other.typedef.c\\\"},\\\"anon_pattern_3\\\":{\\\"match\\\":\\\"\\\\\\\\b(const|extern|register|restrict|static|volatile|inline)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.c\\\"},\\\"anon_pattern_4\\\":{\\\"match\\\":\\\"\\\\\\\\bk[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.variable.mac-classic.c\\\"},\\\"anon_pattern_5\\\":{\\\"match\\\":\\\"\\\\\\\\bg[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.global.mac-classic.c\\\"},\\\"anon_pattern_6\\\":{\\\"match\\\":\\\"\\\\\\\\bs[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.static.mac-classic.c\\\"},\\\"anon_pattern_7\\\":{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.c\\\"},\\\"anon_pattern_8\\\":{\\\"match\\\":\\\"\\\\\\\\b(u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sys-types.c\\\"},\\\"anon_pattern_9\\\":{\\\"match\\\":\\\"\\\\\\\\b(pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.pthread.c\\\"},\\\"anon_pattern_range_1\\\":{\\\"begin\\\":\\\"((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))((#)\\\\\\\\s*define\\\\\\\\b)\\\\\\\\s+((?<!\\\\\\\\w)[a-zA-Z_]\\\\\\\\w*(?!\\\\\\\\w))(?:(\\\\\\\\()([^()\\\\\\\\\\\\\\\\]+)(\\\\\\\\)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.directive.define.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.preprocessor.c\\\"}},\\\"match\\\":\\\"(?<=[(,])\\\\\\\\s*((?<!\\\\\\\\w)[a-zA-Z_]\\\\\\\\w*(?!\\\\\\\\w))\\\\\\\\s*\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.c\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"ellipses.c punctuation.vararg-ellipses.variable.parameter.preprocessor.c\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.c\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"anon_pattern_range_2\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(error|warning))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.diagnostic.$3.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.diagnostic.c\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"[^'\\\\\\\"]\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"string.unquoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"anon_pattern_range_3\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(include(?:_next)?|import))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.$3.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.include.c\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.c\\\"}]},\\\"anon_pattern_range_4\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*line)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.line.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_5\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*undef))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.undef.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_6\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*pragma))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w\\\\\\\\-$]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.pragma.preprocessor.c\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_7\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?!\\\\\\\\s*(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|pthread_rwlock_t|atomic_uintptr_t|atomic_ptrdiff_t|atomic_uintmax_t|atomic_intmax_t|atomic_char32_t|atomic_intptr_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|uint_least8_t|int_least32_t|int_least16_t|pthread_key_t|uint_fast32_t|uint_fast64_t|uint_fast16_t|atomic_size_t|atomic_ushort|atomic_ullong|int_least64_t|atomic_ulong|int_least8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|memory_order|atomic_schar|atomic_uchar|atomic_short|atomic_llong|thread_local|atomic_bool|atomic_uint|atomic_long|int_fast8_t|suseconds_t|atomic_char|atomic_int|useconds_t|_Imaginary|uintmax_t|uintmax_t|in_addr_t|in_port_t|_Noreturn|blksize_t|pthread_t|uintptr_t|volatile|u_quad_t|blkcnt_t|intmax_t|intptr_t|_Complex|uint16_t|uint32_t|uint64_t|_Alignof|_Alignas|continue|unsigned|restrict|intmax_t|register|int64_t|qaddr_t|segsz_t|_Atomic|alignas|default|caddr_t|nlink_t|typedef|u_short|fixpt_t|clock_t|swblk_t|ssize_t|alignof|daddr_t|int16_t|int32_t|uint8_t|struct|mode_t|size_t|time_t|ushort|u_long|u_char|int8_t|double|signed|static|extern|inline|return|switch|xor_eq|and_eq|bitand|not_eq|sizeof|quad_t|uid_t|bitor|union|off_t|key_t|ino_t|compl|u_int|short|const|false|while|float|pid_t|break|_Bool|or_eq|div_t|dev_t|gid_t|id_t|long|case|goto|else|bool|auto|id_t|enum|uint|true|NULL|void|char|for|not|int|and|xor|do|or|if)\\\\\\\\s*\\\\\\\\()(?=[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},\\\"anon_pattern_range_8\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))?(\\\\\\\\[)(?!\\\\\\\\])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.c\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.c\\\"}},\\\"name\\\":\\\"meta.bracket.square.access.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"anon_pattern_range_9\\\":{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\s*\\\\\\\\]\\\",\\\"name\\\":\\\"storage.modifier.array.bracket.square.c\\\"},\\\"backslash_escapes\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3][0-7]{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.c\\\"},\\\"block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"name\\\":\\\"meta.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"block_comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"block_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional-block\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#c_function_call\\\"},{\\\"begin\\\":\\\"(?:(?:(?=\\\\\\\\s)(?<!else|new|return)(?<=\\\\\\\\w)\\\\\\\\s+(and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)))((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.initialization.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.initialization.c\\\"}},\\\"name\\\":\\\"meta.initialization.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"include\\\":\\\"#parens-block\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"c_conditional_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"c_function_call\\\":{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.function-call.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"case_statement\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)case(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.case.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.colon.case.c\\\"}},\\\"name\\\":\\\"meta.conditional.case.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:^)(?>\\\\\\\\s*)(\\\\\\\\/\\\\\\\\/[!\\\\\\\\/]+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.documentation.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.documentation.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.documentation.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.documentation.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*[!*]+(?=\\\\\\\\s))(.+)([!*]*\\\\\\\\*\\\\\\\\/)\\\",\\\"name\\\":\\\"comment.block.documentation.c\\\"},{\\\"begin\\\":\\\"((?>\\\\\\\\s*)\\\\\\\\/\\\\\\\\*[!*]+(?:(?:\\\\\\\\n|$)|(?=\\\\\\\\s)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.documentation.c\\\"}},\\\"end\\\":\\\"([!*]*\\\\\\\\*\\\\\\\\/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.documentation.c\\\"}},\\\"name\\\":\\\"comment.block.documentation.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.block.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\/\\\\\\\\* =(\\\\\\\\s*.*?)\\\\\\\\s*= \\\\\\\\*\\\\\\\\/$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.block.banner.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\*\\\\\\\\/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.line.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\/\\\\\\\\/ =(\\\\\\\\s*.*?)\\\\\\\\s*=$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.banner.c\\\"},{\\\"begin\\\":\\\"((?:^[ \\\\\\\\t]+)?)(?=\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.c\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]}]},{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#line_comment\\\"}]},{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#line_comment\\\"}]},\\\"default_statement\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)default(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.default.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.colon.case.default.c\\\"}},\\\"name\\\":\\\"meta.conditional.case.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"evaluation_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"function-call-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"function-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parameters.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.function.definition.parameters.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#probably_a_parameter\\\"},{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"inline_comment\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?:[^\\\\\\\\*]|(?:\\\\\\\\*)++[^\\\\\\\\/])*+((?:\\\\\\\\*)++\\\\\\\\/))\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?:[^\\\\\\\\*]|(?:\\\\\\\\*)++[^\\\\\\\\/])*+((?:\\\\\\\\*)++\\\\\\\\/))\\\"}]},\\\"line_comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"line_continuation_character\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\n\\\"}]},\\\"member_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.member.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*(\\\\\\\\b(?!(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|atomic_uintptr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintmax_t|pthread_mutex_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_attr_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_cond_t|pthread_once_t|uint_fast64_t|uint_fast16_t|atomic_size_t|uint_least8_t|int_least64_t|int_least32_t|int_least16_t|pthread_key_t|atomic_ullong|atomic_ushort|uint_fast32_t|atomic_schar|atomic_short|uint_fast8_t|int_fast64_t|int_fast32_t|int_fast16_t|atomic_ulong|atomic_llong|int_least8_t|atomic_uchar|memory_order|suseconds_t|int_fast8_t|atomic_bool|atomic_char|atomic_uint|atomic_long|atomic_int|useconds_t|_Imaginary|blksize_t|pthread_t|in_addr_t|uintptr_t|in_port_t|uintmax_t|uintmax_t|blkcnt_t|uint16_t|unsigned|_Complex|uint32_t|intptr_t|intmax_t|intmax_t|uint64_t|u_quad_t|int64_t|int32_t|ssize_t|caddr_t|clock_t|uint8_t|u_short|swblk_t|segsz_t|int16_t|fixpt_t|daddr_t|nlink_t|qaddr_t|size_t|time_t|mode_t|signed|quad_t|ushort|u_long|u_char|double|int8_t|ino_t|uid_t|pid_t|_Bool|float|dev_t|div_t|short|gid_t|off_t|u_int|key_t|id_t|uint|long|void|char|bool|id_t|int)\\\\\\\\b)[a-zA-Z_]\\\\\\\\w*\\\\\\\\b(?!\\\\\\\\())\\\"},\\\"method_access\\\":{\\\"begin\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.c\\\"}},\\\"contentName\\\":\\\"meta.function-call.member.c\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"numbers\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=.)\\\",\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.c\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.c\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.c\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?<!')([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?([lLfF](?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.c\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.c\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.c\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.c\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?<!')([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?([lLfF](?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:[01]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:[0-7]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))+)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?<!')([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?<!')([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"match\\\":\\\"(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric\\\"}]}]}},\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\.?\\\\\\\\d(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])*\\\"},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\w$])(sizeof)(?![\\\\\\\\w$])\\\",\\\"name\\\":\\\"keyword.operator.sizeof.c\\\"},{\\\"match\\\":\\\"--\\\",\\\"name\\\":\\\"keyword.operator.decrement.c\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.c\\\"},{\\\"match\\\":\\\"%=|\\\\\\\\+=|-=|\\\\\\\\*=|(?<!\\\\\\\\()/=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.c\\\"},{\\\"match\\\":\\\"&=|\\\\\\\\^=|<<=|>>=|\\\\\\\\|=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.bitwise.c\\\"},{\\\"match\\\":\\\"<<|>>\\\",\\\"name\\\":\\\"keyword.operator.bitwise.shift.c\\\"},{\\\"match\\\":\\\"!=|<=|>=|==|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.c\\\"},{\\\"match\\\":\\\"&&|!|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.c\\\"},{\\\"match\\\":\\\"&|\\\\\\\\||\\\\\\\\^|~\\\",\\\"name\\\":\\\"keyword.operator.c\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.c\\\"},{\\\"match\\\":\\\"%|\\\\\\\\*|/|-|\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.parens.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"parens-block\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.parens.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"},{\\\"match\\\":\\\"(?-mix:(?<!:):(?!:))\\\",\\\"name\\\":\\\"punctuation.range-based.c\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.pragma.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.pragma-mark.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.pragma-mark.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(((#)\\\\\\\\s*pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.c\\\"},\\\"predefined_macros\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.other.preprocessor.macro.predefined.$1.c\\\"}},\\\"match\\\":\\\"\\\\\\\\b(__cplusplus|__DATE__|__FILE__|__LINE__|__STDC__|__STDC_HOSTED__|__STDC_NO_COMPLEX__|__STDC_VERSION__|__STDCPP_THREADS__|__TIME__|NDEBUG|__OBJC__|__ASSEMBLER__|__ATOM__|__AVX__|__AVX2__|_CHAR_UNSIGNED|__CLR_VER|_CONTROL_FLOW_GUARD|__COUNTER__|__cplusplus_cli|__cplusplus_winrt|_CPPRTTI|_CPPUNWIND|_DEBUG|_DLL|__FUNCDNAME__|__FUNCSIG__|__FUNCTION__|_INTEGRAL_MAX_BITS|__INTELLISENSE__|_ISO_VOLATILE|_KERNEL_MODE|_M_AMD64|_M_ARM|_M_ARM_ARMV7VE|_M_ARM_FP|_M_ARM64|_M_CEE|_M_CEE_PURE|_M_CEE_SAFE|_M_FP_EXCEPT|_M_FP_FAST|_M_FP_PRECISE|_M_FP_STRICT|_M_IX86|_M_IX86_FP|_M_X64|_MANAGED|_MSC_BUILD|_MSC_EXTENSIONS|_MSC_FULL_VER|_MSC_VER|_MSVC_LANG|__MSVC_RUNTIME_CHECKS|_MT|_NATIVE_WCHAR_T_DEFINED|_OPENMP|_PREFAST|__TIMESTAMP__|_VC_NO_DEFAULTLIB|_WCHAR_T_DEFINED|_WIN32|_WIN64|_WINRT_DLL|_ATL_VER|_MFC_VER|__GFORTRAN__|__GNUC__|__GNUC_MINOR__|__GNUC_PATCHLEVEL__|__GNUG__|__STRICT_ANSI__|__BASE_FILE__|__INCLUDE_LEVEL__|__ELF__|__VERSION__|__OPTIMIZE__|__OPTIMIZE_SIZE__|__NO_INLINE__|__GNUC_STDC_INLINE__|__CHAR_UNSIGNED__|__WCHAR_UNSIGNED__|__REGISTER_PREFIX__|__REGISTER_PREFIX__|__SIZE_TYPE__|__PTRDIFF_TYPE__|__WCHAR_TYPE__|__WINT_TYPE__|__INTMAX_TYPE__|__UINTMAX_TYPE__|__SIG_ATOMIC_TYPE__|__INT8_TYPE__|__INT16_TYPE__|__INT32_TYPE__|__INT64_TYPE__|__UINT8_TYPE__|__UINT16_TYPE__|__UINT32_TYPE__|__UINT64_TYPE__|__INT_LEAST8_TYPE__|__INT_LEAST16_TYPE__|__INT_LEAST32_TYPE__|__INT_LEAST64_TYPE__|__UINT_LEAST8_TYPE__|__UINT_LEAST16_TYPE__|__UINT_LEAST32_TYPE__|__UINT_LEAST64_TYPE__|__INT_FAST8_TYPE__|__INT_FAST16_TYPE__|__INT_FAST32_TYPE__|__INT_FAST64_TYPE__|__UINT_FAST8_TYPE__|__UINT_FAST16_TYPE__|__UINT_FAST32_TYPE__|__UINT_FAST64_TYPE__|__INTPTR_TYPE__|__UINTPTR_TYPE__|__CHAR_BIT__|__SCHAR_MAX__|__WCHAR_MAX__|__SHRT_MAX__|__INT_MAX__|__LONG_MAX__|__LONG_LONG_MAX__|__WINT_MAX__|__SIZE_MAX__|__PTRDIFF_MAX__|__INTMAX_MAX__|__UINTMAX_MAX__|__SIG_ATOMIC_MAX__|__INT8_MAX__|__INT16_MAX__|__INT32_MAX__|__INT64_MAX__|__UINT8_MAX__|__UINT16_MAX__|__UINT32_MAX__|__UINT64_MAX__|__INT_LEAST8_MAX__|__INT_LEAST16_MAX__|__INT_LEAST32_MAX__|__INT_LEAST64_MAX__|__UINT_LEAST8_MAX__|__UINT_LEAST16_MAX__|__UINT_LEAST32_MAX__|__UINT_LEAST64_MAX__|__INT_FAST8_MAX__|__INT_FAST16_MAX__|__INT_FAST32_MAX__|__INT_FAST64_MAX__|__UINT_FAST8_MAX__|__UINT_FAST16_MAX__|__UINT_FAST32_MAX__|__UINT_FAST64_MAX__|__INTPTR_MAX__|__UINTPTR_MAX__|__WCHAR_MIN__|__WINT_MIN__|__SIG_ATOMIC_MIN__|__SCHAR_WIDTH__|__SHRT_WIDTH__|__INT_WIDTH__|__LONG_WIDTH__|__LONG_LONG_WIDTH__|__PTRDIFF_WIDTH__|__SIG_ATOMIC_WIDTH__|__SIZE_WIDTH__|__WCHAR_WIDTH__|__WINT_WIDTH__|__INT_LEAST8_WIDTH__|__INT_LEAST16_WIDTH__|__INT_LEAST32_WIDTH__|__INT_LEAST64_WIDTH__|__INT_FAST8_WIDTH__|__INT_FAST16_WIDTH__|__INT_FAST32_WIDTH__|__INT_FAST64_WIDTH__|__INTPTR_WIDTH__|__INTMAX_WIDTH__|__SIZEOF_INT__|__SIZEOF_LONG__|__SIZEOF_LONG_LONG__|__SIZEOF_SHORT__|__SIZEOF_POINTER__|__SIZEOF_FLOAT__|__SIZEOF_DOUBLE__|__SIZEOF_LONG_DOUBLE__|__SIZEOF_SIZE_T__|__SIZEOF_WCHAR_T__|__SIZEOF_WINT_T__|__SIZEOF_PTRDIFF_T__|__BYTE_ORDER__|__ORDER_LITTLE_ENDIAN__|__ORDER_BIG_ENDIAN__|__ORDER_PDP_ENDIAN__|__FLOAT_WORD_ORDER__|__DEPRECATED|__EXCEPTIONS|__GXX_RTTI|__USING_SJLJ_EXCEPTIONS__|__GXX_EXPERIMENTAL_CXX0X__|__GXX_WEAK__|__NEXT_RUNTIME__|__LP64__|_LP64|__SSP__|__SSP_ALL__|__SSP_STRONG__|__SSP_EXPLICIT__|__SANITIZE_ADDRESS__|__SANITIZE_THREAD__|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16|__HAVE_SPECULATION_SAFE_VALUE|__GCC_HAVE_DWARF2_CFI_ASM|__FP_FAST_FMA|__FP_FAST_FMAF|__FP_FAST_FMAL|__FP_FAST_FMAF16|__FP_FAST_FMAF32|__FP_FAST_FMAF64|__FP_FAST_FMAF128|__FP_FAST_FMAF32X|__FP_FAST_FMAF64X|__FP_FAST_FMAF128X|__GCC_IEC_559|__GCC_IEC_559_COMPLEX|__NO_MATH_ERRNO__|__has_builtin|__has_feature|__has_extension|__has_cpp_attribute|__has_c_attribute|__has_attribute|__has_declspec_attribute|__is_identifier|__has_include|__has_include_next|__has_warning|__BASE_FILE__|__FILE_NAME__|__clang__|__clang_major__|__clang_minor__|__clang_patchlevel__|__clang_version__|__fp16|_Float16)\\\\\\\\b\\\"},{\\\"match\\\":\\\"\\\\\\\\b__([A-Z_]+)__\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.other.preprocessor.macro.predefined.probably.$1.c\\\"}]},\\\"preprocessor-rule-conditional\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-line\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:\\\\\\\\bdefined\\\\\\\\b\\\\\\\\s*$)|(?:\\\\\\\\bdefined\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\s*(?:(?!defined\\\\\\\\b)[a-zA-Z_$][\\\\\\\\w$]*\\\\\\\\b)\\\\\\\\s*\\\\\\\\)*\\\\\\\\s*(?:\\\\\\\\n|//|/\\\\\\\\*|\\\\\\\\?|\\\\\\\\:|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},{\\\"match\\\":\\\"\\\\\\\\bdefined\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.macro-name.c\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"end\\\":\\\":\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.c\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]}]},\\\"preprocessor-rule-define-line-blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-define-line-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"name\\\":\\\"meta.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"}]},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas|asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|long|short|signed|struct|typedef|union|unsigned|void)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.function.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"preprocessor-rule-define-line-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-disabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]}]},\\\"preprocessor-rule-enabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]}]},\\\"preprocessor-rule-enabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"preprocessor-rule-enabled-elif-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"preprocessor-rule-enabled-else\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"preprocessor-rule-enabled-else-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"probably_a_parameter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.probably.c\\\"}},\\\"match\\\":\\\"(?<=(?:[a-zA-Z_0-9] |[&*>\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=(?:\\\\\\\\[\\\\\\\\]\\\\\\\\s*)?(?:,|\\\\\\\\)))\\\"},\\\"static_assert\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)static_assert|_Static_assert(?!\\\\\\\\w))((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.static_assert.c\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.static_assert.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.static_assert.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=(?:L|u8|u|U\\\\\\\\s*\\\\\\\\\\\\\\\")?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.delimiter.comma.c\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.static_assert.message.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_context\\\"}]},{\\\"include\\\":\\\"#evaluation_context\\\"}]},\\\"storage_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:unsigned|signed|double|_Bool|short|float|long|void|char|bool|int)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.primitive.c\\\"},{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|pthread_rwlockattr_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_uint_fast16_t|atomic_int_least64_t|atomic_int_least32_t|atomic_int_least16_t|atomic_uint_least8_t|atomic_uint_fast8_t|atomic_int_least8_t|atomic_int_fast16_t|pthread_mutexattr_t|atomic_int_fast32_t|atomic_int_fast64_t|atomic_int_fast8_t|pthread_condattr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintptr_t|atomic_uintmax_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|int_least32_t|pthread_key_t|int_least16_t|int_least64_t|uint_least8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|atomic_ushort|atomic_ullong|atomic_size_t|int_fast16_t|int_fast64_t|uint_fast8_t|atomic_short|atomic_uchar|atomic_schar|int_least8_t|memory_order|atomic_llong|atomic_ulong|int_fast32_t|atomic_long|atomic_uint|atomic_char|int_fast8_t|suseconds_t|atomic_bool|atomic_int|_Imaginary|useconds_t|in_port_t|uintmax_t|uintmax_t|pthread_t|blksize_t|in_addr_t|uintptr_t|blkcnt_t|uint16_t|uint32_t|uint64_t|u_quad_t|_Complex|intptr_t|intmax_t|intmax_t|segsz_t|u_short|nlink_t|uint8_t|int64_t|int32_t|int16_t|fixpt_t|daddr_t|caddr_t|qaddr_t|ssize_t|clock_t|swblk_t|u_long|mode_t|int8_t|time_t|ushort|u_char|quad_t|size_t|pid_t|gid_t|uid_t|dev_t|div_t|off_t|u_int|key_t|ino_t|uint|id_t|id_t)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.c\\\"},{\\\"match\\\":\\\"(?-mix:\\\\\\\\b(enum|struct|union)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.type.$1.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\b(?:__asm__|asm)\\\\\\\\b)\\\\\\\\s*((?:volatile)?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.asm.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.c\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.asm.c\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(?:^)((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))(?:\\\\\\\\n|$)\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.assembly.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.assembly.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(R?)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.encoding.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.assembly.c\\\"}},\\\"contentName\\\":\\\"meta.embedded.assembly.c\\\",\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.assembly.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asm\\\"},{\\\"include\\\":\\\"source.x86\\\"},{\\\"include\\\":\\\"source.x86_64\\\"},{\\\"include\\\":\\\"source.arm\\\"},{\\\"include\\\":\\\"#backslash_escapes\\\"},{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.assembly.inner.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.assembly.inner.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.asm.label.c\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"\\\\\\\\[((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))([a-zA-Z_]\\\\\\\\w*)((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))\\\\\\\\]\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.colon.assembly.c\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.c\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.c\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|l|j|t|z|q|L|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.c\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"switch_conditional_parentheses\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.conditional.switch.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.conditional.switch.c\\\"}},\\\"name\\\":\\\"meta.conditional.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"switch_statement\\\":{\\\"begin\\\":\\\"(((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)switch(?!\\\\\\\\w)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.head.switch.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.switch.c\\\"}},\\\"end\\\":\\\"(?:(?<=\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)|(?=[;>\\\\\\\\[\\\\\\\\]=]))\\\",\\\"name\\\":\\\"meta.block.switch.c\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G ?\\\",\\\"end\\\":\\\"((?:\\\\\\\\{|<%|\\\\\\\\?\\\\\\\\?<|(?=;)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.switch.c\\\"}},\\\"name\\\":\\\"meta.head.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#switch_conditional_parentheses\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\{|<%|\\\\\\\\?\\\\\\\\?<)\\\",\\\"end\\\":\\\"(\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.switch.c\\\"}},\\\"name\\\":\\\"meta.body.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#default_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"$self\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)[\\\\\\\\s\\\\\\\\n]*\\\",\\\"end\\\":\\\"[\\\\\\\\s\\\\\\\\n]*(?=;)\\\",\\\"name\\\":\\\"meta.tail.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"vararg_ellipses\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\.\\\\\\\\.\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"punctuation.vararg-ellipses.c\\\"}},\\\"scopeName\\\":\\\"source.c\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L2MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx3Q0FBd0Msb0RBQW9ELDJDQUEyQyxFQUFFLDRDQUE0QyxFQUFFLCtDQUErQyxFQUFFLG1DQUFtQyxFQUFFLDBCQUEwQixFQUFFLGtDQUFrQyxFQUFFLGdDQUFnQyxFQUFFLCtCQUErQixFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLDJCQUEyQixFQUFFLHlCQUF5QixFQUFFLHlCQUF5QixFQUFFLHNDQUFzQyxFQUFFLHNDQUFzQyxFQUFFLHNDQUFzQyxFQUFFLDZCQUE2QixFQUFFLHNDQUFzQyxFQUFFLHNDQUFzQyxFQUFFLHNDQUFzQyxFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxFQUFFLHVCQUF1QixFQUFFLHdCQUF3QixFQUFFLHNDQUFzQyxFQUFFLDZDQUE2QyxFQUFFLHNDQUFzQyxFQUFFLHNDQUFzQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxrQkFBa0IsbUJBQW1CLGtNQUFrTSxPQUFPLCtCQUErQixRQUFRLGdEQUFnRCxRQUFRLG9EQUFvRCxRQUFRLGVBQWUsb0VBQW9FLEVBQUUscUVBQXFFLEVBQUUsb0VBQW9FLEVBQUUsZ0RBQWdELEVBQUUsUUFBUSwyQ0FBMkMsUUFBUSxrRkFBa0Ysb0NBQW9DLE9BQU8sZ0ZBQWdGLHlEQUF5RCx1Q0FBdUMsRUFBRSxxQkFBcUIsaUhBQWlILHNCQUFzQixvWkFBb1osc0JBQXNCLHdIQUF3SCxzQkFBc0IsZ3pCQUFnekIsc0JBQXNCLHFGQUFxRixzQkFBc0IsYUFBYSxtREFBbUQsc0JBQXNCLCtEQUErRCxxQkFBcUIsMkRBQTJELHFCQUFxQixnSEFBZ0gscUJBQXFCLHdGQUF3RixxQkFBcUIsZ0dBQWdHLHFCQUFxQixnR0FBZ0cscUJBQXFCLHNGQUFzRixxQkFBcUIsNlVBQTZVLHFCQUFxQixnT0FBZ08sMkJBQTJCLHNSQUFzUixPQUFPLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxFQUFFLFFBQVEsZ0RBQWdELFFBQVEsZ0RBQWdELFFBQVEsaURBQWlELFFBQVEsdURBQXVELFFBQVEsZUFBZSxjQUFjLE9BQU8sZ0RBQWdELHlFQUF5RSxFQUFFLGdFQUFnRSxFQUFFLG9IQUFvSCxFQUFFLFNBQVMsc0RBQXNELDBGQUEwRix3REFBd0QsRUFBRSwyQkFBMkIsOEVBQThFLE9BQU8sdURBQXVELFFBQVEsaURBQWlELCtGQUErRixzQ0FBc0MsT0FBTyxvREFBb0QsZ0VBQWdFLE9BQU8sa0RBQWtELG9EQUFvRCw2Q0FBNkMsRUFBRSxFQUFFLG1DQUFtQyxPQUFPLG9EQUFvRCw2REFBNkQsT0FBTyxrREFBa0Qsb0RBQW9ELDZDQUE2QyxFQUFFLEVBQUUscUhBQXFILDZDQUE2QyxFQUFFLDBCQUEwQixFQUFFLEVBQUUsMkJBQTJCLHlGQUF5RixPQUFPLDRDQUE0QyxRQUFRLGlEQUFpRCw4R0FBOEcsNkNBQTZDLEVBQUUsc0NBQXNDLE9BQU8sb0RBQW9ELG1DQUFtQyxPQUFPLGtEQUFrRCw2Q0FBNkMsRUFBRSxtQ0FBbUMsT0FBTyxvREFBb0QsZ0NBQWdDLE9BQU8sa0RBQWtELGtEQUFrRCxFQUFFLDJCQUEyQiw2REFBNkQsT0FBTyw4Q0FBOEMsUUFBUSxpREFBaUQsc0dBQXNHLHlCQUF5QixFQUFFLHlCQUF5QixFQUFFLDZDQUE2QyxFQUFFLDJCQUEyQixrRUFBa0UsT0FBTywrQ0FBK0MsUUFBUSxpREFBaUQsc0dBQXNHLG1GQUFtRixFQUFFLDZDQUE2QyxFQUFFLDJCQUEyQixtRUFBbUUsT0FBTyxnREFBZ0QsUUFBUSxpREFBaUQsNkdBQTZHLHlCQUF5QixFQUFFLHNHQUFzRyxFQUFFLHlCQUF5QixFQUFFLDZDQUE2QyxFQUFFLDJCQUEyQixxNkRBQXE2RCxrQ0FBa0MsRUFBRSwyQkFBMkIsNkZBQTZGLE9BQU8sK0JBQStCLFFBQVEsNERBQTRELG9DQUFvQyxPQUFPLDBEQUEwRCwwREFBMEQsdUNBQXVDLEVBQUUsMkJBQTJCLG9GQUFvRix3QkFBd0IsMkRBQTJELEdBQUcsMEJBQTBCLEdBQUcsY0FBYyxHQUFHLGNBQWMsR0FBRyw2Q0FBNkMsWUFBWSxlQUFlLGFBQWEsc0JBQXNCLE9BQU8sOERBQThELFlBQVksOERBQThELE9BQU8sNERBQTRELDBDQUEwQywrQkFBK0IsRUFBRSxFQUFFLG9CQUFvQixlQUFlLHFEQUFxRCxPQUFPLHFEQUFxRCx5Q0FBeUMsT0FBTyxtREFBbUQsOEJBQThCLEVBQUUscURBQXFELE9BQU8scURBQXFELHlDQUF5QyxPQUFPLG1EQUFtRCw4QkFBOEIsRUFBRSxvQkFBb0IsZUFBZSxpREFBaUQsRUFBRSxrREFBa0QsRUFBRSxxREFBcUQsRUFBRSwrQkFBK0IsRUFBRSwrQkFBK0IsRUFBRSxpQ0FBaUMsRUFBRSxrUkFBa1IsT0FBTyw4QkFBOEIsUUFBUSw4RUFBOEUsb0NBQW9DLE9BQU8sNEVBQTRFLG1EQUFtRCx1Q0FBdUMsRUFBRSxFQUFFLGFBQWEsc0JBQXNCLE9BQU8sOERBQThELFlBQVksOERBQThELE9BQU8sNERBQTRELGdCQUFnQiwrQkFBK0IsRUFBRSxFQUFFLDhCQUE4QixFQUFFLHNCQUFzQixFQUFFLDRCQUE0QixlQUFlLHNCQUFzQixFQUFFLCtCQUErQixFQUFFLHNCQUFzQixxWkFBcVosdUNBQXVDLEVBQUUscUJBQXFCLHlOQUF5TixPQUFPLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxFQUFFLFFBQVEscUNBQXFDLGtDQUFrQyxPQUFPLGlEQUFpRCxxREFBcUQsb0NBQW9DLEVBQUUsdUNBQXVDLEVBQUUsZUFBZSxlQUFlLGVBQWUsZUFBZSxzRUFBc0UsT0FBTyw2REFBNkQsZ0hBQWdILDZDQUE2QyxFQUFFLCtZQUErWSxHQUFHLE9BQU8sK0NBQStDLEVBQUUsY0FBYyxPQUFPLDBDQUEwQyxRQUFRLHNDQUFzQyx3RUFBd0UsRUFBRSxjQUFjLE9BQU8sMENBQTBDLFFBQVEsb0NBQW9DLCtEQUErRCxFQUFFLGNBQWMsT0FBTywwQ0FBMEMsUUFBUSx5Q0FBeUMscUVBQXFFLEVBQUUsdVVBQXVVLEdBQUcsT0FBTywrQ0FBK0MsRUFBRSw2V0FBNlcsR0FBRyxPQUFPLCtDQUErQyxFQUFFLGNBQWMsT0FBTywwQ0FBMEMsUUFBUSxlQUFlLHlFQUF5RSxFQUFFLFFBQVEsbUNBQW1DLGlJQUFpSSxFQUFFLDJTQUEyUyxHQUFHLE9BQU8sK0NBQStDLEVBQUUsMFZBQTBWLEdBQUcsT0FBTywrQ0FBK0MsRUFBRSxnRkFBZ0YsRUFBRSxFQUFFLGNBQWMsT0FBTyxrRUFBa0UsUUFBUSxlQUFlLCtZQUErWSxHQUFHLE9BQU8sK0NBQStDLEVBQUUsY0FBYyxPQUFPLDBDQUEwQyxRQUFRLHNDQUFzQyx3RUFBd0UsRUFBRSxjQUFjLE9BQU8sMENBQTBDLFFBQVEsb0NBQW9DLCtEQUErRCxFQUFFLGNBQWMsT0FBTywwQ0FBMEMsUUFBUSx5Q0FBeUMscUVBQXFFLEVBQUUsdVVBQXVVLEdBQUcsT0FBTywrQ0FBK0MsRUFBRSw2V0FBNlcsR0FBRyxPQUFPLCtDQUErQyxFQUFFLGNBQWMsT0FBTywwQ0FBMEMsUUFBUSxlQUFlLHlFQUF5RSxFQUFFLFFBQVEsbUNBQW1DLGlJQUFpSSxFQUFFLDJTQUEyUyxHQUFHLE9BQU8sK0NBQStDLEVBQUUsMFZBQTBWLEdBQUcsT0FBTywrQ0FBK0MsRUFBRSxnRkFBZ0YsRUFBRSxRQUFRLGlFQUFpRSwwR0FBMEcsRUFBRSxzRkFBc0YsT0FBTyxtRUFBbUUsZ0RBQWdELE9BQU8saUVBQWlFLDJEQUEyRCwrWUFBK1ksR0FBRyxPQUFPLCtDQUErQyxFQUFFLGNBQWMsT0FBTywwQ0FBMEMsUUFBUSxzQ0FBc0Msd0VBQXdFLEVBQUUsY0FBYyxPQUFPLDBDQUEwQyxRQUFRLG9DQUFvQywrREFBK0QsRUFBRSxjQUFjLE9BQU8sMENBQTBDLFFBQVEseUNBQXlDLHFFQUFxRSxFQUFFLHVVQUF1VSxHQUFHLE9BQU8sK0NBQStDLEVBQUUsNldBQTZXLEdBQUcsT0FBTywrQ0FBK0MsRUFBRSxjQUFjLE9BQU8sMENBQTBDLFFBQVEsZUFBZSx5RUFBeUUsRUFBRSxRQUFRLG1DQUFtQyxpSUFBaUksRUFBRSwyU0FBMlMsR0FBRyxPQUFPLCtDQUErQyxFQUFFLDBWQUEwVixHQUFHLE9BQU8sK0NBQStDLEVBQUUsZ0ZBQWdGLEVBQUUsRUFBRSxjQUFjLE9BQU8sMkNBQTJDLHFHQUFxRyxFQUFFLDhDQUE4QyxPQUFPLHFEQUFxRCwyQ0FBMkMsT0FBTyxtREFBbUQsOEJBQThCLEVBQUUsY0FBYyxPQUFPLDBDQUEwQyx5RkFBeUYsRUFBRSxpRUFBaUUsT0FBTyx1REFBdUQsc0NBQXNDLDhDQUE4QyxPQUFPLCtDQUErQywrRUFBK0UsNkNBQTZDLEVBQUUsRUFBRSxFQUFFLEVBQUUsK0JBQStCLEVBQUUsOEJBQThCLEVBQUUsRUFBRSwrQkFBK0IsRUFBRSw4QkFBOEIsRUFBRSx3QkFBd0IsNE5BQTROLE9BQU8sZUFBZSxnQ0FBZ0MsRUFBRSxRQUFRLG9FQUFvRSxRQUFRLDZCQUE2QixRQUFRLGVBQWUsMkZBQTJGLEVBQUUsaURBQWlELEVBQUUsUUFBUSx3Q0FBd0Msa0NBQWtDLE9BQU8seURBQXlELHFEQUFxRCxvQ0FBb0MsRUFBRSx1Q0FBdUMsRUFBRSxlQUFlLG1HQUFtRywwQkFBMEIsRUFBRSw2QkFBNkIsRUFBRSx5QkFBeUIsZUFBZSx1Q0FBdUMsRUFBRSxzQkFBc0IsRUFBRSw0QkFBNEIsZUFBZSwwQkFBMEIsRUFBRSwrQkFBK0IsRUFBRSwrQkFBK0IsRUFBRSwrQkFBK0IsRUFBRSwyQkFBMkIsRUFBRSw0VUFBNFUsT0FBTyxvQ0FBb0MsUUFBUSxrRUFBa0Usb0NBQW9DLE9BQU8sZ0VBQWdFLGdCQUFnQix1Q0FBdUMsRUFBRSxFQUFFLHVDQUF1QyxPQUFPLCtEQUErRCxvQ0FBb0MsT0FBTyw2REFBNkQsZ0JBQWdCLHVDQUF1QyxFQUFFLEVBQUUsK0JBQStCLEVBQUUsdUJBQXVCLGVBQWUsMEJBQTBCLEVBQUUsK0JBQStCLEVBQUUsMkJBQTJCLEVBQUUsaUNBQWlDLEVBQUUsNFVBQTRVLE9BQU8sb0NBQW9DLFFBQVEsbUVBQW1FLG9DQUFvQyxPQUFPLGlFQUFpRSxtRUFBbUUsc0NBQXNDLEVBQUUsa0NBQWtDLEVBQUUsRUFBRSx1Q0FBdUMsT0FBTywrREFBK0Qsb0NBQW9DLE9BQU8sNkRBQTZELGdCQUFnQixrQ0FBa0MsRUFBRSxFQUFFLHNCQUFzQixFQUFFLHFCQUFxQixlQUFlLGVBQWUsY0FBYyxPQUFPLG9FQUFvRSxRQUFRLDZCQUE2QixRQUFRLGVBQWUsMkZBQTJGLEVBQUUsaURBQWlELEdBQUcsbUZBQW1GLEVBQUUsY0FBYyxPQUFPLG9FQUFvRSxRQUFRLDZCQUE2QixRQUFRLGVBQWUsMkZBQTJGLEVBQUUsaURBQWlELEdBQUcsa0ZBQWtGLEVBQUUsRUFBRSxjQUFjLE9BQU8sb0VBQW9FLFFBQVEsNkJBQTZCLFFBQVEsZUFBZSwyRkFBMkYsRUFBRSxpREFBaUQsR0FBRyxrRkFBa0YsRUFBRSxtQkFBbUIsZUFBZSxxREFBcUQsT0FBTywrQ0FBK0MsNERBQTRELHlEQUF5RCw2Q0FBNkMsRUFBRSxFQUFFLHFEQUFxRCxPQUFPLCtDQUErQyw0REFBNEQseURBQXlELDZDQUE2QyxFQUFFLEVBQUUsa0NBQWtDLGVBQWUsY0FBYyxPQUFPLDREQUE0RCwrQkFBK0IsRUFBRSxvQkFBb0IsY0FBYyxPQUFPLDRDQUE0QyxRQUFRLGdEQUFnRCxRQUFRLG9EQUFvRCxRQUFRLGVBQWUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUsY0FBYyxPQUFPLDRDQUE0QyxRQUFRLGdEQUFnRCxRQUFRLHFEQUFxRCx1R0FBdUcsRUFBRSxRQUFRLHNDQUFzQywwc0RBQTBzRCxvQkFBb0Isd09BQXdPLE9BQU8sNENBQTRDLFFBQVEsZ0RBQWdELFFBQVEsb0RBQW9ELFFBQVEsZUFBZSwrQkFBK0IsRUFBRSwrQkFBK0IsRUFBRSxjQUFjLE9BQU8sNENBQTRDLFFBQVEsZ0RBQWdELFFBQVEscURBQXFELHVHQUF1RyxFQUFFLFFBQVEsMkNBQTJDLFFBQVEsa0ZBQWtGLHNGQUFzRixPQUFPLGdGQUFnRixnQkFBZ0IsdUNBQXVDLEVBQUUsY0FBYyxjQUFjLE9BQU8sZUFBZSxpREFBaUQsY0FBYyxPQUFPLDhDQUE4QyxRQUFRLDJEQUEyRCxtR0FBbUcsRUFBRSxRQUFRLG9EQUFvRCxRQUFRLDRDQUE0QyxRQUFRLDJEQUEyRCxtR0FBbUcsRUFBRSxRQUFRLG9EQUFvRCxRQUFRLHVEQUF1RCxRQUFRLDBEQUEwRCxTQUFTLDJEQUEyRCxTQUFTLG9FQUFvRSxtR0FBbUcsRUFBRSxTQUFTLHlEQUF5RCwwVEFBMFQsRUFBRSxjQUFjLE9BQU8sdURBQXVELG1HQUFtRyxFQUFFLFFBQVEsb0RBQW9ELFFBQVEsOENBQThDLFFBQVEsdURBQXVELG1HQUFtRyxFQUFFLFFBQVEsb0RBQW9ELFFBQVEsbURBQW1ELFFBQVEsc0RBQXNELFNBQVMsdURBQXVELFNBQVMsZ0VBQWdFLG1HQUFtRyxFQUFFLFNBQVMseURBQXlELHNTQUFzUyxFQUFFLGNBQWMsT0FBTyx5Q0FBeUMsUUFBUSxzREFBc0QsbUdBQW1HLEVBQUUsUUFBUSxvREFBb0QsUUFBUSxrREFBa0QsMEpBQTBKLEVBQUUsY0FBYyxPQUFPLHdDQUF3QyxRQUFRLHFEQUFxRCxtR0FBbUcsRUFBRSxRQUFRLG9EQUFvRCxRQUFRLGtEQUFrRCxtSkFBbUosRUFBRSxjQUFjLE9BQU8sOENBQThDLFFBQVEsMkRBQTJELG1HQUFtRyxFQUFFLFFBQVEsb0RBQW9ELFFBQVEsdURBQXVELFFBQVEsMERBQTBELFFBQVEsMkRBQTJELFFBQVEsb0VBQW9FLG1HQUFtRyxFQUFFLFFBQVEsa0RBQWtELGlRQUFpUSxFQUFFLGNBQWMsT0FBTyx1REFBdUQsbUdBQW1HLEVBQUUsUUFBUSxvREFBb0QsUUFBUSxtREFBbUQsUUFBUSxzREFBc0QsUUFBUSx1REFBdUQsUUFBUSxnRUFBZ0UsbUdBQW1HLEVBQUUsUUFBUSxrREFBa0QscVFBQXFRLEVBQUUseUdBQXlHLEVBQUUsR0FBRyxpRkFBaUYsZ0JBQWdCLGVBQWUsdUZBQXVGLEVBQUUsMkRBQTJELEVBQUUsbUVBQW1FLEVBQUUsbUdBQW1HLEVBQUUsbUdBQW1HLEVBQUUsa0VBQWtFLEVBQUUseUVBQXlFLEVBQUUsc0VBQXNFLEVBQUUsOERBQThELEVBQUUsMkRBQTJELEVBQUUsZ0VBQWdFLEVBQUUseUNBQXlDLE9BQU8seUNBQXlDLGtDQUFrQyxPQUFPLHlDQUF5QyxnQkFBZ0IsdUNBQXVDLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSxhQUFhLHVDQUF1QyxPQUFPLCtEQUErRCxvQ0FBb0MsT0FBTyw2REFBNkQsMkNBQTJDLHNCQUFzQixFQUFFLG1CQUFtQix1Q0FBdUMsT0FBTywrREFBK0Qsb0NBQW9DLE9BQU8sNkRBQTZELGlEQUFpRCwrQkFBK0IsRUFBRSwwRUFBMEUsRUFBRSxrQkFBa0IsY0FBYyxPQUFPLHdDQUF3QyxRQUFRLDREQUE0RCxRQUFRLGdEQUFnRCxRQUFRLDRDQUE0QywwRkFBMEYsd0JBQXdCLGVBQWUsY0FBYyxPQUFPLG1FQUFtRSxtL0hBQW0vSCxFQUFFLGlIQUFpSCxFQUFFLG9DQUFvQyxlQUFlLHFFQUFxRSxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwyREFBMkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsZ0JBQWdCLDZMQUE2TCxvREFBb0QsRUFBRSxFQUFFLGdEQUFnRCxFQUFFLGdEQUFnRCxFQUFFLGlEQUFpRCxFQUFFLDZEQUE2RCxPQUFPLHFEQUFxRCxRQUFRLGlEQUFpRCwrSEFBK0gsb0RBQW9ELEVBQUUsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLGNBQWMsT0FBTyx5Q0FBeUMsb0RBQW9ELEVBQUUsMENBQTBDLGVBQWUscUVBQXFFLE9BQU8saUNBQWlDLFFBQVEscURBQXFELFFBQVEsaURBQWlELDJEQUEyRCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCxnQkFBZ0IsNkxBQTZMLG9EQUFvRCxFQUFFLEVBQUUsc0RBQXNELEVBQUUsc0RBQXNELEVBQUUsaURBQWlELEVBQUUsNkRBQTZELE9BQU8scURBQXFELFFBQVEsaURBQWlELCtIQUErSCxvREFBb0QsRUFBRSxFQUFFLCtCQUErQixFQUFFLEVBQUUsY0FBYyxPQUFPLHlDQUF5QyxvREFBb0QsRUFBRSx5Q0FBeUMsZUFBZSwyUUFBMlEsRUFBRSwwRUFBMEUsRUFBRSwwQkFBMEIsRUFBRSx5QkFBeUIsRUFBRSx5QkFBeUIsRUFBRSx1Q0FBdUMsT0FBTyx5Q0FBeUMsZ0NBQWdDLE9BQU8seUNBQXlDLGdCQUFnQixvREFBb0QsRUFBRSxFQUFFLDJCQUEyQixFQUFFLHNGQUFzRixFQUFFLG1GQUFtRixFQUFFLDZDQUE2QyxFQUFFLHVDQUF1QyxPQUFPLCtEQUErRCxzR0FBc0csT0FBTyw2REFBNkQsZ0JBQWdCLG9EQUFvRCxFQUFFLEVBQUUsMkNBQTJDLGVBQWUsYUFBYSxzQkFBc0IsT0FBTyw4REFBOEQsWUFBWSwyRkFBMkYsT0FBTyw0REFBNEQsZ0JBQWdCLHNEQUFzRCxFQUFFLHdEQUF3RCxFQUFFLEVBQUUsd0RBQXdELEVBQUUsNkNBQTZDLGVBQWUsaUNBQWlDLEVBQUUsYUFBYSxzQkFBc0IsT0FBTyw4REFBOEQsWUFBWSwyRkFBMkYsT0FBTyw0REFBNEQsMENBQTBDLHNEQUFzRCxFQUFFLEVBQUUsa0ZBQWtGLEVBQUUsZ0ZBQWdGLEVBQUUsNmlCQUE2aUIseURBQXlELEVBQUUsRUFBRSxzQ0FBc0MsT0FBTyxvREFBb0QsZ0VBQWdFLE9BQU8sa0RBQWtELG9EQUFvRCxxQ0FBcUMsRUFBRSxvQ0FBb0MsRUFBRSw2Q0FBNkMsRUFBRSxFQUFFLG1DQUFtQyxPQUFPLG9EQUFvRCw2REFBNkQsT0FBTyxrREFBa0Qsb0RBQW9ELHFDQUFxQyxFQUFFLDZDQUE2QyxFQUFFLEVBQUUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUsc0JBQXNCLEVBQUUsOENBQThDLGVBQWUsMEJBQTBCLEVBQUUsK0JBQStCLEVBQUUsaUNBQWlDLEVBQUUsK0JBQStCLEVBQUUsK0JBQStCLEVBQUUsMkJBQTJCLEVBQUUsNFVBQTRVLE9BQU8sb0NBQW9DLFFBQVEsa0VBQWtFLG1FQUFtRSxPQUFPLGdFQUFnRSxnQkFBZ0IseURBQXlELEVBQUUsRUFBRSx1Q0FBdUMsT0FBTywrREFBK0QsbUVBQW1FLE9BQU8sNkRBQTZELGdCQUFnQix5REFBeUQsRUFBRSxFQUFFLHdEQUF3RCxFQUFFLGlDQUFpQyxlQUFlLGtIQUFrSCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwyREFBMkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsZ0JBQWdCLGdMQUFnTCxvREFBb0QsRUFBRSxFQUFFLDBCQUEwQixFQUFFLGdEQUFnRCxFQUFFLGdEQUFnRCxFQUFFLGlEQUFpRCxFQUFFLDZEQUE2RCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwyRUFBMkUsNkxBQTZMLG9EQUFvRCxFQUFFLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSx5SkFBeUosMEJBQTBCLEVBQUUsNkJBQTZCLEVBQUUsRUFBRSxFQUFFLHVDQUF1QyxlQUFlLGtIQUFrSCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwyREFBMkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsZ0JBQWdCLGdMQUFnTCxvREFBb0QsRUFBRSxFQUFFLDBCQUEwQixFQUFFLHNEQUFzRCxFQUFFLHNEQUFzRCxFQUFFLGlEQUFpRCxFQUFFLDZEQUE2RCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwyRUFBMkUsNkxBQTZMLG9EQUFvRCxFQUFFLEVBQUUsK0JBQStCLEVBQUUsRUFBRSxrS0FBa0ssMEJBQTBCLEVBQUUsNkJBQTZCLEVBQUUsRUFBRSxFQUFFLHNDQUFzQyxvSEFBb0gsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsMkVBQTJFLDZMQUE2TCxvREFBb0QsRUFBRSxFQUFFLDBCQUEwQixFQUFFLDJKQUEySiwwQkFBMEIsRUFBRSw2QkFBNkIsRUFBRSxFQUFFLGdDQUFnQyxlQUFlLG1IQUFtSCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGdEQUFnRCxRQUFRLDhDQUE4QywyREFBMkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsZ0JBQWdCLGdMQUFnTCxvREFBb0QsRUFBRSxFQUFFLDBCQUEwQixFQUFFLDZEQUE2RCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwwSEFBMEgsMEJBQTBCLEVBQUUsNkJBQTZCLEVBQUUsRUFBRSw2REFBNkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsc0lBQXNJLDBCQUEwQixFQUFFLDZCQUE2QixFQUFFLEVBQUUsOEZBQThGLHNCQUFzQixFQUFFLEVBQUUsRUFBRSxzQ0FBc0MsZUFBZSxtSEFBbUgsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsMkRBQTJELE9BQU8saUNBQWlDLFFBQVEscURBQXFELFFBQVEsaURBQWlELGdCQUFnQixnTEFBZ0wsb0RBQW9ELEVBQUUsRUFBRSwwQkFBMEIsRUFBRSw2REFBNkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsbUlBQW1JLDBCQUEwQixFQUFFLDZCQUE2QixFQUFFLEVBQUUsNkRBQTZELE9BQU8saUNBQWlDLFFBQVEscURBQXFELFFBQVEsaURBQWlELCtJQUErSSwwQkFBMEIsRUFBRSw2QkFBNkIsRUFBRSxFQUFFLDhGQUE4RiwrQkFBK0IsRUFBRSxFQUFFLEVBQUUscUNBQXFDLHFIQUFxSCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCw2REFBNkQsNkxBQTZMLG9EQUFvRCxFQUFFLEVBQUUsMEJBQTBCLEVBQUUsb0ZBQW9GLCtEQUErRCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCwwSEFBMEgsMEJBQTBCLEVBQUUsNkJBQTZCLEVBQUUsRUFBRSwrREFBK0QsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsd0lBQXdJLDBCQUEwQixFQUFFLDZCQUE2QixFQUFFLEVBQUUsc0JBQXNCLEVBQUUsRUFBRSwyQ0FBMkMscUhBQXFILE9BQU8saUNBQWlDLFFBQVEscURBQXFELFFBQVEsaURBQWlELDZEQUE2RCw2TEFBNkwsb0RBQW9ELEVBQUUsRUFBRSwwQkFBMEIsRUFBRSxvRkFBb0YsK0RBQStELE9BQU8saUNBQWlDLFFBQVEscURBQXFELFFBQVEsaURBQWlELG1JQUFtSSwwQkFBMEIsRUFBRSw2QkFBNkIsRUFBRSxFQUFFLCtEQUErRCxPQUFPLGlDQUFpQyxRQUFRLHFEQUFxRCxRQUFRLGlEQUFpRCx3SUFBd0ksMEJBQTBCLEVBQUUsNkJBQTZCLEVBQUUsRUFBRSwrQkFBK0IsRUFBRSxFQUFFLHFDQUFxQyw2REFBNkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsNkRBQTZELHNCQUFzQixFQUFFLDJDQUEyQyw2REFBNkQsT0FBTyxpQ0FBaUMsUUFBUSxxREFBcUQsUUFBUSxpREFBaUQsNkRBQTZELCtCQUErQixFQUFFLDJCQUEyQixjQUFjLE9BQU8sNENBQTRDLHVIQUF1SCxvQkFBb0Isc1pBQXNaLE9BQU8sZUFBZSxnQ0FBZ0MsRUFBRSxRQUFRLG9FQUFvRSxRQUFRLDZCQUE2QixRQUFRLGVBQWUsMkZBQTJGLEVBQUUsaURBQWlELEVBQUUsUUFBUSwyQ0FBMkMsUUFBUSxlQUFlLGdDQUFnQyxFQUFFLFFBQVEsb0VBQW9FLFFBQVEsNkJBQTZCLFFBQVEsZUFBZSwyRkFBMkYsRUFBRSxpREFBaUQsRUFBRSxTQUFTLGdGQUFnRixzQ0FBc0MsT0FBTyw4RUFBOEUsZ0JBQWdCLDBFQUEwRSxPQUFPLHNEQUFzRCxnRkFBZ0YsZ0NBQWdDLEVBQUUsRUFBRSxvQ0FBb0MsRUFBRSxvQkFBb0IsZUFBZSw2SkFBNkosRUFBRSw2K0NBQTYrQyxFQUFFLG1GQUFtRixFQUFFLGtGQUFrRixPQUFPLGdDQUFnQyxRQUFRLGlDQUFpQyw4REFBOEQsY0FBYyxPQUFPLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxHQUFHLGlMQUFpTCxFQUFFLDBCQUEwQixFQUFFLDBMQUEwTCxPQUFPLHVFQUF1RSxRQUFRLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxHQUFHLHNDQUFzQyxPQUFPLHNFQUFzRSxnQkFBZ0IsNENBQTRDLE9BQU8sNkJBQTZCLFFBQVEsNkRBQTZELGtGQUFrRixPQUFPLDJEQUEyRCxvREFBb0QsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsOEJBQThCLEVBQUUsMkJBQTJCLEVBQUUsbUNBQW1DLEVBQUUscUNBQXFDLEVBQUUsRUFBRSx5Q0FBeUMsT0FBTyw4RUFBOEUsc0NBQXNDLE9BQU8sNEVBQTRFLGdCQUFnQixvQ0FBb0MsRUFBRSxFQUFFLGNBQWMsT0FBTyxlQUFlLGdDQUFnQyxFQUFFLFFBQVEsb0VBQW9FLFFBQVEsNkJBQTZCLFFBQVEsZUFBZSwyRkFBMkYsRUFBRSxpREFBaUQsRUFBRSxRQUFRLHdDQUF3QyxRQUFRLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxHQUFHLDZVQUE2VSxFQUFFLDhFQUE4RSxFQUFFLDBCQUEwQixFQUFFLEVBQUUsRUFBRSwwQkFBMEIsZUFBZSwyREFBMkQsR0FBRywwQkFBMEIsR0FBRyxjQUFjLEdBQUcsY0FBYyxHQUFHLDZDQUE2QyxFQUFFLHNFQUFzRSxFQUFFLHlCQUF5QixlQUFlLDJDQUEyQyw2TEFBNkwsRUFBRSxjQUFjLE9BQU8sNENBQTRDLDBDQUEwQyxFQUFFLGNBQWMsZUFBZSxzQ0FBc0MsT0FBTyxvREFBb0QsbUNBQW1DLE9BQU8sa0RBQWtELG9EQUFvRCxxQ0FBcUMsRUFBRSxvQ0FBb0MsRUFBRSw2Q0FBNkMsRUFBRSxFQUFFLG1DQUFtQyxPQUFPLG9EQUFvRCxnQ0FBZ0MsT0FBTyxrREFBa0Qsb0RBQW9ELHFDQUFxQyxFQUFFLDZDQUE2QyxFQUFFLEVBQUUscUNBQXFDLHVNQUF1TSxPQUFPLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxFQUFFLFFBQVEsa0ZBQWtGLHNDQUFzQyxPQUFPLGdGQUFnRix1REFBdUQsb0NBQW9DLEVBQUUsdUNBQXVDLEVBQUUsdUJBQXVCLDZOQUE2TixPQUFPLGdDQUFnQyxRQUFRLGVBQWUsZ0NBQWdDLEVBQUUsUUFBUSxvRUFBb0UsUUFBUSw2QkFBNkIsUUFBUSxlQUFlLDJGQUEyRixFQUFFLGlEQUFpRCxFQUFFLFFBQVEsdUNBQXVDLHVCQUF1QixzQkFBc0Isa0VBQWtFLHlDQUF5QyxvQkFBb0IsdUJBQXVCLE9BQU8scUVBQXFFLGdEQUFnRCxnREFBZ0QsRUFBRSxzQkFBc0IsRUFBRSxFQUFFLHFCQUFxQixtQ0FBbUMsb0NBQW9DLE9BQU8sbUVBQW1FLGdEQUFnRCxtQ0FBbUMsRUFBRSxnQ0FBZ0MsRUFBRSxzQkFBc0IsRUFBRSwrQkFBK0IsRUFBRSxFQUFFLHFCQUFxQiwyREFBMkQsbURBQW1ELHNCQUFzQixFQUFFLEVBQUUsc0JBQXNCLDZGQUE2Riw0QkFBNEI7O0FBRXprOEUsaUVBQWU7QUFDZjtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcQHNoaWtpanNcXGxhbmdzXFxkaXN0XFxjLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIkNcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWVuYWJsZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGlzYWJsZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlZGVmaW5lZF9tYWNyb3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3dpdGNoX3N0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbm9uX3BhdHRlcm5fMVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdG9yYWdlX3R5cGVzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl8yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl8zXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl80XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl81XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl82XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl83XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdG9yc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNudW1iZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzFcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hLW1hcmtcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzZcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuXzhcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuXzlcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuXzEwXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl8xMVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbm9uX3BhdHRlcm5fMTJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuXzEzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhcmVuc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbm9uX3BhdHRlcm5fcmFuZ2VfN1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvbl9jaGFyYWN0ZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzhcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuX3JhbmdlXzlcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYW5vbl9wYXR0ZXJuXzE0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Fub25fcGF0dGVybl8xNVxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJhY2Nlc3MtbWV0aG9kXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKFthLXpBLVpfXVthLXpBLVpfMC05XSp8KD88PVtcXFxcXFxcXF1cXFxcXFxcXCldKSlcXFxcXFxcXHMqKD86KFxcXFxcXFxcLil8KC0+KSkoKD86KD86W2EtekEtWl9dW2EtekEtWl8wLTldKilcXFxcXFxcXHMqKD86KD86XFxcXFxcXFwuKXwoPzotPikpKSopXFxcXFxcXFxzKihbYS16QS1aX11bYS16QS1aXzAtOV0qKShcXFxcXFxcXCgpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vYmplY3QuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuZG90LWFjY2Vzcy5jXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5wb2ludGVyLWFjY2Vzcy5jXFxcIn0sXFxcIjRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwuXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kb3QtYWNjZXNzLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiLT5cXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLnBvaW50ZXItYWNjZXNzLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiW2EtekEtWl9dW2EtekEtWl8wLTldKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vYmplY3QuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIuK1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJldmVyeXRoaW5nLmVsc2UuY1xcXCJ9XX0sXFxcIjVcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLm1lbWJlci5jXFxcIn0sXFxcIjZcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYXJndW1lbnRzLmJlZ2luLmJyYWNrZXQucm91bmQuZnVuY3Rpb24ubWVtYmVyLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYXJndW1lbnRzLmVuZC5icmFja2V0LnJvdW5kLmZ1bmN0aW9uLm1lbWJlci5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5mdW5jdGlvbi1jYWxsLm1lbWJlci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tY2FsbC1pbm5hcmRzXFxcIn1dfSxcXFwiYW5vbl9wYXR0ZXJuXzFcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoYnJlYWt8Y29udGludWV8ZG98ZWxzZXxmb3J8Z290b3xpZnxfUHJhZ21hfHJldHVybnx3aGlsZSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzEwXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKGludDhfdHxpbnQxNl90fGludDMyX3R8aW50NjRfdHx1aW50OF90fHVpbnQxNl90fHVpbnQzMl90fHVpbnQ2NF90fGludF9sZWFzdDhfdHxpbnRfbGVhc3QxNl90fGludF9sZWFzdDMyX3R8aW50X2xlYXN0NjRfdHx1aW50X2xlYXN0OF90fHVpbnRfbGVhc3QxNl90fHVpbnRfbGVhc3QzMl90fHVpbnRfbGVhc3Q2NF90fGludF9mYXN0OF90fGludF9mYXN0MTZfdHxpbnRfZmFzdDMyX3R8aW50X2Zhc3Q2NF90fHVpbnRfZmFzdDhfdHx1aW50X2Zhc3QxNl90fHVpbnRfZmFzdDMyX3R8dWludF9mYXN0NjRfdHxpbnRwdHJfdHx1aW50cHRyX3R8aW50bWF4X3R8aW50bWF4X3R8dWludG1heF90fHVpbnRtYXhfdClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlLnN0ZGludC5jXFxcIn0sXFxcImFub25fcGF0dGVybl8xMVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYihub0VycnxrTmlsT3B0aW9uc3xrSW52YWxpZElEfGtWYXJpYWJsZUxlbmd0aEFycmF5KVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmNvbnN0YW50Lm1hYy1jbGFzc2ljLmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzEyXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKEFic29sdXRlVGltZXxCb29sZWFufEJ5dGV8Qnl0ZUNvdW50fEJ5dGVPZmZzZXR8Qnl0ZVB0cnxDb21wVGltZVZhbHVlfENvbnN0TG9naWNhbEFkZHJlc3N8Q29uc3RTdHJGaWxlTmFtZVBhcmFtfENvbnN0U3RyaW5nUHRyfER1cmF0aW9ufEZpeGVkfEZpeGVkUHRyfEZsb2F0MzJ8RmxvYXQzMlBvaW50fEZsb2F0NjR8RmxvYXQ4MHxGbG9hdDk2fEZvdXJDaGFyQ29kZXxGcmFjdHxGcmFjdFB0cnxIYW5kbGV8SXRlbUNvdW50fExvZ2ljYWxBZGRyZXNzfE9wdGlvbkJpdHN8T1NFcnJ8T1NTdGF0dXN8T1NUeXBlfE9TVHlwZVB0cnxQaHlzaWNhbEFkZHJlc3N8UHJvY2Vzc1NlcmlhbE51bWJlcnxQcm9jZXNzU2VyaWFsTnVtYmVyUHRyfFByb2NIYW5kbGV8UHRyfFJlc1R5cGV8UmVzVHlwZVB0cnxTaG9ydEZpeGVkfFNob3J0Rml4ZWRQdHJ8U2lnbmVkQnl0ZXxTSW50MTZ8U0ludDMyfFNJbnQ2NHxTSW50OHxTaXplfFN0ckZpbGVOYW1lfFN0cmluZ0hhbmRsZXxTdHJpbmdQdHJ8VGltZUJhc2V8VGltZVJlY29yZHxUaW1lU2NhbGV8VGltZVZhbHVlfFRpbWVWYWx1ZTY0fFVJbnQxNnxVSW50MzJ8VUludDY0fFVJbnQ4fFVuaUNoYXJ8VW5pQ2hhckNvdW50fFVuaUNoYXJDb3VudFB0cnxVbmlDaGFyUHRyfFVuaWNvZGVTY2FsYXJWYWx1ZXxVbml2ZXJzYWxQcm9jSGFuZGxlfFVuaXZlcnNhbFByb2NQdHJ8VW5zaWduZWRGaXhlZHxVbnNpZ25lZEZpeGVkUHRyfFVuc2lnbmVkV2lkZXxVVEYxNkNoYXJ8VVRGMzJDaGFyfFVURjhDaGFyKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUubWFjLWNsYXNzaWMuY1xcXCJ9LFxcXCJhbm9uX3BhdHRlcm5fMTNcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoW0EtWmEtejAtOV9dK190KVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LnR5cGUucG9zaXgtcmVzZXJ2ZWQuY1xcXCJ9LFxcXCJhbm9uX3BhdHRlcm5fMTRcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCI7XFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnRlcm1pbmF0b3Iuc3RhdGVtZW50LmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzE1XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiLFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuZGVsaW1pdGVyLmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzJcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJ0eXBlZGVmXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHlwZWRlZi5jXFxcIn0sXFxcImFub25fcGF0dGVybl8zXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKGNvbnN0fGV4dGVybnxyZWdpc3RlcnxyZXN0cmljdHxzdGF0aWN8dm9sYXRpbGV8aW5saW5lKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLm1vZGlmaWVyLmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzRcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJrW0EtWl1cXFxcXFxcXHcqXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm90aGVyLnZhcmlhYmxlLm1hYy1jbGFzc2ljLmNcXFwifSxcXFwiYW5vbl9wYXR0ZXJuXzVcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJnW0EtWl1cXFxcXFxcXHcqXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLnJlYWR3cml0ZS5nbG9iYWwubWFjLWNsYXNzaWMuY1xcXCJ9LFxcXCJhbm9uX3BhdHRlcm5fNlxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYnNbQS1aXVxcXFxcXFxcdypcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIucmVhZHdyaXRlLnN0YXRpYy5tYWMtY2xhc3NpYy5jXFxcIn0sXFxcImFub25fcGF0dGVybl83XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKE5VTEx8dHJ1ZXxmYWxzZXxUUlVFfEZBTFNFKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5sYW5ndWFnZS5jXFxcIn0sXFxcImFub25fcGF0dGVybl84XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKHVfY2hhcnx1X3Nob3J0fHVfaW50fHVfbG9uZ3x1c2hvcnR8dWludHx1X3F1YWRfdHxxdWFkX3R8cWFkZHJfdHxjYWRkcl90fGRhZGRyX3R8ZGl2X3R8ZGV2X3R8Zml4cHRfdHxibGtjbnRfdHxibGtzaXplX3R8Z2lkX3R8aW5fYWRkcl90fGluX3BvcnRfdHxpbm9fdHxrZXlfdHxtb2RlX3R8bmxpbmtfdHxpZF90fHBpZF90fG9mZl90fHNlZ3N6X3R8c3dibGtfdHx1aWRfdHxpZF90fGNsb2NrX3R8c2l6ZV90fHNzaXplX3R8dGltZV90fHVzZWNvbmRzX3R8c3VzZWNvbmRzX3QpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZS5zeXMtdHlwZXMuY1xcXCJ9LFxcXCJhbm9uX3BhdHRlcm5fOVxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYihwdGhyZWFkX2F0dHJfdHxwdGhyZWFkX2NvbmRfdHxwdGhyZWFkX2NvbmRhdHRyX3R8cHRocmVhZF9tdXRleF90fHB0aHJlYWRfbXV0ZXhhdHRyX3R8cHRocmVhZF9vbmNlX3R8cHRocmVhZF9yd2xvY2tfdHxwdGhyZWFkX3J3bG9ja2F0dHJfdHxwdGhyZWFkX3R8cHRocmVhZF9rZXlfdClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC50eXBlLnB0aHJlYWQuY1xcXCJ9LFxcXCJhbm9uX3BhdHRlcm5fcmFuZ2VfMVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIigoPzooPzooPz5cXFxcXFxcXHMrKXwoXFxcXFxcXFwvXFxcXFxcXFwqKSgoPz4oPzpbXlxcXFxcXFxcKl18KD8+XFxcXFxcXFwqKylbXlxcXFxcXFxcL10pKikoKD8+XFxcXFxcXFwqKylcXFxcXFxcXC8pKSkrP3woPzooPzooPzooPzpcXFxcXFxcXGJ8KD88PVxcXFxcXFxcVykpfCg/PVxcXFxcXFxcVykpfFxcXFxcXFxcQSl8XFxcXFxcXFxaKSkpKCgjKVxcXFxcXFxccypkZWZpbmVcXFxcXFxcXGIpXFxcXFxcXFxzKygoPzwhXFxcXFxcXFx3KVthLXpBLVpfXVxcXFxcXFxcdyooPyFcXFxcXFxcXHcpKSg/OihcXFxcXFxcXCgpKFteKClcXFxcXFxcXFxcXFxcXFxcXSspKFxcXFxcXFxcKSkpP1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbmxpbmVfY29tbWVudFxcXCJ9XX0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuYmVnaW4uY1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifSxcXFwiNFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXC9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn1dfSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5kZWZpbmUuY1xcXCJ9LFxcXCI2XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn0sXFxcIjdcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjhcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24ucGFyYW1ldGVycy5iZWdpbi5jXFxcIn0sXFxcIjlcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5wcmVwcm9jZXNzb3IuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoPzw9WygsXSlcXFxcXFxcXHMqKCg/PCFcXFxcXFxcXHcpW2EtekEtWl9dXFxcXFxcXFx3Kig/IVxcXFxcXFxcdykpXFxcXFxcXFxzKlxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIsXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5wYXJhbWV0ZXJzLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwuXFxcXFxcXFwuXFxcXFxcXFwuXFxcIixcXFwibmFtZVxcXCI6XFxcImVsbGlwc2VzLmMgcHVuY3R1YXRpb24udmFyYXJnLWVsbGlwc2VzLnZhcmlhYmxlLnBhcmFtZXRlci5wcmVwcm9jZXNzb3IuY1xcXCJ9XX0sXFxcIjEwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnBhcmFtZXRlcnMuZW5kLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IubWFjcm8uY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWRlZmluZS1saW5lLWNvbnRlbnRzXFxcIn1dfSxcXFwiYW5vbl9wYXR0ZXJuX3JhbmdlXzJcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKGVycm9yfHdhcm5pbmcpKVxcXFxcXFxcYlxcXFxcXFxccypcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuZGlhZ25vc3RpYy4kMy5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuZGlhZ25vc3RpYy5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwifCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxccypcXFxcXFxcXG4pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuZW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiJ1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiJ3woPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXHMqXFxcXFxcXFxuKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5zaW5nbGUuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlteJ1xcXFxcXFwiXVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxccypcXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZy51bnF1b3RlZC5zaW5nbGUuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9XX1dfSxcXFwiYW5vbl9wYXR0ZXJuX3JhbmdlXzNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKGluY2x1ZGUoPzpfbmV4dCk/fGltcG9ydCkpXFxcXFxcXFxiXFxcXFxcXFxzKlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS4kMy5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PSg/Oi8vfC9cXFxcXFxcXCopKXwoPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmluY2x1ZGUuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5lbmQuY1xcXCJ9fSxcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuZG91YmxlLmluY2x1ZGUuY1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCI8XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI+XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuZW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLm90aGVyLmx0LWd0LmluY2x1ZGUuY1xcXCJ9XX0sXFxcImFub25fcGF0dGVybl9yYW5nZV80XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmxpbmUpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmxpbmUuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oPzovL3wvXFxcXFxcXFwqKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNudW1iZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9XX0sXFxcImFub25fcGF0dGVybl9yYW5nZV81XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooPzooKCMpXFxcXFxcXFxzKnVuZGVmKSlcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUudW5kZWYuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0oPzovL3wvXFxcXFxcXFwqKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiW2EtekEtWl8kXVtcXFxcXFxcXHckXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24ucHJlcHJvY2Vzc29yLmNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn1dfSxcXFwiYW5vbl9wYXR0ZXJuX3JhbmdlXzZcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKig/OigoIylcXFxcXFxcXHMqcHJhZ21hKSlcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUucHJhZ21hLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89KD86Ly98L1xcXFxcXFxcKikpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IucHJhZ21hLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdzXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlthLXpBLVpfJF1bXFxcXFxcXFx3XFxcXFxcXFwtJF0qXFxcIixcXFwibmFtZVxcXCI6XFxcImVudGl0eS5vdGhlci5hdHRyaWJ1dGUtbmFtZS5wcmFnbWEucHJlcHJvY2Vzc29yLmNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbnVtYmVyc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvbl9jaGFyYWN0ZXJcXFwifV19LFxcXCJhbm9uX3BhdHRlcm5fcmFuZ2VfN1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PCFcXFxcXFxcXHcpKD8hXFxcXFxcXFxzKig/OmF0b21pY191aW50X2xlYXN0NjRfdHxhdG9taWNfdWludF9sZWFzdDE2X3R8YXRvbWljX3VpbnRfbGVhc3QzMl90fGF0b21pY191aW50X2xlYXN0OF90fGF0b21pY19pbnRfbGVhc3QxNl90fGF0b21pY191aW50X2Zhc3Q2NF90fGF0b21pY191aW50X2Zhc3QzMl90fGF0b21pY19pbnRfbGVhc3Q2NF90fGF0b21pY19pbnRfbGVhc3QzMl90fHB0aHJlYWRfcndsb2NrYXR0cl90fGF0b21pY191aW50X2Zhc3QxNl90fHB0aHJlYWRfbXV0ZXhhdHRyX3R8YXRvbWljX2ludF9mYXN0MTZfdHxhdG9taWNfdWludF9mYXN0OF90fGF0b21pY19pbnRfZmFzdDY0X3R8YXRvbWljX2ludF9sZWFzdDhfdHxhdG9taWNfaW50X2Zhc3QzMl90fGF0b21pY19pbnRfZmFzdDhfdHxwdGhyZWFkX2NvbmRhdHRyX3R8cHRocmVhZF9yd2xvY2tfdHxhdG9taWNfdWludHB0cl90fGF0b21pY19wdHJkaWZmX3R8YXRvbWljX3VpbnRtYXhfdHxhdG9taWNfaW50bWF4X3R8YXRvbWljX2NoYXIzMl90fGF0b21pY19pbnRwdHJfdHxhdG9taWNfY2hhcjE2X3R8cHRocmVhZF9tdXRleF90fHB0aHJlYWRfY29uZF90fGF0b21pY193Y2hhcl90fHVpbnRfbGVhc3Q2NF90fHVpbnRfbGVhc3QzMl90fHVpbnRfbGVhc3QxNl90fHB0aHJlYWRfb25jZV90fHB0aHJlYWRfYXR0cl90fHVpbnRfbGVhc3Q4X3R8aW50X2xlYXN0MzJfdHxpbnRfbGVhc3QxNl90fHB0aHJlYWRfa2V5X3R8dWludF9mYXN0MzJfdHx1aW50X2Zhc3Q2NF90fHVpbnRfZmFzdDE2X3R8YXRvbWljX3NpemVfdHxhdG9taWNfdXNob3J0fGF0b21pY191bGxvbmd8aW50X2xlYXN0NjRfdHxhdG9taWNfdWxvbmd8aW50X2xlYXN0OF90fGludF9mYXN0MTZfdHxpbnRfZmFzdDMyX3R8aW50X2Zhc3Q2NF90fHVpbnRfZmFzdDhfdHxtZW1vcnlfb3JkZXJ8YXRvbWljX3NjaGFyfGF0b21pY191Y2hhcnxhdG9taWNfc2hvcnR8YXRvbWljX2xsb25nfHRocmVhZF9sb2NhbHxhdG9taWNfYm9vbHxhdG9taWNfdWludHxhdG9taWNfbG9uZ3xpbnRfZmFzdDhfdHxzdXNlY29uZHNfdHxhdG9taWNfY2hhcnxhdG9taWNfaW50fHVzZWNvbmRzX3R8X0ltYWdpbmFyeXx1aW50bWF4X3R8dWludG1heF90fGluX2FkZHJfdHxpbl9wb3J0X3R8X05vcmV0dXJufGJsa3NpemVfdHxwdGhyZWFkX3R8dWludHB0cl90fHZvbGF0aWxlfHVfcXVhZF90fGJsa2NudF90fGludG1heF90fGludHB0cl90fF9Db21wbGV4fHVpbnQxNl90fHVpbnQzMl90fHVpbnQ2NF90fF9BbGlnbm9mfF9BbGlnbmFzfGNvbnRpbnVlfHVuc2lnbmVkfHJlc3RyaWN0fGludG1heF90fHJlZ2lzdGVyfGludDY0X3R8cWFkZHJfdHxzZWdzel90fF9BdG9taWN8YWxpZ25hc3xkZWZhdWx0fGNhZGRyX3R8bmxpbmtfdHx0eXBlZGVmfHVfc2hvcnR8Zml4cHRfdHxjbG9ja190fHN3YmxrX3R8c3NpemVfdHxhbGlnbm9mfGRhZGRyX3R8aW50MTZfdHxpbnQzMl90fHVpbnQ4X3R8c3RydWN0fG1vZGVfdHxzaXplX3R8dGltZV90fHVzaG9ydHx1X2xvbmd8dV9jaGFyfGludDhfdHxkb3VibGV8c2lnbmVkfHN0YXRpY3xleHRlcm58aW5saW5lfHJldHVybnxzd2l0Y2h8eG9yX2VxfGFuZF9lcXxiaXRhbmR8bm90X2VxfHNpemVvZnxxdWFkX3R8dWlkX3R8Yml0b3J8dW5pb258b2ZmX3R8a2V5X3R8aW5vX3R8Y29tcGx8dV9pbnR8c2hvcnR8Y29uc3R8ZmFsc2V8d2hpbGV8ZmxvYXR8cGlkX3R8YnJlYWt8X0Jvb2x8b3JfZXF8ZGl2X3R8ZGV2X3R8Z2lkX3R8aWRfdHxsb25nfGNhc2V8Z290b3xlbHNlfGJvb2x8YXV0b3xpZF90fGVudW18dWludHx0cnVlfE5VTEx8dm9pZHxjaGFyfGZvcnxub3R8aW50fGFuZHx4b3J8ZG98b3J8aWYpXFxcXFxcXFxzKlxcXFxcXFxcKCkoPz1bYS16QS1aX11cXFxcXFxcXHcqXFxcXFxcXFxzKlxcXFxcXFxcKClcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPyFcXFxcXFxcXEcpKD88PVxcXFxcXFxcKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5mdW5jdGlvbi5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24taW5uYXJkc1xcXCJ9XX0sXFxcImFub25fcGF0dGVybl9yYW5nZV84XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKFthLXpBLVpfXVthLXpBLVpfMC05XSp8KD88PVtcXFxcXFxcXF1cXFxcXFxcXCldKSk/KFxcXFxcXFxcWykoPyFcXFxcXFxcXF0pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vYmplY3QuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmJlZ2luLmJyYWNrZXQuc3F1YXJlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcXVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZW5kLmJyYWNrZXQuc3F1YXJlLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmJyYWNrZXQuc3F1YXJlLmFjY2Vzcy5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tY2FsbC1pbm5hcmRzXFxcIn1dfSxcXFwiYW5vbl9wYXR0ZXJuX3JhbmdlXzlcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFtcXFxcXFxcXHMqXFxcXFxcXFxdXFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UubW9kaWZpZXIuYXJyYXkuYnJhY2tldC5zcXVhcmUuY1xcXCJ9LFxcXCJiYWNrc2xhc2hfZXNjYXBlc1xcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwoXFxcXFxcXFxcXFxcXFxcXHxbYWJlZm5wcnR2J1xcXFxcXFwiP118WzAtM11bMC03XXssMn18WzQtN11cXFxcXFxcXGQ/fHhbYS1mQS1GMC05XXssMn18dVthLWZBLUYwLTldeyw0fXxVW2EtZkEtRjAtOV17LDh9KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmNcXFwifSxcXFwiYmxvY2tcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwie1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5ibG9jay5iZWdpbi5icmFja2V0LmN1cmx5LmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIn18KD89XFxcXFxcXFxzKiNcXFxcXFxcXHMqKD86ZWxpZnxlbHNlfGVuZGlmKVxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmJsb2NrLmVuZC5icmFja2V0LmN1cmx5LmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmJsb2NrLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja19pbm5hcmRzXFxcIn1dfV19LFxcXCJibG9ja19jb21tZW50XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyorKFxcXFxcXFxcL1xcXFxcXFxcKilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXC9cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKisoXFxcXFxcXFwvXFxcXFxcXFwqKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuY1xcXCJ9fSxcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX0sXFxcImJsb2NrX2lubmFyZHNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1ibG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZC1ibG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1ibG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNtZXRob2RfYWNjZXNzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21lbWJlcl9hY2Nlc3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY19mdW5jdGlvbl9jYWxsXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/Oig/Oig/PVxcXFxcXFxccykoPzwhZWxzZXxuZXd8cmV0dXJuKSg/PD1cXFxcXFxcXHcpXFxcXFxcXFxzKyhhbmR8YW5kX2VxfGJpdGFuZHxiaXRvcnxjb21wbHxub3R8bm90X2VxfG9yfG9yX2VxfHR5cGVpZHx4b3J8eG9yX2VxfGFsaWdub2Z8YWxpZ25hcykpKSgoPzpbQS1aYS16X11bQS1aYS16MC05X10qK3w6OikrK3woPzooPzw9b3BlcmF0b3IpKD86Wy0qJjw+PSshXSt8XFxcXFxcXFwoXFxcXFxcXFwpfFxcXFxcXFxcW1xcXFxcXFxcXSkpKVxcXFxcXFxccyooXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5iZWdpbi5icmFja2V0LnJvdW5kLmluaXRpYWxpemF0aW9uLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmVuZC5icmFja2V0LnJvdW5kLmluaXRpYWxpemF0aW9uLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmluaXRpYWxpemF0aW9uLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jYWxsLWlubmFyZHNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJ7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmJsb2NrLmJlZ2luLmJyYWNrZXQuY3VybHkuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwifXwoPz1cXFxcXFxcXHMqI1xcXFxcXFxccyooPzplbGlmfGVsc2V8ZW5kaWYpXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYmxvY2suZW5kLmJyYWNrZXQuY3VybHkuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2tfaW5uYXJkc1xcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhcmVucy1ibG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSxcXFwiY19jb25kaXRpb25hbF9jb250ZXh0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2tfaW5uYXJkc1xcXCJ9XX0sXFxcImNfZnVuY3Rpb25fY2FsbFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/ISg/OndoaWxlfGZvcnxkb3xpZnxlbHNlfHN3aXRjaHxjYXRjaHxlbnVtZXJhdGV8cmV0dXJufHR5cGVpZHxhbGlnbm9mfGFsaWduYXN8c2l6ZW9mfFtjcl0/aXRlcmF0ZXxhbmR8YW5kX2VxfGJpdGFuZHxiaXRvcnxjb21wbHxub3R8bm90X2VxfG9yfG9yX2VxfHR5cGVpZHx4b3J8eG9yX2VxfGFsaWdub2Z8YWxpZ25hcylcXFxcXFxcXHMqXFxcXFxcXFwoKSg/PSg/OltBLVphLXpfXVtBLVphLXowLTlfXSorfDo6KSsrXFxcXFxcXFxzKlxcXFxcXFxcKHwoPzooPzw9b3BlcmF0b3IpKD86Wy0qJjw+PSshXSt8XFxcXFxcXFwoXFxcXFxcXFwpfFxcXFxcXFxcW1xcXFxcXFxcXSkpXFxcXFxcXFxzKlxcXFxcXFxcKClcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzw9XFxcXFxcXFwpKSg/IVxcXFxcXFxcdylcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5mdW5jdGlvbi1jYWxsLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jYWxsLWlubmFyZHNcXFwifV19LFxcXCJjYXNlX3N0YXRlbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIigoPz4oPzooPzooPz4oPzwhXFxcXFxcXFxzKVxcXFxcXFxccyspfChcXFxcXFxcXC9cXFxcXFxcXCopKCg/Pig/OlteXFxcXFxcXFwqXXwoPz5cXFxcXFxcXCorKVteXFxcXFxcXFwvXSkqKSgoPz5cXFxcXFxcXCorKVxcXFxcXFxcLykpKSt8KD86KD86KD86KD86XFxcXFxcXFxifCg/PD1cXFxcXFxcXFcpKXwoPz1cXFxcXFxcXFcpKXxcXFxcXFxcXEEpfFxcXFxcXFxcWikpKSkoKD88IVxcXFxcXFxcdyljYXNlKD8hXFxcXFxcXFx3KSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW5saW5lX2NvbW1lbnRcXFwifV19LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0sXFxcIjRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcXFxcXFwvXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX0sXFxcIjVcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5jYXNlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig6KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb2xvbi5jYXNlLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmNvbmRpdGlvbmFsLmNhc2UuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V2YWx1YXRpb25fY29udGV4dFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjX2NvbmRpdGlvbmFsX2NvbnRleHRcXFwifV19LFxcXCJjb21tZW50c1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwicGF0dGVybnNcXFwiOlt7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIig/Ol4pKD8+XFxcXFxcXFxzKikoXFxcXFxcXFwvXFxcXFxcXFwvWyFcXFxcXFxcXC9dKylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5kb2N1bWVudGF0aW9uLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PD1cXFxcXFxcXG4pKD88IVxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5kb3VibGUtc2xhc2guZG9jdW1lbnRhdGlvbi5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzpjYWxsZXJncmFwaHxjYWxsZ3JhcGh8ZWxzZXxlbmRpZnxmXFxcXFxcXFwkfGZcXFxcXFxcXFt8ZlxcXFxcXFxcXXxoaWRlY2FsbGVyZ3JhcGh8aGlkZWNhbGxncmFwaHxoaWRlcmVmYnl8aGlkZXJlZnN8aGlkZWluaXRpYWxpemVyfGh0bWxpbmNsdWRlfG58bm9zdWJncm91cGluZ3xwcml2YXRlfHByaXZhdGVzZWN0aW9ufHByb3RlY3RlZHxwcm90ZWN0ZWRzZWN0aW9ufHB1YmxpY3xwdWJsaWNzZWN0aW9ufHB1cmV8c2hvd2luaXRpYWxpemVyfHNob3dyZWZieXxzaG93cmVmc3x0YWJsZW9mY29udGVudHN8XFxcXFxcXFwkfFxcXFxcXFxcI3w8fD58JXxcXFxcXFxcInxcXFxcXFxcXC58PXw6OnxcXFxcXFxcXHx8XFxcXFxcXFwtXFxcXFxcXFwtfFxcXFxcXFxcLVxcXFxcXFxcLVxcXFxcXFxcLSlcXFxcXFxcXGIoPzpcXFxcXFxcXHtbXn1dKlxcXFxcXFxcfSk/XFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLml0YWxpYy5kb3h5Z2VuLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKCg/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzphfGVtfGUpKVxcXFxcXFxccysoXFxcXFxcXFxTKylcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmJvbGQuZG94eWdlbi5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdYilcXFxcXFxcXHMrKFxcXFxcXFxcUyspXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5pbmxpbmUucmF3LnN0cmluZy5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86Y3xwKSlcXFxcXFxcXHMrKFxcXFxcXFxcUyspXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzphfGFuY2hvcnxifGN8Y2l0ZXxjb3B5YnJpZWZ8Y29weWRldGFpbHxjb3B5ZG9jfGRlZnxkaXJ8ZG9udGluY2x1ZGV8ZXxlbXxlbW9qaXxlbnVtfGV4YW1wbGV8ZXh0ZW5kc3xmaWxlfGlkbGV4Y2VwdHxpbXBsZW1lbnRzfGluY2x1ZGV8aW5jbHVkZWRvY3xpbmNsdWRlbGluZW5vfGxhdGV4aW5jbHVkZXxsaW5rfG1lbWJlcm9mfG5hbWVzcGFjZXxwfHBhY2thZ2V8cmVmfHJlZml0ZW18cmVsYXRlZHxyZWxhdGVzfHJlbGF0ZWRhbHNvfHJlbGF0ZXNhbHNvfHZlcmJpbmNsdWRlKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YWRkaW5kZXh8YWRkdG9ncm91cHxjYXRlZ29yeXxjbGFzc3xkZWZncm91cHxkaWFmaWxlfGRvdGZpbGV8ZWxzZWlmfGZufGhlYWRlcmZpbGV8aWZ8aWZub3R8aW1hZ2V8aW5ncm91cHxpbnRlcmZhY2V8bGluZXxtYWlucGFnZXxtc2NmaWxlfG5hbWV8b3ZlcmxvYWR8cGFnZXxwcm9wZXJ0eXxwcm90b2NvbHxzZWN0aW9ufHNraXB8c2tpcGxpbmV8c25pcHBldHxzbmlwcGV0ZG9jfHNuaXBwZXRsaW5lbm98c3RydWN0fHN1YnBhZ2V8c3Vic2VjdGlvbnxzdWJzdWJzZWN0aW9ufHR5cGVkZWZ8dW5pb258dW50aWx8dmhkbGZsb3d8d2Vha2dyb3VwKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcImlufG91dFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnBhcmFtZXRlci5kaXJlY3Rpb24uJDAuY1xcXCJ9XX0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdcGFyYW0pKD86XFxcXFxcXFxzKlxcXFxcXFxcWygoPzosP1xcXFxcXFxccyooPzppbnxvdXQpXFxcXFxcXFxzKikrKVxcXFxcXFxcXSk/XFxcXFxcXFxzKyhcXFxcXFxcXGJcXFxcXFxcXHcrXFxcXFxcXFxiKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YXJnfGF0dGVudGlvbnxhdXRob3J8YXV0aG9yc3xicmllZnxidWd8Y29weXJpZ2h0fGRhdGV8ZGVwcmVjYXRlZHxkZXRhaWxzfGV4Y2VwdGlvbnxpbnZhcmlhbnR8bGl8bm90ZXxwYXJ8cGFyYWdyYXBofHBhcmFtfHBvc3R8cHJlfHJlbWFya3xyZW1hcmtzfHJlc3VsdHxyZXR1cm58cmV0dXJuc3xyZXR2YWx8c2F8c2VlfHNob3J0fHNpbmNlfHRlc3R8dGhyb3d8dG9kb3x0cGFyYW18dmVyc2lvbnx3YXJuaW5nfHhyZWZpdGVtKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86Y29kZXxjb25kfGRvY2Jvb2tvbmx5fGRvdHxodG1sb25seXxpbnRlcm5hbHxsYXRleG9ubHl8bGlua3xtYW5vbmx5fG1zY3xwYXJibG9ja3xydGZvbmx5fHNlY3JlZmxpc3R8dW1sfHZlcmJhdGltfHhtbG9ubHl8ZW5kY29kZXxlbmRjb25kfGVuZGRvY2Jvb2tvbmx5fGVuZGRvdHxlbmRodG1sb25seXxlbmRpbnRlcm5hbHxlbmRsYXRleG9ubHl8ZW5kbGlua3xlbmRtYW5vbmx5fGVuZG1zY3xlbmRwYXJibG9ja3xlbmRydGZvbmx5fGVuZHNlY3JlZmxpc3R8ZW5kdW1sfGVuZHZlcmJhdGltfGVuZHhtbG9ubHkpXFxcXFxcXFxiKD86XFxcXFxcXFx7W159XSpcXFxcXFxcXH0pP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/OlxcXFxcXFxcYltBLVpdKzp8QFthLXpfXSs6KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZ3RrZG9jXFxcIn1dfSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5kb2N1bWVudGF0aW9uLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86Y2FsbGVyZ3JhcGh8Y2FsbGdyYXBofGVsc2V8ZW5kaWZ8ZlxcXFxcXFxcJHxmXFxcXFxcXFxbfGZcXFxcXFxcXF18aGlkZWNhbGxlcmdyYXBofGhpZGVjYWxsZ3JhcGh8aGlkZXJlZmJ5fGhpZGVyZWZzfGhpZGVpbml0aWFsaXplcnxodG1saW5jbHVkZXxufG5vc3ViZ3JvdXBpbmd8cHJpdmF0ZXxwcml2YXRlc2VjdGlvbnxwcm90ZWN0ZWR8cHJvdGVjdGVkc2VjdGlvbnxwdWJsaWN8cHVibGljc2VjdGlvbnxwdXJlfHNob3dpbml0aWFsaXplcnxzaG93cmVmYnl8c2hvd3JlZnN8dGFibGVvZmNvbnRlbnRzfFxcXFxcXFxcJHxcXFxcXFxcXCN8PHw+fCV8XFxcXFxcXCJ8XFxcXFxcXFwufD18Ojp8XFxcXFxcXFx8fFxcXFxcXFxcLVxcXFxcXFxcLXxcXFxcXFxcXC1cXFxcXFxcXC1cXFxcXFxcXC0pXFxcXFxcXFxiKD86XFxcXFxcXFx7W159XSpcXFxcXFxcXH0pP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5pdGFsaWMuZG94eWdlbi5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YXxlbXxlKSlcXFxcXFxcXHMrKFxcXFxcXFxcUyspXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5ib2xkLmRveHlnZW4uY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXWIpXFxcXFxcXFxzKyhcXFxcXFxcXFMrKVxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtYXJrdXAuaW5saW5lLnJhdy5zdHJpbmcuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXSg/OmN8cCkpXFxcXFxcXFxzKyhcXFxcXFxcXFMrKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YXxhbmNob3J8YnxjfGNpdGV8Y29weWJyaWVmfGNvcHlkZXRhaWx8Y29weWRvY3xkZWZ8ZGlyfGRvbnRpbmNsdWRlfGV8ZW18ZW1vaml8ZW51bXxleGFtcGxlfGV4dGVuZHN8ZmlsZXxpZGxleGNlcHR8aW1wbGVtZW50c3xpbmNsdWRlfGluY2x1ZGVkb2N8aW5jbHVkZWxpbmVub3xsYXRleGluY2x1ZGV8bGlua3xtZW1iZXJvZnxuYW1lc3BhY2V8cHxwYWNrYWdlfHJlZnxyZWZpdGVtfHJlbGF0ZWR8cmVsYXRlc3xyZWxhdGVkYWxzb3xyZWxhdGVzYWxzb3x2ZXJiaW5jbHVkZSlcXFxcXFxcXGIoPzpcXFxcXFxcXHtbXn1dKlxcXFxcXFxcfSk/XFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXSg/OmFkZGluZGV4fGFkZHRvZ3JvdXB8Y2F0ZWdvcnl8Y2xhc3N8ZGVmZ3JvdXB8ZGlhZmlsZXxkb3RmaWxlfGVsc2VpZnxmbnxoZWFkZXJmaWxlfGlmfGlmbm90fGltYWdlfGluZ3JvdXB8aW50ZXJmYWNlfGxpbmV8bWFpbnBhZ2V8bXNjZmlsZXxuYW1lfG92ZXJsb2FkfHBhZ2V8cHJvcGVydHl8cHJvdG9jb2x8c2VjdGlvbnxza2lwfHNraXBsaW5lfHNuaXBwZXR8c25pcHBldGRvY3xzbmlwcGV0bGluZW5vfHN0cnVjdHxzdWJwYWdlfHN1YnNlY3Rpb258c3Vic3Vic2VjdGlvbnx0eXBlZGVmfHVuaW9ufHVudGlsfHZoZGxmbG93fHdlYWtncm91cClcXFxcXFxcXGIoPzpcXFxcXFxcXHtbXn1dKlxcXFxcXFxcfSk/XFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJpbnxvdXRcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5wYXJhbWV0ZXIuZGlyZWN0aW9uLiQwLmNcXFwifV19LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXXBhcmFtKSg/OlxcXFxcXFxccypcXFxcXFxcXFsoKD86LD9cXFxcXFxcXHMqKD86aW58b3V0KVxcXFxcXFxccyopKylcXFxcXFxcXF0pP1xcXFxcXFxccysoXFxcXFxcXFxiXFxcXFxcXFx3K1xcXFxcXFxcYilcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXSg/OmFyZ3xhdHRlbnRpb258YXV0aG9yfGF1dGhvcnN8YnJpZWZ8YnVnfGNvcHlyaWdodHxkYXRlfGRlcHJlY2F0ZWR8ZGV0YWlsc3xleGNlcHRpb258aW52YXJpYW50fGxpfG5vdGV8cGFyfHBhcmFncmFwaHxwYXJhbXxwb3N0fHByZXxyZW1hcmt8cmVtYXJrc3xyZXN1bHR8cmV0dXJufHJldHVybnN8cmV0dmFsfHNhfHNlZXxzaG9ydHxzaW5jZXx0ZXN0fHRocm93fHRvZG98dHBhcmFtfHZlcnNpb258d2FybmluZ3x4cmVmaXRlbSlcXFxcXFxcXGIoPzpcXFxcXFxcXHtbXn1dKlxcXFxcXFxcfSk/XFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVtcXFxcXFxcXHMqIVxcXFxcXFxcL10pW1xcXFxcXFxcXFxcXFxcXFxAXSg/OmNvZGV8Y29uZHxkb2Nib29rb25seXxkb3R8aHRtbG9ubHl8aW50ZXJuYWx8bGF0ZXhvbmx5fGxpbmt8bWFub25seXxtc2N8cGFyYmxvY2t8cnRmb25seXxzZWNyZWZsaXN0fHVtbHx2ZXJiYXRpbXx4bWxvbmx5fGVuZGNvZGV8ZW5kY29uZHxlbmRkb2Nib29rb25seXxlbmRkb3R8ZW5kaHRtbG9ubHl8ZW5kaW50ZXJuYWx8ZW5kbGF0ZXhvbmx5fGVuZGxpbmt8ZW5kbWFub25seXxlbmRtc2N8ZW5kcGFyYmxvY2t8ZW5kcnRmb25seXxlbmRzZWNyZWZsaXN0fGVuZHVtbHxlbmR2ZXJiYXRpbXxlbmR4bWxvbmx5KVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzpcXFxcXFxcXGJbQS1aXSs6fEBbYS16X10rOilcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmd0a2RvY1xcXCJ9XX0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuZG9jdW1lbnRhdGlvbi5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXC9cXFxcXFxcXCpbISpdKyg/PVxcXFxcXFxccykpKC4rKShbISpdKlxcXFxcXFxcKlxcXFxcXFxcLylcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5kb2N1bWVudGF0aW9uLmNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKCg/PlxcXFxcXFxccyopXFxcXFxcXFwvXFxcXFxcXFwqWyEqXSsoPzooPzpcXFxcXFxcXG58JCl8KD89XFxcXFxcXFxzKSkpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuYmVnaW4uZG9jdW1lbnRhdGlvbi5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoWyEqXSpcXFxcXFxcXCpcXFxcXFxcXC8pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5kb2N1bWVudGF0aW9uLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmRvY3VtZW50YXRpb24uY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzpjYWxsZXJncmFwaHxjYWxsZ3JhcGh8ZWxzZXxlbmRpZnxmXFxcXFxcXFwkfGZcXFxcXFxcXFt8ZlxcXFxcXFxcXXxoaWRlY2FsbGVyZ3JhcGh8aGlkZWNhbGxncmFwaHxoaWRlcmVmYnl8aGlkZXJlZnN8aGlkZWluaXRpYWxpemVyfGh0bWxpbmNsdWRlfG58bm9zdWJncm91cGluZ3xwcml2YXRlfHByaXZhdGVzZWN0aW9ufHByb3RlY3RlZHxwcm90ZWN0ZWRzZWN0aW9ufHB1YmxpY3xwdWJsaWNzZWN0aW9ufHB1cmV8c2hvd2luaXRpYWxpemVyfHNob3dyZWZieXxzaG93cmVmc3x0YWJsZW9mY29udGVudHN8XFxcXFxcXFwkfFxcXFxcXFxcI3w8fD58JXxcXFxcXFxcInxcXFxcXFxcXC58PXw6OnxcXFxcXFxcXHx8XFxcXFxcXFwtXFxcXFxcXFwtfFxcXFxcXFxcLVxcXFxcXFxcLVxcXFxcXFxcLSlcXFxcXFxcXGIoPzpcXFxcXFxcXHtbXn1dKlxcXFxcXFxcfSk/XFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLml0YWxpYy5kb3h5Z2VuLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKCg/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzphfGVtfGUpKVxcXFxcXFxccysoXFxcXFxcXFxTKylcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5jbGFzcy5kb3h5Z2VuLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWFya3VwLmJvbGQuZG94eWdlbi5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdYilcXFxcXFxcXHMrKFxcXFxcXFxcUyspXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1hcmt1cC5pbmxpbmUucmF3LnN0cmluZy5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86Y3xwKSlcXFxcXFxcXHMrKFxcXFxcXFxcUyspXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bXFxcXFxcXFxzKiFcXFxcXFxcXC9dKVtcXFxcXFxcXFxcXFxcXFxcQF0oPzphfGFuY2hvcnxifGN8Y2l0ZXxjb3B5YnJpZWZ8Y29weWRldGFpbHxjb3B5ZG9jfGRlZnxkaXJ8ZG9udGluY2x1ZGV8ZXxlbXxlbW9qaXxlbnVtfGV4YW1wbGV8ZXh0ZW5kc3xmaWxlfGlkbGV4Y2VwdHxpbXBsZW1lbnRzfGluY2x1ZGV8aW5jbHVkZWRvY3xpbmNsdWRlbGluZW5vfGxhdGV4aW5jbHVkZXxsaW5rfG1lbWJlcm9mfG5hbWVzcGFjZXxwfHBhY2thZ2V8cmVmfHJlZml0ZW18cmVsYXRlZHxyZWxhdGVzfHJlbGF0ZWRhbHNvfHJlbGF0ZXNhbHNvfHZlcmJpbmNsdWRlKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YWRkaW5kZXh8YWRkdG9ncm91cHxjYXRlZ29yeXxjbGFzc3xkZWZncm91cHxkaWFmaWxlfGRvdGZpbGV8ZWxzZWlmfGZufGhlYWRlcmZpbGV8aWZ8aWZub3R8aW1hZ2V8aW5ncm91cHxpbnRlcmZhY2V8bGluZXxtYWlucGFnZXxtc2NmaWxlfG5hbWV8b3ZlcmxvYWR8cGFnZXxwcm9wZXJ0eXxwcm90b2NvbHxzZWN0aW9ufHNraXB8c2tpcGxpbmV8c25pcHBldHxzbmlwcGV0ZG9jfHNuaXBwZXRsaW5lbm98c3RydWN0fHN1YnBhZ2V8c3Vic2VjdGlvbnxzdWJzdWJzZWN0aW9ufHR5cGVkZWZ8dW5pb258dW50aWx8dmhkbGZsb3d8d2Vha2dyb3VwKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcImlufG91dFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnBhcmFtZXRlci5kaXJlY3Rpb24uJDAuY1xcXCJ9XX0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdcGFyYW0pKD86XFxcXFxcXFxzKlxcXFxcXFxcWygoPzosP1xcXFxcXFxccyooPzppbnxvdXQpXFxcXFxcXFxzKikrKVxcXFxcXFxcXSk/XFxcXFxcXFxzKyhcXFxcXFxcXGJcXFxcXFxcXHcrXFxcXFxcXFxiKVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86YXJnfGF0dGVudGlvbnxhdXRob3J8YXV0aG9yc3xicmllZnxidWd8Y29weXJpZ2h0fGRhdGV8ZGVwcmVjYXRlZHxkZXRhaWxzfGV4Y2VwdGlvbnxpbnZhcmlhbnR8bGl8bm90ZXxwYXJ8cGFyYWdyYXBofHBhcmFtfHBvc3R8cHJlfHJlbWFya3xyZW1hcmtzfHJlc3VsdHxyZXR1cm58cmV0dXJuc3xyZXR2YWx8c2F8c2VlfHNob3J0fHNpbmNlfHRlc3R8dGhyb3d8dG9kb3x0cGFyYW18dmVyc2lvbnx3YXJuaW5nfHhyZWZpdGVtKVxcXFxcXFxcYig/OlxcXFxcXFxce1tefV0qXFxcXFxcXFx9KT9cXFwiLFxcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmNsYXNzLmRveHlnZW4uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoPzw9W1xcXFxcXFxccyohXFxcXFxcXFwvXSlbXFxcXFxcXFxcXFxcXFxcXEBdKD86Y29kZXxjb25kfGRvY2Jvb2tvbmx5fGRvdHxodG1sb25seXxpbnRlcm5hbHxsYXRleG9ubHl8bGlua3xtYW5vbmx5fG1zY3xwYXJibG9ja3xydGZvbmx5fHNlY3JlZmxpc3R8dW1sfHZlcmJhdGltfHhtbG9ubHl8ZW5kY29kZXxlbmRjb25kfGVuZGRvY2Jvb2tvbmx5fGVuZGRvdHxlbmRodG1sb25seXxlbmRpbnRlcm5hbHxlbmRsYXRleG9ubHl8ZW5kbGlua3xlbmRtYW5vbmx5fGVuZG1zY3xlbmRwYXJibG9ja3xlbmRydGZvbmx5fGVuZHNlY3JlZmxpc3R8ZW5kdW1sfGVuZHZlcmJhdGltfGVuZHhtbG9ubHkpXFxcXFxcXFxiKD86XFxcXFxcXFx7W159XSpcXFxcXFxcXH0pP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZG94eWdlbi5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/OlxcXFxcXFxcYltBLVpdKzp8QFthLXpfXSs6KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuY2xhc3MuZ3RrZG9jXFxcIn1dfSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEudG9jLWxpc3QuYmFubmVyLmJsb2NrLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxcL1xcXFxcXFxcKiA9KFxcXFxcXFxccyouKj8pXFxcXFxcXFxzKj0gXFxcXFxcXFwqXFxcXFxcXFwvJFxcXFxcXFxcbj9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5iYW5uZXIuY1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFwvXFxcXFxcXFwqKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCpcXFxcXFxcXC8pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnRvYy1saXN0LmJhbm5lci5saW5lLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxcL1xcXFxcXFxcLyA9KFxcXFxcXFxccyouKj8pXFxcXFxcXFxzKj0kXFxcXFxcXFxuP1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuYmFubmVyLmNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKCg/Ol5bIFxcXFxcXFxcdF0rKT8pKD89XFxcXFxcXFwvXFxcXFxcXFwvKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ud2hpdGVzcGFjZS5jb21tZW50LmxlYWRpbmcuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD8hXFxcXFxcXFxHKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXC9cXFxcXFxcXC8pXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuZG91YmxlLXNsYXNoLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvbl9jaGFyYWN0ZXJcXFwifV19XX1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2tfY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbW1lbnRcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja19jb21tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29tbWVudFxcXCJ9XX0sXFxcImRlZmF1bHRfc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKCg/Pig/Oig/Oig/Pig/PCFcXFxcXFxcXHMpXFxcXFxcXFxzKyl8KFxcXFxcXFxcL1xcXFxcXFxcKikoKD8+KD86W15cXFxcXFxcXCpdfCg/PlxcXFxcXFxcKispW15cXFxcXFxcXC9dKSopKCg/PlxcXFxcXFxcKispXFxcXFxcXFwvKSkpK3woPzooPzooPzooPzpcXFxcXFxcXGJ8KD88PVxcXFxcXFxcVykpfCg/PVxcXFxcXFxcVykpfFxcXFxcXFxcQSl8XFxcXFxcXFxaKSkpKSgoPzwhXFxcXFxcXFx3KWRlZmF1bHQoPyFcXFxcXFxcXHcpKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbmxpbmVfY29tbWVudFxcXCJ9XX0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuYmVnaW4uY1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifSxcXFwiNFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXC9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn1dfSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRlZmF1bHQuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKDopXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbG9uLmNhc2UuZGVmYXVsdC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5jb25kaXRpb25hbC5jYXNlLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNldmFsdWF0aW9uX2NvbnRleHRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY19jb25kaXRpb25hbF9jb250ZXh0XFxcIn1dfSxcXFwiZGlzYWJsZWRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKiNcXFxcXFxcXHMqaWYobj9kZWYpP1xcXFxcXFxcYi4qJFxcXCIsXFxcImVuZFxcXCI6XFxcIl5cXFxcXFxcXHMqI1xcXFxcXFxccyplbmRpZlxcXFxcXFxcYlxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rpc2FibGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYS1tYXJrXFxcIn1dfSxcXFwiZXZhbHVhdGlvbl9jb250ZXh0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWNhbGwtaW5uYXJkc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSxcXFwiZnVuY3Rpb24tY2FsbC1pbm5hcmRzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0b3JhZ2VfdHlwZXNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbWV0aG9kX2FjY2Vzc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNtZW1iZXJfYWNjZXNzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdG9yc1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoPyEoPzp3aGlsZXxmb3J8ZG98aWZ8ZWxzZXxzd2l0Y2h8Y2F0Y2h8ZW51bWVyYXRlfHJldHVybnx0eXBlaWR8YWxpZ25vZnxhbGlnbmFzfHNpemVvZnxbY3JdP2l0ZXJhdGV8YW5kfGFuZF9lcXxiaXRhbmR8Yml0b3J8Y29tcGx8bm90fG5vdF9lcXxvcnxvcl9lcXx0eXBlaWR8eG9yfHhvcl9lcXxhbGlnbm9mfGFsaWduYXMpXFxcXFxcXFxzKlxcXFxcXFxcKCkoKD86W0EtWmEtel9dW0EtWmEtejAtOV9dKit8OjopKyt8KD86KD88PW9wZXJhdG9yKSg/OlstKiY8Pj0rIV0rfFxcXFxcXFxcKFxcXFxcXFxcKXxcXFxcXFxcXFtcXFxcXFxcXF0pKSlcXFxcXFxcXHMqKFxcXFxcXFxcKClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5hcmd1bWVudHMuYmVnaW4uYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmFyZ3VtZW50cy5lbmQuYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jYWxsLWlubmFyZHNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXChcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmJlZ2luLmJyYWNrZXQucm91bmQuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuZW5kLmJyYWNrZXQucm91bmQuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZnVuY3Rpb24tY2FsbC1pbm5hcmRzXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2tfaW5uYXJkc1xcXCJ9XX0sXFxcImZ1bmN0aW9uLWlubmFyZHNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RvcmFnZV90eXBlc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRvcnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFyYXJnX2VsbGlwc2VzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/ISg/OndoaWxlfGZvcnxkb3xpZnxlbHNlfHN3aXRjaHxjYXRjaHxlbnVtZXJhdGV8cmV0dXJufHR5cGVpZHxhbGlnbm9mfGFsaWduYXN8c2l6ZW9mfFtjcl0/aXRlcmF0ZXxhbmR8YW5kX2VxfGJpdGFuZHxiaXRvcnxjb21wbHxub3R8bm90X2VxfG9yfG9yX2VxfHR5cGVpZHx4b3J8eG9yX2VxfGFsaWdub2Z8YWxpZ25hcylcXFxcXFxcXHMqXFxcXFxcXFwoKSgoPzpbQS1aYS16X11bQS1aYS16MC05X10qK3w6OikrK3woPzooPzw9b3BlcmF0b3IpKD86Wy0qJjw+PSshXSt8XFxcXFxcXFwoXFxcXFxcXFwpfFxcXFxcXFxcW1xcXFxcXFxcXSkpKVxcXFxcXFxccyooXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmFtZXRlcnMuYmVnaW4uYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmFtZXRlcnMuZW5kLmJyYWNrZXQucm91bmQuY1xcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuZnVuY3Rpb24uZGVmaW5pdGlvbi5wYXJhbWV0ZXJzLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9iYWJseV9hX3BhcmFtZXRlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1pbm5hcmRzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5iZWdpbi5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmVuZC5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWlubmFyZHNcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSxcXFwiaW5saW5lX2NvbW1lbnRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0sXFxcIjNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcXFxcXFwvXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXC9cXFxcXFxcXCopKCg/Pig/OlteXFxcXFxcXFwqXXwoPz5cXFxcXFxcXCorKVteXFxcXFxcXFwvXSkqKSgoPz5cXFxcXFxcXCorKVxcXFxcXFxcLykpXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0sXFxcIjNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcXFxcXFwvXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXC9cXFxcXFxcXCopKCg/OlteXFxcXFxcXFwqXXwoPzpcXFxcXFxcXCopKytbXlxcXFxcXFxcL10pKisoKD86XFxcXFxcXFwqKSsrXFxcXFxcXFwvKSlcXFwifV19LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9LFxcXCIzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifV19fSxcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFwvXFxcXFxcXFwqKSgoPzpbXlxcXFxcXFxcKl18KD86XFxcXFxcXFwqKSsrW15cXFxcXFxcXC9dKSorKCg/OlxcXFxcXFxcKikrK1xcXFxcXFxcLykpXFxcIn1dfSxcXFwibGluZV9jb21tZW50XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyorKFxcXFxcXFxcL1xcXFxcXFxcLylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPzw9XFxcXFxcXFxuKSg/PCFcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxuKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7fSxcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5kb3VibGUtc2xhc2guY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyorKFxcXFxcXFxcL1xcXFxcXFxcLylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPzw9XFxcXFxcXFxuKSg/PCFcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxuKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7fSxcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQubGluZS5kb3VibGUtc2xhc2guY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2xpbmVfY29udGludWF0aW9uX2NoYXJhY3RlclxcXCJ9XX1dfSxcXFwibGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmxpbmUtY29udGludWF0aW9uLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxcXFxcXFxcXFwpXFxcXFxcXFxuXFxcIn1dfSxcXFwibWVtYmVyX2FjY2Vzc1xcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5vYmplY3QuYWNjZXNzLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmRvdC1hY2Nlc3MuY1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IucG9pbnRlci1hY2Nlc3MuY1xcXCJ9LFxcXCI0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI21lbWJlcl9hY2Nlc3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbWV0aG9kX2FjY2Vzc1xcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIub2JqZWN0LmFjY2Vzcy5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kb3QtYWNjZXNzLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLnBvaW50ZXItYWNjZXNzLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKCg/OlthLXpBLVpfXVxcXFxcXFxcdyp8KD88PVxcXFxcXFxcXXxcXFxcXFxcXCkpKVxcXFxcXFxccyopKD86KCg/OlxcXFxcXFxcLlxcXFxcXFxcKnxcXFxcXFxcXC4pKXwoKD86LT5cXFxcXFxcXCp8LT4pKSlcXFwifV19LFxcXCI1XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5tZW1iZXIuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKD86W2EtekEtWl9dXFxcXFxcXFx3KnwoPzw9XFxcXFxcXFxdfFxcXFxcXFxcKSkpXFxcXFxcXFxzKikoPzooKD86XFxcXFxcXFwuXFxcXFxcXFwqfFxcXFxcXFxcLikpfCgoPzotPlxcXFxcXFxcKnwtPikpKSgoPzpbYS16QS1aX11cXFxcXFxcXHcqXFxcXFxcXFxzKig/Oig/Oig/OlxcXFxcXFxcLlxcXFxcXFxcKnxcXFxcXFxcXC4pKXwoPzooPzotPlxcXFxcXFxcKnwtPikpKVxcXFxcXFxccyopKilcXFxcXFxcXHMqKFxcXFxcXFxcYig/ISg/OmF0b21pY191aW50X2xlYXN0NjRfdHxhdG9taWNfdWludF9sZWFzdDE2X3R8YXRvbWljX3VpbnRfbGVhc3QzMl90fGF0b21pY191aW50X2xlYXN0OF90fGF0b21pY19pbnRfbGVhc3QxNl90fGF0b21pY191aW50X2Zhc3Q2NF90fGF0b21pY191aW50X2Zhc3QzMl90fGF0b21pY19pbnRfbGVhc3Q2NF90fGF0b21pY19pbnRfbGVhc3QzMl90fHB0aHJlYWRfcndsb2NrYXR0cl90fGF0b21pY191aW50X2Zhc3QxNl90fHB0aHJlYWRfbXV0ZXhhdHRyX3R8YXRvbWljX2ludF9mYXN0MTZfdHxhdG9taWNfdWludF9mYXN0OF90fGF0b21pY19pbnRfZmFzdDY0X3R8YXRvbWljX2ludF9sZWFzdDhfdHxhdG9taWNfaW50X2Zhc3QzMl90fGF0b21pY19pbnRfZmFzdDhfdHxwdGhyZWFkX2NvbmRhdHRyX3R8YXRvbWljX3VpbnRwdHJfdHxhdG9taWNfcHRyZGlmZl90fHB0aHJlYWRfcndsb2NrX3R8YXRvbWljX3VpbnRtYXhfdHxwdGhyZWFkX211dGV4X3R8YXRvbWljX2ludG1heF90fGF0b21pY19pbnRwdHJfdHxhdG9taWNfY2hhcjMyX3R8YXRvbWljX2NoYXIxNl90fHB0aHJlYWRfYXR0cl90fGF0b21pY193Y2hhcl90fHVpbnRfbGVhc3Q2NF90fHVpbnRfbGVhc3QzMl90fHVpbnRfbGVhc3QxNl90fHB0aHJlYWRfY29uZF90fHB0aHJlYWRfb25jZV90fHVpbnRfZmFzdDY0X3R8dWludF9mYXN0MTZfdHxhdG9taWNfc2l6ZV90fHVpbnRfbGVhc3Q4X3R8aW50X2xlYXN0NjRfdHxpbnRfbGVhc3QzMl90fGludF9sZWFzdDE2X3R8cHRocmVhZF9rZXlfdHxhdG9taWNfdWxsb25nfGF0b21pY191c2hvcnR8dWludF9mYXN0MzJfdHxhdG9taWNfc2NoYXJ8YXRvbWljX3Nob3J0fHVpbnRfZmFzdDhfdHxpbnRfZmFzdDY0X3R8aW50X2Zhc3QzMl90fGludF9mYXN0MTZfdHxhdG9taWNfdWxvbmd8YXRvbWljX2xsb25nfGludF9sZWFzdDhfdHxhdG9taWNfdWNoYXJ8bWVtb3J5X29yZGVyfHN1c2Vjb25kc190fGludF9mYXN0OF90fGF0b21pY19ib29sfGF0b21pY19jaGFyfGF0b21pY191aW50fGF0b21pY19sb25nfGF0b21pY19pbnR8dXNlY29uZHNfdHxfSW1hZ2luYXJ5fGJsa3NpemVfdHxwdGhyZWFkX3R8aW5fYWRkcl90fHVpbnRwdHJfdHxpbl9wb3J0X3R8dWludG1heF90fHVpbnRtYXhfdHxibGtjbnRfdHx1aW50MTZfdHx1bnNpZ25lZHxfQ29tcGxleHx1aW50MzJfdHxpbnRwdHJfdHxpbnRtYXhfdHxpbnRtYXhfdHx1aW50NjRfdHx1X3F1YWRfdHxpbnQ2NF90fGludDMyX3R8c3NpemVfdHxjYWRkcl90fGNsb2NrX3R8dWludDhfdHx1X3Nob3J0fHN3YmxrX3R8c2Vnc3pfdHxpbnQxNl90fGZpeHB0X3R8ZGFkZHJfdHxubGlua190fHFhZGRyX3R8c2l6ZV90fHRpbWVfdHxtb2RlX3R8c2lnbmVkfHF1YWRfdHx1c2hvcnR8dV9sb25nfHVfY2hhcnxkb3VibGV8aW50OF90fGlub190fHVpZF90fHBpZF90fF9Cb29sfGZsb2F0fGRldl90fGRpdl90fHNob3J0fGdpZF90fG9mZl90fHVfaW50fGtleV90fGlkX3R8dWludHxsb25nfHZvaWR8Y2hhcnxib29sfGlkX3R8aW50KVxcXFxcXFxcYilbYS16QS1aX11cXFxcXFxcXHcqXFxcXFxcXFxiKD8hXFxcXFxcXFwoKSlcXFwifSxcXFwibWV0aG9kX2FjY2Vzc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIigoPzpbYS16QS1aX11cXFxcXFxcXHcqfCg/PD1cXFxcXFxcXF18XFxcXFxcXFwpKSlcXFxcXFxcXHMqKSg/OigoPzpcXFxcXFxcXC5cXFxcXFxcXCp8XFxcXFxcXFwuKSl8KCg/Oi0+XFxcXFxcXFwqfC0+KSkpKCg/OlthLXpBLVpfXVxcXFxcXFxcdypcXFxcXFxcXHMqKD86KD86KD86XFxcXFxcXFwuXFxcXFxcXFwqfFxcXFxcXFxcLikpfCg/Oig/Oi0+XFxcXFxcXFwqfC0+KSkpXFxcXFxcXFxzKikqKVxcXFxcXFxccyooW2EtekEtWl9dXFxcXFxcXFx3KikoXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIub2JqZWN0LmFjY2Vzcy5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kb3QtYWNjZXNzLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLnBvaW50ZXItYWNjZXNzLmNcXFwifSxcXFwiNFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNtZW1iZXJfYWNjZXNzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21ldGhvZF9hY2Nlc3NcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm90aGVyLm9iamVjdC5hY2Nlc3MuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuZG90LWFjY2Vzcy5jXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5wb2ludGVyLWFjY2Vzcy5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoPzpbYS16QS1aX11cXFxcXFxcXHcqfCg/PD1cXFxcXFxcXF18XFxcXFxcXFwpKSlcXFxcXFxcXHMqKSg/OigoPzpcXFxcXFxcXC5cXFxcXFxcXCp8XFxcXFxcXFwuKSl8KCg/Oi0+XFxcXFxcXFwqfC0+KSkpXFxcIn1dfSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24ubWVtYmVyLmNcXFwifSxcXFwiNlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5hcmd1bWVudHMuYmVnaW4uYnJhY2tldC5yb3VuZC5mdW5jdGlvbi5tZW1iZXIuY1xcXCJ9fSxcXFwiY29udGVudE5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLWNhbGwubWVtYmVyLmNcXFwiLFxcXCJlbmRcXFwiOlxcXCIoXFxcXFxcXFwpKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYXJndW1lbnRzLmVuZC5icmFja2V0LnJvdW5kLmZ1bmN0aW9uLm1lbWJlci5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jYWxsLWlubmFyZHNcXFwifV19LFxcXCJudW1iZXJzXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiKD89LilcXFwiLFxcXCJlbmRcXFwiOlxcXCIkXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudW5pdC5oZXhhZGVjaW1hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuaGV4YWRlY2ltYWwuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb25zdGFudC5udW1lcmljXFxcIn1dfSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5oZXhhZGVjaW1hbC5jXFxcIn0sXFxcIjVcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuaGV4YWRlY2ltYWwuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb25zdGFudC5udW1lcmljXFxcIn1dfSxcXFwiNlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifSxcXFwiOFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci51bml0LmV4cG9uZW50LmhleGFkZWNpbWFsLmNcXFwifSxcXFwiOVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5wbHVzLmV4cG9uZW50LmhleGFkZWNpbWFsLmNcXFwifSxcXFwiMTBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IubWludXMuZXhwb25lbnQuaGV4YWRlY2ltYWwuY1xcXCJ9LFxcXCIxMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5leHBvbmVudC5oZXhhZGVjaW1hbC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifV19LFxcXCIxMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci51bml0LnN1ZmZpeC5mbG9hdGluZy1wb2ludC5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXEcwW3hYXSkoWzAtOWEtZkEtRl0oPzpbMC05YS1mQS1GXXwoKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopPygoPzooPzw9WzAtOWEtZkEtRl0pXFxcXFxcXFwufFxcXFxcXFxcLig/PVswLTlhLWZBLUZdKSkpKFswLTlhLWZBLUZdKD86WzAtOWEtZkEtRl18KCg/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pKSkqKT8oKD88IScpKFtwUF0pKFxcXFxcXFxcKz8pKFxcXFxcXFxcLT8pKCg/OlswLTldKD86WzAtOV18KD86KD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopKSk/KFtsTGZGXSg/IVxcXFxcXFxcdykpPyRcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuZGVjaW1hbC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifV19LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCI0XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmRlY2ltYWwucG9pbnQuY1xcXCJ9LFxcXCI1XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmRlY2ltYWwuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb25zdGFudC5udW1lcmljXFxcIn1dfSxcXFwiNlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifSxcXFwiOFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci51bml0LmV4cG9uZW50LmRlY2ltYWwuY1xcXCJ9LFxcXCI5XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnBsdXMuZXhwb25lbnQuZGVjaW1hbC5jXFxcIn0sXFxcIjEwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLm1pbnVzLmV4cG9uZW50LmRlY2ltYWwuY1xcXCJ9LFxcXCIxMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5leHBvbmVudC5kZWNpbWFsLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzw9WzAtOWEtZkEtRl0pJyg/PVswLTlhLWZBLUZdKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9XX0sXFxcIjEyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnVuaXQuc3VmZml4LmZsb2F0aW5nLXBvaW50LmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxcRyg/PVswLTkuXSkoPyEwW3hYYkJdKSkoWzAtOV0oPzpbMC05XXwoKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopPygoPzooPzw9WzAtOV0pXFxcXFxcXFwufFxcXFxcXFxcLig/PVswLTldKSkpKFswLTldKD86WzAtOV18KCg/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pKSkqKT8oKD88IScpKFtlRV0pKFxcXFxcXFxcKz8pKFxcXFxcXFxcLT8pKCg/OlswLTldKD86WzAtOV18KD86KD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopKSk/KFtsTGZGXSg/IVxcXFxcXFxcdykpPyRcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudW5pdC5iaW5hcnkuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmJpbmFyeS5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifV19LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCI0XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnVuaXQuc3VmZml4LmludGVnZXIuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxHMFtiQl0pKFswMV0oPzpbMDFdfCgoPzw9WzAtOWEtZkEtRl0pJyg/PVswLTlhLWZBLUZdKSkpKikoKD86KD86KD86KD86KD86W3VVXXxbdVVdbGw/KXxbdVVdTEw/KXxsbD9bdVVdPyl8TEw/W3VVXT8pfFtmRl0pKD8hXFxcXFxcXFx3KSk/JFxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci51bml0Lm9jdGFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5vY3RhbC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifV19LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9LFxcXCI0XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnVuaXQuc3VmZml4LmludGVnZXIuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxHMCkoKD86WzAtN118KCg/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pKSkrKSgoPzooPzooPzooPzooPzpbdVVdfFt1VV1sbD8pfFt1VV1MTD8pfGxsP1t1VV0/KXxMTD9bdVVdPyl8W2ZGXSkoPyFcXFxcXFxcXHcpKT8kXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnVuaXQuaGV4YWRlY2ltYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmhleGFkZWNpbWFsLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzw9WzAtOWEtZkEtRl0pJyg/PVswLTlhLWZBLUZdKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9XX0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb25zdGFudC5udW1lcmljXFxcIn0sXFxcIjVcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudW5pdC5leHBvbmVudC5oZXhhZGVjaW1hbC5jXFxcIn0sXFxcIjZcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IucGx1cy5leHBvbmVudC5oZXhhZGVjaW1hbC5jXFxcIn0sXFxcIjdcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IubWludXMuZXhwb25lbnQuaGV4YWRlY2ltYWwuY1xcXCJ9LFxcXCI4XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmV4cG9uZW50LmhleGFkZWNpbWFsLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzw9WzAtOWEtZkEtRl0pJyg/PVswLTlhLWZBLUZdKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZXBhcmF0b3IuY29uc3RhbnQubnVtZXJpY1xcXCJ9XX0sXFxcIjlcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudW5pdC5zdWZmaXguaW50ZWdlci5jXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIihcXFxcXFxcXEcwW3hYXSkoWzAtOWEtZkEtRl0oPzpbMC05YS1mQS1GXXwoKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopKCg/PCEnKShbcFBdKShcXFxcXFxcXCs/KShcXFxcXFxcXC0/KSgoPzpbMC05XSg/OlswLTldfCg/Oig/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pKSkqKSkpPygoPzooPzooPzooPzooPzpbdVVdfFt1VV1sbD8pfFt1VV1MTD8pfGxsP1t1VV0/KXxMTD9bdVVdPyl8W2ZGXSkoPyFcXFxcXFxcXHcpKT8kXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmRlY2ltYWwuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PD1bMC05YS1mQS1GXSknKD89WzAtOWEtZkEtRl0pXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5jb25zdGFudC5udW1lcmljXFxcIn1dfSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci51bml0LmV4cG9uZW50LmRlY2ltYWwuY1xcXCJ9LFxcXCI2XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnBsdXMuZXhwb25lbnQuZGVjaW1hbC5jXFxcIn0sXFxcIjdcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IubWludXMuZXhwb25lbnQuZGVjaW1hbC5jXFxcIn0sXFxcIjhcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuZXhwb25lbnQuZGVjaW1hbC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmNvbnN0YW50Lm51bWVyaWNcXFwifV19LFxcXCI5XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnVuaXQuc3VmZml4LmludGVnZXIuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxHKD89WzAtOS5dKSg/ITBbeFhiQl0pKShbMC05XSg/OlswLTldfCgoPzw9WzAtOWEtZkEtRl0pJyg/PVswLTlhLWZBLUZdKSkpKikoKD88IScpKFtlRV0pKFxcXFxcXFxcKz8pKFxcXFxcXFxcLT8pKCg/OlswLTldKD86WzAtOV18KD86KD88PVswLTlhLWZBLUZdKScoPz1bMC05YS1mQS1GXSkpKSopKSk/KCg/Oig/Oig/Oig/Oig/Olt1VV18W3VVXWxsPyl8W3VVXUxMPyl8bGw/W3VVXT8pfExMP1t1VV0/KXxbZkZdKSg/IVxcXFxcXFxcdykpPyRcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD86KD86WzAtOWEtekEtWl9cXFxcXFxcXC5dfCcpfCg/PD1bZUVwUF0pWystXSkrXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5jb25zdGFudC5udW1lcmljXFxcIn1dfV19fSxcXFwibWF0Y2hcXFwiOlxcXCIoPzwhXFxcXFxcXFx3KVxcXFxcXFxcLj9cXFxcXFxcXGQoPzooPzpbMC05YS16QS1aX1xcXFxcXFxcLl18Jyl8KD88PVtlRXBQXSlbKy1dKSpcXFwifSxcXFwib3BlcmF0b3JzXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIig/PCFbXFxcXFxcXFx3JF0pKHNpemVvZikoPyFbXFxcXFxcXFx3JF0pXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iuc2l6ZW9mLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiLS1cXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5kZWNyZW1lbnQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCtcXFxcXFxcXCtcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5pbmNyZW1lbnQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIlPXxcXFxcXFxcXCs9fC09fFxcXFxcXFxcKj18KD88IVxcXFxcXFxcKCkvPVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmFzc2lnbm1lbnQuY29tcG91bmQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCImPXxcXFxcXFxcXF49fDw8PXw+Pj18XFxcXFxcXFx8PVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmFzc2lnbm1lbnQuY29tcG91bmQuYml0d2lzZS5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIjw8fD4+XFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuYml0d2lzZS5zaGlmdC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIiE9fDw9fD49fD09fDx8PlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmNvbXBhcmlzb24uY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCImJnwhfFxcXFxcXFxcfFxcXFxcXFxcfFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmxvZ2ljYWwuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCImfFxcXFxcXFxcfHxcXFxcXFxcXF58flxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiPVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmFzc2lnbm1lbnQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIlfFxcXFxcXFxcKnwvfC18XFxcXFxcXFwrXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuY1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFw/KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci50ZXJuYXJ5LmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig6KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IudGVybmFyeS5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNmdW5jdGlvbi1jYWxsLWlubmFyZHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX1dfSxcXFwicGFyZW5zXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5iZWdpbi5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmVuZC5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnBhcmVucy5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0sXFxcInBhcmVucy1ibG9ja1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuYmVnaW4uYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5lbmQuYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wYXJlbnMuYmxvY2suY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD8tbWl4Oig/PCE6KTooPyE6KSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ucmFuZ2UtYmFzZWQuY1xcXCJ9XX0sXFxcInByYWdtYS1tYXJrXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLnByYWdtYS5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUucHJhZ21hLnByYWdtYS1tYXJrLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9LFxcXCI0XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50YWcucHJhZ21hLW1hcmsuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJeXFxcXFxcXFxzKigoKCMpXFxcXFxcXFxzKnByYWdtYVxcXFxcXFxccyttYXJrKVxcXFxcXFxccysoLiopKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnNlY3Rpb24uY1xcXCJ9LFxcXCJwcmVkZWZpbmVkX21hY3Jvc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUub3RoZXIucHJlcHJvY2Vzc29yLm1hY3JvLnByZWRlZmluZWQuJDEuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoX19jcGx1c3BsdXN8X19EQVRFX198X19GSUxFX198X19MSU5FX198X19TVERDX198X19TVERDX0hPU1RFRF9ffF9fU1REQ19OT19DT01QTEVYX198X19TVERDX1ZFUlNJT05fX3xfX1NURENQUF9USFJFQURTX198X19USU1FX198TkRFQlVHfF9fT0JKQ19ffF9fQVNTRU1CTEVSX198X19BVE9NX198X19BVlhfX3xfX0FWWDJfX3xfQ0hBUl9VTlNJR05FRHxfX0NMUl9WRVJ8X0NPTlRST0xfRkxPV19HVUFSRHxfX0NPVU5URVJfX3xfX2NwbHVzcGx1c19jbGl8X19jcGx1c3BsdXNfd2lucnR8X0NQUFJUVEl8X0NQUFVOV0lORHxfREVCVUd8X0RMTHxfX0ZVTkNETkFNRV9ffF9fRlVOQ1NJR19ffF9fRlVOQ1RJT05fX3xfSU5URUdSQUxfTUFYX0JJVFN8X19JTlRFTExJU0VOU0VfX3xfSVNPX1ZPTEFUSUxFfF9LRVJORUxfTU9ERXxfTV9BTUQ2NHxfTV9BUk18X01fQVJNX0FSTVY3VkV8X01fQVJNX0ZQfF9NX0FSTTY0fF9NX0NFRXxfTV9DRUVfUFVSRXxfTV9DRUVfU0FGRXxfTV9GUF9FWENFUFR8X01fRlBfRkFTVHxfTV9GUF9QUkVDSVNFfF9NX0ZQX1NUUklDVHxfTV9JWDg2fF9NX0lYODZfRlB8X01fWDY0fF9NQU5BR0VEfF9NU0NfQlVJTER8X01TQ19FWFRFTlNJT05TfF9NU0NfRlVMTF9WRVJ8X01TQ19WRVJ8X01TVkNfTEFOR3xfX01TVkNfUlVOVElNRV9DSEVDS1N8X01UfF9OQVRJVkVfV0NIQVJfVF9ERUZJTkVEfF9PUEVOTVB8X1BSRUZBU1R8X19USU1FU1RBTVBfX3xfVkNfTk9fREVGQVVMVExJQnxfV0NIQVJfVF9ERUZJTkVEfF9XSU4zMnxfV0lONjR8X1dJTlJUX0RMTHxfQVRMX1ZFUnxfTUZDX1ZFUnxfX0dGT1JUUkFOX198X19HTlVDX198X19HTlVDX01JTk9SX198X19HTlVDX1BBVENITEVWRUxfX3xfX0dOVUdfX3xfX1NUUklDVF9BTlNJX198X19CQVNFX0ZJTEVfX3xfX0lOQ0xVREVfTEVWRUxfX3xfX0VMRl9ffF9fVkVSU0lPTl9ffF9fT1BUSU1JWkVfX3xfX09QVElNSVpFX1NJWkVfX3xfX05PX0lOTElORV9ffF9fR05VQ19TVERDX0lOTElORV9ffF9fQ0hBUl9VTlNJR05FRF9ffF9fV0NIQVJfVU5TSUdORURfX3xfX1JFR0lTVEVSX1BSRUZJWF9ffF9fUkVHSVNURVJfUFJFRklYX198X19TSVpFX1RZUEVfX3xfX1BUUkRJRkZfVFlQRV9ffF9fV0NIQVJfVFlQRV9ffF9fV0lOVF9UWVBFX198X19JTlRNQVhfVFlQRV9ffF9fVUlOVE1BWF9UWVBFX198X19TSUdfQVRPTUlDX1RZUEVfX3xfX0lOVDhfVFlQRV9ffF9fSU5UMTZfVFlQRV9ffF9fSU5UMzJfVFlQRV9ffF9fSU5UNjRfVFlQRV9ffF9fVUlOVDhfVFlQRV9ffF9fVUlOVDE2X1RZUEVfX3xfX1VJTlQzMl9UWVBFX198X19VSU5UNjRfVFlQRV9ffF9fSU5UX0xFQVNUOF9UWVBFX198X19JTlRfTEVBU1QxNl9UWVBFX198X19JTlRfTEVBU1QzMl9UWVBFX198X19JTlRfTEVBU1Q2NF9UWVBFX198X19VSU5UX0xFQVNUOF9UWVBFX198X19VSU5UX0xFQVNUMTZfVFlQRV9ffF9fVUlOVF9MRUFTVDMyX1RZUEVfX3xfX1VJTlRfTEVBU1Q2NF9UWVBFX198X19JTlRfRkFTVDhfVFlQRV9ffF9fSU5UX0ZBU1QxNl9UWVBFX198X19JTlRfRkFTVDMyX1RZUEVfX3xfX0lOVF9GQVNUNjRfVFlQRV9ffF9fVUlOVF9GQVNUOF9UWVBFX198X19VSU5UX0ZBU1QxNl9UWVBFX198X19VSU5UX0ZBU1QzMl9UWVBFX198X19VSU5UX0ZBU1Q2NF9UWVBFX198X19JTlRQVFJfVFlQRV9ffF9fVUlOVFBUUl9UWVBFX198X19DSEFSX0JJVF9ffF9fU0NIQVJfTUFYX198X19XQ0hBUl9NQVhfX3xfX1NIUlRfTUFYX198X19JTlRfTUFYX198X19MT05HX01BWF9ffF9fTE9OR19MT05HX01BWF9ffF9fV0lOVF9NQVhfX3xfX1NJWkVfTUFYX198X19QVFJESUZGX01BWF9ffF9fSU5UTUFYX01BWF9ffF9fVUlOVE1BWF9NQVhfX3xfX1NJR19BVE9NSUNfTUFYX198X19JTlQ4X01BWF9ffF9fSU5UMTZfTUFYX198X19JTlQzMl9NQVhfX3xfX0lOVDY0X01BWF9ffF9fVUlOVDhfTUFYX198X19VSU5UMTZfTUFYX198X19VSU5UMzJfTUFYX198X19VSU5UNjRfTUFYX198X19JTlRfTEVBU1Q4X01BWF9ffF9fSU5UX0xFQVNUMTZfTUFYX198X19JTlRfTEVBU1QzMl9NQVhfX3xfX0lOVF9MRUFTVDY0X01BWF9ffF9fVUlOVF9MRUFTVDhfTUFYX198X19VSU5UX0xFQVNUMTZfTUFYX198X19VSU5UX0xFQVNUMzJfTUFYX198X19VSU5UX0xFQVNUNjRfTUFYX198X19JTlRfRkFTVDhfTUFYX198X19JTlRfRkFTVDE2X01BWF9ffF9fSU5UX0ZBU1QzMl9NQVhfX3xfX0lOVF9GQVNUNjRfTUFYX198X19VSU5UX0ZBU1Q4X01BWF9ffF9fVUlOVF9GQVNUMTZfTUFYX198X19VSU5UX0ZBU1QzMl9NQVhfX3xfX1VJTlRfRkFTVDY0X01BWF9ffF9fSU5UUFRSX01BWF9ffF9fVUlOVFBUUl9NQVhfX3xfX1dDSEFSX01JTl9ffF9fV0lOVF9NSU5fX3xfX1NJR19BVE9NSUNfTUlOX198X19TQ0hBUl9XSURUSF9ffF9fU0hSVF9XSURUSF9ffF9fSU5UX1dJRFRIX198X19MT05HX1dJRFRIX198X19MT05HX0xPTkdfV0lEVEhfX3xfX1BUUkRJRkZfV0lEVEhfX3xfX1NJR19BVE9NSUNfV0lEVEhfX3xfX1NJWkVfV0lEVEhfX3xfX1dDSEFSX1dJRFRIX198X19XSU5UX1dJRFRIX198X19JTlRfTEVBU1Q4X1dJRFRIX198X19JTlRfTEVBU1QxNl9XSURUSF9ffF9fSU5UX0xFQVNUMzJfV0lEVEhfX3xfX0lOVF9MRUFTVDY0X1dJRFRIX198X19JTlRfRkFTVDhfV0lEVEhfX3xfX0lOVF9GQVNUMTZfV0lEVEhfX3xfX0lOVF9GQVNUMzJfV0lEVEhfX3xfX0lOVF9GQVNUNjRfV0lEVEhfX3xfX0lOVFBUUl9XSURUSF9ffF9fSU5UTUFYX1dJRFRIX198X19TSVpFT0ZfSU5UX198X19TSVpFT0ZfTE9OR19ffF9fU0laRU9GX0xPTkdfTE9OR19ffF9fU0laRU9GX1NIT1JUX198X19TSVpFT0ZfUE9JTlRFUl9ffF9fU0laRU9GX0ZMT0FUX198X19TSVpFT0ZfRE9VQkxFX198X19TSVpFT0ZfTE9OR19ET1VCTEVfX3xfX1NJWkVPRl9TSVpFX1RfX3xfX1NJWkVPRl9XQ0hBUl9UX198X19TSVpFT0ZfV0lOVF9UX198X19TSVpFT0ZfUFRSRElGRl9UX198X19CWVRFX09SREVSX198X19PUkRFUl9MSVRUTEVfRU5ESUFOX198X19PUkRFUl9CSUdfRU5ESUFOX198X19PUkRFUl9QRFBfRU5ESUFOX198X19GTE9BVF9XT1JEX09SREVSX198X19ERVBSRUNBVEVEfF9fRVhDRVBUSU9OU3xfX0dYWF9SVFRJfF9fVVNJTkdfU0pMSl9FWENFUFRJT05TX198X19HWFhfRVhQRVJJTUVOVEFMX0NYWDBYX198X19HWFhfV0VBS19ffF9fTkVYVF9SVU5USU1FX198X19MUDY0X198X0xQNjR8X19TU1BfX3xfX1NTUF9BTExfX3xfX1NTUF9TVFJPTkdfX3xfX1NTUF9FWFBMSUNJVF9ffF9fU0FOSVRJWkVfQUREUkVTU19ffF9fU0FOSVRJWkVfVEhSRUFEX198X19HQ0NfSEFWRV9TWU5DX0NPTVBBUkVfQU5EX1NXQVBfMXxfX0dDQ19IQVZFX1NZTkNfQ09NUEFSRV9BTkRfU1dBUF8yfF9fR0NDX0hBVkVfU1lOQ19DT01QQVJFX0FORF9TV0FQXzR8X19HQ0NfSEFWRV9TWU5DX0NPTVBBUkVfQU5EX1NXQVBfOHxfX0dDQ19IQVZFX1NZTkNfQ09NUEFSRV9BTkRfU1dBUF8xNnxfX0hBVkVfU1BFQ1VMQVRJT05fU0FGRV9WQUxVRXxfX0dDQ19IQVZFX0RXQVJGMl9DRklfQVNNfF9fRlBfRkFTVF9GTUF8X19GUF9GQVNUX0ZNQUZ8X19GUF9GQVNUX0ZNQUx8X19GUF9GQVNUX0ZNQUYxNnxfX0ZQX0ZBU1RfRk1BRjMyfF9fRlBfRkFTVF9GTUFGNjR8X19GUF9GQVNUX0ZNQUYxMjh8X19GUF9GQVNUX0ZNQUYzMlh8X19GUF9GQVNUX0ZNQUY2NFh8X19GUF9GQVNUX0ZNQUYxMjhYfF9fR0NDX0lFQ181NTl8X19HQ0NfSUVDXzU1OV9DT01QTEVYfF9fTk9fTUFUSF9FUlJOT19ffF9faGFzX2J1aWx0aW58X19oYXNfZmVhdHVyZXxfX2hhc19leHRlbnNpb258X19oYXNfY3BwX2F0dHJpYnV0ZXxfX2hhc19jX2F0dHJpYnV0ZXxfX2hhc19hdHRyaWJ1dGV8X19oYXNfZGVjbHNwZWNfYXR0cmlidXRlfF9faXNfaWRlbnRpZmllcnxfX2hhc19pbmNsdWRlfF9faGFzX2luY2x1ZGVfbmV4dHxfX2hhc193YXJuaW5nfF9fQkFTRV9GSUxFX198X19GSUxFX05BTUVfX3xfX2NsYW5nX198X19jbGFuZ19tYWpvcl9ffF9fY2xhbmdfbWlub3JfX3xfX2NsYW5nX3BhdGNobGV2ZWxfX3xfX2NsYW5nX3ZlcnNpb25fX3xfX2ZwMTZ8X0Zsb2F0MTYpXFxcXFxcXFxiXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYl9fKFtBLVpfXSspX19cXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUub3RoZXIucHJlcHJvY2Vzc29yLm1hY3JvLnByZWRlZmluZWQucHJvYmFibHkuJDEuY1xcXCJ9XX0sXFxcInByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyppZig/Om4/ZGVmKT9cXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZW5kaWZcXFxcXFxcXGIpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxHKD89LikoPyEvL3wvXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89Ly8pfCg/PS9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWwtbGluZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWVuYWJsZWQtZWxpZlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1lbmFibGVkLWVsc2VcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGlzYWJsZWQtZWxpZlxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZWxpZlxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0vLyl8KD89L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKXwoPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1saW5lXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJpbnZhbGlkLmlsbGVnYWwuc3RyYXktJDEuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCJeXFxcXFxcXFxzKiNcXFxcXFxcXHMqKGVsc2V8ZWxpZnxlbmRpZilcXFxcXFxcXGJcXFwifV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1ibG9ja1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqaWYoPzpuP2RlZik/XFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVuZGlmXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcRyg/PS4pKD8hLy98L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PS8vKXwoPz0vXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1lbmFibGVkLWVsaWYtYmxvY2tcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1lbHNlLWJsb2NrXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWRpc2FibGVkLWVsaWZcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsaWZcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89Ly8pfCg/PS9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWwtbGluZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifV19LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLnN0cmF5LSQxLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXlxcXFxcXFxccyojXFxcXFxcXFxzKihlbHNlfGVsaWZ8ZW5kaWYpXFxcXFxcXFxiXFxcIn1dfSxcXFwicHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWwtbGluZVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCIoPzpcXFxcXFxcXGJkZWZpbmVkXFxcXFxcXFxiXFxcXFxcXFxzKiQpfCg/OlxcXFxcXFxcYmRlZmluZWRcXFxcXFxcXGIoPz1cXFxcXFxcXHMqXFxcXFxcXFwoKlxcXFxcXFxccyooPzooPyFkZWZpbmVkXFxcXFxcXFxiKVthLXpBLVpfJF1bXFxcXFxcXFx3JF0qXFxcXFxcXFxiKVxcXFxcXFxccypcXFxcXFxcXCkqXFxcXFxcXFxzKig/OlxcXFxcXFxcbnwvL3wvXFxcXFxcXFwqfFxcXFxcXFxcP3xcXFxcXFxcXDp8JiZ8XFxcXFxcXFx8XFxcXFxcXFx8fFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSkpXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGJkZWZpbmVkXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC5tYWNyby1uYW1lLmNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNudW1iZXJzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcP1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci50ZXJuYXJ5LmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIjpcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnRlcm5hcnkuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWwtbGluZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdG9yc1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoTlVMTHx0cnVlfGZhbHNlfFRSVUV8RkFMU0UpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiW2EtekEtWl8kXVtcXFxcXFxcXHckXSpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24ucHJlcHJvY2Vzc29yLmNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuYmVnaW4uYnJhY2tldC5yb3VuZC5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXCl8KD89Ly8pfCg/PS9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmVuZC5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19XX0sXFxcInByZXByb2Nlc3Nvci1ydWxlLWRlZmluZS1saW5lLWJsb2Nrc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJ7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmJsb2NrLmJlZ2luLmJyYWNrZXQuY3VybHkuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwifXwoPz1cXFxcXFxcXHMqI1xcXFxcXFxccyooPzplbGlmfGVsc2V8ZW5kaWYpXFxcXFxcXFxiKXwoPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXHMqXFxcXFxcXFxuKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYmxvY2suZW5kLmJyYWNrZXQuY3VybHkuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGVmaW5lLWxpbmUtYmxvY2tzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWRlZmluZS1saW5lLWNvbnRlbnRzXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGVmaW5lLWxpbmUtY29udGVudHNcXFwifV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1kZWZpbmUtbGluZS1jb250ZW50c1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN2YXJhcmdfZWxsaXBzZXNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwie1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5ibG9jay5iZWdpbi5icmFja2V0LmN1cmx5LmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIn18KD89XFxcXFxcXFxzKiNcXFxcXFxcXHMqKD86ZWxpZnxlbHNlfGVuZGlmKVxcXFxcXFxcYil8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxzKlxcXFxcXFxcbilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmJsb2NrLmVuZC5icmFja2V0LmN1cmx5LmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmJsb2NrLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1kZWZpbmUtbGluZS1ibG9ja3NcXFwifV19LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXChcXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuYmVnaW4uYnJhY2tldC5yb3VuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5lbmQuYnJhY2tldC5yb3VuZC5jXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/ISg/OndoaWxlfGZvcnxkb3xpZnxlbHNlfHN3aXRjaHxjYXRjaHxlbnVtZXJhdGV8cmV0dXJufHR5cGVpZHxhbGlnbm9mfGFsaWduYXN8c2l6ZW9mfFtjcl0/aXRlcmF0ZXxhbmR8YW5kX2VxfGJpdGFuZHxiaXRvcnxjb21wbHxub3R8bm90X2VxfG9yfG9yX2VxfHR5cGVpZHx4b3J8eG9yX2VxfGFsaWdub2Z8YWxpZ25hc3xhc218X19hc21fX3xhdXRvfGJvb2x8X0Jvb2x8Y2hhcnxfQ29tcGxleHxkb3VibGV8ZW51bXxmbG9hdHxfSW1hZ2luYXJ5fGludHxsb25nfHNob3J0fHNpZ25lZHxzdHJ1Y3R8dHlwZWRlZnx1bmlvbnx1bnNpZ25lZHx2b2lkKVxcXFxcXFxccypcXFxcXFxcXCgpKD89KD86W0EtWmEtel9dW0EtWmEtejAtOV9dKit8OjopKytcXFxcXFxcXHMqXFxcXFxcXFwofCg/Oig/PD1vcGVyYXRvcikoPzpbLSomPD49KyFdK3xcXFxcXFxcXChcXFxcXFxcXCl8XFxcXFxcXFxbXFxcXFxcXFxdKSlcXFxcXFxcXHMqXFxcXFxcXFwoKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PD1cXFxcXFxcXCkpKD8hXFxcXFxcXFx3KXwoPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXHMqXFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1kZWZpbmUtbGluZS1mdW5jdGlvbnNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJ8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxzKlxcXFxcXFxcbilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5lbmQuY1xcXCJ9fSxcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuZG91YmxlLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdfZXNjYXBlZF9jaGFyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ19wbGFjZWhvbGRlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvbl9jaGFyYWN0ZXJcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCInXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnN0cmluZy5iZWdpbi5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCInfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxccypcXFxcXFxcXG4pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuZW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnNpbmdsZS5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nX2VzY2FwZWRfY2hhclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lX2NvbnRpbnVhdGlvbl9jaGFyYWN0ZXJcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNtZXRob2RfYWNjZXNzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21lbWJlcl9hY2Nlc3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX0sXFxcInByZXByb2Nlc3Nvci1ydWxlLWRlZmluZS1saW5lLWZ1bmN0aW9uc1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdG9yYWdlX3R5cGVzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ZhcmFyZ19lbGxpcHNlc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNtZXRob2RfYWNjZXNzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21lbWJlcl9hY2Nlc3NcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3BlcmF0b3JzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/ISg/OndoaWxlfGZvcnxkb3xpZnxlbHNlfHN3aXRjaHxjYXRjaHxlbnVtZXJhdGV8cmV0dXJufHR5cGVpZHxhbGlnbm9mfGFsaWduYXN8c2l6ZW9mfFtjcl0/aXRlcmF0ZXxhbmR8YW5kX2VxfGJpdGFuZHxiaXRvcnxjb21wbHxub3R8bm90X2VxfG9yfG9yX2VxfHR5cGVpZHx4b3J8eG9yX2VxfGFsaWdub2Z8YWxpZ25hcylcXFxcXFxcXHMqXFxcXFxcXFwoKSgoPzpbQS1aYS16X11bQS1aYS16MC05X10qK3w6OikrK3woPzooPzw9b3BlcmF0b3IpKD86Wy0qJjw+PSshXSt8XFxcXFxcXFwoXFxcXFxcXFwpfFxcXFxcXFxcW1xcXFxcXFxcXSkpKVxcXFxcXFxccyooXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLmFyZ3VtZW50cy5iZWdpbi5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxccypcXFxcXFxcXG4pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5hcmd1bWVudHMuZW5kLmJyYWNrZXQucm91bmQuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGVmaW5lLWxpbmUtZnVuY3Rpb25zXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5iZWdpbi5icmFja2V0LnJvdW5kLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxccypcXFxcXFxcXG4pXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuZW5kLmJyYWNrZXQucm91bmQuY1xcXCJ9fSxcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGVmaW5lLWxpbmUtZnVuY3Rpb25zXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZGVmaW5lLWxpbmUtY29udGVudHNcXFwifV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqaWZcXFxcXFxcXGIpKD89XFxcXFxcXFxzKlxcXFxcXFxcKCpcXFxcXFxcXGIwK1xcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbmRpZlxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcoPz0uKSg/IS8vfC9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0vLyl8KD89L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKXwoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1saW5lXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1lbGlmXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWVuYWJsZWQtZWxzZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZC1lbGlmXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbGlmXFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKig/OmVsaWZ8ZWxzZXxlbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcRyg/PS4pKD8hLy98L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PS8vKXwoPz0vXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxuXFxcIixcXFwiY29udGVudE5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnByZXByb2Nlc3Nvci5pZi1icmFuY2guY1xcXCIsXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooPzplbHNlfGVsaWZ8ZW5kaWYpXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXNhYmxlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWEtbWFya1xcXCJ9XX1dfV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZC1ibG9ja1xcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqaWZcXFxcXFxcXGIpKD89XFxcXFxcXFxzKlxcXFxcXFxcKCpcXFxcXFxcXGIwK1xcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbmRpZlxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcoPz0uKSg/IS8vfC9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0vLyl8KD89L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKXwoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1saW5lXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1lbGlmLWJsb2NrXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWVuYWJsZWQtZWxzZS1ibG9ja1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZC1lbGlmXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbGlmXFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKig/OmVsaWZ8ZWxzZXxlbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcRyg/PS4pKD8hLy98L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PS8vKXwoPz0vXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNibG9ja19pbm5hcmRzXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxuXFxcIixcXFwiY29udGVudE5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnByZXByb2Nlc3Nvci5pZi1icmFuY2guaW4tYmxvY2suY1xcXCIsXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooPzplbHNlfGVsaWZ8ZW5kaWYpXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXNhYmxlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWEtbWFya1xcXCJ9XX1dfV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1kaXNhYmxlZC1lbGlmXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsaWZcXFxcXFxcXGIpKD89XFxcXFxcXFxzKlxcXFxcXFxcKCpcXFxcXFxcXGIwK1xcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooPzplbGlmfGVsc2V8ZW5kaWYpXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcoPz0uKSg/IS8vfC9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0vLyl8KD89L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKXwoPzwhXFxcXFxcXFxcXFxcXFxcXCkoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1saW5lXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxuXFxcIixcXFwiY29udGVudE5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnByZXByb2Nlc3Nvci5lbGlmLWJyYW5jaC5jXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKig/OmVsc2V8ZWxpZnxlbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rpc2FibGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYS1tYXJrXFxcIn1dfV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1lbmFibGVkXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyppZlxcXFxcXFxcYikoPz1cXFxcXFxcXHMqXFxcXFxcXFwoKlxcXFxcXFxcYjAqMVxcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy5wcmVwcm9jZXNzb3IuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVuZGlmXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcRyg/PS4pKD8hLy98L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PS8vKXwoPz0vXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpfCg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZWxzZVxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wcmVwcm9jZXNzb3IuZWxzZS1icmFuY2guY1xcXCIsXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbmRpZlxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGlzYWJsZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hLW1hcmtcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZWxpZlxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wcmVwcm9jZXNzb3IuaWYtYnJhbmNoLmNcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKD86ZWxzZXxlbGlmfGVuZGlmKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGlzYWJsZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hLW1hcmtcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXG5cXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKD86ZWxzZXxlbGlmfGVuZGlmKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX1dfV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1lbmFibGVkLWJsb2NrXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyppZlxcXFxcXFxcYikoPz1cXFxcXFxcXHMqXFxcXFxcXFwoKlxcXFxcXFxcYjAqMVxcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbmRpZlxcXFxcXFxcYilcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcoPz0uKSg/IS8vfC9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSlcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz0vLyl8KD89L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKXwoPz1cXFxcXFxcXG4pXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmVwcm9jZXNzb3ItcnVsZS1jb25kaXRpb25hbC1saW5lXFxcIn1dfSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29tbWVudHNcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsc2VcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJjb250ZW50TmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sucHJlcHJvY2Vzc29yLmVsc2UtYnJhbmNoLmluLWJsb2NrLmNcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZW5kaWZcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rpc2FibGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYS1tYXJrXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsaWZcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJjb250ZW50TmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sucHJlcHJvY2Vzc29yLmlmLWJyYW5jaC5pbi1ibG9jay5jXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKig/OmVsc2V8ZWxpZnxlbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rpc2FibGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYS1tYXJrXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxuXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKig/OmVsc2V8ZWxpZnxlbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifV19XX1dfSxcXFwicHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1lbGlmXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsaWZcXFxcXFxcXGIpKD89XFxcXFxcXFxzKlxcXFxcXFxcKCpcXFxcXFxcXGIwKjFcXFxcXFxcXGJcXFxcXFxcXCkqXFxcXFxcXFxzKig/OiR8Ly98L1xcXFxcXFxcKikpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZW5kaWZcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcRyg/PS4pKD8hLy98L1xcXFxcXFxcKig/IS4qXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxccypcXFxcXFxcXG4pKVxcXCIsXFxcImVuZFxcXCI6XFxcIig/PS8vKXwoPz0vXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpfCg/PCFcXFxcXFxcXFxcXFxcXFxcKSg/PVxcXFxcXFxcbilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByZXByb2Nlc3Nvci1ydWxlLWNvbmRpdGlvbmFsLWxpbmVcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXG5cXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKD86ZW5kaWYpXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKGVsc2UpXFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiY29udGVudE5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnByZXByb2Nlc3Nvci5lbGlmLWJyYW5jaC5jXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVuZGlmXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXNhYmxlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWEtbWFya1xcXCJ9XX0se1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooZWxpZilcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJjb250ZW50TmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sucHJlcHJvY2Vzc29yLmVsaWYtYnJhbmNoLmNcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqKD86ZWxzZXxlbGlmfGVuZGlmKVxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGlzYWJsZWRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJhZ21hLW1hcmtcXFwifV19LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfV19LFxcXCJwcmVwcm9jZXNzb3ItcnVsZS1lbmFibGVkLWVsaWYtYmxvY2tcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJeXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZWxpZlxcXFxcXFxcYikoPz1cXFxcXFxcXHMqXFxcXFxcXFwoKlxcXFxcXFxcYjAqMVxcXFxcXFxcYlxcXFxcXFxcKSpcXFxcXFxcXHMqKD86JHwvL3wvXFxcXFxcXFwqKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbmRpZlxcXFxcXFxcYikpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxHKD89LikoPyEvL3wvXFxcXFxcXFwqKD8hLipcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxzKlxcXFxcXFxcbikpXFxcIixcXFwiZW5kXFxcIjpcXFwiKD89Ly8pfCg/PS9cXFxcXFxcXCooPyEuKlxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXHMqXFxcXFxcXFxuKSl8KD88IVxcXFxcXFxcXFxcXFxcXFwpKD89XFxcXFxcXFxuKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJlcHJvY2Vzc29yLXJ1bGUtY29uZGl0aW9uYWwtbGluZVxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcblxcXCIsXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooPzplbmRpZilcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooZWxzZSlcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJjb250ZW50TmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sucHJlcHJvY2Vzc29yLmVsaWYtYnJhbmNoLmluLWJsb2NrLmNcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZW5kaWZcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Rpc2FibGVkXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3ByYWdtYS1tYXJrXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKihlbGlmKVxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJlcHJvY2Vzc29yLmNcXFwifSxcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLmRpcmVjdGl2ZS5jb25kaXRpb25hbC5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uZGlyZWN0aXZlLmNcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wcmVwcm9jZXNzb3IuZWxpZi1icmFuY2guY1xcXCIsXFxcImVuZFxcXCI6XFxcIig/PV5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyooPzplbHNlfGVsaWZ8ZW5kaWYpXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXNhYmxlZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcmFnbWEtbWFya1xcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifV19XX0sXFxcInByZXByb2Nlc3Nvci1ydWxlLWVuYWJsZWQtZWxzZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIl5cXFxcXFxcXHMqKCgjKVxcXFxcXFxccyplbHNlXFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5wcmVwcm9jZXNzb3IuY1xcXCJ9LFxcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wuZGlyZWN0aXZlLmNvbmRpdGlvbmFsLmNcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5kaXJlY3RpdmUuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89XlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVuZGlmXFxcXFxcXFxiKSlcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn1dfSxcXFwicHJlcHJvY2Vzc29yLXJ1bGUtZW5hYmxlZC1lbHNlLWJsb2NrXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXlxcXFxcXFxccyooKCMpXFxcXFxcXFxzKmVsc2VcXFxcXFxcXGIpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByZXByb2Nlc3Nvci5jXFxcIn0sXFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5kaXJlY3RpdmUuY29uZGl0aW9uYWwuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmRpcmVjdGl2ZS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1eXFxcXFxcXFxzKigoIylcXFxcXFxcXHMqZW5kaWZcXFxcXFxcXGIpKVxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifV19LFxcXCJwcm9iYWJseV9hX3BhcmFtZXRlclxcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5wYXJhbWV0ZXIucHJvYmFibHkuY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoPzw9KD86W2EtekEtWl8wLTldIHxbJio+XFxcXFxcXFxdXFxcXFxcXFwpXSkpXFxcXFxcXFxzKihbYS16QS1aX11cXFxcXFxcXHcqKVxcXFxcXFxccyooPz0oPzpcXFxcXFxcXFtcXFxcXFxcXF1cXFxcXFxcXHMqKT8oPzosfFxcXFxcXFxcKSkpXFxcIn0sXFxcInN0YXRpY19hc3NlcnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoKD8+KD86KD86KD8+KD88IVxcXFxcXFxccylcXFxcXFxcXHMrKXwoXFxcXFxcXFwvXFxcXFxcXFwqKSgoPz4oPzpbXlxcXFxcXFxcKl18KD8+XFxcXFxcXFwqKylbXlxcXFxcXFxcL10pKikoKD8+XFxcXFxcXFwqKylcXFxcXFxcXC8pKSkrfCg/Oig/Oig/Oig/OlxcXFxcXFxcYnwoPzw9XFxcXFxcXFxXKSl8KD89XFxcXFxcXFxXKSl8XFxcXFxcXFxBKXxcXFxcXFxcXFopKSkpKCg/PCFcXFxcXFxcXHcpc3RhdGljX2Fzc2VydHxfU3RhdGljX2Fzc2VydCg/IVxcXFxcXFxcdykpKCg/Pig/Oig/Oig/Pig/PCFcXFxcXFxcXHMpXFxcXFxcXFxzKyl8KFxcXFxcXFxcL1xcXFxcXFxcKikoKD8+KD86W15cXFxcXFxcXCpdfCg/PlxcXFxcXFxcKispW15cXFxcXFxcXC9dKSopKCg/PlxcXFxcXFxcKispXFxcXFxcXFwvKSkpK3woPzooPzooPzooPzpcXFxcXFxcXGJ8KD88PVxcXFxcXFxcVykpfCg/PVxcXFxcXFxcVykpfFxcXFxcXFxcQSl8XFxcXFxcXFxaKSkpKShcXFxcXFxcXCgpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lubGluZV9jb21tZW50XFxcIn1dfSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9LFxcXCI0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifV19LFxcXCI1XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnN0YXRpY19hc3NlcnQuY1xcXCJ9LFxcXCI2XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lubGluZV9jb21tZW50XFxcIn1dfSxcXFwiN1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn0sXFxcIjhcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9LFxcXCI5XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifV19LFxcXCIxMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5hcmd1bWVudHMuYmVnaW4uYnJhY2tldC5yb3VuZC5zdGF0aWNfYXNzZXJ0LmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5hcmd1bWVudHMuZW5kLmJyYWNrZXQucm91bmQuc3RhdGljX2Fzc2VydC5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoLClcXFxcXFxcXHMqKD89KD86THx1OHx1fFVcXFxcXFxcXHMqXFxcXFxcXFxcXFxcXFxcIik/KVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VwYXJhdG9yLmRlbGltaXRlci5jb21tYS5jXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1cXFxcXFxcXCkpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuc3RhdGljX2Fzc2VydC5tZXNzYWdlLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdfY29udGV4dFxcXCJ9XX0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V2YWx1YXRpb25fY29udGV4dFxcXCJ9XX0sXFxcInN0b3JhZ2VfdHlwZXNcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiKD8tbWl4Oig/PCFcXFxcXFxcXHcpKD86dW5zaWduZWR8c2lnbmVkfGRvdWJsZXxfQm9vbHxzaG9ydHxmbG9hdHxsb25nfHZvaWR8Y2hhcnxib29sfGludCkoPyFcXFxcXFxcXHcpKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuYnVpbHQtaW4ucHJpbWl0aXZlLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD8tbWl4Oig/PCFcXFxcXFxcXHcpKD86YXRvbWljX3VpbnRfbGVhc3Q2NF90fGF0b21pY191aW50X2xlYXN0MTZfdHxhdG9taWNfdWludF9sZWFzdDMyX3R8cHRocmVhZF9yd2xvY2thdHRyX3R8YXRvbWljX3VpbnRfZmFzdDY0X3R8YXRvbWljX3VpbnRfZmFzdDMyX3R8YXRvbWljX3VpbnRfZmFzdDE2X3R8YXRvbWljX2ludF9sZWFzdDY0X3R8YXRvbWljX2ludF9sZWFzdDMyX3R8YXRvbWljX2ludF9sZWFzdDE2X3R8YXRvbWljX3VpbnRfbGVhc3Q4X3R8YXRvbWljX3VpbnRfZmFzdDhfdHxhdG9taWNfaW50X2xlYXN0OF90fGF0b21pY19pbnRfZmFzdDE2X3R8cHRocmVhZF9tdXRleGF0dHJfdHxhdG9taWNfaW50X2Zhc3QzMl90fGF0b21pY19pbnRfZmFzdDY0X3R8YXRvbWljX2ludF9mYXN0OF90fHB0aHJlYWRfY29uZGF0dHJfdHxhdG9taWNfcHRyZGlmZl90fHB0aHJlYWRfcndsb2NrX3R8YXRvbWljX3VpbnRwdHJfdHxhdG9taWNfdWludG1heF90fGF0b21pY19pbnRtYXhfdHxhdG9taWNfaW50cHRyX3R8YXRvbWljX2NoYXIzMl90fGF0b21pY19jaGFyMTZfdHxwdGhyZWFkX211dGV4X3R8cHRocmVhZF9jb25kX3R8YXRvbWljX3djaGFyX3R8dWludF9sZWFzdDY0X3R8dWludF9sZWFzdDMyX3R8dWludF9sZWFzdDE2X3R8cHRocmVhZF9vbmNlX3R8cHRocmVhZF9hdHRyX3R8aW50X2xlYXN0MzJfdHxwdGhyZWFkX2tleV90fGludF9sZWFzdDE2X3R8aW50X2xlYXN0NjRfdHx1aW50X2xlYXN0OF90fHVpbnRfZmFzdDE2X3R8dWludF9mYXN0MzJfdHx1aW50X2Zhc3Q2NF90fGF0b21pY191c2hvcnR8YXRvbWljX3VsbG9uZ3xhdG9taWNfc2l6ZV90fGludF9mYXN0MTZfdHxpbnRfZmFzdDY0X3R8dWludF9mYXN0OF90fGF0b21pY19zaG9ydHxhdG9taWNfdWNoYXJ8YXRvbWljX3NjaGFyfGludF9sZWFzdDhfdHxtZW1vcnlfb3JkZXJ8YXRvbWljX2xsb25nfGF0b21pY191bG9uZ3xpbnRfZmFzdDMyX3R8YXRvbWljX2xvbmd8YXRvbWljX3VpbnR8YXRvbWljX2NoYXJ8aW50X2Zhc3Q4X3R8c3VzZWNvbmRzX3R8YXRvbWljX2Jvb2x8YXRvbWljX2ludHxfSW1hZ2luYXJ5fHVzZWNvbmRzX3R8aW5fcG9ydF90fHVpbnRtYXhfdHx1aW50bWF4X3R8cHRocmVhZF90fGJsa3NpemVfdHxpbl9hZGRyX3R8dWludHB0cl90fGJsa2NudF90fHVpbnQxNl90fHVpbnQzMl90fHVpbnQ2NF90fHVfcXVhZF90fF9Db21wbGV4fGludHB0cl90fGludG1heF90fGludG1heF90fHNlZ3N6X3R8dV9zaG9ydHxubGlua190fHVpbnQ4X3R8aW50NjRfdHxpbnQzMl90fGludDE2X3R8Zml4cHRfdHxkYWRkcl90fGNhZGRyX3R8cWFkZHJfdHxzc2l6ZV90fGNsb2NrX3R8c3dibGtfdHx1X2xvbmd8bW9kZV90fGludDhfdHx0aW1lX3R8dXNob3J0fHVfY2hhcnxxdWFkX3R8c2l6ZV90fHBpZF90fGdpZF90fHVpZF90fGRldl90fGRpdl90fG9mZl90fHVfaW50fGtleV90fGlub190fHVpbnR8aWRfdHxpZF90KSg/IVxcXFxcXFxcdykpXFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5idWlsdC1pbi5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/LW1peDpcXFxcXFxcXGIoZW51bXxzdHJ1Y3R8dW5pb24pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLnR5cGUuJDEuY1xcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoXFxcXFxcXFxiKD86X19hc21fX3xhc20pXFxcXFxcXFxiKVxcXFxcXFxccyooKD86dm9sYXRpbGUpPylcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5hc20uY1xcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdG9yYWdlLm1vZGlmaWVyLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/IVxcXFxcXFxcRylcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5hc20uY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lubGluZV9jb21tZW50XFxcIn1dfSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9LFxcXCI0XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifV19fSxcXFwibWF0Y2hcXFwiOlxcXCIoPzpeKSgoPzooPzooPz5cXFxcXFxcXHMrKXwoXFxcXFxcXFwvXFxcXFxcXFwqKSgoPz4oPzpbXlxcXFxcXFxcKl18KD8+XFxcXFxcXFwqKylbXlxcXFxcXFxcL10pKikoKD8+XFxcXFxcXFwqKylcXFxcXFxcXC8pKSkrP3woPzooPzooPzooPzpcXFxcXFxcXGJ8KD88PVxcXFxcXFxcVykpfCg/PVxcXFxcXFxcVykpfFxcXFxcXFxcQSl8XFxcXFxcXFxaKSkpKD86XFxcXFxcXFxufCQpXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NvbW1lbnRzXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIigoKD86KD86KD8+XFxcXFxcXFxzKyl8KFxcXFxcXFxcL1xcXFxcXFxcKikoKD8+KD86W15cXFxcXFxcXCpdfCg/PlxcXFxcXFxcKispW15cXFxcXFxcXC9dKSopKCg/PlxcXFxcXFxcKispXFxcXFxcXFwvKSkpKz98KD86KD86KD86KD86XFxcXFxcXFxifCg/PD1cXFxcXFxcXFcpKXwoPz1cXFxcXFxcXFcpKXxcXFxcXFxcXEEpfFxcXFxcXFxcWikpKVxcXFxcXFxcKClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmJlZ2luLmJyYWNrZXQucm91bmQuYXNzZW1ibHkuY1xcXCJ9LFxcXCIyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lubGluZV9jb21tZW50XFxcIn1dfSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5iZWdpbi5jXFxcIn0sXFxcIjRcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9LFxcXCI1XFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXFxcXFxcL1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmVuZC5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcKlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifV19fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5lbmQuYnJhY2tldC5yb3VuZC5hc3NlbWJseS5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCIoUj8pKFxcXFxcXFwiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5lbmNvZGluZy5jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLmFzc2VtYmx5LmNcXFwifX0sXFxcImNvbnRlbnROYW1lXFxcIjpcXFwibWV0YS5lbWJlZGRlZC5hc3NlbWJseS5jXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFwiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5hc3NlbWJseS5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5kb3VibGUuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwic291cmNlLmFzbVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcInNvdXJjZS54ODZcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCJzb3VyY2UueDg2XzY0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwic291cmNlLmFybVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNiYWNrc2xhc2hfZXNjYXBlc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdfZXNjYXBlZF9jaGFyXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiKFxcXFxcXFxcKClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmJlZ2luLmJyYWNrZXQucm91bmQuYXNzZW1ibHkuaW5uZXIuY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcKSlcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5zZWN0aW9uLnBhcmVucy5lbmQuYnJhY2tldC5yb3VuZC5hc3NlbWJseS5pbm5lci5jXFxcIn19LFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNldmFsdWF0aW9uX2NvbnRleHRcXFwifV19LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbmxpbmVfY29tbWVudFxcXCJ9XX0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuYmVnaW4uY1xcXCJ9LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifSxcXFwiNFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXC9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn1dfSxcXFwiNVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUub3RoZXIuYXNtLmxhYmVsLmNcXFwifSxcXFwiNlxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbmxpbmVfY29tbWVudFxcXCJ9XX0sXFxcIjdcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuYmVnaW4uY1xcXCJ9LFxcXCI4XFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmNcXFwifSxcXFwiOVxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXC9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jIHB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5lbmQuY1xcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCpcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn1dfX0sXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxbKCg/Oig/Oig/PlxcXFxcXFxccyspfChcXFxcXFxcXC9cXFxcXFxcXCopKCg/Pig/OlteXFxcXFxcXFwqXXwoPz5cXFxcXFxcXCorKVteXFxcXFxcXFwvXSkqKSgoPz5cXFxcXFxcXCorKVxcXFxcXFxcLykpKSs/fCg/Oig/Oig/Oig/OlxcXFxcXFxcYnwoPzw9XFxcXFxcXFxXKSl8KD89XFxcXFxcXFxXKSl8XFxcXFxcXFxBKXxcXFxcXFxcXFopKSkoW2EtekEtWl9dXFxcXFxcXFx3KikoKD86KD86KD8+XFxcXFxcXFxzKyl8KFxcXFxcXFxcL1xcXFxcXFxcKikoKD8+KD86W15cXFxcXFxcXCpdfCg/PlxcXFxcXFxcKispW15cXFxcXFxcXC9dKSopKCg/PlxcXFxcXFxcKispXFxcXFxcXFwvKSkpKz98KD86KD86KD86KD86XFxcXFxcXFxifCg/PD1cXFxcXFxcXFcpKXwoPz1cXFxcXFxcXFcpKXxcXFxcXFxcXEEpfFxcXFxcXFxcWikpKVxcXFxcXFxcXVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCI6XFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlcGFyYXRvci5kZWxpbWl0ZXIuY29sb24uYXNzZW1ibHkuY1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjb21tZW50c1xcXCJ9XX1dfV19LFxcXCJzdHJpbmdfZXNjYXBlZF9jaGFyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwoXFxcXFxcXFxcXFxcXFxcXHxbYWJlZm5wcnR2J1xcXFxcXFwiP118WzAtM11cXFxcXFxcXGR7LDJ9fFs0LTddXFxcXFxcXFxkP3x4W2EtZkEtRjAtOV17LDJ9fHVbYS1mQS1GMC05XXssNH18VVthLWZBLUYwLTldeyw4fSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQuY2hhcmFjdGVyLmVzY2FwZS5jXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcXFxcXFxcXFwuXFxcIixcXFwibmFtZVxcXCI6XFxcImludmFsaWQuaWxsZWdhbC51bmtub3duLWVzY2FwZS5jXFxcIn1dfSxcXFwic3RyaW5nX3BsYWNlaG9sZGVyXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIiUoXFxcXFxcXFxkK1xcXFxcXFxcJCk/WyMwXFxcXFxcXFwtICsnXSpbLDs6X10/KCgtP1xcXFxcXFxcZCspfFxcXFxcXFxcKigtP1xcXFxcXFxcZCtcXFxcXFxcXCQpPyk/KFxcXFxcXFxcLigoLT9cXFxcXFxcXGQrKXxcXFxcXFxcXCooLT9cXFxcXFxcXGQrXFxcXFxcXFwkKT8pPyk/KGhofGh8bGx8bHxqfHR8enxxfEx8dmh8dmx8dnxodnxobCk/W2Rpb3V4WERPVWVFZkZnR2FBQ2NTc3BuJV1cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQub3RoZXIucGxhY2Vob2xkZXIuY1xcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiaW52YWxpZC5pbGxlZ2FsLnBsYWNlaG9sZGVyLmNcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKCUpKD8hXFxcXFxcXCJcXFxcXFxcXHMqKFBSSXxTQ04pKVxcXCJ9XX0sXFxcInN0cmluZ3NcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuZW5kLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nX2VzY2FwZWRfY2hhclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdHJpbmdfcGxhY2Vob2xkZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn1dfSx7XFxcImJlZ2luXFxcIjpcXFwiJ1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi5zdHJpbmcuYmVnaW4uY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiJ1xcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwic3RyaW5nLnF1b3RlZC5zaW5nbGUuY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZ19lc2NhcGVkX2NoYXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbGluZV9jb250aW51YXRpb25fY2hhcmFjdGVyXFxcIn1dfV19LFxcXCJzd2l0Y2hfY29uZGl0aW9uYWxfcGFyZW50aGVzZXNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoKD8+KD86KD86KD8+KD88IVxcXFxcXFxccylcXFxcXFxcXHMrKXwoXFxcXFxcXFwvXFxcXFxcXFwqKSgoPz4oPzpbXlxcXFxcXFxcKl18KD8+XFxcXFxcXFwqKylbXlxcXFxcXFxcL10pKikoKD8+XFxcXFxcXFwqKylcXFxcXFxcXC8pKSkrfCg/Oig/Oig/Oig/OlxcXFxcXFxcYnwoPzw9XFxcXFxcXFxXKSl8KD89XFxcXFxcXFxXKSl8XFxcXFxcXFxBKXxcXFxcXFxcXFopKSkpKFxcXFxcXFxcKClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW5saW5lX2NvbW1lbnRcXFwifV19LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0sXFxcIjRcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcXFxcXFwvXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX0sXFxcIjVcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24ucGFyZW5zLmJlZ2luLmJyYWNrZXQucm91bmQuY29uZGl0aW9uYWwuc3dpdGNoLmNcXFwifX0sXFxcImVuZFxcXCI6XFxcIihcXFxcXFxcXCkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5wYXJlbnMuZW5kLmJyYWNrZXQucm91bmQuY29uZGl0aW9uYWwuc3dpdGNoLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmNvbmRpdGlvbmFsLnN3aXRjaC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXZhbHVhdGlvbl9jb250ZXh0XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2NfY29uZGl0aW9uYWxfY29udGV4dFxcXCJ9XX0sXFxcInN3aXRjaF9zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoKCg/Pig/Oig/Oig/Pig/PCFcXFxcXFxcXHMpXFxcXFxcXFxzKyl8KFxcXFxcXFxcL1xcXFxcXFxcKikoKD8+KD86W15cXFxcXFxcXCpdfCg/PlxcXFxcXFxcKispW15cXFxcXFxcXC9dKSopKCg/PlxcXFxcXFxcKispXFxcXFxcXFwvKSkpK3woPzooPzooPzooPzpcXFxcXFxcXGJ8KD88PVxcXFxcXFxcVykpfCg/PVxcXFxcXFxcVykpfFxcXFxcXFxcQSl8XFxcXFxcXFxaKSkpKSgoPzwhXFxcXFxcXFx3KXN3aXRjaCg/IVxcXFxcXFxcdykpKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwibWV0YS5oZWFkLnN3aXRjaC5jXFxcIn0sXFxcIjJcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW5saW5lX2NvbW1lbnRcXFwifV19LFxcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLmMgcHVuY3R1YXRpb24uZGVmaW5pdGlvbi5jb21tZW50LmJlZ2luLmNcXFwifSxcXFwiNFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5jXFxcIn0sXFxcIjVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcXFxcXFwvXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suYyBwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQuZW5kLmNcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwqXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suY1xcXCJ9XX0sXFxcIjZcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbC5zd2l0Y2guY1xcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD86KD88PVxcXFxcXFxcfXwlPnxcXFxcXFxcXD9cXFxcXFxcXD8+KXwoPz1bOz5cXFxcXFxcXFtcXFxcXFxcXF09XSkpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuYmxvY2suc3dpdGNoLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXEcgP1xcXCIsXFxcImVuZFxcXCI6XFxcIigoPzpcXFxcXFxcXHt8PCV8XFxcXFxcXFw/XFxcXFxcXFw/PHwoPz07KSkpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc2VjdGlvbi5ibG9jay5iZWdpbi5icmFja2V0LmN1cmx5LnN3aXRjaC5jXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5oZWFkLnN3aXRjaC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3dpdGNoX2NvbmRpdGlvbmFsX3BhcmVudGhlc2VzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiJHNlbGZcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9XFxcXFxcXFx7fDwlfFxcXFxcXFxcP1xcXFxcXFxcPzwpXFxcIixcXFwiZW5kXFxcIjpcXFwiKFxcXFxcXFxcfXwlPnxcXFxcXFxcXD9cXFxcXFxcXD8+KVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNlY3Rpb24uYmxvY2suZW5kLmJyYWNrZXQuY3VybHkuc3dpdGNoLmNcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmJvZHkuc3dpdGNoLmNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWZhdWx0X3N0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNjYXNlX3N0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiRzZWxmXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jsb2NrX2lubmFyZHNcXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCIoPzw9XFxcXFxcXFx9fCU+fFxcXFxcXFxcP1xcXFxcXFxcPz4pW1xcXFxcXFxcc1xcXFxcXFxcbl0qXFxcIixcXFwiZW5kXFxcIjpcXFwiW1xcXFxcXFxcc1xcXFxcXFxcbl0qKD89OylcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS50YWlsLnN3aXRjaC5jXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIkc2VsZlxcXCJ9XX1dfSxcXFwidmFyYXJnX2VsbGlwc2VzXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiKD88IVxcXFxcXFxcLilcXFxcXFxcXC5cXFxcXFxcXC5cXFxcXFxcXC4oPyFcXFxcXFxcXC4pXFxcIixcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnZhcmFyZy1lbGxpcHNlcy5jXFxcIn19LFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2UuY1xcXCJ9XCIpKVxuXG5leHBvcnQgZGVmYXVsdCBbXG5sYW5nXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/c.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/glsl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/glsl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _c_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./c.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/c.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"GLSL\\\",\\\"fileTypes\\\":[\\\"vs\\\",\\\"fs\\\",\\\"gs\\\",\\\"vsh\\\",\\\"fsh\\\",\\\"gsh\\\",\\\"vshader\\\",\\\"fshader\\\",\\\"gshader\\\",\\\"vert\\\",\\\"frag\\\",\\\"geom\\\",\\\"f.glsl\\\",\\\"v.glsl\\\",\\\"g.glsl\\\"],\\\"foldingStartMarker\\\":\\\"/\\\\\\\\*\\\\\\\\*|\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"\\\\\\\\*\\\\\\\\*/|^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"glsl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(void|bool|int|uint|float|vec2|vec3|vec4|bvec2|bvec3|bvec4|ivec2|ivec2|ivec3|uvec2|uvec2|uvec3|mat2|mat3|mat4|mat2x2|mat2x3|mat2x4|mat3x2|mat3x3|mat3x4|mat4x2|mat4x3|mat4x4|sampler[1|2|3]D|samplerCube|sampler2DRect|sampler[1|2]DShadow|sampler2DRectShadow|sampler[1|2]DArray|sampler[1|2]DArrayShadow|samplerBuffer|sampler2DMS|sampler2DMSArray|struct|isampler[1|2|3]D|isamplerCube|isampler2DRect|isampler[1|2]DArray|isamplerBuffer|isampler2DMS|isampler2DMSArray|usampler[1|2|3]D|usamplerCube|usampler2DRect|usampler[1|2]DArray|usamplerBuffer|usampler2DMS|usampler2DMSArray)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(attribute|centroid|const|flat|in|inout|invariant|noperspective|out|smooth|uniform|varying)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(gl_BackColor|gl_BackLightModelProduct|gl_BackLightProduct|gl_BackMaterial|gl_BackSecondaryColor|gl_ClipDistance|gl_ClipPlane|gl_ClipVertex|gl_Color|gl_DepthRange|gl_DepthRangeParameters|gl_EyePlaneQ|gl_EyePlaneR|gl_EyePlaneS|gl_EyePlaneT|gl_Fog|gl_FogCoord|gl_FogFragCoord|gl_FogParameters|gl_FragColor|gl_FragCoord|gl_FragDat|gl_FragDept|gl_FrontColor|gl_FrontFacing|gl_FrontLightModelProduct|gl_FrontLightProduct|gl_FrontMaterial|gl_FrontSecondaryColor|gl_InstanceID|gl_Layer|gl_LightModel|gl_LightModelParameters|gl_LightModelProducts|gl_LightProducts|gl_LightSource|gl_LightSourceParameters|gl_MaterialParameters|gl_ModelViewMatrix|gl_ModelViewMatrixInverse|gl_ModelViewMatrixInverseTranspose|gl_ModelViewMatrixTranspose|gl_ModelViewProjectionMatrix|gl_ModelViewProjectionMatrixInverse|gl_ModelViewProjectionMatrixInverseTranspose|gl_ModelViewProjectionMatrixTranspose|gl_MultiTexCoord[0-7]|gl_Normal|gl_NormalMatrix|gl_NormalScale|gl_ObjectPlaneQ|gl_ObjectPlaneR|gl_ObjectPlaneS|gl_ObjectPlaneT|gl_Point|gl_PointCoord|gl_PointParameters|gl_PointSize|gl_Position|gl_PrimitiveIDIn|gl_ProjectionMatrix|gl_ProjectionMatrixInverse|gl_ProjectionMatrixInverseTranspose|gl_ProjectionMatrixTranspose|gl_SecondaryColor|gl_TexCoord|gl_TextureEnvColor|gl_TextureMatrix|gl_TextureMatrixInverse|gl_TextureMatrixInverseTranspose|gl_TextureMatrixTranspose|gl_Vertex|gl_VertexIDh)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(gl_MaxClipPlanes|gl_MaxCombinedTextureImageUnits|gl_MaxDrawBuffers|gl_MaxFragmentUniformComponents|gl_MaxLights|gl_MaxTextureCoords|gl_MaxTextureImageUnits|gl_MaxTextureUnits|gl_MaxVaryingFloats|gl_MaxVertexAttribs|gl_MaxVertexTextureImageUnits|gl_MaxVertexUniformComponents)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(abs|acos|all|any|asin|atan|ceil|clamp|cos|cross|degrees|dFdx|dFdy|distance|dot|equal|exp|exp2|faceforward|floor|fract|ftransform|fwidth|greaterThan|greaterThanEqual|inversesqrt|length|lessThan|lessThanEqual|log|log2|matrixCompMult|max|min|mix|mod|noise[1-4]|normalize|not|notEqual|outerProduct|pow|radians|reflect|refract|shadow1D|shadow1DLod|shadow1DProj|shadow1DProjLod|shadow2D|shadow2DLod|shadow2DProj|shadow2DProjLod|sign|sin|smoothstep|sqrt|step|tan|texture1D|texture1DLod|texture1DProj|texture1DProjLod|texture2D|texture2DLod|texture2DProj|texture2DProjLod|texture3D|texture3DLod|texture3DProj|texture3DProjLod|textureCube|textureCubeLod|transpose)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(asm|double|enum|extern|goto|inline|long|short|sizeof|static|typedef|union|unsigned|volatile)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.glsl\\\"},{\\\"include\\\":\\\"source.c\\\"}],\\\"scopeName\\\":\\\"source.glsl\\\",\\\"embeddedLangs\\\":[\\\"c\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._c_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/glsl.mjs\n"));

/***/ })

}]);