import type { Metadata } from 'next';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import ShowQuery from '@/services/queries/ShowQuery';
import ExhibitorImportClient from './components/ExhibitorImportClient';

export const metadata: Metadata = {
  title: 'Import Exhibitors | GOODKEY SHOW SERVICES LTD.',
  description:
    'Import exhibitor information from Excel files with validation, duplicate resolution, and automated processing at GOODKEY SHOW SERVICES LTD.',
};

export default async function ExhibitorImportPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const showId = Number(params.id);

  if (isNaN(showId)) {
    redirect('/dashboard/setup/list-of-shows');
  }

  const queryClient = getQueryClient();

  try {
    // Prefetch show data
    await queryClient.prefetchQuery({
      queryKey: [ShowQuery.tags, showId],
      queryFn: () => ShowQuery.getOne(showId),
    });
  } catch (error) {
    console.error('Failed to prefetch show data:', error);
    redirect('/dashboard/setup/list-of-shows');
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ExhibitorImportClient showId={showId} />
    </HydrationBoundary>
  );
}
