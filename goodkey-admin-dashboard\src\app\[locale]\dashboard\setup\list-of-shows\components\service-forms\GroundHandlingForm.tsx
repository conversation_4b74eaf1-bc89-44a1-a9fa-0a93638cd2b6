'use client';

import { BaseServiceForm } from './BaseServiceForm';
import {
  JanitorialServicesSchema,
  JanitorialServicesFormType,
} from '@/schema/ServiceFormSchemas';
import { ServiceFormType } from '@/models/Service';
import Field from '@/components/ui/inputs/field';

interface GroundHandlingFormProps {
  defaultValues?: JanitorialServicesFormType;
  onSubmit: (data: JanitorialServicesFormType) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function GroundHandlingForm({
  defaultValues,
  onSubmit,
  onCancel,
  isLoading,
}: GroundHandlingFormProps) {
  return (
    <BaseServiceForm
      schema={JanitorialServicesSchema}
      defaultValues={defaultValues}
      onSubmit={onSubmit}
      onCancel={onCancel}
      isLoading={isLoading}
      serviceType={ServiceFormType.JANITORIAL_SERVICES}
    >
      {(form) => (
        <>
          <Field
            control={form.control}
            name="serviceIncludes"
            label="Service Includes"
            type="textarea"
            placeholder="Describe what services are included"
            rows={4}
            required
          />

          <Field
            control={form.control}
            name="rateType"
            label="Rate Type"
            type={{
              type: 'select',
              props: {
                options: [
                  { label: 'sq. ft', value: 'sq_ft' },
                  { label: 'Hourly', value: 'hourly' },
                  { label: 'Daily', value: 'daily' },
                ],
                placeholder: 'Select rate type',
              },
            }}
            required
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Field
              control={form.control}
              name="oneTimeRate"
              label="One Time Rate ($)"
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              required
            />

            <Field
              control={form.control}
              name="twoDaysRate"
              label="Two Days Rate ($)"
              type="number"
              placeholder="0.25"
              min="0"
              step="0.01"
              required
            />

            <Field
              control={form.control}
              name="threeDaysRate"
              label="Three Days Rate ($)"
              type="number"
              placeholder="0.52"
              min="0"
              step="0.01"
              required
            />
          </div>
        </>
      )}
    </BaseServiceForm>
  );
}
