﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IWarehouseRepository
    {
        Task<IEnumerable<Warehouse>> GetAllAsync();
        Task<Warehouse> GetByIdAsync(int id);
        Task AddAsync(Warehouse warehouse);
        Task UpdateAsync(Warehouse warehouse);
        IEnumerable<WarehouseTypes> GetAll();
    }

    public class WarehouseRepository : IWarehouseRepository
    {
        private readonly GoodkeyContext _context;

        public WarehouseRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Warehouse>> GetAllAsync()
        {
            return await _context.Warehouse
                .Include(w => w.Country)
                .Include(w => w.Province)
                .Include(w => w.WarehouseType)
                .Include(w => w.ContactPerson)
                .Include(w => w.CreatedBy)
                .Include(w => w.UpdatedBy)
                .ToListAsync();
        }

        public async Task<Warehouse> GetByIdAsync(int id)
        {
            return await _context.Warehouse
                .Include(w => w.Country)
                .Include(w => w.Province)
                .Include(w => w.WarehouseType)
                .Include(w => w.ContactPerson)
                .Include(w => w.CreatedBy)
                .Include(w => w.UpdatedBy)
                .FirstOrDefaultAsync(w => w.WarehouseId == id);
        }

        public async Task AddAsync(Warehouse warehouse)
        {
            await _context.Warehouse.AddAsync(warehouse);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Warehouse warehouse)
        {
            _context.Warehouse.Update(warehouse);
            await _context.SaveChangesAsync();
        }

        public IEnumerable<WarehouseTypes> GetAll()
        {
            return _context.WarehouseTypes;
        }

    }

}
