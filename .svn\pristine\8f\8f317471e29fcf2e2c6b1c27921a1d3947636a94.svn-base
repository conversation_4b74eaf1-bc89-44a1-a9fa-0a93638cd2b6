﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Offering
    {
        public Offering()
        {
            OfferingAddOnAddOnOffering = new HashSet<OfferingAddOn>();
            OfferingAddOnOffering = new HashSet<OfferingAddOn>();
            OfferingProperty = new HashSet<OfferingProperty>();
            OfferingRate = new HashSet<OfferingRate>();
            OfferingTax = new HashSet<OfferingTax>();
        }

        public int Id { get; set; }
        public int? CategoryId { get; set; }
        public int? GroupTypeId { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string SupplierItemNumber { get; set; }
        public string PublicDescription { get; set; }
        public string InternalDescription { get; set; }
        public int? DisplayOrder { get; set; }
        public int? UnitChargedId { get; set; }
        public bool? IsUnitTypeEach { get; set; }
        public bool? IsAddOn { get; set; }
        public bool? IsForSmOnly { get; set; }
        public bool? IsInternalOnly { get; set; }
        public string Image { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsObsolete { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }

        public virtual Category Category { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual GroupType GroupType { get; set; }
        public virtual Unit UnitCharged { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<OfferingAddOn> OfferingAddOnAddOnOffering { get; set; }
        public virtual ICollection<OfferingAddOn> OfferingAddOnOffering { get; set; }
        public virtual ICollection<OfferingProperty> OfferingProperty { get; set; }
        public virtual ICollection<OfferingRate> OfferingRate { get; set; }
        public virtual ICollection<OfferingTax> OfferingTax { get; set; }
    }
}