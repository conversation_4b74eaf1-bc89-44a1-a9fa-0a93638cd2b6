import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import DepartmentQuery from '@/services/queries/DepartmentQuery';
import DepartmentTable from './department/components/department_table';

export const metadata: Metadata = {
  title: 'Goodkey | Department',
};

export default async function Cluster() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: DepartmentQuery.tags,
    queryFn: () => DepartmentQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Department',
          link: '/dashboard/setup/master-setup/department',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <DepartmentTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
