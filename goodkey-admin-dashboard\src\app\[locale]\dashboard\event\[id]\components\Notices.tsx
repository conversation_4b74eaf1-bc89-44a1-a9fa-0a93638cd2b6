import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Clock } from 'lucide-react';
import UnderDevelopment from './UnderDevelopment';
import { format } from 'date-fns';
import { ShowGeneralInfoData } from '../../../setup/list-of-shows/components/show-tabs/GeneralInfoTab';

export default function Notices({ show }: { show: ShowGeneralInfoData }) {
  if (!show) {
    return <UnderDevelopment sectionName="Notices" />;
  }

  const currentDate = new Date();
  const formattedCurrentDate = currentDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });

  const formattedCurrentTime = currentDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle>Notices</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-3 bg-green-50 text-green-700 rounded-md">
            <p className="font-medium">Online Ordering is now Open!</p>
          </div>
          <div>
            <p>
              <span className="font-medium">Order Deadline Date:</span>{' '}
              {show.orderDeadlineDate
                ? format(show.orderDeadlineDate, 'PPP')
                : 'N/A'}
            </p>
            <p className="text-sm text-slate-500">
              (After this date a {show.lateChargePercentage}% surcharge will be
              added)
            </p>
          </div>
          <div className="flex items-center gap-1 text-slate-500">
            <Clock className="h-4 w-4" />
            <span>
              System time now: {formattedCurrentDate} {formattedCurrentTime}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
