using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Infrastructure.Utils;
using goodkey_cms.DTO.ExhibitorImport;
using goodkey_common.Repositories;
using goodkey_common.Services;
using goodkey_common.Models;
using goodkey_common.DTO;
using goodkey_cms.DTO.ShowExhibitor;
using goodkey_cms.Services;
using goodkey_cms.DTO.User;
using goodkey_cms.Repositories;

namespace goodkey_cms.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class ExhibitorImportController : ControllerBase
    {
        private readonly IExhibitorImportRepository _importRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IShowExhibitorRepository _exhibitorRepository;
        private readonly IExcelImportService _excelService;
        private readonly goodkey_cms.Services.StorageService _storageService;
        private readonly IUserRepository _userRepository;
        private readonly AuthService _authService;
        private readonly IFixSuggestionService _fixSuggestionService;
        private readonly ICountryRepository _countryRepository;
        private readonly IProvinceRepository _provinceRepository;

        public ExhibitorImportController(
            IExhibitorImportRepository importRepository,
            ICompanyRepository companyRepository,
            IShowExhibitorRepository exhibitorRepository,
            IExcelImportService excelService,
            goodkey_cms.Services.StorageService storageService,
            IUserRepository userRepository,
            AuthService authService,
            IFixSuggestionService fixSuggestionService,
            ICountryRepository countryRepository,
            IProvinceRepository provinceRepository)
        {
            _importRepository = importRepository;
            _companyRepository = companyRepository;
            _exhibitorRepository = exhibitorRepository;
            _excelService = excelService;
            _storageService = storageService;
            _userRepository = userRepository;
            _authService = authService;
            _fixSuggestionService = fixSuggestionService;
            _countryRepository = countryRepository;
            _provinceRepository = provinceRepository;
        }

        // =====================================================
        // Phase 1: Upload and Validation
        // =====================================================

        /// <summary>
        /// Upload Excel file and validate exhibitor data
        /// </summary>
        [HttpPost("validate")]
        public GenericRespond<ExhibitorImportValidationResponseDto> ValidateExcelFile([FromForm] ExhibitorImportUploadDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            if (dto.ExcelFile == null || dto.ExcelFile.Length == 0)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Excel file is required"
                };
            }

            // Validate file type
            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(dto.ExcelFile.FileName).ToLower();
            if (!allowedExtensions.Contains(fileExtension))
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Only Excel files (.xlsx, .xls) are allowed"
                };
            }

            // Validate file size (max 10MB)
            if (dto.ExcelFile.Length > 10 * 1024 * 1024)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "File size cannot exceed 10MB"
                };
            }


            // Save file temporarily
            var fileName = $"import_{Guid.NewGuid()}{fileExtension}";
            var uploadResult = _storageService.UploadFile(dto.ExcelFile, goodkey_cms.Services.FileType.Document, "imports", goodkey_cms.Services.Visibility.Protected, true, fileName);
            var filePath = uploadResult.Path;

            // Create import session
            var session = _importRepository.CreateSession(
                dto.ShowId,
                fileName,
                dto.ExcelFile.FileName,
                filePath,
                dto.ExcelFile.Length,
                dto.ExcelFile.ContentType,
                user.UserId
            );

            // Read Excel file
            List<goodkey_common.DTO.ExcelRowData> excelRows;
            using (var stream = dto.ExcelFile.OpenReadStream())
            {
                excelRows = _excelService.ReadExcelFile(stream, dto.ExcelFile.FileName);
            }

            if (!excelRows.Any())
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Excel file contains no data rows"
                };
            }

            // Process and validate each row
            var validationMessages = new List<ExhibitorImportValidationMessageDto>();
            var duplicates = new List<ExhibitorImportDuplicateDto>();
            var importRows = new List<ExhibitorImportRowDto>();

            foreach (var excelRow in excelRows)
            {
                // Create import row record
                var rowId = _importRepository.CreateImportRow(
                    session.SessionId,
                    excelRow.RowNumber,
                    excelRow.CompanyName,
                    excelRow.CompanyPhone,
                    excelRow.CompanyEmail,
                    excelRow.CompanyAddress1,
                    excelRow.CompanyAddress2,
                    excelRow.CompanyCity,
                    excelRow.CompanyProvince,
                    excelRow.CompanyPostalCode,
                    excelRow.CompanyCountry,
                    excelRow.CompanyWebsite,
                    excelRow.ContactFirstName,
                    excelRow.ContactLastName,
                    excelRow.ContactEmail,
                    excelRow.ContactPhone,
                    excelRow.ContactMobile,
                    excelRow.ContactExt,
                    excelRow.BoothNumbers,
                    excelRow.ContactType
                );

                // Validate row data
                var rowMessages = _excelService.ValidateRowData(excelRow, excelRow.RowNumber);

                // Convert to the expected DTO type
                var convertedMessages = rowMessages.Select(m => new ExhibitorImportValidationMessageDto
                {
                    RowNumber = m.RowNumber,
                    FieldName = m.FieldName,
                    FieldValue = m.FieldValue,
                    MessageType = m.MessageType,
                    ValidationRule = m.ValidationRule,
                    MessageCode = m.MessageCode,
                    Message = m.Message
                }).ToList();

                validationMessages.AddRange(convertedMessages);

                // Store validation messages
                foreach (var message in convertedMessages)
                {
                    _importRepository.CreateValidationMessage(
                        session.SessionId,
                        rowId,
                        message.RowNumber,
                        message.FieldName,
                        message.FieldValue,
                        message.MessageType,
                        message.ValidationRule,
                        message.MessageCode,
                        message.Message
                    );
                }

                // Resolve companies and contact types
                var resolvedCompany = ResolveCompany(excelRow.CompanyName);
                var resolvedContactType = _importRepository.FindContactTypeByText(excelRow.ContactType);
                var resolvedBoothNumbers = _excelService.ParseBoothNumbers(excelRow.BoothNumbers);

                // Update row with resolved values
                _importRepository.UpdateRowResolvedValues(
                    rowId,
                    resolvedCompany?.CompanyId,
                    resolvedContactType?.Id,
                    resolvedBoothNumbers.ToArray(),
                    resolvedCompany == null,
                    true, // Always create new contact for imports
                    false // Will be set during duplicate detection
                );

                // Create DTO for response
                var importRow = new ExhibitorImportRowDto
                {
                    RowNumber = excelRow.RowNumber,
                    Status = convertedMessages.Any(m => m.MessageType == "Error") ? "Error" :
                            convertedMessages.Any(m => m.MessageType == "Warning") ? "Warning" : "Valid",
                    CompanyName = excelRow.CompanyName,
                    CompanyPhone = excelRow.CompanyPhone,
                    CompanyEmail = excelRow.CompanyEmail,
                    CompanyAddress1 = excelRow.CompanyAddress1,
                    CompanyAddress2 = excelRow.CompanyAddress2,
                    CompanyCity = excelRow.CompanyCity,
                    CompanyProvince = excelRow.CompanyProvince,
                    CompanyPostalCode = excelRow.CompanyPostalCode,
                    CompanyCountry = excelRow.CompanyCountry,
                    CompanyWebsite = excelRow.CompanyWebsite,
                    ContactFirstName = excelRow.ContactFirstName,
                    ContactLastName = excelRow.ContactLastName,
                    ContactEmail = excelRow.ContactEmail,
                    ContactPhone = excelRow.ContactPhone,
                    ContactMobile = excelRow.ContactMobile,
                    ContactExt = excelRow.ContactExt,
                    BoothNumbers = excelRow.BoothNumbers,
                    ContactType = excelRow.ContactType,
                    ResolvedCompanyId = resolvedCompany?.CompanyId,
                    ResolvedCompanyName = resolvedCompany?.CompanyName,
                    ResolvedContactTypeId = resolvedContactType?.Id,
                    ResolvedContactTypeName = resolvedContactType?.Name,
                    ResolvedBoothNumbers = resolvedBoothNumbers,
                    IsNewCompany = resolvedCompany == null,
                    IsNewContact = true,
                    HasErrors = convertedMessages.Any(m => m.MessageType == "Error"),
                    HasWarnings = convertedMessages.Any(m => m.MessageType == "Warning"),
                    ErrorCount = convertedMessages.Count(m => m.MessageType == "Error"),
                    WarningCount = convertedMessages.Count(m => m.MessageType == "Warning")
                };

                importRows.Add(importRow);

                // Update row validation results
                _importRepository.UpdateRowValidationResults(
                    rowId,
                    importRow.Status,
                    importRow.HasErrors,
                    importRow.HasWarnings,
                    importRow.ErrorCount,
                    importRow.WarningCount
                );
            }

            // Detect duplicates
            duplicates = DetectDuplicates(session.SessionId, importRows);

            // Calculate summary
            var errorRows = importRows.Count(r => r.HasErrors);
            var warningRows = importRows.Count(r => r.HasWarnings && !r.HasErrors);
            var validRows = importRows.Count - errorRows;
            var canProceed = errorRows == 0;

            // Update session with counts
            _importRepository.UpdateSessionCounts(session.SessionId, importRows.Count, validRows, errorRows, warningRows);
            _importRepository.UpdateSessionStatus(session.SessionId, "Validated", canProceed);

            var summary = new ExhibitorImportSummaryDto
            {
                TotalRows = importRows.Count,
                ValidRows = validRows,
                ErrorRows = errorRows,
                WarningRows = warningRows,
                NewCompanies = importRows.Count(r => r.IsNewCompany),
                ExistingCompanies = importRows.Count(r => !r.IsNewCompany),
                NewContacts = importRows.Count(r => r.IsNewContact),
                DuplicateEmails = duplicates.Count(d => d.DuplicateType == "Email"),
                DuplicateBooths = 0 // Booth duplicate detection removed
            };

            var response = new ExhibitorImportValidationResponseDto
            {
                SessionId = session.SessionId.ToString(),
                ShowId = dto.ShowId,
                FileName = dto.ExcelFile.FileName,
                Summary = summary,
                Rows = importRows,
                ValidationMessages = validationMessages,
                Duplicates = duplicates,
                CanProceed = canProceed,
                ExpiresAt = session.ExpiresAt
            };

            return new GenericRespond<ExhibitorImportValidationResponseDto>
            {
                Data = response,
                StatusCode = 200,
                Message = canProceed ? "Validation completed successfully" : "Validation completed with errors"
            };

        }

        /// <summary>
        /// Resolve a duplicate conflict by user selection
        /// </summary>
        [HttpPost("resolve-duplicate")]
        public GenericRespond<DuplicateResolutionResponseDto> ResolveDuplicate([FromBody] DuplicateResolutionRequestDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<DuplicateResolutionResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<DuplicateResolutionResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var session = _importRepository.GetSession(dto.SessionId);
            if (session == null)
            {
                return new GenericRespond<DuplicateResolutionResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            // Apply the user's resolution choice
            var result = ApplyDuplicateResolution(dto);

            return new GenericRespond<DuplicateResolutionResponseDto>
            {
                StatusCode = 200,
                Message = "Duplicate resolved successfully",
                Data = result
            };
        }

        // =====================================================
        // Helper Methods
        // =====================================================

        private Company? ResolveCompany(string companyName)
        {
            if (string.IsNullOrWhiteSpace(companyName))
                return null;

            var companies = _importRepository.FindCompaniesByName(companyName);

            // Try exact match first
            var exactMatch = companies.FirstOrDefault(c =>
                c.CompanyName.Equals(companyName, StringComparison.OrdinalIgnoreCase));

            if (exactMatch != null)
                return exactMatch;

            // Try partial match with high similarity
            var partialMatch = companies.FirstOrDefault(c =>
                c.CompanyName.ToLower().Contains(companyName.ToLower()) ||
                companyName.ToLower().Contains(c.CompanyName.ToLower()));

            return partialMatch;
        }

        private List<ExhibitorImportDuplicateDto> DetectDuplicates(Guid sessionId, List<ExhibitorImportRowDto> rows)
        {
            var duplicates = new List<ExhibitorImportDuplicateDto>();

            // First, check each email against existing database records (this is the main focus)
            foreach (var row in rows.Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail)))
            {
                var existingContacts = _importRepository.FindContactsByEmail(row.ContactEmail);
                if (existingContacts.Any())
                {
                    var conflictDetails = new List<DuplicateConflictDetail>();

                    // Compare import row with each existing contact
                    foreach (var existingContact in existingContacts)
                    {
                        conflictDetails.AddRange(GenerateExistingContactConflictsForDetection(row, existingContact));
                    }

                    // Only create a duplicate if there are actual conflicts
                    if (conflictDetails.Any())
                    {
                        var resolutionOptions = new List<DuplicateResolutionOption>();

                        // Only one option for database conflicts: field-by-field comparison
                        resolutionOptions.Add(new DuplicateResolutionOption
                        {
                            OptionType = "FieldByField",
                            OptionLabel = "Compare and choose field values",
                            OptionDescription = "Choose which values to keep for each field"
                        });

                        var duplicate = new ExhibitorImportDuplicateDto
                        {
                            DuplicateType = "DatabaseConflict",
                            DuplicateValue = row.ContactEmail,
                            RowNumbers = new List<int> { row.RowNumber },
                            ConflictResolution = "Manual",
                            ConflictDetails = conflictDetails,
                            ResolutionOptions = resolutionOptions,
                            RequiresUserDecision = true,
                            ConflictDescription = $"Email {row.ContactEmail} already exists in database with different information"
                        };
                        duplicates.Add(duplicate);

                        // Store in database
                        _importRepository.CreateDuplicate(
                            sessionId,
                            "DatabaseConflict",
                            row.ContactEmail,
                            row.RowNumber.ToString(),
                            row.RowNumber.ToString(),
                            "Manual"
                        );
                    }
                    // If records are identical, no duplicate is created - import proceeds silently
                }
            }

            // Then, check for duplicates within the Excel file itself
            var emailGroups = rows
                .Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail))
                .GroupBy(r => r.ContactEmail.ToLower())
                .Where(g => g.Count() > 1);

            // Also check for emails that have existing user accounts (even if not duplicated in import)
            var emailsWithUserAccounts = rows
                .Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail))
                .Where(r => _userRepository.GetAll().Any(u => u.VerificationEmail == r.ContactEmail))
                .GroupBy(r => r.ContactEmail.ToLower())
                .Where(g => !emailGroups.Any(eg => eg.Key == g.Key)); // Don't duplicate if already in emailGroups

            foreach (var group in emailGroups)
            {
                var rowNumbers = group.Select(r => r.RowNumber).ToList();
                var conflictDetails = new List<DuplicateConflictDetail>();
                var resolutionOptions = new List<DuplicateResolutionOption>();

                // Check for differences in contact information and against existing contacts
                var firstRow = group.First();
                var existingContacts = _importRepository.FindContactsByEmail(group.Key);

                // Compare within import rows
                foreach (var row in group.Skip(1))
                {
                    if (row.ContactFirstName != firstRow.ContactFirstName)
                    {
                        conflictDetails.Add(new DuplicateConflictDetail
                        {
                            RowNumber = row.RowNumber,
                            FieldName = "ContactFirstName",
                            CurrentValue = row.ContactFirstName,
                            ConflictingValue = firstRow.ContactFirstName
                        });
                    }
                    if (row.ContactLastName != firstRow.ContactLastName)
                    {
                        conflictDetails.Add(new DuplicateConflictDetail
                        {
                            RowNumber = row.RowNumber,
                            FieldName = "ContactLastName",
                            CurrentValue = row.ContactLastName,
                            ConflictingValue = firstRow.ContactLastName
                        });
                    }
                    if (row.ContactPhone != firstRow.ContactPhone)
                    {
                        conflictDetails.Add(new DuplicateConflictDetail
                        {
                            RowNumber = row.RowNumber,
                            FieldName = "ContactPhone",
                            CurrentValue = row.ContactPhone,
                            ConflictingValue = firstRow.ContactPhone
                        });
                    }
                    if (row.ContactMobile != firstRow.ContactMobile)
                    {
                        conflictDetails.Add(new DuplicateConflictDetail
                        {
                            RowNumber = row.RowNumber,
                            FieldName = "ContactMobile",
                            CurrentValue = row.ContactMobile,
                            ConflictingValue = firstRow.ContactMobile
                        });
                    }
                    if (row.CompanyName != firstRow.CompanyName)
                    {
                        conflictDetails.Add(new DuplicateConflictDetail
                        {
                            RowNumber = row.RowNumber,
                            FieldName = "CompanyName",
                            CurrentValue = row.CompanyName,
                            ConflictingValue = firstRow.CompanyName
                        });
                    }
                }

                // Compare against existing contacts in database using enhanced comparison
                foreach (var existingContact in existingContacts)
                {
                    conflictDetails.AddRange(GenerateExistingContactConflictsForDetection(firstRow, existingContact));
                }

                // Check against existing contacts in database (already declared above)
                foreach (var existingContact in existingContacts)
                {
                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "UseExisting",
                        OptionLabel = $"Use existing contact: {existingContact.FirstName} {existingContact.LastName}",
                        OptionDescription = $"Phone: {existingContact.Telephone}, Company: {existingContact.Company?.CompanyName ?? "N/A"}",
                        ExistingRecordId = existingContact.ContactId
                    });
                }

                // Check for existing user account
                var existingUser = _userRepository.GetAll().FirstOrDefault(u => u.VerificationEmail == group.Key);
                if (existingUser != null)
                {
                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "UpdateContactInfo",
                        OptionLabel = "Update contact information",
                        OptionDescription = $"Update existing user account contact info for {existingUser.FirstName} {existingUser.LastName}",
                        ExistingRecordId = existingUser.UserId
                    });

                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "KeepExistingInfo",
                        OptionLabel = "Keep existing information",
                        OptionDescription = $"Keep current contact info for {existingUser.FirstName} {existingUser.LastName} and link to exhibitor",
                        ExistingRecordId = existingUser.UserId
                    });
                }

                // Add field-by-field comparison option
                resolutionOptions.Add(new DuplicateResolutionOption
                {
                    OptionType = "CompareAndUpdate",
                    OptionLabel = "Compare and choose field values",
                    OptionDescription = "Show field-by-field comparison and choose which values to keep for updating existing contact"
                });

                resolutionOptions.Add(new DuplicateResolutionOption
                {
                    OptionType = "Skip",
                    OptionLabel = "Skip these rows",
                    OptionDescription = "Skip importing these duplicate email rows"
                });

                var duplicate = new ExhibitorImportDuplicateDto
                {
                    DuplicateType = "Email",
                    DuplicateValue = group.Key,
                    RowNumbers = rowNumbers,
                    ConflictResolution = "Manual",
                    ConflictDetails = conflictDetails,
                    ResolutionOptions = resolutionOptions,
                    RequiresUserDecision = conflictDetails.Any() || existingContacts.Any(),
                    ConflictDescription = conflictDetails.Any()
                        ? $"Email {group.Key} appears in {rowNumbers.Count} rows with different contact information"
                        : existingContacts.Any()
                        ? $"Email {group.Key} already exists in database"
                        : $"Email {group.Key} appears in {rowNumbers.Count} rows"
                };
                duplicates.Add(duplicate);

                // Store in database
                _importRepository.CreateDuplicate(
                    sessionId,
                    "Email",
                    group.Key,
                    string.Join(",", rowNumbers),
                    string.Join(",", rowNumbers),
                    "Manual"
                );
            }

            // Handle emails with existing user accounts (not already processed as duplicates)
            foreach (var group in emailsWithUserAccounts)
            {
                var rowNumbers = group.Select(r => r.RowNumber).ToList();
                var conflictDetails = new List<DuplicateConflictDetail>();
                var resolutionOptions = new List<DuplicateResolutionOption>();

                var existingUser = _userRepository.GetAll().FirstOrDefault(u => u.VerificationEmail == group.Key);
                if (existingUser != null)
                {
                    conflictDetails.Add(new DuplicateConflictDetail
                    {
                        RowNumber = group.First().RowNumber,
                        FieldName = "Email",
                        CurrentValue = group.Key,
                        ConflictingValue = $"Existing user: {existingUser.FirstName} {existingUser.LastName}"
                    });

                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "UpdateContactInfo",
                        OptionLabel = "Update contact information",
                        OptionDescription = $"Update existing user account contact info for {existingUser.FirstName} {existingUser.LastName}",
                        ExistingRecordId = existingUser.UserId
                    });

                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "KeepExistingInfo",
                        OptionLabel = "Keep existing information",
                        OptionDescription = $"Keep current contact info for {existingUser.FirstName} {existingUser.LastName} and link to exhibitor",
                        ExistingRecordId = existingUser.UserId
                    });

                    resolutionOptions.Add(new DuplicateResolutionOption
                    {
                        OptionType = "Skip",
                        OptionLabel = "Skip this row",
                        OptionDescription = "Do not import this contact"
                    });

                    var duplicate = new ExhibitorImportDuplicateDto
                    {
                        DuplicateType = "UserAccount",
                        DuplicateValue = group.Key,
                        RowNumbers = rowNumbers,
                        ConflictResolution = "Manual",
                        ConflictDetails = conflictDetails,
                        ResolutionOptions = resolutionOptions,
                        RequiresUserDecision = true,
                        ConflictDescription = $"Email {group.Key} already has a user account. Choose to update contact info or keep existing."
                    };
                    duplicates.Add(duplicate);

                    // Store in database
                    _importRepository.CreateDuplicate(
                        sessionId,
                        "UserAccount",
                        group.Key,
                        string.Join(",", rowNumbers),
                        string.Join(",", rowNumbers),
                        "Manual"
                    );
                }
            }

            // Booth number duplicate detection removed - booth numbers can be duplicated

            // Company duplicates (same company name, different contacts)
            var companyGroups = rows
                .GroupBy(r => r.CompanyName.ToLower())
                .Where(g => g.Count() > 1);

            foreach (var group in companyGroups)
            {
                var rowNumbers = group.Select(r => r.RowNumber).ToList();
                var duplicate = new ExhibitorImportDuplicateDto
                {
                    DuplicateType = "Company",
                    DuplicateValue = group.Key,
                    RowNumbers = rowNumbers,
                    ConflictResolution = "Merge" // Default to merge for same company
                };
                duplicates.Add(duplicate);

                // Store in database
                _importRepository.CreateDuplicate(
                    sessionId,
                    "Company",
                    group.Key,
                    string.Join(",", rowNumbers),
                    string.Join(",", rowNumbers),
                    "Merge"
                );
            }

            return duplicates;
        }

        private DuplicateResolutionResponseDto ApplyDuplicateResolution(DuplicateResolutionRequestDto dto)
        {
            var response = new DuplicateResolutionResponseDto
            {
                Success = false,
                UpdatedRowNumbers = new List<int>()
            };

            // Only support FieldByField resolution for simplicity
            if (dto.ResolutionType == "FieldByField")
            {
                // Store field-by-field resolution choices for updating existing contact
                foreach (var rowNumber in dto.AffectedRowNumbers)
                {
                    var rows = _importRepository.GetSessionRows(dto.SessionId);
                    var row = rows.FirstOrDefault(r => r.RowNumber == rowNumber);
                    if (row != null)
                    {
                        // Mark for field-by-field update by setting a special status
                        _importRepository.UpdateRowValidationResults(row.Id, "UpdateExisting", false, false, 0, 0);

                        // Store the existing contact ID that should be updated
                        if (dto.SelectedExistingId.HasValue)
                        {
                            // Store the existing contact ID in a way that can be retrieved during import
                            // We'll use the CreatedContactId field temporarily to store the ID of the contact to update
                            _importRepository.UpdateRowProcessingResults(row.Id, null, dto.SelectedExistingId.Value, null, null);
                        }

                        // Apply field resolution choices directly to the row data
                        foreach (var fieldResolution in dto.FieldResolutions)
                        {
                            var fieldName = fieldResolution.Key;
                            var selectedValue = fieldResolution.Value.SelectedValue;

                            // Debug logging
                            Console.WriteLine($"Applying field resolution: {fieldName} = {selectedValue}");

                            // Update the actual field value with the user's choice
                            _importRepository.UpdateRowFieldValue(row.Id, fieldName, selectedValue);
                        }

                        response.UpdatedRowNumbers.Add(rowNumber);
                    }
                }
                response.ResolvedToRecordId = dto.SelectedExistingId;
                response.ResolutionAction = "Will update existing contact with chosen field values";
            }

            // Update duplicate resolution in database
            var duplicates = _importRepository.GetSessionDuplicates(dto.SessionId);
            var duplicate = duplicates.FirstOrDefault(d =>
                d.DuplicateType == dto.DuplicateType &&
                d.DuplicateValue == dto.DuplicateValue);

            if (duplicate != null)
            {
                _importRepository.UpdateDuplicateResolution(duplicate.Id, dto.ResolutionType);
            }

            response.Success = true;
            response.Message = $"Duplicate resolved: {response.ResolutionAction}";

            return response;
        }

        // =====================================================
        // Phase 2: Execution
        // =====================================================

        /// <summary>
        /// Execute the import after validation
        /// </summary>
        [HttpPost("execute")]
        public GenericRespond<ExhibitorImportExecutionResponseDto> ExecuteImport([FromBody] ExhibitorImportExecuteDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var session = _importRepository.GetSessionBySessionId(dto.SessionId);
            if (session == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            if (session.Status != "Validated" || !session.CanProceed)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session is not ready for execution"
                };
            }

            if (session.ExpiresAt < DateTime.Now)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session has expired"
                };
            }

            // Start execution
            _importRepository.SetSessionExecutionStart(session.SessionId, user.UserId);

            var rows = _importRepository.GetSessionRows(session.SessionId);
            var results = new List<ExhibitorImportExecutionResultDto>();
            var summary = new ExhibitorImportExecutionSummaryDto
            {
                TotalRows = rows.Count
            };

            foreach (var row in rows.Where(r => r.Status == "Valid" || r.Status == "Warning"))
            {
                var result = ProcessImportRow(row, dto.SendEmailInvites, user.Username);
                results.Add(result);

                // Update summary based on result
                if (result.Status == "Processed")
                {
                    summary.ProcessedRows++;
                    if (result.CreatedCompanyId.HasValue) summary.CreatedCompanies++;
                    if (result.CreatedContactId.HasValue) summary.CreatedContacts++;
                    if (result.CreatedExhibitorId.HasValue) summary.CreatedExhibitors++;
                    if (result.CreatedUserId.HasValue) summary.CreatedUsers++;
                    if (result.EmailInviteSent) summary.EmailInvitesSent++;
                }
                else if (result.Status == "Skipped")
                {
                    summary.SkippedRows++;
                }
                else
                {
                    summary.FailedRows++;
                }
            }

            // Complete execution
            _importRepository.SetSessionExecutionComplete(session.SessionId);

            var response = new ExhibitorImportExecutionResponseDto
            {
                SessionId = dto.SessionId,
                Status = "Completed",
                Summary = summary,
                Results = results,
                CompletedAt = DateTime.Now
            };

            return new GenericRespond<ExhibitorImportExecutionResponseDto>
            {
                Data = response,
                StatusCode = 200,
                Message = $"Import completed successfully. Processed {summary.ProcessedRows} of {summary.TotalRows} rows."
            };
        }

        // =====================================================
        // Row Processing Method
        // =====================================================

        private ExhibitorImportExecutionResultDto ProcessImportRow(ExhibitorImportRows row, bool sendEmailInvites, string username)
        {
            var result = new ExhibitorImportExecutionResultDto
            {
                RowNumber = row.RowNumber,
                CompanyName = row.CompanyName,
                ContactName = $"{row.ContactFirstName} {row.ContactLastName}".Trim(),
                ProcessedAt = DateTime.Now,
                CreationAttempts = new List<CreationAttemptResult>()
            };


            // Check if row should be skipped due to duplicate resolution
            if (row.Status == "Skipped")
            {
                result.Status = "Skipped";
                result.SkipReason = "Row marked as skipped during duplicate resolution";
                return result;
            }

            // Check if this row should update an existing contact (from field-by-field duplicate resolution)
            if (row.Status == "UpdateExisting" && row.CreatedContactId.HasValue)
            {
                return ProcessExistingContactUpdate(row, result, username);
            }

            // Step 1: Create or get company
            int companyId;
            var companyAttempt = new CreationAttemptResult { RecordType = "Company" };
            result.CreationAttempts.Add(companyAttempt);

            if (row.ResolvedCompanyId.HasValue)
            {
                companyId = row.ResolvedCompanyId.Value;
                companyAttempt.WasExisting = true;
                companyAttempt.RecordId = companyId;
                companyAttempt.Action = "Used Existing";
                companyAttempt.Details["CompanyName"] = row.CompanyName;
            }
            else
            {
                // Create new company with field values (already resolved from duplicate resolution)
                var success = _companyRepository.Add(
                    row.CompanyName,
                    row.CompanyPhone ?? "", // phone
                    row.CompanyEmail ?? "", // email
                    row.CompanyAddress1 ?? "", // address1
                    row.CompanyAddress2 ?? "", // address2
                    row.CompanyPostalCode ?? "", // postalCode
                    row.CompanyCity ?? "", // city
                    ResolveProvinceId(row.CompanyProvince) ?? 1,  // provinceId
                    ResolveCountryId(row.CompanyCountry) ?? 1,  // countryId
                    row.CompanyWebsite ?? "", // websiteUrl
                    "", // accountNumber
                    "Exhibitor", // companyGroup
                    "", // note
                    false, // isArchived
                    username
                );

                if (!success)
                {
                    companyAttempt.WasCreated = false;
                    companyAttempt.FailureReason = "Company creation failed - database operation unsuccessful";
                    companyAttempt.Action = "Failed";
                    result.Status = "Failed";
                    result.ErrorMessage = "Failed to create company";
                    return result;
                }

                // Get the created company ID
                var companies = _importRepository.FindCompaniesByName(row.CompanyName);
                var createdCompany = companies.FirstOrDefault(c => c.CompanyName == row.CompanyName);
                if (createdCompany == null)
                {
                    companyAttempt.WasCreated = false;
                    companyAttempt.FailureReason = "Company was created but could not be retrieved from database";
                    companyAttempt.Action = "Failed";
                    result.Status = "Failed";
                    result.ErrorMessage = "Failed to retrieve created company";
                    return result;
                }

                companyId = createdCompany.CompanyId;
                result.CreatedCompanyId = companyId;
                companyAttempt.WasCreated = true;
                companyAttempt.RecordId = companyId;
                companyAttempt.Action = "Created";
                companyAttempt.Details["CompanyName"] = row.CompanyName;
            }

            // Step 2: Create contact
            var contactAttempt = new CreationAttemptResult { RecordType = "Contact" };
            result.CreationAttempts.Add(contactAttempt);

            // Create new contact with field values (already resolved from duplicate resolution)
            var contactId = _companyRepository.AddContact(
                row.ResolvedContactTypeId ?? 1,
                companyId,
                row.ContactFirstName ?? "",
                row.ContactLastName ?? "",
                row.ContactEmail ?? "",
                row.ContactPhone ?? "",
                row.ContactExt ?? "",
                row.ContactMobile ?? "",
                false,
                username
            );

            if (contactId.HasValue && contactId.Value > 0)
            {
                result.CreatedContactId = contactId;
                contactAttempt.WasCreated = true;
                contactAttempt.RecordId = contactId.Value;
                contactAttempt.Action = "Created";
                contactAttempt.Details["ContactEmail"] = row.ContactEmail;
                contactAttempt.Details["ContactName"] = $"{row.ContactFirstName} {row.ContactLastName}".Trim();
            }
            else
            {
                contactAttempt.WasCreated = false;
                contactAttempt.FailureReason = "Contact creation failed - database operation returned null or invalid ID";
                contactAttempt.Action = "Failed";
                result.Status = "Failed";
                result.ErrorMessage = "Failed to create contact";
                return result;
            }

            // Step 3: Handle user account based on resolution type
            int? userId = null;
            if (!string.IsNullOrWhiteSpace(row.ContactEmail))
            {
                // Check if we should use an existing user account
                var useExistingUserField = GetRowFieldValue(row, "UseExistingUser");
                var updateExistingUserField = GetRowFieldValue(row, "UpdateExistingUser");
                var existingUserIdField = GetRowFieldValue(row, "ExistingUserId");

                if (!string.IsNullOrEmpty(useExistingUserField) && useExistingUserField == "true" &&
                    !string.IsNullOrEmpty(existingUserIdField) && int.TryParse(existingUserIdField, out var existingUserId))
                {
                    // Use existing user account without changes
                    userId = existingUserId;
                    var userAttempt = new CreationAttemptResult
                    {
                        RecordType = "User",
                        WasExisting = true,
                        RecordId = existingUserId,
                        Action = "Used Existing",
                        Details = { ["Email"] = row.ContactEmail }
                    };
                    result.CreationAttempts.Add(userAttempt);
                }
                else if (!string.IsNullOrEmpty(updateExistingUserField) && updateExistingUserField == "true" &&
                         !string.IsNullOrEmpty(existingUserIdField) && int.TryParse(existingUserIdField, out var updateUserId))
                {
                    // Use existing user account (update functionality can be implemented later)
                    userId = updateUserId;
                    var userAttempt = new CreationAttemptResult
                    {
                        RecordType = "User",
                        WasExisting = true,
                        RecordId = updateUserId,
                        Action = "Used Existing (Update Requested)",
                        Details = { ["Email"] = row.ContactEmail, ["Note"] = "User chose to update contact info - functionality pending" }
                    };
                    result.CreationAttempts.Add(userAttempt);
                }
                else
                {
                    // Create new user account
                    // Generate unique verification email that will be used as username
                    var verificationEmail = GenerateUniqueVerificationEmail(row.ContactEmail);

                    // Use contact email directly as WorkEmail
                    var workEmail = row.ContactEmail;

                    // Handle phone numbers - use null for empty strings to avoid duplicate empty string conflicts
                    var workPhone = string.IsNullOrWhiteSpace(row.ContactPhone) ? null : row.ContactPhone;
                    var mobilePhone = string.IsNullOrWhiteSpace(row.ContactMobile) ? null : row.ContactMobile;

                    var userDto = new CreateUserDto
                    {
                        FirstName = row.ContactFirstName ?? "Contact",
                        LastName = row.ContactLastName ?? "User",
                        Email = row.ContactEmail,
                        WorkEmail = workEmail, // Use work email
                        VerificationEmail = row.ContactEmail, // Use contact email as verification email
                        WorkPhoneNumber = workPhone,
                        MobileNumber = mobilePhone,
                        StatusId = 1,
                        RoleId = 4, // Exhibitor role
                        SalutationId = 1,
                        DepartmentId = 1
                    };

                    userId = _userRepository.CreateUser(username, userDto);

                    if (userId.HasValue)
                    {
                        // Set password and link contact
                        var password = "blue";
                        var hashedPassword = HashUtility.HashPassword(password);
                        _userRepository.SetPassword(userId.Value, hashedPassword);

                        var contact = _companyRepository.GetContact(result.CreatedContactId.Value);
                        if (contact != null)
                        {
                            contact.Authuserid = userId.Value;
                            _companyRepository.UpdateContact(
                                result.CreatedContactId.Value,
                                contact.ContactTypeId,
                                contact.CompanyId,
                                contact.FirstName,
                                contact.LastName,
                                contact.Email,
                                contact.Telephone,
                                contact.Ext,
                                contact.Cellphone,
                                contact.IsArchived ?? false,
                                username
                            );
                        }

                        result.CreatedUserId = userId;

                        // Send invitation email if requested
                        if (sendEmailInvites)
                        {
                            var verificationToken = _userRepository.SetInvitationTokenForUser(userId.Value);
                            if (!string.IsNullOrEmpty(verificationToken))
                            {
                                _authService.SendInviteEmail(row.ContactEmail, verificationToken);
                                result.EmailInviteSent = true;
                            }
                        }
                    }
                    else
                    {
                        // Rollback: Delete contact if user creation failed
                        _companyRepository.DeleteContact(result.CreatedContactId.Value, username);
                        result.Status = "Failed";
                        result.ErrorMessage = "Failed to create user account for contact";
                        return result;
                    }
                }
            }

            // Step 4: Create exhibitor
            var exhibitor = new ShowExhibitors
            {
                ShowId = row.Session.ShowId,
                CompanyId = companyId,
                ContactId = result.CreatedContactId,
                BoothNumber = row.ResolvedBoothNumbersArray ?? Array.Empty<string>(),
                CreatedById = _userRepository.GetUserByUsername(username)?.UserId ?? 0
            };

            var createdExhibitor = _exhibitorRepository.CreateShowExhibitor(exhibitor);
            result.CreatedExhibitorId = createdExhibitor.Id;

            // Step 5: Update row with processing results
            _importRepository.UpdateRowProcessingResults(
                row.Id,
                result.CreatedCompanyId,
                result.CreatedContactId,
                result.CreatedExhibitorId,
                result.CreatedUserId
            );

            result.Status = "Processed";
            return result;
        }

        private string GenerateUniqueVerificationEmail(string email)
        {
            // Generate verification email from contact email
            var baseEmail = email.Split('@')[0]; // Get part before @
            var verificationEmail = $"{baseEmail}@verification.local";

            // Check if it already exists
            if (!_userRepository.GetAll().Any(u => u.VerificationEmail == verificationEmail))
            {
                return verificationEmail;
            }

            // If it exists, try with sequence numbers
            for (int sequence = 1; sequence <= 999; sequence++)
            {
                var candidateEmail = $"{baseEmail}_{sequence}@verification.local";

                if (!_userRepository.GetAll().Any(u => u.VerificationEmail == candidateEmail))
                {
                    return candidateEmail;
                }
            }

            // If all sequences are taken, use GUID as fallback
            return $"{baseEmail}_{Guid.NewGuid().ToString().Substring(0, 8)}@verification.local";
        }

        private string GetOriginalFieldValue(ExhibitorImportRows row, string fieldName)
        {
            return fieldName.ToLower() switch
            {
                "companyname" => row.CompanyName,
                "contactfirstname" => row.ContactFirstName,
                "contactlastname" => row.ContactLastName,
                "contactemail" => row.ContactEmail,
                "contactphone" => row.ContactPhone,
                "contactmobile" => row.ContactMobile,
                "contactext" => row.ContactExt,
                "boothnumbers" => row.BoothNumbers,
                "contacttype" => row.ContactType,
                _ => ""
            };
        }

        private string GetRowFieldValue(ExhibitorImportRows row, string fieldName)
        {
            // This method gets custom field values that might be set during duplicate resolution
            // For now, we'll use a simple approach - in a full implementation,
            // you might store these in a separate table or JSON field
            return fieldName.ToLower() switch
            {
                "existingcontactid" => "", // Would be stored in custom fields
                "forcecreatenew" => "",
                "mergedintorow" => "",
                _ => ""
            };
        }

        private goodkey_common.DTO.ExcelRowData ConvertToExcelRowData(ExhibitorImportRows row)
        {
            return new goodkey_common.DTO.ExcelRowData
            {
                RowNumber = row.RowNumber,
                CompanyName = row.CompanyName,
                CompanyPhone = row.CompanyPhone,
                CompanyEmail = row.CompanyEmail,
                CompanyAddress1 = row.CompanyAddress1,
                CompanyAddress2 = row.CompanyAddress2,
                CompanyCity = row.CompanyCity,
                CompanyProvince = row.CompanyProvince,
                CompanyPostalCode = row.CompanyPostalCode,
                CompanyCountry = row.CompanyCountry,
                CompanyWebsite = row.CompanyWebsite,
                ContactFirstName = row.ContactFirstName,
                ContactLastName = row.ContactLastName,
                ContactEmail = row.ContactEmail,
                ContactPhone = row.ContactPhone,
                ContactMobile = row.ContactMobile,
                ContactExt = row.ContactExt,
                BoothNumbers = row.BoothNumbers,
                ContactType = row.ContactType
            };
        }

        private int? ResolveProvinceId(string provinceName)
        {
            return _provinceRepository.FindProvince(provinceName);
        }

        private int? ResolveCountryId(string countryName)
        {
            return _countryRepository.FindCountry(countryName);
        }

        // Helper methods for field-by-field conflict detection and resolution
        private List<DuplicateConflictDetailDto> GenerateFieldConflicts(List<ExhibitorImportRowDto> duplicateRows)
        {
            var conflicts = new List<DuplicateConflictDetailDto>();
            var firstRow = duplicateRows.First();

            foreach (var row in duplicateRows.Skip(1))
            {
                // Company field conflicts
                AddFieldConflict(conflicts, row.RowNumber, "CompanyName", row.CompanyName, firstRow.CompanyName);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyPhone", row.CompanyPhone, firstRow.CompanyPhone);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyEmail", row.CompanyEmail, firstRow.CompanyEmail);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyAddress1", row.CompanyAddress1, firstRow.CompanyAddress1);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyAddress2", row.CompanyAddress2, firstRow.CompanyAddress2);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyCity", row.CompanyCity, firstRow.CompanyCity);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyProvince", row.CompanyProvince, firstRow.CompanyProvince);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyPostalCode", row.CompanyPostalCode, firstRow.CompanyPostalCode);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyCountry", row.CompanyCountry, firstRow.CompanyCountry);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyWebsite", row.CompanyWebsite, firstRow.CompanyWebsite);

                // Contact field conflicts
                AddFieldConflict(conflicts, row.RowNumber, "ContactFirstName", row.ContactFirstName, firstRow.ContactFirstName);
                AddFieldConflict(conflicts, row.RowNumber, "ContactLastName", row.ContactLastName, firstRow.ContactLastName);
                AddFieldConflict(conflicts, row.RowNumber, "ContactEmail", row.ContactEmail, firstRow.ContactEmail);
                AddFieldConflict(conflicts, row.RowNumber, "ContactPhone", row.ContactPhone, firstRow.ContactPhone);
                AddFieldConflict(conflicts, row.RowNumber, "ContactMobile", row.ContactMobile, firstRow.ContactMobile);
                AddFieldConflict(conflicts, row.RowNumber, "ContactExt", row.ContactExt, firstRow.ContactExt);
                AddFieldConflict(conflicts, row.RowNumber, "ContactType", row.ContactType, firstRow.ContactType);
                AddFieldConflict(conflicts, row.RowNumber, "BoothNumbers", row.BoothNumbers, firstRow.BoothNumbers);
            }

            return conflicts;
        }

        private List<DuplicateConflictDetailDto> GenerateExistingContactConflicts(ExhibitorImportRowDto importRow, Contact existingContact)
        {
            var conflicts = new List<DuplicateConflictDetailDto>();

            // Compare import row with existing contact
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactFirstName", importRow.ContactFirstName, existingContact.FirstName, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactLastName", importRow.ContactLastName, existingContact.LastName, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactPhone", importRow.ContactPhone, existingContact.Telephone, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactMobile", importRow.ContactMobile, existingContact.Cellphone, existingContact.ContactId);

            // Company comparison if available
            if (existingContact.Company != null)
            {
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyName", importRow.CompanyName, existingContact.Company.CompanyName, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyPhone", importRow.CompanyPhone, existingContact.Company.Phone, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyEmail", importRow.CompanyEmail, existingContact.Company.Email, existingContact.ContactId);
            }

            return conflicts;
        }

        private void AddFieldConflict(List<DuplicateConflictDetailDto> conflicts, int rowNumber, string fieldName,
            string currentValue, string conflictingValue, int? existingRecordId = null)
        {
            // Normalize values for comparison
            var current = (currentValue ?? "").Trim();
            var conflicting = (conflictingValue ?? "").Trim();

            // Only add conflict if values are actually different
            if (!string.Equals(current, conflicting, StringComparison.OrdinalIgnoreCase))
            {
                conflicts.Add(new DuplicateConflictDetailDto
                {
                    RowNumber = rowNumber,
                    FieldName = fieldName,
                    CurrentValue = current,
                    ConflictingValue = conflicting,
                    ExistingRecordId = existingRecordId,
                    ExistingRecordInfo = existingRecordId.HasValue ? $"Database Record ID: {existingRecordId}" : ""
                });
            }
        }

        // Overloaded methods for working with database models
        private List<DuplicateConflictDetailDto> GenerateFieldConflictsFromDbRows(List<ExhibitorImportRows> duplicateRows)
        {
            var conflicts = new List<DuplicateConflictDetailDto>();
            var firstRow = duplicateRows.First();

            foreach (var row in duplicateRows.Skip(1))
            {
                // Company field conflicts
                AddFieldConflict(conflicts, row.RowNumber, "CompanyName", row.CompanyName, firstRow.CompanyName);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyPhone", row.CompanyPhone, firstRow.CompanyPhone);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyEmail", row.CompanyEmail, firstRow.CompanyEmail);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyAddress1", row.CompanyAddress1, firstRow.CompanyAddress1);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyAddress2", row.CompanyAddress2, firstRow.CompanyAddress2);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyCity", row.CompanyCity, firstRow.CompanyCity);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyProvince", row.CompanyProvince, firstRow.CompanyProvince);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyPostalCode", row.CompanyPostalCode, firstRow.CompanyPostalCode);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyCountry", row.CompanyCountry, firstRow.CompanyCountry);
                AddFieldConflict(conflicts, row.RowNumber, "CompanyWebsite", row.CompanyWebsite, firstRow.CompanyWebsite);

                // Contact field conflicts
                AddFieldConflict(conflicts, row.RowNumber, "ContactFirstName", row.ContactFirstName, firstRow.ContactFirstName);
                AddFieldConflict(conflicts, row.RowNumber, "ContactLastName", row.ContactLastName, firstRow.ContactLastName);
                AddFieldConflict(conflicts, row.RowNumber, "ContactEmail", row.ContactEmail, firstRow.ContactEmail);
                AddFieldConflict(conflicts, row.RowNumber, "ContactPhone", row.ContactPhone, firstRow.ContactPhone);
                AddFieldConflict(conflicts, row.RowNumber, "ContactMobile", row.ContactMobile, firstRow.ContactMobile);
                AddFieldConflict(conflicts, row.RowNumber, "ContactExt", row.ContactExt, firstRow.ContactExt);
                AddFieldConflict(conflicts, row.RowNumber, "ContactType", row.ContactType, firstRow.ContactType);
                AddFieldConflict(conflicts, row.RowNumber, "BoothNumbers", row.BoothNumbers, firstRow.BoothNumbers);
            }

            return conflicts;
        }

        private List<DuplicateConflictDetailDto> GenerateExistingContactConflictsFromDbRow(ExhibitorImportRows importRow, Contact existingContact)
        {
            var conflicts = new List<DuplicateConflictDetailDto>();

            // Compare ALL contact fields between import and existing database record
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactFirstName", importRow.ContactFirstName, existingContact.FirstName, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactLastName", importRow.ContactLastName, existingContact.LastName, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactEmail", importRow.ContactEmail, existingContact.Email, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactPhone", importRow.ContactPhone, existingContact.Telephone, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactMobile", importRow.ContactMobile, existingContact.Cellphone, existingContact.ContactId);
            AddFieldConflict(conflicts, importRow.RowNumber, "ContactExt", importRow.ContactExt, existingContact.Ext, existingContact.ContactId);

            // Compare ALL company fields if existing contact has a company
            if (existingContact.Company != null)
            {
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyName", importRow.CompanyName, existingContact.Company.CompanyName, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyPhone", importRow.CompanyPhone, existingContact.Company.Phone, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyEmail", importRow.CompanyEmail, existingContact.Company.Email, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyAddress1", importRow.CompanyAddress1, existingContact.Company.Address1, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyAddress2", importRow.CompanyAddress2, existingContact.Company.Address2, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyCity", importRow.CompanyCity, existingContact.Company.City, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyPostalCode", importRow.CompanyPostalCode, existingContact.Company.PostalCode, existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyWebsite", importRow.CompanyWebsite, existingContact.Company.WebsiteUrl, existingContact.ContactId);

                // Province and Country - need to resolve names from IDs
                var existingProvince = existingContact.Company.ProvinceId.HasValue ?
                    _provinceRepository.Get(existingContact.Company.ProvinceId.Value)?.ProvinceName ?? "" : "";
                var existingCountry = existingContact.Company.CountryId.HasValue ?
                    _countryRepository.GetAll().FirstOrDefault(c => c.CountryId == existingContact.Company.CountryId.Value)?.CountryName ?? "" : "";

                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyProvince", importRow.CompanyProvince, existingProvince ?? "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyCountry", importRow.CompanyCountry, existingCountry ?? "", existingContact.ContactId);
            }
            else
            {
                // If existing contact has no company, compare against empty/new company data
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyName", importRow.CompanyName, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyPhone", importRow.CompanyPhone, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyEmail", importRow.CompanyEmail, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyAddress1", importRow.CompanyAddress1, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyAddress2", importRow.CompanyAddress2, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyCity", importRow.CompanyCity, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyProvince", importRow.CompanyProvince, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyPostalCode", importRow.CompanyPostalCode, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyCountry", importRow.CompanyCountry, "", existingContact.ContactId);
                AddFieldConflict(conflicts, importRow.RowNumber, "CompanyWebsite", importRow.CompanyWebsite, "", existingContact.ContactId);
            }

            return conflicts;
        }

        // =====================================================
        // Error Resolution Endpoints
        // =====================================================

        /// <summary>
        /// Get validation results with fix suggestions for error resolution
        /// </summary>
        [HttpGet("validation-results/{sessionId}")]
        public GenericRespond<ValidationResultsWithSuggestionsDto> GetValidationResultsWithSuggestions(Guid sessionId)
        {
            var session = _importRepository.GetSession(sessionId);
            if (session == null)
            {
                return new GenericRespond<ValidationResultsWithSuggestionsDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            var rows = _importRepository.GetSessionRows(sessionId);
            var validationMessages = _importRepository.GetSessionValidationMessages(sessionId);

            var results = new List<RowValidationWithSuggestionsDto>();

            foreach (var row in rows)
            {
                var rowMessages = validationMessages.Where(m => m.RowId == row.Id).ToList();
                var errorMessages = rowMessages.Where(m => m.MessageType == "Error").ToList();
                var suggestions = _fixSuggestionService.GenerateFixSuggestions(row, errorMessages);

                var errorsWithSuggestions = errorMessages.Select(m => new ValidationErrorWithSuggestionsDto
                {
                    FieldName = m.FieldName,
                    FieldValue = m.FieldValue,
                    ErrorCode = m.MessageCode,
                    ErrorMessage = m.Message,
                    Suggestions = suggestions.Where(s => s.FieldName == m.FieldName).ToList()
                }).ToList();

                results.Add(new RowValidationWithSuggestionsDto
                {
                    RowId = row.Id,
                    RowNumber = row.RowNumber,
                    Status = row.Status,
                    OriginalData = new
                    {
                        // Company Data
                        CompanyName = row.CompanyName,
                        CompanyPhone = row.CompanyPhone,
                        CompanyEmail = row.CompanyEmail,
                        CompanyAddress1 = row.CompanyAddress1,
                        CompanyAddress2 = row.CompanyAddress2,
                        CompanyCity = row.CompanyCity,
                        CompanyProvince = row.CompanyProvince,
                        CompanyPostalCode = row.CompanyPostalCode,
                        CompanyCountry = row.CompanyCountry,
                        CompanyWebsite = row.CompanyWebsite,
                        // Contact Data
                        ContactFirstName = row.ContactFirstName,
                        ContactLastName = row.ContactLastName,
                        ContactEmail = row.ContactEmail,
                        ContactPhone = row.ContactPhone,
                        ContactMobile = row.ContactMobile,
                        ContactExt = row.ContactExt,
                        BoothNumbers = row.BoothNumbers,
                        ContactType = row.ContactType
                    },
                    Errors = errorsWithSuggestions,
                    Warnings = rowMessages.Where(m => m.MessageType == "Warning")
                        .Select(m => new ValidationMessageDto
                        {
                            FieldName = m.FieldName,
                            FieldValue = m.FieldValue,
                            MessageType = m.MessageType,
                            ValidationRule = m.ValidationRule,
                            MessageCode = m.MessageCode,
                            Message = m.Message
                        }).ToList(),
                    AppliedFixes = _importRepository.GetAppliedFixes(row.Id)
                        .Select(f => new AppliedFixDto
                        {
                            FieldName = f.FieldName,
                            OriginalValue = f.OriginalValue,
                            FixedValue = f.FixedValue,
                            FixType = f.FixType,
                            SuggestionUsed = f.SuggestionUsed ?? false,
                            AppliedByUsername = f.AppliedBy,
                            AppliedAt = f.AppliedAt
                        }).ToList()
                });
            }

            // Get duplicates for this session
            var sessionDuplicates = _importRepository.GetSessionDuplicates(sessionId);
            var duplicateInfos = new List<DuplicateInfoDto>();

            foreach (var duplicate in sessionDuplicates)
            {
                var rowNumbers = duplicate.RowNumbers.Split(',').Select(int.Parse).ToList();
                var conflictDetails = new List<DuplicateConflictDetailDto>();
                var resolutionOptions = new List<DuplicateResolutionOptionDto>();

                // Check against existing contacts in database (for all duplicate types)
                var existingContacts = new List<Contact>();
                if (duplicate.DuplicateType == "Email" || duplicate.DuplicateType == "DatabaseConflict")
                {
                    existingContacts = _importRepository.FindContactsByEmail(duplicate.DuplicateValue);
                }

                // Generate conflict details and resolution options based on duplicate type
                if (duplicate.DuplicateType == "Email")
                {
                    // Check for differences in contact information between rows
                    var duplicateRows = rows.Where(r => rowNumbers.Contains(r.RowNumber)).ToList();
                    if (duplicateRows.Count > 1)
                    {
                        conflictDetails.AddRange(GenerateFieldConflictsFromDbRows(duplicateRows));
                    }

                    // Check against existing contacts in database
                    if (existingContacts.Any())
                    {
                        var importRow = duplicateRows.First();
                        foreach (var existingContact in existingContacts)
                        {
                            conflictDetails.AddRange(GenerateExistingContactConflictsFromDbRow(importRow, existingContact));
                        }
                    }

                    // Only field-by-field comparison for email duplicates with conflicts
                    if (conflictDetails.Any())
                    {
                        resolutionOptions.Add(new DuplicateResolutionOptionDto
                        {
                            OptionType = "FieldByField",
                            OptionLabel = "Compare and choose field values",
                            OptionDescription = "Choose which values to keep for each field"
                        });
                    }
                }
                else if (duplicate.DuplicateType == "DatabaseConflict")
                {
                    // For DatabaseConflict, we only need to compare against existing contacts
                    var duplicateRows = rows.Where(r => rowNumbers.Contains(r.RowNumber)).ToList();
                    if (duplicateRows.Any() && existingContacts.Any())
                    {
                        var importRow = duplicateRows.First();
                        foreach (var existingContact in existingContacts)
                        {
                            conflictDetails.AddRange(GenerateExistingContactConflictsFromDbRow(importRow, existingContact));
                        }
                    }

                    // Only add resolution options if there are actual conflicts
                    if (conflictDetails.Any())
                    {
                        resolutionOptions.Add(new DuplicateResolutionOptionDto
                        {
                            OptionType = "FieldByField",
                            OptionLabel = "Compare and choose field values",
                            OptionDescription = "Choose which values to keep for each field"
                        });
                    }
                }

                // Only add to duplicateInfos if there are actual conflicts or it's not a DatabaseConflict
                if (duplicate.DuplicateType != "DatabaseConflict" || conflictDetails.Any())
                {
                    var duplicateInfo = new DuplicateInfoDto
                    {
                        DuplicateType = duplicate.DuplicateType,
                        DuplicateValue = duplicate.DuplicateValue,
                        RowNumbers = rowNumbers,
                        ConflictResolution = duplicate.ConflictResolution ?? "Manual",
                        ConflictDetails = conflictDetails,
                        ResolutionOptions = resolutionOptions,
                        RequiresUserDecision = conflictDetails.Any(),
                        ConflictDescription = conflictDetails.Any()
                            ? $"{duplicate.DuplicateType} '{duplicate.DuplicateValue}' appears in {rowNumbers.Count} rows with different information"
                            : $"{duplicate.DuplicateType} '{duplicate.DuplicateValue}' appears in {rowNumbers.Count} rows",
                        IsResolved = duplicate.ConflictResolution != "Manual"
                    };

                    duplicateInfos.Add(duplicateInfo);
                }
            }

            var response = new ValidationResultsWithSuggestionsDto
            {
                SessionId = sessionId,
                TotalRows = rows.Count,
                ValidRows = rows.Count(r => r.Status == "Valid"),
                ErrorRows = rows.Count(r => r.Status == "Error"),
                FixedRows = rows.Count(r => r.Status == "Fixed"),
                SkippedRows = rows.Count(r => r.Status == "Skipped"),
                Rows = results,
                CanProceedToReview = !results.Any(r => r.Status == "Error") && duplicateInfos.Where(d => d.RequiresUserDecision).All(d => d.IsResolved),
                Duplicates = duplicateInfos,
                TotalDuplicates = duplicateInfos.Count,
                UnresolvedDuplicates = duplicateInfos.Count(d => d.RequiresUserDecision && !d.IsResolved)
            };

            return new GenericRespond<ValidationResultsWithSuggestionsDto>
            {
                Data = response,
                StatusCode = 200,
                Message = "Validation results retrieved successfully"
            };
        }

        /// <summary>
        /// Apply a fix to a specific row field
        /// </summary>
        [HttpPost("apply-fix")]
        public GenericRespond<ApplyFixResponseDto> ApplyFix([FromBody] ApplyFixRequestDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<ApplyFixResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);

            if (user == null)
            {
                return new GenericRespond<ApplyFixResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var row = _importRepository.GetSessionRow(dto.SessionId, dto.RowId);
            if (row == null)
            {
                return new GenericRespond<ApplyFixResponseDto>
                {
                    StatusCode = 404,
                    Message = "Row not found"
                };
            }

            // Get original field value for tracking
            var originalValue = GetOriginalFieldValue(row, dto.FieldName);

            // Record the fix applied by user
            _importRepository.CreateAppliedFix(
                dto.SessionId,
                dto.RowId,
                dto.FieldName,
                originalValue,
                dto.FixedValue,
                dto.FixType,
                dto.SuggestionUsed,
                user.Username
            );

            // Update the row data based on fix type
            switch (dto.FixType)
            {
                case "AutoFix":
                case "Manual":
                    // Update the original field value in the row
                    _importRepository.UpdateRowFieldValue(dto.RowId, dto.FieldName, dto.FixedValue);
                    break;

                case "ExistingMatch":
                    if (dto.FieldName == "CompanyName")
                    {
                        // For company selection, update resolved company ID
                        var companyId = int.Parse(dto.FixedValue);
                        _importRepository.UpdateRowResolvedValues(
                            dto.RowId, companyId, row.ResolvedContactTypeId,
                            row.ResolvedBoothNumbersArray, false, true, false);
                    }
                    else
                    {
                        // For other fields, update the field value
                        _importRepository.UpdateRowFieldValue(dto.RowId, dto.FieldName, dto.FixedValue);
                    }
                    break;

                case "CreateNew":
                    if (dto.FieldName == "CompanyName")
                    {
                        // Update the company name and mark as new company
                        _importRepository.UpdateRowFieldValue(dto.RowId, dto.FieldName, dto.FixedValue);
                        _importRepository.UpdateRowResolvedValues(
                            dto.RowId, null, row.ResolvedContactTypeId,
                            row.ResolvedBoothNumbersArray, true, true, false);
                    }
                    else
                    {
                        // For other fields, just update the field value
                        _importRepository.UpdateRowFieldValue(dto.RowId, dto.FieldName, dto.FixedValue);
                    }
                    break;

                case "Skip":
                case "SkipRow":
                    _importRepository.UpdateRowValidationResults(dto.RowId, "Skipped", false, false, 0, 0);
                    break;

                default:
                    // Fallback: always update the field value
                    _importRepository.UpdateRowFieldValue(dto.RowId, dto.FieldName, dto.FixedValue);
                    break;
            }

            // Re-validate the row after fix (unless skipped)
            string newStatus = "Skipped";
            bool hasErrors = false;
            bool hasWarnings = false;
            var remainingErrors = new List<ValidationMessageDto>();

            if (dto.FixType != "SkipRow")
            {
                var updatedRow = _importRepository.GetRowById(dto.RowId);
                var updatedRowData = ConvertToExcelRowData(updatedRow);
                var newValidationMessages = _excelService.ValidateRowData(updatedRowData, updatedRow.RowNumber);

                // Update validation messages
                _importRepository.ClearValidationMessages(dto.RowId);
                foreach (var message in newValidationMessages)
                {
                    _importRepository.CreateValidationMessage(
                        dto.SessionId, dto.RowId, message.RowNumber, message.FieldName,
                        message.FieldValue, message.MessageType, message.ValidationRule,
                        message.MessageCode, message.Message
                    );
                }

                // Update row status
                hasErrors = newValidationMessages.Any(m => m.MessageType == "Error");
                hasWarnings = newValidationMessages.Any(m => m.MessageType == "Warning");
                newStatus = hasErrors ? "Error" : (hasWarnings ? "Warning" : "Fixed");

                _importRepository.UpdateRowValidationResults(
                    dto.RowId, newStatus, hasErrors, hasWarnings,
                    newValidationMessages.Count(m => m.MessageType == "Error"),
                    newValidationMessages.Count(m => m.MessageType == "Warning")
                );

                // Get remaining errors for response
                remainingErrors = newValidationMessages.Where(m => m.MessageType == "Error")
                    .Select(m => new ValidationMessageDto
                    {
                        FieldName = m.FieldName,
                        FieldValue = m.FieldValue,
                        MessageType = m.MessageType,
                        ValidationRule = m.ValidationRule,
                        MessageCode = m.MessageCode,
                        Message = m.Message
                    }).ToList();
            }

            // Check if all rows in the session are now valid (no errors)
            var allRows = _importRepository.GetSessionRows(dto.SessionId);
            var errorRows = allRows.Count(r => r.HasErrors == true);
            var warningRows = allRows.Count(r => r.HasWarnings == true && r.HasErrors != true);
            var validRows = allRows.Count - errorRows;
            bool canProceed = errorRows == 0;

            // Update session counts and status if all errors are resolved
            _importRepository.UpdateSessionCounts(dto.SessionId, allRows.Count, validRows, errorRows, warningRows);
            _importRepository.UpdateSessionStatus(dto.SessionId, canProceed ? "Validated" : "Uploaded", canProceed);

            return new GenericRespond<ApplyFixResponseDto>
            {
                StatusCode = 200,
                Message = "Fix applied successfully",
                Data = new ApplyFixResponseDto
                {
                    NewStatus = newStatus,
                    HasErrors = hasErrors,
                    HasWarnings = hasWarnings,
                    RemainingErrors = remainingErrors
                }
            };
        }

        // Version that returns DuplicateConflictDetail for duplicate detection
        private List<DuplicateConflictDetail> GenerateExistingContactConflictsForDetection(ExhibitorImportRowDto importRow, Contact existingContact)
        {
            var conflicts = new List<DuplicateConflictDetail>();

            // Compare ALL contact fields between import and existing database record
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactFirstName", importRow.ContactFirstName, existingContact.FirstName, existingContact.ContactId);
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactLastName", importRow.ContactLastName, existingContact.LastName, existingContact.ContactId);
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactEmail", importRow.ContactEmail, existingContact.Email, existingContact.ContactId);
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactPhone", importRow.ContactPhone, existingContact.Telephone, existingContact.ContactId);
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactMobile", importRow.ContactMobile, existingContact.Cellphone, existingContact.ContactId);
            AddFieldConflictForDetection(conflicts, importRow.RowNumber, "ContactExt", importRow.ContactExt, existingContact.Ext, existingContact.ContactId);

            // Compare ALL company fields if existing contact has a company
            if (existingContact.Company != null)
            {
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyName", importRow.CompanyName, existingContact.Company.CompanyName, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyPhone", importRow.CompanyPhone, existingContact.Company.Phone, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyEmail", importRow.CompanyEmail, existingContact.Company.Email, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyAddress1", importRow.CompanyAddress1, existingContact.Company.Address1, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyAddress2", importRow.CompanyAddress2, existingContact.Company.Address2, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyCity", importRow.CompanyCity, existingContact.Company.City, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyPostalCode", importRow.CompanyPostalCode, existingContact.Company.PostalCode, existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyWebsite", importRow.CompanyWebsite, existingContact.Company.WebsiteUrl, existingContact.ContactId);

                // Province and Country - need to resolve names from IDs
                var existingProvince = existingContact.Company.ProvinceId.HasValue ?
                    _provinceRepository.Get(existingContact.Company.ProvinceId.Value)?.ProvinceName ?? "" : "";
                var existingCountry = existingContact.Company.CountryId.HasValue ?
                    _countryRepository.GetAll().FirstOrDefault(c => c.CountryId == existingContact.Company.CountryId.Value)?.CountryName ?? "" : "";

                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyProvince", importRow.CompanyProvince, existingProvince ?? "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyCountry", importRow.CompanyCountry, existingCountry ?? "", existingContact.ContactId);
            }
            else
            {
                // If existing contact has no company, compare against empty/new company data
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyName", importRow.CompanyName, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyPhone", importRow.CompanyPhone, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyEmail", importRow.CompanyEmail, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyAddress1", importRow.CompanyAddress1, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyAddress2", importRow.CompanyAddress2, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyCity", importRow.CompanyCity, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyProvince", importRow.CompanyProvince, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyPostalCode", importRow.CompanyPostalCode, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyCountry", importRow.CompanyCountry, "", existingContact.ContactId);
                AddFieldConflictForDetection(conflicts, importRow.RowNumber, "CompanyWebsite", importRow.CompanyWebsite, "", existingContact.ContactId);
            }

            return conflicts;
        }

        private void AddFieldConflictForDetection(List<DuplicateConflictDetail> conflicts, int rowNumber, string fieldName,
            string currentValue, string conflictingValue, int? existingRecordId = null)
        {
            // Normalize values for comparison
            var current = (currentValue ?? "").Trim();
            var conflicting = (conflictingValue ?? "").Trim();

            // Only add conflict if values are actually different
            if (!string.Equals(current, conflicting, StringComparison.OrdinalIgnoreCase))
            {
                conflicts.Add(new DuplicateConflictDetail
                {
                    RowNumber = rowNumber,
                    FieldName = fieldName,
                    CurrentValue = current,
                    ConflictingValue = conflicting,
                    ExistingRecordId = existingRecordId
                });
            }
        }

        private ExhibitorImportExecutionResultDto ProcessExistingContactUpdate(ExhibitorImportRows row, ExhibitorImportExecutionResultDto result, string username)
        {
            var existingContactId = row.CreatedContactId.Value; // This contains the ID of the contact to update
            var contactAttempt = new CreationAttemptResult { RecordType = "Contact" };
            result.CreationAttempts.Add(contactAttempt);

            // Get the existing contact
            var existingContact = _companyRepository.GetContact(existingContactId);
            if (existingContact != null)
            {
                // Update the contact with the resolved field values from the row
                _companyRepository.UpdateContact(
                    existingContactId,
                    existingContact.ContactTypeId,
                    existingContact.CompanyId,
                    row.ContactFirstName ?? existingContact.FirstName,
                    row.ContactLastName ?? existingContact.LastName,
                    row.ContactEmail ?? existingContact.Email,
                    row.ContactPhone ?? existingContact.Telephone,
                    row.ContactExt ?? existingContact.Ext ?? "",
                    row.ContactMobile ?? existingContact.Cellphone,
                    existingContact.IsArchived ?? false,
                    username
                );

                result.CreatedContactId = existingContactId;
                contactAttempt.WasExisting = true;
                contactAttempt.RecordId = existingContactId;
                contactAttempt.Action = "Updated with Field-by-Field Choices";
                contactAttempt.Details["ContactEmail"] = row.ContactEmail;
                contactAttempt.Details["ContactName"] = $"{row.ContactFirstName} {row.ContactLastName}".Trim();
                contactAttempt.Details["UpdatedFields"] = "Applied user's field-by-field resolution choices";

                result.Status = "Processed";
            }
            else
            {
                contactAttempt.WasExisting = false;
                contactAttempt.FailureReason = $"Existing contact with ID {existingContactId} not found";
                contactAttempt.Action = "Failed";
                result.Status = "Failed";
                result.ErrorMessage = $"Could not find existing contact with ID {existingContactId}";
            }

            return result;
        }

    }
}
