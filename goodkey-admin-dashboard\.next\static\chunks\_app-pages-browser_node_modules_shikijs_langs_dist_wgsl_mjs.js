"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_wgsl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/wgsl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/wgsl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"WGSL\\\",\\\"name\\\":\\\"wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}],\\\"repository\\\":{\\\"attributes\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.attribute.at\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.attribute.wgsl\\\"}},\\\"comment\\\":\\\"attribute declaration\\\",\\\"match\\\":\\\"(@)([A-Za-z_]+)\\\",\\\"name\\\":\\\"meta.attribute.wgsl\\\"}]},\\\"block_comments\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"empty block comments\\\",\\\"match\\\":\\\"/\\\\\\\\*\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.wgsl\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*\\\",\\\"comment\\\":\\\"block documentation comments\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comments\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*(?!\\\\\\\\*)\\\",\\\"comment\\\":\\\"block comments\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comments\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"decimal float literal\\\",\\\"match\\\":\\\"(-?\\\\\\\\b[0-9][0-9]*\\\\\\\\.[0-9][0-9]*)([eE][+-]?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.wgsl\\\"},{\\\"comment\\\":\\\"int literal\\\",\\\"match\\\":\\\"-?\\\\\\\\b0x[0-9a-fA-F]+\\\\\\\\b|\\\\\\\\b0\\\\\\\\b|-?\\\\\\\\b[1-9][0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.wgsl\\\"},{\\\"comment\\\":\\\"uint literal\\\",\\\"match\\\":\\\"\\\\\\\\b0x[0-9a-fA-F]+u\\\\\\\\b|\\\\\\\\b0u\\\\\\\\b|\\\\\\\\b[1-9][0-9]*u\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.wgsl\\\"},{\\\"comment\\\":\\\"boolean constant\\\",\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.wgsl\\\"}]},\\\"function_calls\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([A-Za-z0-9_]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.wgsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"comment\\\":\\\"function/method calls\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"name\\\":\\\"meta.function.call.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([A-Za-z0-9_]+)((\\\\\\\\()|(<))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.fn.wgsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.wgsl\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"}},\\\"comment\\\":\\\"function definition\\\",\\\"end\\\":\\\"\\\\\\\\{\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.brackets.curly.wgsl\\\"}},\\\"name\\\":\\\"meta.function.definition.wgsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_comments\\\"},{\\\"include\\\":\\\"#block_comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#function_calls\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"other keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(bitcast|block|break|case|continue|continuing|default|discard|else|elseif|enable|fallthrough|for|function|if|loop|private|read|read_write|return|storage|switch|uniform|while|workgroup|write)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.wgsl\\\"},{\\\"comment\\\":\\\"reserved keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(asm|const|do|enum|handle|mat|premerge|regardless|typedef|unless|using|vec|void)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.wgsl\\\"},{\\\"comment\\\":\\\"storage keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(let|var)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.wgsl storage.type.wgsl\\\"},{\\\"comment\\\":\\\"type keyword\\\",\\\"match\\\":\\\"\\\\\\\\b(type)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.type.wgsl storage.type.wgsl\\\"},{\\\"comment\\\":\\\"enum keyword\\\",\\\"match\\\":\\\"\\\\\\\\b(enum)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.enum.wgsl storage.type.wgsl\\\"},{\\\"comment\\\":\\\"struct keyword\\\",\\\"match\\\":\\\"\\\\\\\\b(struct)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.declaration.struct.wgsl storage.type.wgsl\\\"},{\\\"comment\\\":\\\"fn\\\",\\\"match\\\":\\\"\\\\\\\\bfn\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.fn.wgsl\\\"},{\\\"comment\\\":\\\"logical operators\\\",\\\"match\\\":\\\"(\\\\\\\\^|\\\\\\\\||\\\\\\\\|\\\\\\\\||&&|<<|>>|!)(?!=)\\\",\\\"name\\\":\\\"keyword.operator.logical.wgsl\\\"},{\\\"comment\\\":\\\"logical AND, borrow references\\\",\\\"match\\\":\\\"&(?![&=])\\\",\\\"name\\\":\\\"keyword.operator.borrow.and.wgsl\\\"},{\\\"comment\\\":\\\"assignment operators\\\",\\\"match\\\":\\\"(\\\\\\\\+=|-=|\\\\\\\\*=|/=|%=|\\\\\\\\^=|&=|\\\\\\\\|=|<<=|>>=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.wgsl\\\"},{\\\"comment\\\":\\\"single equal\\\",\\\"match\\\":\\\"(?<![<>])=(?!=|>)\\\",\\\"name\\\":\\\"keyword.operator.assignment.equal.wgsl\\\"},{\\\"comment\\\":\\\"comparison operators\\\",\\\"match\\\":\\\"(=(=)?(?!>)|!=|<=|(?<!=)>=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.wgsl\\\"},{\\\"comment\\\":\\\"math operators\\\",\\\"match\\\":\\\"(([+%]|(\\\\\\\\*(?!\\\\\\\\w)))(?!=))|(-(?!>))|(/(?!/))\\\",\\\"name\\\":\\\"keyword.operator.math.wgsl\\\"},{\\\"comment\\\":\\\"dot access\\\",\\\"match\\\":\\\"\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.operator.access.dot.wgsl\\\"},{\\\"comment\\\":\\\"dashrocket, skinny arrow\\\",\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"keyword.operator.arrow.skinny.wgsl\\\"}]},\\\"line_comments\\\":{\\\"comment\\\":\\\"single line comment\\\",\\\"match\\\":\\\"\\\\\\\\s*//.*\\\",\\\"name\\\":\\\"comment.line.double-slash.wgsl\\\"},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"comma\\\",\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.wgsl\\\"},{\\\"comment\\\":\\\"curly braces\\\",\\\"match\\\":\\\"[{}]\\\",\\\"name\\\":\\\"punctuation.brackets.curly.wgsl\\\"},{\\\"comment\\\":\\\"parentheses, round brackets\\\",\\\"match\\\":\\\"[()]\\\",\\\"name\\\":\\\"punctuation.brackets.round.wgsl\\\"},{\\\"comment\\\":\\\"semicolon\\\",\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semi.wgsl\\\"},{\\\"comment\\\":\\\"square brackets\\\",\\\"match\\\":\\\"[\\\\\\\\[\\\\\\\\]]\\\",\\\"name\\\":\\\"punctuation.brackets.square.wgsl\\\"},{\\\"comment\\\":\\\"angle brackets\\\",\\\"match\\\":\\\"(?<![=-])[<>]\\\",\\\"name\\\":\\\"punctuation.brackets.angle.wgsl\\\"}]},\\\"types\\\":{\\\"comment\\\":\\\"types\\\",\\\"name\\\":\\\"storage.type.wgsl\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"scalar Types\\\",\\\"match\\\":\\\"\\\\\\\\b(bool|i32|u32|f32)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"reserved scalar Types\\\",\\\"match\\\":\\\"\\\\\\\\b(i64|u64|f64)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"vector type aliasses\\\",\\\"match\\\":\\\"\\\\\\\\b(vec2i|vec3i|vec4i|vec2u|vec3u|vec4u|vec2f|vec3f|vec4f|vec2h|vec3h|vec4h)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"matrix type aliasses\\\",\\\"match\\\":\\\"\\\\\\\\b(mat2x2f|mat2x3f|mat2x4f|mat3x2f|mat3x3f|mat3x4f|mat4x2f|mat4x3f|mat4x4f|mat2x2h|mat2x3h|mat2x4h|mat3x2h|mat3x3h|mat3x4h|mat4x2h|mat4x3h|mat4x4h)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"vector/matrix types\\\",\\\"match\\\":\\\"\\\\\\\\b(vec[2-4]|mat[2-4]x[2-4])\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"atomic types\\\",\\\"match\\\":\\\"\\\\\\\\b(atomic)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"array types\\\",\\\"match\\\":\\\"\\\\\\\\b(array)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.wgsl\\\"},{\\\"comment\\\":\\\"Custom type\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Za-z0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.wgsl\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"variables\\\",\\\"match\\\":\\\"\\\\\\\\b(?<!(?<!\\\\\\\\.)\\\\\\\\.)(?:r#(?!(crate|[Ss]elf|super)))?[a-z0-9_]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.wgsl\\\"}]}},\\\"scopeName\\\":\\\"source.wgsl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/wgsl.mjs\n"));

/***/ })

}]);