"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-responsive";
exports.ids = ["vendor-chunks/react-responsive"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-responsive/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-responsive/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   MediaQuery: () => (/* binding */ MediaQuery),\n/* harmony export */   \"default\": () => (/* binding */ MediaQuery),\n/* harmony export */   toQuery: () => (/* binding */ toQuery),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var matchmediaquery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! matchmediaquery */ \"(ssr)/./node_modules/matchmediaquery/index.js\");\n/* harmony import */ var matchmediaquery__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(matchmediaquery__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hyphenate-style-name */ \"(ssr)/./node_modules/hyphenate-style-name/index.js\");\n/* harmony import */ var shallow_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! shallow-equal */ \"(ssr)/./node_modules/shallow-equal/dist/index.modern.mjs\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\nconst stringOrNumber = prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOfType([(prop_types__WEBPACK_IMPORTED_MODULE_3___default().string), (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number)]);\n// media types\nconst types = {\n    all: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    grid: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    aural: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    braille: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    handheld: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    print: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    projection: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    screen: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    tty: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    tv: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    embossed: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool)\n};\n// properties that match media queries\nconst matchers = {\n    orientation: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOf(['portrait', 'landscape']),\n    scan: prop_types__WEBPACK_IMPORTED_MODULE_3___default().oneOf(['progressive', 'interlace']),\n    aspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    deviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    height: stringOrNumber,\n    deviceHeight: stringOrNumber,\n    width: stringOrNumber,\n    deviceWidth: stringOrNumber,\n    color: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    colorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    monochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().bool),\n    resolution: stringOrNumber,\n    type: Object.keys(types)\n};\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst { type, ...featureMatchers } = matchers;\n// media features\nconst features = {\n    minAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    maxAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    minDeviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    maxDeviceAspectRatio: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().string),\n    minHeight: stringOrNumber,\n    maxHeight: stringOrNumber,\n    minDeviceHeight: stringOrNumber,\n    maxDeviceHeight: stringOrNumber,\n    minWidth: stringOrNumber,\n    maxWidth: stringOrNumber,\n    minDeviceWidth: stringOrNumber,\n    maxDeviceWidth: stringOrNumber,\n    minColor: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxColor: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minColorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxColorIndex: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minMonochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    maxMonochrome: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().number),\n    minResolution: stringOrNumber,\n    maxResolution: stringOrNumber,\n    ...featureMatchers\n};\nconst all = { ...types, ...features };\nvar mq = {\n    all: all,\n    types: types,\n    matchers: matchers,\n    features: features\n};\n\nconst negate = (cond) => `not ${cond}`;\nconst keyVal = (k, v) => {\n    const realKey = (0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(k);\n    // px shorthand\n    if (typeof v === 'number') {\n        v = `${v}px`;\n    }\n    if (v === true) {\n        return realKey;\n    }\n    if (v === false) {\n        return negate(realKey);\n    }\n    return `(${realKey}: ${v})`;\n};\nconst join = (conds) => conds.join(' and ');\nconst toQuery = (obj) => {\n    const rules = [];\n    Object.keys(mq.all).forEach((k) => {\n        const v = obj[k];\n        if (v != null) {\n            rules.push(keyVal(k, v));\n        }\n    });\n    return join(rules);\n};\n\nconst Context = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nconst makeQuery = (settings) => settings.query || toQuery(settings);\nconst hyphenateKeys = (obj) => {\n    if (!obj)\n        return undefined;\n    const keys = Object.keys(obj);\n    return keys.reduce((result, key) => {\n        result[(0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key)] = obj[key];\n        return result;\n    }, {});\n};\nconst useIsUpdate = () => {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        ref.current = true;\n    }, []);\n    return ref.current;\n};\nconst useDevice = (deviceFromProps) => {\n    const deviceFromContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    const getDevice = () => hyphenateKeys(deviceFromProps) || hyphenateKeys(deviceFromContext);\n    const [device, setDevice] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getDevice);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const newDevice = getDevice();\n        if (!(0,shallow_equal__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(device, newDevice)) {\n            setDevice(newDevice);\n        }\n    }, [deviceFromProps, deviceFromContext]);\n    return device;\n};\nconst useQuery = (settings) => {\n    const getQuery = () => makeQuery(settings);\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getQuery);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const newQuery = getQuery();\n        if (query !== newQuery) {\n            setQuery(newQuery);\n        }\n    }, [settings]);\n    return query;\n};\nconst useMatchMedia = (query, device) => {\n    const getMatchMedia = () => matchmediaquery__WEBPACK_IMPORTED_MODULE_1___default()(query, device || {}, !!device);\n    const [mq, setMq] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getMatchMedia);\n    const isUpdate = useIsUpdate();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (isUpdate) {\n            // skip on mounting, it has already been set\n            const newMq = getMatchMedia();\n            setMq(newMq);\n            return () => {\n                if (newMq) {\n                    newMq.dispose();\n                }\n            };\n        }\n    }, [query, device]);\n    return mq;\n};\nconst useMatches = (mediaQuery) => {\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(mediaQuery.matches);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const updateMatches = (ev) => {\n            setMatches(ev.matches);\n        };\n        mediaQuery.addListener(updateMatches);\n        setMatches(mediaQuery.matches);\n        return () => {\n            mediaQuery.removeListener(updateMatches);\n        };\n    }, [mediaQuery]);\n    return matches;\n};\nconst useMediaQuery = (settings, device, onChange) => {\n    const deviceSettings = useDevice(device);\n    const query = useQuery(settings);\n    if (!query)\n        throw new Error('Invalid or missing MediaQuery!');\n    const mq = useMatchMedia(query, deviceSettings);\n    const matches = useMatches(mq);\n    const isUpdate = useIsUpdate();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (isUpdate && onChange) {\n            onChange(matches);\n        }\n    }, [matches]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n        if (mq) {\n            mq.dispose();\n        }\n    }, []);\n    return matches;\n};\n\n// ReactNode and ReactElement typings are a little funky for functional components, so the ReactElement cast is needed on the return\nconst MediaQuery = ({ children, device, onChange, ...settings }) => {\n    const matches = useMediaQuery(settings, device, onChange);\n    if (typeof children === 'function') {\n        return children(matches);\n    }\n    return matches ? children : null;\n};\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-responsive/dist/esm/index.js\n");

/***/ })

};
;