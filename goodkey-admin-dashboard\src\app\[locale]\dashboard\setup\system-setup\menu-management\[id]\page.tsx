import { getQueryClient } from '@/utils/query-client';
import { redirect } from 'next/navigation';
import RoleQuery from '@/services/queries/RoleQuery';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import Content from './components/content';
import PermissionQuery from '@/services/queries/PermissionQuery';
import MenuQuery from '@/services/queries/MenuQuery';

export const revalidate = 0;
export const fetchCache = 'force-no-store';

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = await params;
  try {
    const queryClient = getQueryClient();

    if (resolvedParams.id !== 'add') {
      if (isNaN(Number.parseInt(resolvedParams.id))) throw new Error();
    }
    await queryClient.prefetchQuery({
      queryKey: [...MenuQuery.tags],
      queryFn: MenuQuery.getAll,
    });
    await queryClient.prefetchQuery({
      queryKey: [...MenuQuery.tags, 'menus'],
      queryFn: MenuQuery.getSections,
    });
    await queryClient.prefetchQuery({
      queryKey: PermissionQuery.tags,
      queryFn: PermissionQuery.getAll,
    });
    await queryClient.prefetchQuery({
      queryKey: RoleQuery.tags,
      queryFn: RoleQuery.getAll,
    });
    return (
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Content
          id={
            isNaN(Number.parseInt(resolvedParams.id))
              ? undefined
              : Number.parseInt(resolvedParams.id)
          }
        />
      </HydrationBoundary>
    );
  } catch (e) {
    redirect(`/dashboard/setup/system-setup/menu-management`);
  }
}
