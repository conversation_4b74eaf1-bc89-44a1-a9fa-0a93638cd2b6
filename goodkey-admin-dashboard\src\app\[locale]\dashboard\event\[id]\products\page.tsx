import type { Metadata } from 'next';
import ProductCatalogClient from './ProductCatalogClient';

export const metadata: Metadata = {
  title: 'Products Catalog | GOODKEY SHOW SERVICES LTD.',
  description:
    'Browse available products for your event at GOODKEY SHOW SERVICES LTD.',
};

export default async function ProductCatalogPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return <ProductCatalogClient params={params} />;
}
