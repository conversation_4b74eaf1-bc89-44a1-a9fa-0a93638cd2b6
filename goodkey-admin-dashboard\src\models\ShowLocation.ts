import { ShowHallData } from '@/schema/ShowHallSchema';

export interface ShowLocationInList {
  id: number;
  name?: string;
  locationCode?: string;
  city?: string;
  province?: string;
  isArchived?: boolean;
  createdAt: string; // Date serialized as ISO string
  updatedAt: string; // Date serialized as ISO string
}

export interface Hall extends ShowHallData {
  id: number;
}

export interface ShowLocationDetail {
  id?: number;
  locationCode?: string;
  name?: string;
  telephone?: string;
  tollfree?: string;
  fax?: string;
  mapLink?: string;
  website?: string;
  email?: string;
  accessPlan?: string;
  accessPlanPath?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: string;
  postalCode?: string;
  countryId?: string;
  shippingAddress1?: string;
  shippingAddress2?: string;
  shippingCity?: string;
  shippingProvinceId?: string;
  shippingPostalCode?: string;
  shippingCountryId?: string;
  isArchived?: boolean;
  sameForShipping?: boolean;
}
