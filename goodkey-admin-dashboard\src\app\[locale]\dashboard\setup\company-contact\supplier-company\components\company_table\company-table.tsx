'use client';

import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { CompanyInList } from '@/models/Company';
import { ContactsTable } from '../contacts_table/contacts-table';
import { ChevronDownIcon, ChevronRightIcon } from '@/assets/Icons';

export const CompanyTable = () => {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const { data: companies, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, 'Supplier'],
    queryFn: () => CompanyQuery.getAll('Supplier'),
  });

  const toggleRow = (companyId: number) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(companyId)) {
      newExpandedRows.delete(companyId);
    } else {
      newExpandedRows.add(companyId);
    }
    setExpandedRows(newExpandedRows);
  };

  const columns = generateTableColumns<CompanyInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      accountNumber: { name: 'Account Number', type: 'text', sortable: true },
      phone: { name: 'Phone', type: 'text', sortable: true },
      email: { name: 'Email', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      isArchived: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full ">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleRow(row.id)}
                title={
                  expandedRows.has(row.id) ? 'Hide Contacts' : 'Show Contacts'
                }
              >
                {expandedRows.has(row.id) ? (
                  <ChevronDownIcon size={16} />
                ) : (
                  <ChevronRightIcon size={16} />
                )}
              </Button>
              <Link
                href={`/dashboard/setup/company-contact/supplier-company/${row.id}/view`}
              >
                <Button
                  size="sm"
                  variant="outline"
                  iconName="EyeIcon"
                  title="View Details"
                ></Button>
              </Link>
              <Link
                href={`/dashboard/setup/company-contact/supplier-company/${row.id}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  title="Edit Company"
                ></Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
    false,
    { column: 'name', direction: 'asc' },
  );

  const filters = generateTableFilters<CompanyInList>({
    name: {
      name: 'Company Name',
      type: 'text',
    },
    accountNumber: {
      name: 'Account Number',
      type: 'text',
    },
    city: {
      name: 'City',
      type: 'text',
    },
    email: {
      name: 'Email',
      type: 'text',
    },
  });

  return (
    <DataTable
      columns={columns}
      data={companies || []}
      filterFields={filters}
      isLoading={isLoading}
      expandedRows={expandedRows}
      disableStripedRows
      renderExpandedRow={(row) => (
        <div className="p-3">
          <ContactsTable companyId={row.id} companyName={row.name} />
        </div>
      )}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/company-contact/supplier-company/add">
            <Button variant="main" iconName="AddIcon">
              Add New Company
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default CompanyTable;
