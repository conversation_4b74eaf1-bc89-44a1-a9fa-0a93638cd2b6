'use client';
import './style/index.scss';

import { Portal } from './Portal';
//import { useRouteChange } from '@libs/routeChange';
import { useAlt } from './stores/alt';
import { useModalNode } from './stores/modal';
import portal from './stores/portal';

function AltContainer() {
  const alt = useAlt();

  return (
    <>
      {alt && (
        <Portal
          popperTarget={alt.popperTarget}
          clickThrough
          closeOnClickOutside
          closeOnBlur
          backdropColor={false}
          clickable={false}
          autoFocus={false}
          overall
        >
          <div
            style={{
              padding: 5,
              background: 'black',
              borderRadius: 5,
              pointerEvents: 'none',
            }}
            className={'alt-content-' + alt.id}
          >
            {alt.alt}
          </div>
        </Portal>
      )}
    </>
  );
}
function ModalContainer() {
  const nodes = useModalNode();
  return (
    <>
      {nodes?.map(({ node, id, hidden: hide }) => (
        <div
          style={hide ? { display: 'none' } : undefined}
          className="overlay-container"
          key={id}
        >
          <div>{node}</div>
        </div>
      ))}
    </>
  );
}
function PortalContainer() {
  return (
    <div
      className="overlay-container"
      ref={(e) => {
        if (e) portal(e);
      }}
    />
  );
}
export function OverlayContainer() {
  /*useRouteChange({
    onChange: () => {
      modal.clear();
      tooltip.clear();
    },
    key: 'overlay',
  });*/

  return (
    <>
      <AltContainer />
      <ModalContainer />
      <PortalContainer />
    </>
  );
}
