import { cn } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../card';

interface ICardGenerator {
  title?: string | React.ReactNode;
  description?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  onClick?: () => void;
}

function CardGenerator({
  title,
  children,
  description,
  footer,
  className,
  onClick,
  contentClassName,
}: ICardGenerator) {
  return (
    <Card className={cn('w-full', className)} onClick={onClick}>
      {(title || description) && (
        <CardHeader>
          {title ? (
            typeof title === 'string' ? (
              <CardTitle>{title}</CardTitle>
            ) : (
              title
            )
          ) : null}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      {children && (
        <CardContent className={cn('', contentClassName)}>
          {children}
        </CardContent>
      )}
      {footer && <CardFooter>{footer}</CardFooter>}
    </Card>
  );
}

export default CardGenerator;
