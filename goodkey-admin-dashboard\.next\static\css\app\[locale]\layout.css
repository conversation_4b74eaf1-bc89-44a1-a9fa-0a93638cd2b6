/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!./src/components/ui/overlay/style/index.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.overlay-container {
  display: flex;
  position: fixed;
  align-content: stretch;
  width: -webkit-fill-available;
  width: 100%;
  height: -webkit-fill-available;
  height: 100%;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 11;
  padding: 2vh 1vw;
}
.overlay-container > div {
  display: flex;
  position: absolute;
  align-content: stretch;
  width: -webkit-fill-available;
  width: 100%;
  height: -webkit-fill-available;
  height: 100%;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}
.overlay-container > div > .backdrop {
  display: flex;
  position: fixed;
  width: 100vw;
  height: 100vh;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  align-items: center;
}
.overlay-container > div > .layer {
  display: flex;
  flex-direction: column;
  z-index: 1;
}
.overlay-container > div > .layer > .close-btn {
  align-self: flex-end;
  border-radius: 5px;
  padding-bottom: 5px;
}
.overlay-container > div > .layer > .close-btn.inner {
  position: absolute;
  margin: 15px;
}
.overlay-container > div > .layer > .close-btn.outer {
  position: static;
  margin-right: -25px;
}
.overlay-container > div > .layer:focus {
  outline: none;
}
@keyframes appear-top {
  0% {
    transform: translate(0, -50vh);
  }
  100% {
    transform: translate(0, 0);
  }
}
@keyframes appear-left {
  0% {
    transform: translate(-50vw, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
@keyframes appear-right {
  0% {
    transform: translate(50vw, 0);
  }
  100% {
    transform: translate(0, 0);
  }
}
@keyframes zoom {
  0% {
    transform: scale(0);
  }
  100% {
    transform: translate(1);
  }
}
@keyframes appear-bottom {
  0% {
    transform: translate(0, 50vh);
  }
  100% {
    transform: translate(0, 0);
  }
}
