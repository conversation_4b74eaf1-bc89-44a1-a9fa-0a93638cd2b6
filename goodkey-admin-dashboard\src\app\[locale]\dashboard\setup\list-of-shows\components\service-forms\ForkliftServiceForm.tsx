import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ForkliftServiceSchema,
  ForkliftServiceFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface ForkliftServiceFormProps {
  onSubmit: (data: ForkliftServiceFormType) => void;
  initialData?: Partial<ForkliftServiceFormType>;
  isLoading?: boolean;
}

export function ForkliftServiceForm({
  onSubmit,
  initialData,
  isLoading = false,
}: ForkliftServiceFormProps) {
  const form = useForm<ForkliftServiceFormType>({
    resolver: zodResolver(ForkliftServiceSchema),
    defaultValues: {
      rates: initialData?.rates || [
        { weight: 5000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
        { weight: 10000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
        { weight: 20000, regularRate: 50, overtimeRate: 80, doubleRate: 100 },
      ],
      daysHours: {
        regular: initialData?.daysHours?.regular || '',
        regularMinimumCharge:
          initialData?.daysHours?.regularMinimumCharge || 100,
        overtime: initialData?.daysHours?.overtime || '',
        overtimeMinimumCharge:
          initialData?.daysHours?.overtimeMinimumCharge || 200,
        doubleTime: initialData?.daysHours?.doubleTime || '',
        doubleTimeMinimumCharge:
          initialData?.daysHours?.doubleTimeMinimumCharge || 400,
      },
      additionalServices: {
        manCageRate: initialData?.additionalServices?.manCageRate || 10,
        boomRate: initialData?.additionalServices?.boomRate || 100,
      },
    },
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'rates',
  });

  const handleSubmit = (data: ForkliftServiceFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Rates Table */}
        <div className="space-y-4">
          <div className="text-base font-medium">Rates:</div>
          <div className="grid grid-cols-4 gap-4 text-sm font-medium">
            <div>Weight up to (lbs.)</div>
            <div>Regular Rate ($/hr)</div>
            <div>Overtime Rate ($/hr)</div>
            <div>Double Rate ($/hr)</div>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-4 gap-4">
              <Field
                control={form.control}
                name={`rates.${index}.weight`}
                type="number"
                min="0"
              />

              <Field
                control={form.control}
                name={`rates.${index}.regularRate`}
                type="number"
                step="0.01"
                min="0"
              />

              <Field
                control={form.control}
                name={`rates.${index}.overtimeRate`}
                type="number"
                step="0.01"
                min="0"
              />

              <Field
                control={form.control}
                name={`rates.${index}.doubleRate`}
                type="number"
                step="0.01"
                min="0"
              />
            </div>
          ))}
        </div>

        {/* Days & Hours Section */}
        <div className="space-y-4">
          <div className="text-base font-medium">Days & Hours:</div>

          <div className="space-y-4">
            <Field
              control={form.control}
              name="daysHours.regular"
              type="textarea"
              label="Regular"
              placeholder="Specify regular hours..."
              className="min-h-[80px]"
            />
            <div className="relative max-w-xs">
              <Field
                control={form.control}
                name="daysHours.regularMinimumCharge"
                type="number"
                label="Minimum Charge"
                step="0.01"
                min="0"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                $
              </span>
            </div>
          </div>

          <Field
            control={form.control}
            name="daysHours.overtime"
            type="textarea"
            label="Overtime"
            placeholder="Specify overtime hours..."
            className="min-h-[80px]"
          />
          <div className="relative max-w-xs">
            <Field
              control={form.control}
              name="daysHours.overtimeMinimumCharge"
              type="number"
              label="Minimum Charge"
              step="0.01"
              min="0"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>

          <Field
            control={form.control}
            name="daysHours.doubleTime"
            type="textarea"
            label="Double Time"
            placeholder="Specify double time hours..."
            className="min-h-[80px]"
          />
          <div className="relative max-w-xs">
            <Field
              control={form.control}
              name="daysHours.doubleTimeMinimumCharge"
              type="number"
              label="Minimum Charge"
              step="0.01"
              min="0"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>
        </div>

        {/* Additional Services */}
        <div className="space-y-4">
          <div className="text-base font-medium">Additional Services:</div>
          <div className="grid grid-cols-2 gap-4">
            <div className="relative">
              <Field
                control={form.control}
                name="additionalServices.manCageRate"
                type="number"
                label="Man Cage Rate"
                step="0.01"
                min="0"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                $ / hr
              </span>
            </div>

            <div className="relative">
              <Field
                control={form.control}
                name="additionalServices.boomRate"
                type="number"
                label="Boom Rate"
                step="0.01"
                min="0"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                $ / hr
              </span>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isLoading}
            iconName="SaveIcon"
            iconProps={{ className: 'text-white' }}
          >
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
