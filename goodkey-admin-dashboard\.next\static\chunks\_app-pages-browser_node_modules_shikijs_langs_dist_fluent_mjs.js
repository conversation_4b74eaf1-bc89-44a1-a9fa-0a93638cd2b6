"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fluent_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fluent.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fluent.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fluent\\\",\\\"name\\\":\\\"fluent\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#message\\\"},{\\\"include\\\":\\\"#wrong-line\\\"}],\\\"repository\\\":{\\\"attributes\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_-]*\\\\\\\\s*=\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.attribute-begin.fluent\\\"}},\\\"end\\\":\\\"^(?=\\\\\\\\s*[^\\\\\\\\.])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable\\\"}]},\\\"comment\\\":{\\\"match\\\":\\\"^##?#?\\\\\\\\s.*$\\\",\\\"name\\\":\\\"comment.fluent\\\"},\\\"function-comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"support.function.function-comma.fluent\\\"},\\\"function-named-argument\\\":{\\\"begin\\\":\\\"([a-zA-Z0-9]+:)\\\\\\\\s*([\\\\\\\"a-zA-Z0-9]+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.named-argument.name.fluent\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.named-argument.value.fluent\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\)|,|\\\\\\\\s)\\\",\\\"name\\\":\\\"variable.other.named-argument.fluent\\\"},\\\"function-positional-argument\\\":{\\\"match\\\":\\\"\\\\\\\\$[a-zA-Z0-9_-]+\\\",\\\"name\\\":\\\"variable.other.function.positional-argument.fluent\\\"},\\\"invalid-placeable-string-missing-end-quote\\\":{\\\"match\\\":\\\"\\\\\\\"[^\\\\\\\"]+$\\\",\\\"name\\\":\\\"invalid.illegal.wrong-placeable-missing-end-quote.fluent\\\"},\\\"invalid-placeable-wrong-placeable-missing-end\\\":{\\\"match\\\":\\\"([^}A-Z]*$|[^-][^>]$)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.wrong-placeable-missing-end.fluent\\\"},\\\"message\\\":{\\\"begin\\\":\\\"^(-?[a-zA-Z][a-zA-Z0-9_-]*\\\\\\\\s*=\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.class.message-identifier.fluent\\\"}},\\\"contentName\\\":\\\"string.fluent\\\",\\\"end\\\":\\\"^(?=\\\\\\\\S)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes\\\"},{\\\"include\\\":\\\"#placeable\\\"}]},\\\"placeable\\\":{\\\"begin\\\":\\\"({)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.placeable.begin.fluent\\\"}},\\\"contentName\\\":\\\"variable.other.placeable.content.fluent\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.placeable.end.fluent\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable-string\\\"},{\\\"include\\\":\\\"#placeable-function\\\"},{\\\"include\\\":\\\"#placeable-reference-or-number\\\"},{\\\"include\\\":\\\"#selector\\\"},{\\\"include\\\":\\\"#invalid-placeable-wrong-placeable-missing-end\\\"},{\\\"include\\\":\\\"#invalid-placeable-string-missing-end-quote\\\"},{\\\"include\\\":\\\"#invalid-placeable-wrong-function-name\\\"}]},\\\"placeable-function\\\":{\\\"begin\\\":\\\"([A-Z][A-Z0-9_-]*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.placeable-function.call.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.placeable-function.fluent\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.placeable-function.call.end.fluent\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-comma\\\"},{\\\"include\\\":\\\"#function-positional-argument\\\"},{\\\"include\\\":\\\"#function-named-argument\\\"}]},\\\"placeable-reference-or-number\\\":{\\\"match\\\":\\\"((-|\\\\\\\\$)[a-zA-Z0-9_-]+|[a-zA-Z][a-zA-Z0-9_-]*|[0-9]+)\\\",\\\"name\\\":\\\"variable.other.placeable.reference-or-number.fluent\\\"},\\\"placeable-string\\\":{\\\"begin\\\":\\\"(\\\\\\\")(?=[^\\\\\\\\n]*\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.placeable-string-begin.fluent\\\"}},\\\"contentName\\\":\\\"string.placeable-string-content.fluent\\\",\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.placeable-string-end.fluent\\\"}}},\\\"selector\\\":{\\\"begin\\\":\\\"(->)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.selector.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.selector.content.fluent\\\",\\\"end\\\":\\\"^(?=\\\\\\\\s*})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selector-item\\\"}]},\\\"selector-item\\\":{\\\"begin\\\":\\\"(\\\\\\\\s*\\\\\\\\*?\\\\\\\\[)([a-zA-Z0-9_-]+)(\\\\\\\\]\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.selector-item.begin.fluent\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.selector-item.begin.fluent\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.selector-item.begin.fluent\\\"}},\\\"contentName\\\":\\\"string.selector-item.content.fluent\\\",\\\"end\\\":\\\"^(?=(\\\\\\\\s*})|(\\\\\\\\s*\\\\\\\\[)|(\\\\\\\\s*\\\\\\\\*))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#placeable\\\"}]},\\\"wrong-line\\\":{\\\"match\\\":\\\".*\\\",\\\"name\\\":\\\"invalid.illegal.wrong-line.fluent\\\"}},\\\"scopeName\\\":\\\"source.ftl\\\",\\\"aliases\\\":[\\\"ftl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fluent.mjs\n"));

/***/ })

}]);