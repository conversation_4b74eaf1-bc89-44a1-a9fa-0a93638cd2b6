/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-tweet";
exports.ids = ["vendor-chunks/react-tweet"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-tweet/dist/api/fetch-tweet.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-tweet/dist/api/fetch-tweet.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwitterApiError: () => (/* binding */ TwitterApiError),\n/* harmony export */   fetchTweet: () => (/* binding */ fetchTweet)\n/* harmony export */ });\nconst SYNDICATION_URL = 'https://cdn.syndication.twimg.com';\nclass TwitterApiError extends Error {\n    constructor({ message, status, data }){\n        super(message);\n        this.name = 'TwitterApiError';\n        this.status = status;\n        this.data = data;\n    }\n}\nconst TWEET_ID = /^[0-9]+$/;\nfunction getToken(id) {\n    return (Number(id) / 1e15 * Math.PI).toString(6 ** 2).replace(/(0+|\\.)/g, '');\n}\n/**\n * Fetches a tweet from the Twitter syndication API.\n */ async function fetchTweet(id, fetchOptions) {\n    var _res_headers_get;\n    if (id.length > 40 || !TWEET_ID.test(id)) {\n        throw new Error(`Invalid tweet id: ${id}`);\n    }\n    const url = new URL(`${SYNDICATION_URL}/tweet-result`);\n    url.searchParams.set('id', id);\n    url.searchParams.set('lang', 'en');\n    url.searchParams.set('features', [\n        'tfw_timeline_list:',\n        'tfw_follower_count_sunset:true',\n        'tfw_tweet_edit_backend:on',\n        'tfw_refsrc_session:on',\n        'tfw_fosnr_soft_interventions_enabled:on',\n        'tfw_show_birdwatch_pivots_enabled:on',\n        'tfw_show_business_verified_badge:on',\n        'tfw_duplicate_scribes_to_settings:on',\n        'tfw_use_profile_image_shape_enabled:on',\n        'tfw_show_blue_verified_badge:on',\n        'tfw_legacy_timeline_sunset:true',\n        'tfw_show_gov_verified_badge:on',\n        'tfw_show_business_affiliate_badge:on',\n        'tfw_tweet_edit_frontend:on'\n    ].join(';'));\n    url.searchParams.set('token', getToken(id));\n    const res = await fetch(url.toString(), fetchOptions);\n    const isJson = (_res_headers_get = res.headers.get('content-type')) == null ? void 0 : _res_headers_get.includes('application/json');\n    const data = isJson ? await res.json() : undefined;\n    if (res.ok) {\n        if ((data == null ? void 0 : data.__typename) === 'TweetTombstone') {\n            return {\n                tombstone: true\n            };\n        }\n        return {\n            data\n        };\n    }\n    if (res.status === 404) {\n        return {\n            notFound: true\n        };\n    }\n    throw new TwitterApiError({\n        message: typeof data.error === 'string' ? data.error : `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        status: res.status,\n        data\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/api/fetch-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/date-utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-tweet/dist/date-utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\nconst options = {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n    weekday: 'short',\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric'\n};\nconst formatter = new Intl.DateTimeFormat('en-US', options);\nconst partsArrayToObject = (parts)=>{\n    const result = {};\n    for (const part of parts){\n        result[part.type] = part.value;\n    }\n    return result;\n};\nconst formatDate = (date)=>{\n    const parts = partsArrayToObject(formatter.formatToParts(date));\n    const formattedTime = `${parts.hour}:${parts.minute} ${parts.dayPeriod}`;\n    const formattedDate = `${parts.month} ${parts.day}, ${parts.year}`;\n    return `${formattedTime} · ${formattedDate}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC9kYXRlLXV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLDZCQUE2QixXQUFXLEdBQUcsY0FBYyxFQUFFLGdCQUFnQjtBQUMzRSw2QkFBNkIsYUFBYSxFQUFFLFVBQVUsSUFBSSxXQUFXO0FBQ3JFLGNBQWMsZUFBZSxJQUFJLGNBQWM7QUFDL0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXGRhdGUtdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgb3B0aW9ucyA9IHtcbiAgICBob3VyOiAnbnVtZXJpYycsXG4gICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgaG91cjEyOiB0cnVlLFxuICAgIHdlZWtkYXk6ICdzaG9ydCcsXG4gICAgbW9udGg6ICdzaG9ydCcsXG4gICAgZGF5OiAnbnVtZXJpYycsXG4gICAgeWVhcjogJ251bWVyaWMnXG59O1xuY29uc3QgZm9ybWF0dGVyID0gbmV3IEludGwuRGF0ZVRpbWVGb3JtYXQoJ2VuLVVTJywgb3B0aW9ucyk7XG5jb25zdCBwYXJ0c0FycmF5VG9PYmplY3QgPSAocGFydHMpPT57XG4gICAgY29uc3QgcmVzdWx0ID0ge307XG4gICAgZm9yIChjb25zdCBwYXJ0IG9mIHBhcnRzKXtcbiAgICAgICAgcmVzdWx0W3BhcnQudHlwZV0gPSBwYXJ0LnZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbmV4cG9ydCBjb25zdCBmb3JtYXREYXRlID0gKGRhdGUpPT57XG4gICAgY29uc3QgcGFydHMgPSBwYXJ0c0FycmF5VG9PYmplY3QoZm9ybWF0dGVyLmZvcm1hdFRvUGFydHMoZGF0ZSkpO1xuICAgIGNvbnN0IGZvcm1hdHRlZFRpbWUgPSBgJHtwYXJ0cy5ob3VyfToke3BhcnRzLm1pbnV0ZX0gJHtwYXJ0cy5kYXlQZXJpb2R9YDtcbiAgICBjb25zdCBmb3JtYXR0ZWREYXRlID0gYCR7cGFydHMubW9udGh9ICR7cGFydHMuZGF5fSwgJHtwYXJ0cy55ZWFyfWA7XG4gICAgcmV0dXJuIGAke2Zvcm1hdHRlZFRpbWV9IMK3ICR7Zm9ybWF0dGVkRGF0ZX1gO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/date-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/hooks.js":
/*!************************************************!*\
  !*** ./node_modules/react-tweet/dist/hooks.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMounted: () => (/* binding */ useMounted),\n/* harmony export */   useTweet: () => (/* binding */ useTweet)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _api_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api/index.js */ \"(ssr)/./node_modules/react-tweet/dist/api/fetch-tweet.js\");\n/* __next_internal_client_entry_do_not_use__ useTweet,useMounted auto */ \n\n\n// Avoids an error when used in the pages directory where useSWR might be in `default`.\nconst useSWR = swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"][\"default\"] || swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\nconst host = 'https://react-tweet.vercel.app';\nasync function fetcher([url, fetchOptions]) {\n    const res = await fetch(url, fetchOptions);\n    const json = await res.json();\n    // We return null in case `json.data` is undefined, that way we can check for \"loading\" by\n    // checking if data is `undefined`. `null` means it was fetched.\n    if (res.ok) return json.data || null;\n    throw new _api_index_js__WEBPACK_IMPORTED_MODULE_2__.TwitterApiError({\n        message: `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        data: json,\n        status: res.status\n    });\n}\n/**\n * SWR hook for fetching a tweet in the browser.\n */ const useTweet = (id, apiUrl, fetchOptions)=>{\n    const { isLoading, data, error } = useSWR({\n        \"useTweet.useSWR\": ()=>apiUrl || id ? [\n                apiUrl || id && `${host}/api/tweet/${id}`,\n                fetchOptions\n            ] : null\n    }[\"useTweet.useSWR\"], fetcher, {\n        revalidateIfStale: false,\n        revalidateOnFocus: false,\n        shouldRetryOnError: false\n    });\n    return {\n        // If data is `undefined` then it might be the first render where SWR hasn't started doing\n        // any work, so we set `isLoading` to `true`.\n        isLoading: Boolean(isLoading || data === undefined && !error),\n        data,\n        error\n    };\n};\nconst useMounted = ()=>{\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMounted.useEffect\": ()=>setMounted(true)\n    }[\"useMounted.useEffect\"], []);\n    return mounted;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC9ob29rcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozt5RUFDNEM7QUFDdEI7QUFDMkI7QUFDakQsdUZBQXVGO0FBQ3ZGLE1BQU1JLFNBQVNGLHNEQUFXLElBQUlBLDJDQUFHQTtBQUNqQyxNQUFNSSxPQUFPO0FBQ2IsZUFBZUMsUUFBUSxDQUFDQyxLQUFLQyxhQUFhO0lBQ3RDLE1BQU1DLE1BQU0sTUFBTUMsTUFBTUgsS0FBS0M7SUFDN0IsTUFBTUcsT0FBTyxNQUFNRixJQUFJRSxJQUFJO0lBQzNCLDBGQUEwRjtJQUMxRixnRUFBZ0U7SUFDaEUsSUFBSUYsSUFBSUcsRUFBRSxFQUFFLE9BQU9ELEtBQUtFLElBQUksSUFBSTtJQUNoQyxNQUFNLElBQUlYLDBEQUFlQSxDQUFDO1FBQ3RCWSxTQUFTLENBQUMsMEJBQTBCLEVBQUVQLElBQUksUUFBUSxFQUFFRSxJQUFJTSxNQUFNLENBQUMsRUFBRSxDQUFDO1FBQ2xFRixNQUFNRjtRQUNOSSxRQUFRTixJQUFJTSxNQUFNO0lBQ3RCO0FBQ0o7QUFDQTs7Q0FFQyxHQUFVLE1BQU1DLFdBQVcsQ0FBQ0MsSUFBSUMsUUFBUVY7SUFDckMsTUFBTSxFQUFFVyxTQUFTLEVBQUVOLElBQUksRUFBRU8sS0FBSyxFQUFFLEdBQUdqQjsyQkFBTyxJQUFJZSxVQUFVRCxLQUFLO2dCQUNyREMsVUFBVUQsTUFBTSxHQUFHWixLQUFLLFdBQVcsRUFBRVksSUFBSTtnQkFDekNUO2FBQ0gsR0FBRzswQkFBTUYsU0FBUztRQUNuQmUsbUJBQW1CO1FBQ25CQyxtQkFBbUI7UUFDbkJDLG9CQUFvQjtJQUN4QjtJQUNBLE9BQU87UUFDSCwwRkFBMEY7UUFDMUYsNkNBQTZDO1FBQzdDSixXQUFXSyxRQUFRTCxhQUFhTixTQUFTWSxhQUFhLENBQUNMO1FBQ3ZEUDtRQUNBTztJQUNKO0FBQ0osRUFBRTtBQUNLLE1BQU1NLGFBQWE7SUFDdEIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN2Q0QsZ0RBQVNBO2dDQUFDLElBQUk2QixXQUFXOytCQUFPLEVBQUU7SUFDbEMsT0FBT0Q7QUFDWCxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxob29rcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHN3ciBmcm9tICdzd3InO1xuaW1wb3J0IHsgVHdpdHRlckFwaUVycm9yIH0gZnJvbSAnLi9hcGkvaW5kZXguanMnO1xuLy8gQXZvaWRzIGFuIGVycm9yIHdoZW4gdXNlZCBpbiB0aGUgcGFnZXMgZGlyZWN0b3J5IHdoZXJlIHVzZVNXUiBtaWdodCBiZSBpbiBgZGVmYXVsdGAuXG5jb25zdCB1c2VTV1IgPSBzd3IuZGVmYXVsdCB8fCBzd3I7XG5jb25zdCBob3N0ID0gJ2h0dHBzOi8vcmVhY3QtdHdlZXQudmVyY2VsLmFwcCc7XG5hc3luYyBmdW5jdGlvbiBmZXRjaGVyKFt1cmwsIGZldGNoT3B0aW9uc10pIHtcbiAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCh1cmwsIGZldGNoT3B0aW9ucyk7XG4gICAgY29uc3QganNvbiA9IGF3YWl0IHJlcy5qc29uKCk7XG4gICAgLy8gV2UgcmV0dXJuIG51bGwgaW4gY2FzZSBganNvbi5kYXRhYCBpcyB1bmRlZmluZWQsIHRoYXQgd2F5IHdlIGNhbiBjaGVjayBmb3IgXCJsb2FkaW5nXCIgYnlcbiAgICAvLyBjaGVja2luZyBpZiBkYXRhIGlzIGB1bmRlZmluZWRgLiBgbnVsbGAgbWVhbnMgaXQgd2FzIGZldGNoZWQuXG4gICAgaWYgKHJlcy5vaykgcmV0dXJuIGpzb24uZGF0YSB8fCBudWxsO1xuICAgIHRocm93IG5ldyBUd2l0dGVyQXBpRXJyb3Ioe1xuICAgICAgICBtZXNzYWdlOiBgRmFpbGVkIHRvIGZldGNoIHR3ZWV0IGF0IFwiJHt1cmx9XCIgd2l0aCBcIiR7cmVzLnN0YXR1c31cIi5gLFxuICAgICAgICBkYXRhOiBqc29uLFxuICAgICAgICBzdGF0dXM6IHJlcy5zdGF0dXNcbiAgICB9KTtcbn1cbi8qKlxuICogU1dSIGhvb2sgZm9yIGZldGNoaW5nIGEgdHdlZXQgaW4gdGhlIGJyb3dzZXIuXG4gKi8gZXhwb3J0IGNvbnN0IHVzZVR3ZWV0ID0gKGlkLCBhcGlVcmwsIGZldGNoT3B0aW9ucyk9PntcbiAgICBjb25zdCB7IGlzTG9hZGluZywgZGF0YSwgZXJyb3IgfSA9IHVzZVNXUigoKT0+YXBpVXJsIHx8IGlkID8gW1xuICAgICAgICAgICAgYXBpVXJsIHx8IGlkICYmIGAke2hvc3R9L2FwaS90d2VldC8ke2lkfWAsXG4gICAgICAgICAgICBmZXRjaE9wdGlvbnNcbiAgICAgICAgXSA6IG51bGwsIGZldGNoZXIsIHtcbiAgICAgICAgcmV2YWxpZGF0ZUlmU3RhbGU6IGZhbHNlLFxuICAgICAgICByZXZhbGlkYXRlT25Gb2N1czogZmFsc2UsXG4gICAgICAgIHNob3VsZFJldHJ5T25FcnJvcjogZmFsc2VcbiAgICB9KTtcbiAgICByZXR1cm4ge1xuICAgICAgICAvLyBJZiBkYXRhIGlzIGB1bmRlZmluZWRgIHRoZW4gaXQgbWlnaHQgYmUgdGhlIGZpcnN0IHJlbmRlciB3aGVyZSBTV1IgaGFzbid0IHN0YXJ0ZWQgZG9pbmdcbiAgICAgICAgLy8gYW55IHdvcmssIHNvIHdlIHNldCBgaXNMb2FkaW5nYCB0byBgdHJ1ZWAuXG4gICAgICAgIGlzTG9hZGluZzogQm9vbGVhbihpc0xvYWRpbmcgfHwgZGF0YSA9PT0gdW5kZWZpbmVkICYmICFlcnJvciksXG4gICAgICAgIGRhdGEsXG4gICAgICAgIGVycm9yXG4gICAgfTtcbn07XG5leHBvcnQgY29uc3QgdXNlTW91bnRlZCA9ICgpPT57XG4gICAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIHVzZUVmZmVjdCgoKT0+c2V0TW91bnRlZCh0cnVlKSwgW10pO1xuICAgIHJldHVybiBtb3VudGVkO1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInN3ciIsIlR3aXR0ZXJBcGlFcnJvciIsInVzZVNXUiIsImRlZmF1bHQiLCJob3N0IiwiZmV0Y2hlciIsInVybCIsImZldGNoT3B0aW9ucyIsInJlcyIsImZldGNoIiwianNvbiIsIm9rIiwiZGF0YSIsIm1lc3NhZ2UiLCJzdGF0dXMiLCJ1c2VUd2VldCIsImlkIiwiYXBpVXJsIiwiaXNMb2FkaW5nIiwiZXJyb3IiLCJyZXZhbGlkYXRlSWZTdGFsZSIsInJldmFsaWRhdGVPbkZvY3VzIiwic2hvdWxkUmV0cnlPbkVycm9yIiwiQm9vbGVhbiIsInVuZGVmaW5lZCIsInVzZU1vdW50ZWQiLCJtb3VudGVkIiwic2V0TW91bnRlZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/hooks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/swr.js":
/*!**********************************************!*\
  !*** ./node_modules/react-tweet/dist/swr.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tweet: () => (/* binding */ Tweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\");\n/* harmony import */ var _hooks_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks.js */ \"(ssr)/./node_modules/react-tweet/dist/hooks.js\");\n/* __next_internal_client_entry_do_not_use__ Tweet auto */ \n\n\nconst Tweet = ({ id, apiUrl, fallback = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__.TweetSkeleton, {}), components, fetchOptions, onError })=>{\n    const { data, error, isLoading } = (0,_hooks_js__WEBPACK_IMPORTED_MODULE_2__.useTweet)(id, apiUrl, fetchOptions);\n    if (isLoading) return fallback;\n    if (error || !data) {\n        const NotFound = (components == null ? void 0 : components.TweetNotFound) || _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_3__.TweetNotFound;\n        return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NotFound, {\n            error: onError ? onError(error) : error\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_4__.EmbeddedTweet, {\n        tweet: data,\n        components: components\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/swr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/avatar-img.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AvatarImg: () => (/* binding */ AvatarImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst AvatarImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL2F2YXRhci1pbWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDaEQ7QUFDTyx5Q0FBeUMsc0RBQUk7QUFDcEQ7QUFDQSxLQUFLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxhdmF0YXItaW1nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUganN4LWExMXkvYWx0LXRleHQgLS0gVGhlIGFsdCB0ZXh0IGlzIHBhcnQgb2YgYC4uLnByb3BzYFxuZXhwb3J0IGNvbnN0IEF2YXRhckltZyA9IChwcm9wcyk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImltZ1wiLCB7XG4gICAgICAgIC4uLnByb3BzXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmbeddedTweet: () => (/* binding */ EmbeddedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_header_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-header.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js\");\n/* harmony import */ var _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tweet-in-reply-to.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\");\n/* harmony import */ var _tweet_body_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-body.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tweet-media.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n/* harmony import */ var _tweet_info_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tweet-info.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js\");\n/* harmony import */ var _tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./tweet-actions.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\");\n/* harmony import */ var _tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./tweet-replies.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\");\n/* harmony import */ var _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quoted-tweet/index.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst EmbeddedTweet = ({ tweet: t, components })=>{\n    var _tweet_mediaDetails;\n    // useMemo does nothing for RSC but it helps when the component is used in the client (e.g by SWR)\n    const tweet = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.enrichTweet)(t), [\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_header_js__WEBPACK_IMPORTED_MODULE_4__.TweetHeader, {\n                tweet: tweet,\n                components: components\n            }),\n            tweet.in_reply_to_status_id_str && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__.TweetInReplyTo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_body_js__WEBPACK_IMPORTED_MODULE_6__.TweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_7__.TweetMedia, {\n                tweet: tweet,\n                components: components\n            }) : null,\n            tweet.quoted_tweet && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__.QuotedTweet, {\n                tweet: tweet.quoted_tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_js__WEBPACK_IMPORTED_MODULE_9__.TweetInfo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__.TweetActions, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__.TweetReplies, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css":
/*!****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css ***!
  \****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verified\": \"icons_verified__1eJnA\"\n};\n\nmodule.exports.__checksum = \"5465ad0b5b2d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL2ljb25zL2ljb25zLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxpY29uc1xcaWNvbnMubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ2ZXJpZmllZFwiOiBcImljb25zX3ZlcmlmaWVkX18xZUpuQVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI1NDY1YWQwYjViMmRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBusiness: () => (/* binding */ VerifiedBusiness)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedBusiness = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-a\",\n                    x1: \"4.411\",\n                    x2: \"18.083\",\n                    y1: \"2.495\",\n                    y2: \"21.508\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f4e72a\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".539\",\n                            stopColor: \"#cd8105\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".68\",\n                            stopColor: \"#cb7b00\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4ec26\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4e72a\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-b\",\n                    x1: \"5.355\",\n                    x2: \"16.361\",\n                    y1: \"3.395\",\n                    y2: \"19.133\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f9e87f\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".406\",\n                            stopColor: \"#e2b719\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".989\",\n                            stopColor: \"#e2b719\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n                    clipRule: \"evenodd\",\n                    fillRule: \"evenodd\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-a)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-b)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z\",\n                            fill: \"#d18800\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedGovernment: () => (/* binding */ VerifiedGovernment)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedGovernment = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                clipRule: \"evenodd\",\n                d: \"M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                fillRule: \"evenodd\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/icons/verified.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Verified: () => (/* binding */ Verified)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst Verified = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 24 24\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: \"M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL2ljb25zL3ZlcmlmaWVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNiO0FBQzVCLG1DQUFtQyxzREFBSTtBQUM5QztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdURBQVU7QUFDN0IsZ0NBQWdDLHNEQUFJO0FBQ3BDLG9DQUFvQyxzREFBSTtBQUN4QztBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcaWNvbnNcXHZlcmlmaWVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL2ljb25zLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFZlcmlmaWVkID0gKCk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInN2Z1wiLCB7XG4gICAgICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgICAgIFwiYXJpYS1sYWJlbFwiOiBcIlZlcmlmaWVkIGFjY291bnRcIixcbiAgICAgICAgcm9sZTogXCJpbWdcIixcbiAgICAgICAgY2xhc3NOYW1lOiBzLnZlcmlmaWVkLFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiZ1wiLCB7XG4gICAgICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwicGF0aFwiLCB7XG4gICAgICAgICAgICAgICAgZDogXCJNMjIuMjUgMTJjMC0xLjQzLS44OC0yLjY3LTIuMTktMy4zNC40Ni0xLjM5LjItMi45LS44MS0zLjkxcy0yLjUyLTEuMjctMy45MS0uODFjLS42Ni0xLjMxLTEuOTEtMi4xOS0zLjM0LTIuMTlzLTIuNjcuODgtMy4zMyAyLjE5Yy0xLjQtLjQ2LTIuOTEtLjItMy45Mi44MXMtMS4yNiAyLjUyLS44IDMuOTFjLTEuMzEuNjctMi4yIDEuOTEtMi4yIDMuMzRzLjg5IDIuNjcgMi4yIDMuMzRjLS40NiAxLjM5LS4yMSAyLjkuOCAzLjkxczIuNTIgMS4yNiAzLjkxLjgxYy42NyAxLjMxIDEuOTEgMi4xOSAzLjM0IDIuMTlzMi42OC0uODggMy4zNC0yLjE5YzEuMzkuNDUgMi45LjIgMy45MS0uODFzMS4yNy0yLjUyLjgxLTMuOTFjMS4zMS0uNjcgMi4xOS0xLjkxIDIuMTktMy4zNHptLTExLjcxIDQuMkw2LjggMTIuNDZsMS40MS0xLjQyIDIuMjYgMi4yNiA0LjgtNS4yMyAxLjQ3IDEuMzYtNi4yIDYuNzd6XCJcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/media-img.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/media-img.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaImg: () => (/* binding */ MediaImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst MediaImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL21lZGlhLWltZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUNoRDtBQUNPLHdDQUF3QyxzREFBSTtBQUNuRDtBQUNBLEtBQUsiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXG1lZGlhLWltZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGpzeC1hMTF5L2FsdC10ZXh0IC0tIFRoZSBhbHQgdGV4dCBpcyBwYXJ0IG9mIGAuLi5wcm9wc2BcbmV4cG9ydCBjb25zdCBNZWRpYUltZyA9IChwcm9wcyk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImltZ1wiLCB7XG4gICAgICAgIC4uLnByb3BzXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/media-img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetBody: () => (/* binding */ QuotedTweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-body.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\");\n\n\nconst QuotedTweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                dangerouslySetInnerHTML: {\n                    __html: item.text\n                }\n            }, i))\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtYm9keS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDRDtBQUN4QywyQkFBMkIsT0FBTyxpQkFBaUIsc0RBQUk7QUFDOUQsbUJBQW1CLCtEQUFNO0FBQ3pCO0FBQ0E7QUFDQSw4REFBOEQsc0RBQUk7QUFDbEU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLEtBQUsiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWJvZHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBzIGZyb20gJy4vcXVvdGVkLXR3ZWV0LWJvZHkubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgUXVvdGVkVHdlZXRCb2R5ID0gKHsgdHdlZXQgfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInBcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMucm9vdCxcbiAgICAgICAgbGFuZzogdHdlZXQubGFuZyxcbiAgICAgICAgZGlyOiBcImF1dG9cIixcbiAgICAgICAgY2hpbGRyZW46IHR3ZWV0LmVudGl0aWVzLm1hcCgoaXRlbSwgaSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInNwYW5cIiwge1xuICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgICAgIF9faHRtbDogaXRlbS50ZXh0XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgaSkpXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css":
/*!***********************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css ***!
  \***********************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-body_root__szSfI\"\n};\n\nmodule.exports.__checksum = \"f99c163af8f8\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtYm9keS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxxdW90ZWQtdHdlZXQtYm9keS5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJxdW90ZWQtdHdlZXQtYm9keV9yb290X19zelNmSVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJmOTljMTYzYWY4ZjhcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetContainer: () => (/* binding */ QuotedTweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\");\n/* __next_internal_client_entry_do_not_use__ QuotedTweetContainer auto */ \n\nconst QuotedTweetContainer = ({ tweet, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        onClick: (e)=>{\n            e.preventDefault();\n            window.open(tweet.url, '_blank');\n        },\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtY29udGFpbmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OzswRUFDZ0Q7QUFDSTtBQUM3QyxNQUFNRyx1QkFBdUIsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHLFdBQVcsR0FBR0osc0RBQUlBLENBQUMsT0FBTztRQUM3RUssV0FBV0osb0VBQU07UUFDakJNLFNBQVMsQ0FBQ0M7WUFDTkEsRUFBRUMsY0FBYztZQUNoQkMsT0FBT0MsSUFBSSxDQUFDUixNQUFNUyxHQUFHLEVBQUU7UUFDM0I7UUFDQVIsVUFBVSxXQUFXLEdBQUdKLHNEQUFJQSxDQUFDLFdBQVc7WUFDcENLLFdBQVdKLHVFQUFTO1lBQ3BCRyxVQUFVQTtRQUNkO0lBQ0osR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxxdW90ZWQtdHdlZXQtY29udGFpbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL3F1b3RlZC10d2VldC1jb250YWluZXIubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgUXVvdGVkVHdlZXRDb250YWluZXIgPSAoeyB0d2VldCwgY2hpbGRyZW4gfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogcy5yb290LFxuICAgICAgICBvbkNsaWNrOiAoZSk9PntcbiAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIHdpbmRvdy5vcGVuKHR3ZWV0LnVybCwgJ19ibGFuaycpO1xuICAgICAgICB9LFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiYXJ0aWNsZVwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IHMuYXJ0aWNsZSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlblxuICAgICAgICB9KVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbImpzeCIsIl9qc3giLCJzIiwiUXVvdGVkVHdlZXRDb250YWluZXIiLCJ0d2VldCIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicm9vdCIsIm9uQ2xpY2siLCJlIiwicHJldmVudERlZmF1bHQiLCJ3aW5kb3ciLCJvcGVuIiwidXJsIiwiYXJ0aWNsZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css":
/*!****************************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css ***!
  \****************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-container_root__92393\",\n\t\"article\": \"quoted-tweet-container_article__FoJQN\"\n};\n\nmodule.exports.__checksum = \"607dd84034c5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWNvbnRhaW5lci5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJxdW90ZWQtdHdlZXQtY29udGFpbmVyX3Jvb3RfXzkyMzkzXCIsXG5cdFwiYXJ0aWNsZVwiOiBcInF1b3RlZC10d2VldC1jb250YWluZXJfYXJ0aWNsZV9fRm9KUU5cIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiNjA3ZGQ4NDAzNGM1XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetHeader: () => (/* binding */ QuotedTweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../avatar-img.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../verified-badge.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst QuotedTweetHeader = ({ tweet })=>{\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg, {\n                        src: user.profile_image_url_https,\n                        alt: user.name,\n                        width: 20,\n                        height: 20\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorText,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                            title: user.name,\n                            children: user.name\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                        user: user\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                            title: `@${user.screen_name}`,\n                            children: [\n                                \"@\",\n                                user.screen_name\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css ***!
  \*************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"quoted-tweet-header_header___qrcQ\",\n\t\"avatar\": \"quoted-tweet-header_avatar__lGzrW\",\n\t\"avatarSquare\": \"quoted-tweet-header_avatarSquare__l_eYT\",\n\t\"author\": \"quoted-tweet-header_author__k48VI\",\n\t\"authorText\": \"quoted-tweet-header_authorText__FULly\",\n\t\"username\": \"quoted-tweet-header_username__YLPXR\"\n};\n\nmodule.exports.__checksum = \"661cc432f73d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtaGVhZGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxccXVvdGVkLXR3ZWV0XFxxdW90ZWQtdHdlZXQtaGVhZGVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiaGVhZGVyXCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl9oZWFkZXJfX19xcmNRXCIsXG5cdFwiYXZhdGFyXCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl9hdmF0YXJfX2xHenJXXCIsXG5cdFwiYXZhdGFyU3F1YXJlXCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl9hdmF0YXJTcXVhcmVfX2xfZVlUXCIsXG5cdFwiYXV0aG9yXCI6IFwicXVvdGVkLXR3ZWV0LWhlYWRlcl9hdXRob3JfX2s0OFZJXCIsXG5cdFwiYXV0aG9yVGV4dFwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfYXV0aG9yVGV4dF9fRlVMbHlcIixcblx0XCJ1c2VybmFtZVwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfdXNlcm5hbWVfX1lMUFhSXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjY2MWNjNDMyZjczZFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweet: () => (/* binding */ QuotedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\");\n/* harmony import */ var _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\");\n/* harmony import */ var _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quoted-tweet-body.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tweet-media.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n\n\n\n\n\nconst QuotedTweet = ({ tweet })=>{\n    var _tweet_mediaDetails;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.QuotedTweetContainer, {\n        tweet: tweet,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__.QuotedTweetHeader, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__.QuotedTweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_4__.TweetMedia, {\n                quoted: true,\n                tweet: tweet\n            }) : null\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/skeleton.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./skeleton.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\");\n\n\nconst Skeleton = ({ style })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n        className: _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__.skeleton,\n        style: style\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3NrZWxldG9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNMO0FBQ3BDLG9CQUFvQixPQUFPLGlCQUFpQixzREFBSTtBQUN2RCxtQkFBbUIsMERBQWU7QUFDbEM7QUFDQSxLQUFLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxza2VsZXRvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL3NrZWxldG9uLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFNrZWxldG9uID0gKHsgc3R5bGUgfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcInNwYW5cIiwge1xuICAgICAgICBjbGFzc05hbWU6IHN0eWxlcy5za2VsZXRvbixcbiAgICAgICAgc3R5bGU6IHN0eWxlXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css":
/*!*************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"skeleton\": \"skeleton_skeleton__gUMqh\",\n\t\"loading\": \"skeleton_loading__XZoZ6\"\n};\n\nmodule.exports.__checksum = \"73ef85480f1f\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3NrZWxldG9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHNrZWxldG9uLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwic2tlbGV0b25cIjogXCJza2VsZXRvbl9za2VsZXRvbl9fZ1VNcWhcIixcblx0XCJsb2FkaW5nXCI6IFwic2tlbGV0b25fbG9hZGluZ19fWFpvWjZcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiNzNlZjg1NDgwZjFmXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/theme.css":
/*!***************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/theme.css ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"049ecef6f3cd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3RoZW1lLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdGhlbWUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDQ5ZWNlZjZmM2NkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/theme.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActionsCopy: () => (/* binding */ TweetActionsCopy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetActionsCopy auto */ \n\n\nconst TweetActionsCopy = ({ tweet })=>{\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = ()=>{\n        navigator.clipboard.writeText(tweet.url);\n        setCopied(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TweetActionsCopy.useEffect\": ()=>{\n            if (copied) {\n                const timeout = setTimeout({\n                    \"TweetActionsCopy.useEffect.timeout\": ()=>{\n                        setCopied(false);\n                    }\n                }[\"TweetActionsCopy.useEffect.timeout\"], 6000);\n                return ({\n                    \"TweetActionsCopy.useEffect\": ()=>clearTimeout(timeout)\n                })[\"TweetActionsCopy.useEffect\"];\n            }\n        }\n    }[\"TweetActionsCopy.useEffect\"], [\n        copied\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"button\", {\n        type: \"button\",\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copy,\n        \"aria-label\": \"Copy link\",\n        onClick: handleCopy,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIconWrapper,\n                children: copied ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z\"\n                        })\n                    })\n                }) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z\"\n                        })\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyText,\n                children: copied ? 'Copied!' : 'Copy link'\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActions: () => (/* binding */ TweetActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-actions-copy.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n\n\n\n\nconst TweetActions = ({ tweet })=>{\n    const favoriteCount = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.favorite_count);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.actions,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.like,\n                href: tweet.like_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": `Like. This Tweet has ${favoriteCount} likes`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeCount,\n                        children: favoriteCount\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.reply,\n                href: tweet.reply_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Reply to this Tweet on Twitter\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyText,\n                        children: \"Reply\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__.TweetActionsCopy, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css ***!
  \******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"actions\": \"tweet-actions_actions__UDw7H\",\n\t\"like\": \"tweet-actions_like__H1xYv\",\n\t\"reply\": \"tweet-actions_reply__S4rFc\",\n\t\"copy\": \"tweet-actions_copy__Tbdg_\",\n\t\"likeIconWrapper\": \"tweet-actions_likeIconWrapper__JQkhp\",\n\t\"likeCount\": \"tweet-actions_likeCount__MyxBd\",\n\t\"replyIconWrapper\": \"tweet-actions_replyIconWrapper__NVdGa\",\n\t\"copyIconWrapper\": \"tweet-actions_copyIconWrapper__toM2y\",\n\t\"likeIcon\": \"tweet-actions_likeIcon__fhDng\",\n\t\"replyIcon\": \"tweet-actions_replyIcon__MI2tG\",\n\t\"copyIcon\": \"tweet-actions_copyIcon__SEaWw\",\n\t\"replyText\": \"tweet-actions_replyText__doQct\",\n\t\"copyText\": \"tweet-actions_copyText__fEqBx\"\n};\n\nmodule.exports.__checksum = \"3b7e2bd03618\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWFjdGlvbnMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWFjdGlvbnMubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJhY3Rpb25zXCI6IFwidHdlZXQtYWN0aW9uc19hY3Rpb25zX19VRHc3SFwiLFxuXHRcImxpa2VcIjogXCJ0d2VldC1hY3Rpb25zX2xpa2VfX0gxeFl2XCIsXG5cdFwicmVwbHlcIjogXCJ0d2VldC1hY3Rpb25zX3JlcGx5X19TNHJGY1wiLFxuXHRcImNvcHlcIjogXCJ0d2VldC1hY3Rpb25zX2NvcHlfX1RiZGdfXCIsXG5cdFwibGlrZUljb25XcmFwcGVyXCI6IFwidHdlZXQtYWN0aW9uc19saWtlSWNvbldyYXBwZXJfX0pRa2hwXCIsXG5cdFwibGlrZUNvdW50XCI6IFwidHdlZXQtYWN0aW9uc19saWtlQ291bnRfX015eEJkXCIsXG5cdFwicmVwbHlJY29uV3JhcHBlclwiOiBcInR3ZWV0LWFjdGlvbnNfcmVwbHlJY29uV3JhcHBlcl9fTlZkR2FcIixcblx0XCJjb3B5SWNvbldyYXBwZXJcIjogXCJ0d2VldC1hY3Rpb25zX2NvcHlJY29uV3JhcHBlcl9fdG9NMnlcIixcblx0XCJsaWtlSWNvblwiOiBcInR3ZWV0LWFjdGlvbnNfbGlrZUljb25fX2ZoRG5nXCIsXG5cdFwicmVwbHlJY29uXCI6IFwidHdlZXQtYWN0aW9uc19yZXBseUljb25fX01JMnRHXCIsXG5cdFwiY29weUljb25cIjogXCJ0d2VldC1hY3Rpb25zX2NvcHlJY29uX19TRWFXd1wiLFxuXHRcInJlcGx5VGV4dFwiOiBcInR3ZWV0LWFjdGlvbnNfcmVwbHlUZXh0X19kb1FjdFwiLFxuXHRcImNvcHlUZXh0XCI6IFwidHdlZXQtYWN0aW9uc19jb3B5VGV4dF9fZkVxQnhcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiM2I3ZTJiZDAzNjE4XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-body.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetBody: () => (/* binding */ TweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_link_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-link.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js\");\n/* harmony import */ var _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-body.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\");\n\n\n\nconst TweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>{\n            switch(item.type){\n                case 'hashtag':\n                case 'mention':\n                case 'url':\n                case 'symbol':\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_link_js__WEBPACK_IMPORTED_MODULE_2__.TweetLink, {\n                        href: item.href,\n                        children: item.text\n                    }, i);\n                case 'media':\n                    // Media text is currently never displayed, some tweets however might have indices\n                    // that do match `display_text_range` so for those cases we ignore the content.\n                    return;\n                default:\n                    // We use `dangerouslySetInnerHTML` to preserve the text encoding.\n                    // https://github.com/vercel-labs/react-tweet/issues/29\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        dangerouslySetInnerHTML: {\n                            __html: item.text\n                        }\n                    }, i);\n            }\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-body_root__ChzUj\"\n};\n\nmodule.exports.__checksum = \"5b262099c55a\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWJvZHkubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWJvZHkubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtYm9keV9yb290X19DaHpValwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI1YjI2MjA5OWM1NWFcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-container.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetContainer: () => (/* binding */ TweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-container.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\");\n/* harmony import */ var _theme_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./theme.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/theme.css\");\n\n\n\n\nconst TweetContainer = ({ className, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-tweet-theme', _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.root, className),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWNvbnRhaW5lci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnRDtBQUN4QjtBQUNxQjtBQUN4QjtBQUNkLDBCQUEwQixxQkFBcUIsaUJBQWlCLHNEQUFJO0FBQzNFLG1CQUFtQixnREFBSSxzQkFBc0IsNkRBQU07QUFDbkQsZ0NBQWdDLHNEQUFJO0FBQ3BDLHVCQUF1QixnRUFBUztBQUNoQztBQUNBLFNBQVM7QUFDVCxLQUFLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1jb250YWluZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBjbHN4IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1jb250YWluZXIubW9kdWxlLmNzcyc7XG5pbXBvcnQgJy4vdGhlbWUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldENvbnRhaW5lciA9ICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4gfSk9Pi8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogY2xzeCgncmVhY3QtdHdlZXQtdGhlbWUnLCBzLnJvb3QsIGNsYXNzTmFtZSksXG4gICAgICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovIF9qc3goXCJhcnRpY2xlXCIsIHtcbiAgICAgICAgICAgIGNsYXNzTmFtZTogcy5hcnRpY2xlLFxuICAgICAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css ***!
  \********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-container_root__0rJLq\",\n\t\"article\": \"tweet-container_article__0ERPK\"\n};\n\nmodule.exports.__checksum = \"c1205dd014b2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWNvbnRhaW5lci5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1jb250YWluZXIubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtY29udGFpbmVyX3Jvb3RfXzBySkxxXCIsXG5cdFwiYXJ0aWNsZVwiOiBcInR3ZWV0LWNvbnRhaW5lcl9hcnRpY2xlX18wRVJQS1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJjMTIwNWRkMDE0YjJcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-header.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetHeader: () => (/* binding */ TweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./avatar-img.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-header.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./verified-badge.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst TweetHeader = ({ tweet, components })=>{\n    var _components_AvatarImg;\n    const Img = (_components_AvatarImg = components == null ? void 0 : components.AvatarImg) != null ? _components_AvatarImg : _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg;\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                            src: user.profile_image_url_https,\n                            alt: user.name,\n                            width: 48,\n                            height: 48\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                            className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarShadow\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLink,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLinkText,\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                    title: user.name,\n                                    children: user.name\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                                user: user,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorVerified\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorMeta,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                href: tweet.url,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                                    title: `@${user.screen_name}`,\n                                    children: [\n                                        \"@\",\n                                        user.screen_name\n                                    ]\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorFollow,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.separator,\n                                        children: \"·\"\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                        href: user.follow_url,\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.follow,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: \"Follow\"\n                                    })\n                                ]\n                            })\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.brand,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"View on Twitter\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.twitterIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"tweet-header_header__CXzdi\",\n\t\"avatar\": \"tweet-header_avatar__0Wi9G\",\n\t\"avatarOverflow\": \"tweet-header_avatarOverflow__E2gxj\",\n\t\"avatarSquare\": \"tweet-header_avatarSquare__uIUBO\",\n\t\"avatarShadow\": \"tweet-header_avatarShadow__CB9Zo\",\n\t\"author\": \"tweet-header_author___jWoR\",\n\t\"authorLink\": \"tweet-header_authorLink__qj5Sm\",\n\t\"authorVerified\": \"tweet-header_authorVerified__OFYo2\",\n\t\"authorLinkText\": \"tweet-header_authorLinkText__y6HdU\",\n\t\"authorMeta\": \"tweet-header_authorMeta__gIC3U\",\n\t\"authorFollow\": \"tweet-header_authorFollow__w_j4h\",\n\t\"username\": \"tweet-header_username__UebZb\",\n\t\"follow\": \"tweet-header_follow__Fi7bf\",\n\t\"separator\": \"tweet-header_separator__d4pqe\",\n\t\"brand\": \"tweet-header_brand__0FLQl\",\n\t\"twitterIcon\": \"tweet-header_twitterIcon__m0Rzu\"\n};\n\nmodule.exports.__checksum = \"ff53fe35f495\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWhlYWRlci5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaGVhZGVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiaGVhZGVyXCI6IFwidHdlZXQtaGVhZGVyX2hlYWRlcl9fQ1h6ZGlcIixcblx0XCJhdmF0YXJcIjogXCJ0d2VldC1oZWFkZXJfYXZhdGFyX18wV2k5R1wiLFxuXHRcImF2YXRhck92ZXJmbG93XCI6IFwidHdlZXQtaGVhZGVyX2F2YXRhck92ZXJmbG93X19FMmd4alwiLFxuXHRcImF2YXRhclNxdWFyZVwiOiBcInR3ZWV0LWhlYWRlcl9hdmF0YXJTcXVhcmVfX3VJVUJPXCIsXG5cdFwiYXZhdGFyU2hhZG93XCI6IFwidHdlZXQtaGVhZGVyX2F2YXRhclNoYWRvd19fQ0I5Wm9cIixcblx0XCJhdXRob3JcIjogXCJ0d2VldC1oZWFkZXJfYXV0aG9yX19faldvUlwiLFxuXHRcImF1dGhvckxpbmtcIjogXCJ0d2VldC1oZWFkZXJfYXV0aG9yTGlua19fcWo1U21cIixcblx0XCJhdXRob3JWZXJpZmllZFwiOiBcInR3ZWV0LWhlYWRlcl9hdXRob3JWZXJpZmllZF9fT0ZZbzJcIixcblx0XCJhdXRob3JMaW5rVGV4dFwiOiBcInR3ZWV0LWhlYWRlcl9hdXRob3JMaW5rVGV4dF9feTZIZFVcIixcblx0XCJhdXRob3JNZXRhXCI6IFwidHdlZXQtaGVhZGVyX2F1dGhvck1ldGFfX2dJQzNVXCIsXG5cdFwiYXV0aG9yRm9sbG93XCI6IFwidHdlZXQtaGVhZGVyX2F1dGhvckZvbGxvd19fd19qNGhcIixcblx0XCJ1c2VybmFtZVwiOiBcInR3ZWV0LWhlYWRlcl91c2VybmFtZV9fVWViWmJcIixcblx0XCJmb2xsb3dcIjogXCJ0d2VldC1oZWFkZXJfZm9sbG93X19GaTdiZlwiLFxuXHRcInNlcGFyYXRvclwiOiBcInR3ZWV0LWhlYWRlcl9zZXBhcmF0b3JfX2Q0cHFlXCIsXG5cdFwiYnJhbmRcIjogXCJ0d2VldC1oZWFkZXJfYnJhbmRfXzBGTFFsXCIsXG5cdFwidHdpdHRlckljb25cIjogXCJ0d2VldC1oZWFkZXJfdHdpdHRlckljb25fX20wUnp1XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImZmNTNmZTM1ZjQ5NVwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInReplyTo: () => (/* binding */ TweetInReplyTo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-in-reply-to.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\");\n\n\nconst TweetInReplyTo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n        href: tweet.in_reply_to_url,\n        className: _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: [\n            \"Replying to @\",\n            tweet.in_reply_to_screen_name\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluLXJlcGx5LXRvLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNIO0FBQ3hDLDBCQUEwQixPQUFPLGlCQUFpQix1REFBSztBQUM5RDtBQUNBLG1CQUFtQiwrREFBTTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1pbi1yZXBseS10by5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3hzIGFzIF9qc3hzIH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL3R3ZWV0LWluLXJlcGx5LXRvLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFR3ZWV0SW5SZXBseVRvID0gKHsgdHdlZXQgfSk9Pi8qI19fUFVSRV9fKi8gX2pzeHMoXCJhXCIsIHtcbiAgICAgICAgaHJlZjogdHdlZXQuaW5fcmVwbHlfdG9fdXJsLFxuICAgICAgICBjbGFzc05hbWU6IHMucm9vdCxcbiAgICAgICAgdGFyZ2V0OiBcIl9ibGFua1wiLFxuICAgICAgICByZWw6IFwibm9vcGVuZXIgbm9yZWZlcnJlclwiLFxuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgXCJSZXBseWluZyB0byBAXCIsXG4gICAgICAgICAgICB0d2VldC5pbl9yZXBseV90b19zY3JlZW5fbmFtZVxuICAgICAgICBdXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-in-reply-to_root__o784R\"\n};\n\nmodule.exports.__checksum = \"90440fda9dd1\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluLXJlcGx5LXRvLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1pbi1yZXBseS10by5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1pbi1yZXBseS10b19yb290X19vNzg0UlwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI5MDQ0MGZkYTlkZDFcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfoCreatedAt: () => (/* binding */ TweetInfoCreatedAt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _date_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../date-utils.js */ \"(ssr)/./node_modules/react-tweet/dist/date-utils.js\");\n/* harmony import */ var _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info-created-at.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\");\n\n\n\nconst TweetInfoCreatedAt = ({ tweet })=>{\n    const createdAt = new Date(tweet.created_at);\n    const formattedCreatedAtDate = (0,_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatDate)(createdAt);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        className: _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        href: tweet.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        \"aria-label\": formattedCreatedAtDate,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"time\", {\n            dateTime: createdAt.toISOString(),\n            children: formattedCreatedAtDate\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8tY3JlYXRlZC1hdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdEO0FBQ0Y7QUFDSztBQUM1Qyw4QkFBOEIsT0FBTztBQUM1QztBQUNBLG1DQUFtQywwREFBVTtBQUM3Qyx5QkFBeUIsc0RBQUk7QUFDN0IsbUJBQW1CLG1FQUFNO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHNEQUFJO0FBQ3BDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1pbmZvLWNyZWF0ZWQtYXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCB7IGZvcm1hdERhdGUgfSBmcm9tICcuLi9kYXRlLXV0aWxzLmpzJztcbmltcG9ydCBzIGZyb20gJy4vdHdlZXQtaW5mby1jcmVhdGVkLWF0Lm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFR3ZWV0SW5mb0NyZWF0ZWRBdCA9ICh7IHR3ZWV0IH0pPT57XG4gICAgY29uc3QgY3JlYXRlZEF0ID0gbmV3IERhdGUodHdlZXQuY3JlYXRlZF9hdCk7XG4gICAgY29uc3QgZm9ybWF0dGVkQ3JlYXRlZEF0RGF0ZSA9IGZvcm1hdERhdGUoY3JlYXRlZEF0KTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qLyBfanN4KFwiYVwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogcy5yb290LFxuICAgICAgICBocmVmOiB0d2VldC51cmwsXG4gICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgcmVsOiBcIm5vb3BlbmVyIG5vcmVmZXJyZXJcIixcbiAgICAgICAgXCJhcmlhLWxhYmVsXCI6IGZvcm1hdHRlZENyZWF0ZWRBdERhdGUsXG4gICAgICAgIGNoaWxkcmVuOiAvKiNfX1BVUkVfXyovIF9qc3goXCJ0aW1lXCIsIHtcbiAgICAgICAgICAgIGRhdGVUaW1lOiBjcmVhdGVkQXQudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBmb3JtYXR0ZWRDcmVhdGVkQXREYXRlXG4gICAgICAgIH0pXG4gICAgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css":
/*!**************************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-info-created-at_root__KaxZi\"\n};\n\nmodule.exports.__checksum = \"522ae04b0147\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8tY3JlYXRlZC1hdC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW5mby1jcmVhdGVkLWF0Lm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LWluZm8tY3JlYXRlZC1hdF9yb290X19LYXhaaVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI1MjJhZTA0YjAxNDdcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfo: () => (/* binding */ TweetInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-info-created-at.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\");\n/* harmony import */ var _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\");\n\n\n\nconst TweetInfo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.info,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__.TweetInfoCreatedAt, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoLink,\n                href: \"https://help.x.com/en/x-for-websites-ads-info-and-privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Twitter for Websites, Ads Information and Privacy\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"info\": \"tweet-info_info__ll_kH\",\n\t\"infoLink\": \"tweet-info_infoLink__xdgYO\",\n\t\"infoIcon\": \"tweet-info_infoIcon__S8lzA\"\n};\n\nmodule.exports.__checksum = \"b47e6adf1414\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1pbmZvLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiaW5mb1wiOiBcInR3ZWV0LWluZm9faW5mb19fbGxfa0hcIixcblx0XCJpbmZvTGlua1wiOiBcInR3ZWV0LWluZm9faW5mb0xpbmtfX3hkZ1lPXCIsXG5cdFwiaW5mb0ljb25cIjogXCJ0d2VldC1pbmZvX2luZm9JY29uX19TOGx6QVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJiNDdlNmFkZjE0MTRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-link.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetLink: () => (/* binding */ TweetLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-link.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\");\n\n\nconst TweetLink = ({ href, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        href: href,\n        className: _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer nofollow\",\n        children: children\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWxpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEO0FBQ1I7QUFDakMscUJBQXFCLGdCQUFnQixpQkFBaUIsc0RBQUk7QUFDakU7QUFDQSxtQkFBbUIsd0RBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0EsS0FBSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1saW5rLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFR3ZWV0TGluayA9ICh7IGhyZWYsIGNoaWxkcmVuIH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJhXCIsIHtcbiAgICAgICAgaHJlZjogaHJlZixcbiAgICAgICAgY2xhc3NOYW1lOiBzLnJvb3QsXG4gICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgcmVsOiBcIm5vb3BlbmVyIG5vcmVmZXJyZXIgbm9mb2xsb3dcIixcbiAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css":
/*!***************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css ***!
  \***************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-link_root__4EzRS\"\n};\n\nmodule.exports.__checksum = \"13b6d46c69e0\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWxpbmsubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWxpbmsubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtbGlua19yb290X180RXpSU1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIxM2I2ZDQ2YzY5ZTBcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMediaVideo: () => (/* binding */ TweetMediaVideo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n/* harmony import */ var _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-media-video.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetMediaVideo auto */ \n\n\n\n\n\nconst TweetMediaVideo = ({ tweet, media })=>{\n    const [playButton, setPlayButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ended, setEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mp4Video = (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMp4Video)(media);\n    let timeout = 0;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"video\", {\n                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                poster: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                controls: !playButton,\n                playsInline: true,\n                preload: \"none\",\n                tabIndex: playButton ? -1 : 0,\n                onPlay: ()=>{\n                    if (timeout) window.clearTimeout(timeout);\n                    if (!isPlaying) setIsPlaying(true);\n                    if (ended) setEnded(false);\n                },\n                onPause: ()=>{\n                    // When the video is seeked (moved to a different timestamp), it will pause for a moment\n                    // before resuming. We don't want to show the message in that case so we wait a bit.\n                    if (timeout) window.clearTimeout(timeout);\n                    timeout = window.setTimeout(()=>{\n                        if (isPlaying) setIsPlaying(false);\n                        timeout = 0;\n                    }, 100);\n                },\n                onEnded: ()=>{\n                    setEnded(true);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"source\", {\n                    src: mp4Video.url,\n                    type: mp4Video.content_type\n                })\n            }),\n            playButton && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n                type: \"button\",\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButton,\n                \"aria-label\": \"View video on X\",\n                onClick: (e)=>{\n                    const video = e.currentTarget.previousSibling;\n                    e.preventDefault();\n                    setPlayButton(false);\n                    video.load();\n                    video.play().then(()=>{\n                        setIsPlaying(true);\n                        video.focus();\n                    }).catch((error)=>{\n                        console.error('Error playing video:', error);\n                        setPlayButton(true);\n                        setIsPlaying(false);\n                    });\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButtonIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M21 12L4 2v20l17-10z\"\n                        })\n                    })\n                })\n            }),\n            !isPlaying && !ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.watchOnTwitter,\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                    href: tweet.url,\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    children: playButton ? 'Watch on X' : 'Continue watching on X'\n                })\n            }),\n            ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor, _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.viewReplies),\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"View replies\"\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"anchor\": \"tweet-media-video_anchor__EMqq1\",\n\t\"videoButton\": \"tweet-media-video_videoButton__P9iF2\",\n\t\"videoButtonIcon\": \"tweet-media-video_videoButtonIcon__7gRo1\",\n\t\"watchOnTwitter\": \"tweet-media-video_watchOnTwitter__2ucCU\",\n\t\"viewReplies\": \"tweet-media-video_viewReplies__dp8G_\"\n};\n\nmodule.exports.__checksum = \"d52a74c301d8\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW1lZGlhLXZpZGVvLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LW1lZGlhLXZpZGVvLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiYW5jaG9yXCI6IFwidHdlZXQtbWVkaWEtdmlkZW9fYW5jaG9yX19FTXFxMVwiLFxuXHRcInZpZGVvQnV0dG9uXCI6IFwidHdlZXQtbWVkaWEtdmlkZW9fdmlkZW9CdXR0b25fX1A5aUYyXCIsXG5cdFwidmlkZW9CdXR0b25JY29uXCI6IFwidHdlZXQtbWVkaWEtdmlkZW9fdmlkZW9CdXR0b25JY29uX183Z1JvMVwiLFxuXHRcIndhdGNoT25Ud2l0dGVyXCI6IFwidHdlZXQtbWVkaWEtdmlkZW9fd2F0Y2hPblR3aXR0ZXJfXzJ1Y0NVXCIsXG5cdFwidmlld1JlcGxpZXNcIjogXCJ0d2VldC1tZWRpYS12aWRlb192aWV3UmVwbGllc19fZHA4R19cIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZDUyYTc0YzMwMWQ4XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMedia: () => (/* binding */ TweetMedia)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-media-video.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\");\n/* harmony import */ var _media_img_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./media-img.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/media-img.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n\n\n\n\n\n\n\nconst getSkeletonStyle = (media, itemCount)=>{\n    let paddingBottom = 56.25 // default of 16x9\n    ;\n    // if we only have 1 item, show at original ratio\n    if (itemCount === 1) paddingBottom = 100 / media.original_info.width * media.original_info.height;\n    // if we have 2 items, double the default to be 16x9 total\n    if (itemCount === 2) paddingBottom = paddingBottom * 2;\n    return {\n        width: media.type === 'photo' ? undefined : 'unset',\n        paddingBottom: `${paddingBottom}%`\n    };\n};\nconst TweetMedia = ({ tweet, components, quoted })=>{\n    var _tweet_mediaDetails, _tweet_mediaDetails1;\n    var _tweet_mediaDetails_length;\n    const length = (_tweet_mediaDetails_length = (_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) != null ? _tweet_mediaDetails_length : 0;\n    var _components_MediaImg;\n    const Img = (_components_MediaImg = components == null ? void 0 : components.MediaImg) != null ? _components_MediaImg : _media_img_js__WEBPACK_IMPORTED_MODULE_4__.MediaImg;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.root, !quoted && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.rounded),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaWrapper, length > 1 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2Columns, length === 3 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid3, length > 4 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2x2),\n            children: (_tweet_mediaDetails1 = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails1.map((media)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                    children: media.type === 'photo' ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer, _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaLink),\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                                src: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                                alt: media.ext_alt_text || 'Image',\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                                draggable: true\n                            })\n                        ]\n                    }, media.media_url_https) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__.TweetMediaVideo, {\n                                tweet: tweet,\n                                media: media\n                            })\n                        ]\n                    }, media.media_url_https)\n                }, media.media_url_https))\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css":
/*!****************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css ***!
  \****************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-media_root__k6gQ2\",\n\t\"rounded\": \"tweet-media_rounded__LgwFx\",\n\t\"mediaWrapper\": \"tweet-media_mediaWrapper__6rfqr\",\n\t\"grid2Columns\": \"tweet-media_grid2Columns__tO2Ea\",\n\t\"grid3\": \"tweet-media_grid3__XbH4s\",\n\t\"grid2x2\": \"tweet-media_grid2x2__Wiunq\",\n\t\"mediaContainer\": \"tweet-media_mediaContainer__rjXGp\",\n\t\"mediaLink\": \"tweet-media_mediaLink__vFkZL\",\n\t\"skeleton\": \"tweet-media_skeleton__qZmSS\",\n\t\"image\": \"tweet-media_image__yoPJg\"\n};\n\nmodule.exports.__checksum = \"d50eeeab3c28\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW1lZGlhLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1tZWRpYS5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1tZWRpYV9yb290X19rNmdRMlwiLFxuXHRcInJvdW5kZWRcIjogXCJ0d2VldC1tZWRpYV9yb3VuZGVkX19MZ3dGeFwiLFxuXHRcIm1lZGlhV3JhcHBlclwiOiBcInR3ZWV0LW1lZGlhX21lZGlhV3JhcHBlcl9fNnJmcXJcIixcblx0XCJncmlkMkNvbHVtbnNcIjogXCJ0d2VldC1tZWRpYV9ncmlkMkNvbHVtbnNfX3RPMkVhXCIsXG5cdFwiZ3JpZDNcIjogXCJ0d2VldC1tZWRpYV9ncmlkM19fWGJINHNcIixcblx0XCJncmlkMngyXCI6IFwidHdlZXQtbWVkaWFfZ3JpZDJ4Ml9fV2l1bnFcIixcblx0XCJtZWRpYUNvbnRhaW5lclwiOiBcInR3ZWV0LW1lZGlhX21lZGlhQ29udGFpbmVyX19yalhHcFwiLFxuXHRcIm1lZGlhTGlua1wiOiBcInR3ZWV0LW1lZGlhX21lZGlhTGlua19fdkZrWkxcIixcblx0XCJza2VsZXRvblwiOiBcInR3ZWV0LW1lZGlhX3NrZWxldG9uX19xWm1TU1wiLFxuXHRcImltYWdlXCI6IFwidHdlZXQtbWVkaWFfaW1hZ2VfX3lvUEpnXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImQ1MGVlZWFiM2MyOFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetNotFound: () => (/* binding */ TweetNotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-not-found.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\");\n\n\n\nconst TweetNotFound = (_props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n            className: _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h3\", {\n                    children: \"Tweet not found\"\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                    children: \"The embedded tweet could not be found…\"\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW5vdC1mb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStEO0FBQ1Q7QUFDSjtBQUMzQyw4Q0FBOEMsc0RBQUksQ0FBQywrREFBYztBQUN4RSxnQ0FBZ0MsdURBQUs7QUFDckMsdUJBQXVCLDZEQUFXO0FBQ2xDO0FBQ0EsOEJBQThCLHNEQUFJO0FBQ2xDO0FBQ0EsaUJBQWlCO0FBQ2pCLDhCQUE4QixzREFBSTtBQUNsQztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVCxLQUFLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1ub3QtZm91bmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3gsIGpzeHMgYXMgX2pzeHMgfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCB7IFR3ZWV0Q29udGFpbmVyIH0gZnJvbSAnLi90d2VldC1jb250YWluZXIuanMnO1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL3R3ZWV0LW5vdC1mb3VuZC5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldE5vdEZvdW5kID0gKF9wcm9wcyk9Pi8qI19fUFVSRV9fKi8gX2pzeChUd2VldENvbnRhaW5lciwge1xuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4cyhcImRpdlwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IHN0eWxlcy5yb290LFxuICAgICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgICAgICAvKiNfX1BVUkVfXyovIF9qc3goXCJoM1wiLCB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIlR3ZWV0IG5vdCBmb3VuZFwiXG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwicFwiLCB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIlRoZSBlbWJlZGRlZCB0d2VldCBjb3VsZCBub3QgYmUgZm91bmTigKZcIlxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBdXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css":
/*!********************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css ***!
  \********************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-not-found_root__KQedq\"\n};\n\nmodule.exports.__checksum = \"d6a331fc0c32\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW5vdC1mb3VuZC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbm90LWZvdW5kLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LW5vdC1mb3VuZF9yb290X19LUWVkcVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJkNmEzMzFmYzBjMzJcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetReplies: () => (/* binding */ TweetReplies)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-replies.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\");\n\n\n\nconst TweetReplies = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.replies,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n            className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.link,\n            href: tweet.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.text,\n                children: tweet.conversation_count === 0 ? 'Read more on X' : tweet.conversation_count === 1 ? `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} reply` : `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} replies`\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LXJlcGxpZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnRDtBQUNMO0FBQ0E7QUFDcEMsd0JBQXdCLE9BQU8saUJBQWlCLHNEQUFJO0FBQzNELG1CQUFtQiw4REFBUztBQUM1QixnQ0FBZ0Msc0RBQUk7QUFDcEMsdUJBQXVCLDJEQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzREFBSTtBQUN4QywyQkFBMkIsMkRBQU07QUFDakMsdUhBQXVILHVEQUFZLDRCQUE0QixpQkFBaUIsdURBQVksNEJBQTRCO0FBQ3hOLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtcmVwbGllcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHsgZm9ybWF0TnVtYmVyIH0gZnJvbSAnLi4vdXRpbHMuanMnO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1yZXBsaWVzLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFR3ZWV0UmVwbGllcyA9ICh7IHR3ZWV0IH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJkaXZcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMucmVwbGllcyxcbiAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChcImFcIiwge1xuICAgICAgICAgICAgY2xhc3NOYW1lOiBzLmxpbmssXG4gICAgICAgICAgICBocmVmOiB0d2VldC51cmwsXG4gICAgICAgICAgICB0YXJnZXQ6IFwiX2JsYW5rXCIsXG4gICAgICAgICAgICByZWw6IFwibm9vcGVuZXIgbm9yZWZlcnJlclwiLFxuICAgICAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChcInNwYW5cIiwge1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy50ZXh0LFxuICAgICAgICAgICAgICAgIGNoaWxkcmVuOiB0d2VldC5jb252ZXJzYXRpb25fY291bnQgPT09IDAgPyAnUmVhZCBtb3JlIG9uIFgnIDogdHdlZXQuY29udmVyc2F0aW9uX2NvdW50ID09PSAxID8gYFJlYWQgJHtmb3JtYXROdW1iZXIodHdlZXQuY29udmVyc2F0aW9uX2NvdW50KX0gcmVwbHlgIDogYFJlYWQgJHtmb3JtYXROdW1iZXIodHdlZXQuY29udmVyc2F0aW9uX2NvdW50KX0gcmVwbGllc2BcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css":
/*!******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css ***!
  \******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"replies\": \"tweet-replies_replies__PUxl8\",\n\t\"link\": \"tweet-replies_link__roxYQ\",\n\t\"text\": \"tweet-replies_text__o0Naf\"\n};\n\nmodule.exports.__checksum = \"f1968e941114\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LXJlcGxpZXMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1yZXBsaWVzLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicmVwbGllc1wiOiBcInR3ZWV0LXJlcGxpZXNfcmVwbGllc19fUFV4bDhcIixcblx0XCJsaW5rXCI6IFwidHdlZXQtcmVwbGllc19saW5rX19yb3hZUVwiLFxuXHRcInRleHRcIjogXCJ0d2VldC1yZXBsaWVzX3RleHRfX28wTmFmXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImYxOTY4ZTk0MTExNFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetSkeleton: () => (/* binding */ TweetSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _skeleton_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./skeleton.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/skeleton.js\");\n/* harmony import */ var _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-skeleton.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\");\n\n\n\n\nconst TweetSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        className: _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '3rem',\n                    marginBottom: '0.75rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '6rem',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: {\n                    borderTop: 'var(--tweet-border)',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem',\n                    borderRadius: '9999px',\n                    marginTop: '0.5rem'\n                }\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-skeleton_root__1sn43\"\n};\n\nmodule.exports.__checksum = \"fb50b8a2a5ad\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LXNrZWxldG9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1za2VsZXRvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1za2VsZXRvbl9yb290X18xc240M1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJmYjUwYjhhMmE1YWRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/verified-badge.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBadge: () => (/* binding */ VerifiedBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified.js\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\");\n/* harmony import */ var _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./verified-badge.module.css */ \"(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\");\n\n\n\n\nconst VerifiedBadge = ({ user, className })=>{\n    const verified = user.verified || user.is_blue_verified || user.verified_type;\n    let icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_3__.Verified, {});\n    let iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedBlue;\n    if (verified) {\n        if (!user.is_blue_verified) {\n            iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedOld;\n        }\n        switch(user.verified_type){\n            case 'Government':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedGovernment, {});\n                iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedGovernment;\n                break;\n            case 'Business':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_5__.VerifiedBusiness, {});\n                iconClassName = null;\n                break;\n        }\n    }\n    return verified ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(className, iconClassName),\n        children: icon\n    }) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3ZlcmlmaWVkLWJhZGdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBZ0Q7QUFDeEI7QUFDMEQ7QUFDdEM7QUFDckMseUJBQXlCLGlCQUFpQjtBQUNqRDtBQUNBLDZCQUE2QixzREFBSSxDQUFDLHFEQUFRLElBQUk7QUFDOUMsd0JBQXdCLG9FQUFjO0FBQ3RDO0FBQ0E7QUFDQSw0QkFBNEIsbUVBQWE7QUFDekM7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHNEQUFJLENBQUMsK0RBQWtCLElBQUk7QUFDaEUsZ0NBQWdDLDBFQUFvQjtBQUNwRDtBQUNBO0FBQ0EscUNBQXFDLHNEQUFJLENBQUMsNkRBQWdCLElBQUk7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc0RBQUk7QUFDeEMsbUJBQW1CLGdEQUFJO0FBQ3ZCO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx2ZXJpZmllZC1iYWRnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IGNsc3ggZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyBWZXJpZmllZCwgVmVyaWZpZWRCdXNpbmVzcywgVmVyaWZpZWRHb3Zlcm5tZW50IH0gZnJvbSAnLi9pY29ucy9pbmRleC5qcyc7XG5pbXBvcnQgcyBmcm9tICcuL3ZlcmlmaWVkLWJhZGdlLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFZlcmlmaWVkQmFkZ2UgPSAoeyB1c2VyLCBjbGFzc05hbWUgfSk9PntcbiAgICBjb25zdCB2ZXJpZmllZCA9IHVzZXIudmVyaWZpZWQgfHwgdXNlci5pc19ibHVlX3ZlcmlmaWVkIHx8IHVzZXIudmVyaWZpZWRfdHlwZTtcbiAgICBsZXQgaWNvbiA9IC8qI19fUFVSRV9fKi8gX2pzeChWZXJpZmllZCwge30pO1xuICAgIGxldCBpY29uQ2xhc3NOYW1lID0gcy52ZXJpZmllZEJsdWU7XG4gICAgaWYgKHZlcmlmaWVkKSB7XG4gICAgICAgIGlmICghdXNlci5pc19ibHVlX3ZlcmlmaWVkKSB7XG4gICAgICAgICAgICBpY29uQ2xhc3NOYW1lID0gcy52ZXJpZmllZE9sZDtcbiAgICAgICAgfVxuICAgICAgICBzd2l0Y2godXNlci52ZXJpZmllZF90eXBlKXtcbiAgICAgICAgICAgIGNhc2UgJ0dvdmVybm1lbnQnOlxuICAgICAgICAgICAgICAgIGljb24gPSAvKiNfX1BVUkVfXyovIF9qc3goVmVyaWZpZWRHb3Zlcm5tZW50LCB7fSk7XG4gICAgICAgICAgICAgICAgaWNvbkNsYXNzTmFtZSA9IHMudmVyaWZpZWRHb3Zlcm5tZW50O1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnQnVzaW5lc3MnOlxuICAgICAgICAgICAgICAgIGljb24gPSAvKiNfX1BVUkVfXyovIF9qc3goVmVyaWZpZWRCdXNpbmVzcywge30pO1xuICAgICAgICAgICAgICAgIGljb25DbGFzc05hbWUgPSBudWxsO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB2ZXJpZmllZCA/IC8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgIGNsYXNzTmFtZTogY2xzeChjbGFzc05hbWUsIGljb25DbGFzc05hbWUpLFxuICAgICAgICBjaGlsZHJlbjogaWNvblxuICAgIH0pIDogbnVsbDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verifiedOld\": \"verified-badge_verifiedOld__zcaba\",\n\t\"verifiedBlue\": \"verified-badge_verifiedBlue__s3_Vu\",\n\t\"verifiedGovernment\": \"verified-badge_verifiedGovernment__qRJxq\"\n};\n\nmodule.exports.__checksum = \"41717edb1b06\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3ZlcmlmaWVkLWJhZGdlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdmVyaWZpZWQtYmFkZ2UubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ2ZXJpZmllZE9sZFwiOiBcInZlcmlmaWVkLWJhZGdlX3ZlcmlmaWVkT2xkX196Y2FiYVwiLFxuXHRcInZlcmlmaWVkQmx1ZVwiOiBcInZlcmlmaWVkLWJhZGdlX3ZlcmlmaWVkQmx1ZV9fczNfVnVcIixcblx0XCJ2ZXJpZmllZEdvdmVybm1lbnRcIjogXCJ2ZXJpZmllZC1iYWRnZV92ZXJpZmllZEdvdmVybm1lbnRfX3FSSnhxXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjQxNzE3ZWRiMWIwNlwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-tweet/dist/utils.js":
/*!************************************************!*\
  !*** ./node_modules/react-tweet/dist/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enrichTweet: () => (/* binding */ enrichTweet),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getMediaUrl: () => (/* binding */ getMediaUrl),\n/* harmony export */   getMp4Video: () => (/* binding */ getMp4Video),\n/* harmony export */   getMp4Videos: () => (/* binding */ getMp4Videos)\n/* harmony export */ });\nconst getTweetUrl = (tweet)=>`https://x.com/${tweet.user.screen_name}/status/${tweet.id_str}`;\nconst getUserUrl = (usernameOrTweet)=>`https://x.com/${typeof usernameOrTweet === 'string' ? usernameOrTweet : usernameOrTweet.user.screen_name}`;\nconst getLikeUrl = (tweet)=>`https://x.com/intent/like?tweet_id=${tweet.id_str}`;\nconst getReplyUrl = (tweet)=>`https://x.com/intent/tweet?in_reply_to=${tweet.id_str}`;\nconst getFollowUrl = (tweet)=>`https://x.com/intent/follow?screen_name=${tweet.user.screen_name}`;\nconst getHashtagUrl = (hashtag)=>`https://x.com/hashtag/${hashtag.text}`;\nconst getSymbolUrl = (symbol)=>`https://x.com/search?q=%24${symbol.text}`;\nconst getInReplyToUrl = (tweet)=>`https://x.com/${tweet.in_reply_to_screen_name}/status/${tweet.in_reply_to_status_id_str}`;\nconst getMediaUrl = (media, size)=>{\n    const url = new URL(media.media_url_https);\n    const extension = url.pathname.split('.').pop();\n    if (!extension) return media.media_url_https;\n    url.pathname = url.pathname.replace(`.${extension}`, '');\n    url.searchParams.set('format', extension);\n    url.searchParams.set('name', size);\n    return url.toString();\n};\nconst getMp4Videos = (media)=>{\n    const { variants } = media.video_info;\n    const sortedMp4Videos = variants.filter((vid)=>vid.content_type === 'video/mp4').sort((a, b)=>{\n        var _b_bitrate, _a_bitrate;\n        return ((_b_bitrate = b.bitrate) != null ? _b_bitrate : 0) - ((_a_bitrate = a.bitrate) != null ? _a_bitrate : 0);\n    });\n    return sortedMp4Videos;\n};\nconst getMp4Video = (media)=>{\n    const mp4Videos = getMp4Videos(media);\n    // Skip the highest quality video and use the next quality\n    return mp4Videos.length > 1 ? mp4Videos[1] : mp4Videos[0];\n};\nconst formatNumber = (n)=>{\n    if (n > 999999) return `${(n / 1000000).toFixed(1)}M`;\n    if (n > 999) return `${(n / 1000).toFixed(1)}K`;\n    return n.toString();\n};\nfunction getEntities(tweet) {\n    const textMap = Array.from(tweet.text);\n    const result = [\n        {\n            indices: tweet.display_text_range,\n            type: 'text'\n        }\n    ];\n    addEntities(result, 'hashtag', tweet.entities.hashtags);\n    addEntities(result, 'mention', tweet.entities.user_mentions);\n    addEntities(result, 'url', tweet.entities.urls);\n    addEntities(result, 'symbol', tweet.entities.symbols);\n    if (tweet.entities.media) {\n        addEntities(result, 'media', tweet.entities.media);\n    }\n    fixRange(tweet, result);\n    return result.map((entity)=>{\n        const text = textMap.slice(entity.indices[0], entity.indices[1]).join('');\n        switch(entity.type){\n            case 'hashtag':\n                return Object.assign(entity, {\n                    href: getHashtagUrl(entity),\n                    text\n                });\n            case 'mention':\n                return Object.assign(entity, {\n                    href: getUserUrl(entity.screen_name),\n                    text\n                });\n            case 'url':\n            case 'media':\n                return Object.assign(entity, {\n                    href: entity.expanded_url,\n                    text: entity.display_url\n                });\n            case 'symbol':\n                return Object.assign(entity, {\n                    href: getSymbolUrl(entity),\n                    text\n                });\n            default:\n                return Object.assign(entity, {\n                    text\n                });\n        }\n    });\n}\nfunction addEntities(result, type, entities) {\n    for (const entity of entities){\n        for (const [i, item] of result.entries()){\n            if (item.indices[0] > entity.indices[0] || item.indices[1] < entity.indices[1]) {\n                continue;\n            }\n            const items = [\n                {\n                    ...entity,\n                    type\n                }\n            ];\n            if (item.indices[0] < entity.indices[0]) {\n                items.unshift({\n                    indices: [\n                        item.indices[0],\n                        entity.indices[0]\n                    ],\n                    type: 'text'\n                });\n            }\n            if (item.indices[1] > entity.indices[1]) {\n                items.push({\n                    indices: [\n                        entity.indices[1],\n                        item.indices[1]\n                    ],\n                    type: 'text'\n                });\n            }\n            result.splice(i, 1, ...items);\n            break; // Break out of the loop to avoid iterating over the new items\n        }\n    }\n}\n/**\n * Update display_text_range to work w/ Array.from\n * Array.from is unicode aware, unlike string.slice()\n */ function fixRange(tweet, entities) {\n    if (tweet.entities.media && tweet.entities.media[0].indices[0] < tweet.display_text_range[1]) {\n        tweet.display_text_range[1] = tweet.entities.media[0].indices[0];\n    }\n    const lastEntity = entities.at(-1);\n    if (lastEntity && lastEntity.indices[1] > tweet.display_text_range[1]) {\n        lastEntity.indices[1] = tweet.display_text_range[1];\n    }\n}\n/**\n * Enriches a tweet with additional data used to more easily use the tweet in a UI.\n */ const enrichTweet = (tweet)=>({\n        ...tweet,\n        url: getTweetUrl(tweet),\n        user: {\n            ...tweet.user,\n            url: getUserUrl(tweet),\n            follow_url: getFollowUrl(tweet)\n        },\n        like_url: getLikeUrl(tweet),\n        reply_url: getReplyUrl(tweet),\n        in_reply_to_url: tweet.in_reply_to_screen_name ? getInReplyToUrl(tweet) : undefined,\n        entities: getEntities(tweet),\n        quoted_tweet: tweet.quoted_tweet ? {\n            ...tweet.quoted_tweet,\n            url: getTweetUrl(tweet.quoted_tweet),\n            entities: getEntities(tweet.quoted_tweet)\n        } : undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLDhDQUE4Qyx1QkFBdUIsVUFBVSxhQUFhO0FBQzVGLHVEQUF1RCx5RkFBeUY7QUFDaEosa0VBQWtFLGFBQWE7QUFDL0UsdUVBQXVFLGFBQWE7QUFDcEYseUVBQXlFLHVCQUF1QjtBQUNoRywwREFBMEQsYUFBYTtBQUN2RSw0REFBNEQsWUFBWTtBQUN4RSxrREFBa0QsOEJBQThCLFVBQVUsZ0NBQWdDO0FBQ25IO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLFVBQVU7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLDhCQUE4Qix5QkFBeUI7QUFDdkQsMkJBQTJCLHNCQUFzQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLEtBQUsiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGdldFR3ZWV0VXJsID0gKHR3ZWV0KT0+YGh0dHBzOi8veC5jb20vJHt0d2VldC51c2VyLnNjcmVlbl9uYW1lfS9zdGF0dXMvJHt0d2VldC5pZF9zdHJ9YDtcbmNvbnN0IGdldFVzZXJVcmwgPSAodXNlcm5hbWVPclR3ZWV0KT0+YGh0dHBzOi8veC5jb20vJHt0eXBlb2YgdXNlcm5hbWVPclR3ZWV0ID09PSAnc3RyaW5nJyA/IHVzZXJuYW1lT3JUd2VldCA6IHVzZXJuYW1lT3JUd2VldC51c2VyLnNjcmVlbl9uYW1lfWA7XG5jb25zdCBnZXRMaWtlVXJsID0gKHR3ZWV0KT0+YGh0dHBzOi8veC5jb20vaW50ZW50L2xpa2U/dHdlZXRfaWQ9JHt0d2VldC5pZF9zdHJ9YDtcbmNvbnN0IGdldFJlcGx5VXJsID0gKHR3ZWV0KT0+YGh0dHBzOi8veC5jb20vaW50ZW50L3R3ZWV0P2luX3JlcGx5X3RvPSR7dHdlZXQuaWRfc3RyfWA7XG5jb25zdCBnZXRGb2xsb3dVcmwgPSAodHdlZXQpPT5gaHR0cHM6Ly94LmNvbS9pbnRlbnQvZm9sbG93P3NjcmVlbl9uYW1lPSR7dHdlZXQudXNlci5zY3JlZW5fbmFtZX1gO1xuY29uc3QgZ2V0SGFzaHRhZ1VybCA9IChoYXNodGFnKT0+YGh0dHBzOi8veC5jb20vaGFzaHRhZy8ke2hhc2h0YWcudGV4dH1gO1xuY29uc3QgZ2V0U3ltYm9sVXJsID0gKHN5bWJvbCk9PmBodHRwczovL3guY29tL3NlYXJjaD9xPSUyNCR7c3ltYm9sLnRleHR9YDtcbmNvbnN0IGdldEluUmVwbHlUb1VybCA9ICh0d2VldCk9PmBodHRwczovL3guY29tLyR7dHdlZXQuaW5fcmVwbHlfdG9fc2NyZWVuX25hbWV9L3N0YXR1cy8ke3R3ZWV0LmluX3JlcGx5X3RvX3N0YXR1c19pZF9zdHJ9YDtcbmV4cG9ydCBjb25zdCBnZXRNZWRpYVVybCA9IChtZWRpYSwgc2l6ZSk9PntcbiAgICBjb25zdCB1cmwgPSBuZXcgVVJMKG1lZGlhLm1lZGlhX3VybF9odHRwcyk7XG4gICAgY29uc3QgZXh0ZW5zaW9uID0gdXJsLnBhdGhuYW1lLnNwbGl0KCcuJykucG9wKCk7XG4gICAgaWYgKCFleHRlbnNpb24pIHJldHVybiBtZWRpYS5tZWRpYV91cmxfaHR0cHM7XG4gICAgdXJsLnBhdGhuYW1lID0gdXJsLnBhdGhuYW1lLnJlcGxhY2UoYC4ke2V4dGVuc2lvbn1gLCAnJyk7XG4gICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2Zvcm1hdCcsIGV4dGVuc2lvbik7XG4gICAgdXJsLnNlYXJjaFBhcmFtcy5zZXQoJ25hbWUnLCBzaXplKTtcbiAgICByZXR1cm4gdXJsLnRvU3RyaW5nKCk7XG59O1xuZXhwb3J0IGNvbnN0IGdldE1wNFZpZGVvcyA9IChtZWRpYSk9PntcbiAgICBjb25zdCB7IHZhcmlhbnRzIH0gPSBtZWRpYS52aWRlb19pbmZvO1xuICAgIGNvbnN0IHNvcnRlZE1wNFZpZGVvcyA9IHZhcmlhbnRzLmZpbHRlcigodmlkKT0+dmlkLmNvbnRlbnRfdHlwZSA9PT0gJ3ZpZGVvL21wNCcpLnNvcnQoKGEsIGIpPT57XG4gICAgICAgIHZhciBfYl9iaXRyYXRlLCBfYV9iaXRyYXRlO1xuICAgICAgICByZXR1cm4gKChfYl9iaXRyYXRlID0gYi5iaXRyYXRlKSAhPSBudWxsID8gX2JfYml0cmF0ZSA6IDApIC0gKChfYV9iaXRyYXRlID0gYS5iaXRyYXRlKSAhPSBudWxsID8gX2FfYml0cmF0ZSA6IDApO1xuICAgIH0pO1xuICAgIHJldHVybiBzb3J0ZWRNcDRWaWRlb3M7XG59O1xuZXhwb3J0IGNvbnN0IGdldE1wNFZpZGVvID0gKG1lZGlhKT0+e1xuICAgIGNvbnN0IG1wNFZpZGVvcyA9IGdldE1wNFZpZGVvcyhtZWRpYSk7XG4gICAgLy8gU2tpcCB0aGUgaGlnaGVzdCBxdWFsaXR5IHZpZGVvIGFuZCB1c2UgdGhlIG5leHQgcXVhbGl0eVxuICAgIHJldHVybiBtcDRWaWRlb3MubGVuZ3RoID4gMSA/IG1wNFZpZGVvc1sxXSA6IG1wNFZpZGVvc1swXTtcbn07XG5leHBvcnQgY29uc3QgZm9ybWF0TnVtYmVyID0gKG4pPT57XG4gICAgaWYgKG4gPiA5OTk5OTkpIHJldHVybiBgJHsobiAvIDEwMDAwMDApLnRvRml4ZWQoMSl9TWA7XG4gICAgaWYgKG4gPiA5OTkpIHJldHVybiBgJHsobiAvIDEwMDApLnRvRml4ZWQoMSl9S2A7XG4gICAgcmV0dXJuIG4udG9TdHJpbmcoKTtcbn07XG5mdW5jdGlvbiBnZXRFbnRpdGllcyh0d2VldCkge1xuICAgIGNvbnN0IHRleHRNYXAgPSBBcnJheS5mcm9tKHR3ZWV0LnRleHQpO1xuICAgIGNvbnN0IHJlc3VsdCA9IFtcbiAgICAgICAge1xuICAgICAgICAgICAgaW5kaWNlczogdHdlZXQuZGlzcGxheV90ZXh0X3JhbmdlLFxuICAgICAgICAgICAgdHlwZTogJ3RleHQnXG4gICAgICAgIH1cbiAgICBdO1xuICAgIGFkZEVudGl0aWVzKHJlc3VsdCwgJ2hhc2h0YWcnLCB0d2VldC5lbnRpdGllcy5oYXNodGFncyk7XG4gICAgYWRkRW50aXRpZXMocmVzdWx0LCAnbWVudGlvbicsIHR3ZWV0LmVudGl0aWVzLnVzZXJfbWVudGlvbnMpO1xuICAgIGFkZEVudGl0aWVzKHJlc3VsdCwgJ3VybCcsIHR3ZWV0LmVudGl0aWVzLnVybHMpO1xuICAgIGFkZEVudGl0aWVzKHJlc3VsdCwgJ3N5bWJvbCcsIHR3ZWV0LmVudGl0aWVzLnN5bWJvbHMpO1xuICAgIGlmICh0d2VldC5lbnRpdGllcy5tZWRpYSkge1xuICAgICAgICBhZGRFbnRpdGllcyhyZXN1bHQsICdtZWRpYScsIHR3ZWV0LmVudGl0aWVzLm1lZGlhKTtcbiAgICB9XG4gICAgZml4UmFuZ2UodHdlZXQsIHJlc3VsdCk7XG4gICAgcmV0dXJuIHJlc3VsdC5tYXAoKGVudGl0eSk9PntcbiAgICAgICAgY29uc3QgdGV4dCA9IHRleHRNYXAuc2xpY2UoZW50aXR5LmluZGljZXNbMF0sIGVudGl0eS5pbmRpY2VzWzFdKS5qb2luKCcnKTtcbiAgICAgICAgc3dpdGNoKGVudGl0eS50eXBlKXtcbiAgICAgICAgICAgIGNhc2UgJ2hhc2h0YWcnOlxuICAgICAgICAgICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKGVudGl0eSwge1xuICAgICAgICAgICAgICAgICAgICBocmVmOiBnZXRIYXNodGFnVXJsKGVudGl0eSksXG4gICAgICAgICAgICAgICAgICAgIHRleHRcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNhc2UgJ21lbnRpb24nOlxuICAgICAgICAgICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKGVudGl0eSwge1xuICAgICAgICAgICAgICAgICAgICBocmVmOiBnZXRVc2VyVXJsKGVudGl0eS5zY3JlZW5fbmFtZSksXG4gICAgICAgICAgICAgICAgICAgIHRleHRcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNhc2UgJ3VybCc6XG4gICAgICAgICAgICBjYXNlICdtZWRpYSc6XG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oZW50aXR5LCB7XG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IGVudGl0eS5leHBhbmRlZF91cmwsXG4gICAgICAgICAgICAgICAgICAgIHRleHQ6IGVudGl0eS5kaXNwbGF5X3VybFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY2FzZSAnc3ltYm9sJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihlbnRpdHksIHtcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogZ2V0U3ltYm9sVXJsKGVudGl0eSksXG4gICAgICAgICAgICAgICAgICAgIHRleHRcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oZW50aXR5LCB7XG4gICAgICAgICAgICAgICAgICAgIHRleHRcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuZnVuY3Rpb24gYWRkRW50aXRpZXMocmVzdWx0LCB0eXBlLCBlbnRpdGllcykge1xuICAgIGZvciAoY29uc3QgZW50aXR5IG9mIGVudGl0aWVzKXtcbiAgICAgICAgZm9yIChjb25zdCBbaSwgaXRlbV0gb2YgcmVzdWx0LmVudHJpZXMoKSl7XG4gICAgICAgICAgICBpZiAoaXRlbS5pbmRpY2VzWzBdID4gZW50aXR5LmluZGljZXNbMF0gfHwgaXRlbS5pbmRpY2VzWzFdIDwgZW50aXR5LmluZGljZXNbMV0pIHtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGl0ZW1zID0gW1xuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgLi4uZW50aXR5LFxuICAgICAgICAgICAgICAgICAgICB0eXBlXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXTtcbiAgICAgICAgICAgIGlmIChpdGVtLmluZGljZXNbMF0gPCBlbnRpdHkuaW5kaWNlc1swXSkge1xuICAgICAgICAgICAgICAgIGl0ZW1zLnVuc2hpZnQoe1xuICAgICAgICAgICAgICAgICAgICBpbmRpY2VzOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtLmluZGljZXNbMF0sXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnRpdHkuaW5kaWNlc1swXVxuICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAndGV4dCdcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpdGVtLmluZGljZXNbMV0gPiBlbnRpdHkuaW5kaWNlc1sxXSkge1xuICAgICAgICAgICAgICAgIGl0ZW1zLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICBpbmRpY2VzOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnRpdHkuaW5kaWNlc1sxXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uaW5kaWNlc1sxXVxuICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAndGV4dCdcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlc3VsdC5zcGxpY2UoaSwgMSwgLi4uaXRlbXMpO1xuICAgICAgICAgICAgYnJlYWs7IC8vIEJyZWFrIG91dCBvZiB0aGUgbG9vcCB0byBhdm9pZCBpdGVyYXRpbmcgb3ZlciB0aGUgbmV3IGl0ZW1zXG4gICAgICAgIH1cbiAgICB9XG59XG4vKipcbiAqIFVwZGF0ZSBkaXNwbGF5X3RleHRfcmFuZ2UgdG8gd29yayB3LyBBcnJheS5mcm9tXG4gKiBBcnJheS5mcm9tIGlzIHVuaWNvZGUgYXdhcmUsIHVubGlrZSBzdHJpbmcuc2xpY2UoKVxuICovIGZ1bmN0aW9uIGZpeFJhbmdlKHR3ZWV0LCBlbnRpdGllcykge1xuICAgIGlmICh0d2VldC5lbnRpdGllcy5tZWRpYSAmJiB0d2VldC5lbnRpdGllcy5tZWRpYVswXS5pbmRpY2VzWzBdIDwgdHdlZXQuZGlzcGxheV90ZXh0X3JhbmdlWzFdKSB7XG4gICAgICAgIHR3ZWV0LmRpc3BsYXlfdGV4dF9yYW5nZVsxXSA9IHR3ZWV0LmVudGl0aWVzLm1lZGlhWzBdLmluZGljZXNbMF07XG4gICAgfVxuICAgIGNvbnN0IGxhc3RFbnRpdHkgPSBlbnRpdGllcy5hdCgtMSk7XG4gICAgaWYgKGxhc3RFbnRpdHkgJiYgbGFzdEVudGl0eS5pbmRpY2VzWzFdID4gdHdlZXQuZGlzcGxheV90ZXh0X3JhbmdlWzFdKSB7XG4gICAgICAgIGxhc3RFbnRpdHkuaW5kaWNlc1sxXSA9IHR3ZWV0LmRpc3BsYXlfdGV4dF9yYW5nZVsxXTtcbiAgICB9XG59XG4vKipcbiAqIEVucmljaGVzIGEgdHdlZXQgd2l0aCBhZGRpdGlvbmFsIGRhdGEgdXNlZCB0byBtb3JlIGVhc2lseSB1c2UgdGhlIHR3ZWV0IGluIGEgVUkuXG4gKi8gZXhwb3J0IGNvbnN0IGVucmljaFR3ZWV0ID0gKHR3ZWV0KT0+KHtcbiAgICAgICAgLi4udHdlZXQsXG4gICAgICAgIHVybDogZ2V0VHdlZXRVcmwodHdlZXQpLFxuICAgICAgICB1c2VyOiB7XG4gICAgICAgICAgICAuLi50d2VldC51c2VyLFxuICAgICAgICAgICAgdXJsOiBnZXRVc2VyVXJsKHR3ZWV0KSxcbiAgICAgICAgICAgIGZvbGxvd191cmw6IGdldEZvbGxvd1VybCh0d2VldClcbiAgICAgICAgfSxcbiAgICAgICAgbGlrZV91cmw6IGdldExpa2VVcmwodHdlZXQpLFxuICAgICAgICByZXBseV91cmw6IGdldFJlcGx5VXJsKHR3ZWV0KSxcbiAgICAgICAgaW5fcmVwbHlfdG9fdXJsOiB0d2VldC5pbl9yZXBseV90b19zY3JlZW5fbmFtZSA/IGdldEluUmVwbHlUb1VybCh0d2VldCkgOiB1bmRlZmluZWQsXG4gICAgICAgIGVudGl0aWVzOiBnZXRFbnRpdGllcyh0d2VldCksXG4gICAgICAgIHF1b3RlZF90d2VldDogdHdlZXQucXVvdGVkX3R3ZWV0ID8ge1xuICAgICAgICAgICAgLi4udHdlZXQucXVvdGVkX3R3ZWV0LFxuICAgICAgICAgICAgdXJsOiBnZXRUd2VldFVybCh0d2VldC5xdW90ZWRfdHdlZXQpLFxuICAgICAgICAgICAgZW50aXRpZXM6IGdldEVudGl0aWVzKHR3ZWV0LnF1b3RlZF90d2VldClcbiAgICAgICAgfSA6IHVuZGVmaW5lZFxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-tweet/dist/utils.js\n");

/***/ })

};
;