'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, ChevronRight, XCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import { ShowHallData } from '@/schema/ShowHallSchema'; // TS type for hall data
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import AddHallModal from '../add_hall_modal';
import { Hall } from '@/models/ShowLocation';
import { useRouter } from 'next/navigation';

interface ShowHallTableProps {
  locationId: number;
}

export const ShowHallTable = ({ locationId }: ShowHallTableProps) => {
  const { data, isLoading } = useQuery({
    queryKey: ['ShowHall', { location: Number(locationId) }],
    queryFn: () => ShowHallQuery.getByLocation(locationId),
    enabled: !!locationId,
  });
  const { push } = useRouter();

  const columns = generateTableColumns<Hall>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      hallName: { name: 'Name', type: 'text', sortable: true },
      hallCode: { name: 'Code', type: 'text', sortable: true },
      hallStyle: { name: 'Style', type: 'text', sortable: true },
      hallFloorType: { name: 'Floor Type', type: 'text', sortable: true },
      banquetCapacity: { name: 'Banquet Cap.', type: 'text', sortable: true },
      isArchived: {
        name: 'Active',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">Inactive</span>
                </>
              ) : (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Active</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(
                  <AddHallModal hallId={row.id} locationId={locationId} />,
                  {
                    ...DEFAULT_MODAL,
                    width: '60%',
                  },
                ).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowHallData>({
    hallName: {
      name: 'Name',
      type: 'text',
    },
    isArchived: {
      name: 'Active',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'false' },
          { label: 'Inactive', value: 'true' },
        ],
      },
    },
  });

  return (
    <div>
      <DataTable
        columns={columns}
        filterFields={filters}
        data={data}
        isLoading={isLoading}
        controls={
          <Button
            variant="primary"
            iconName="AddIcon"
            onClick={() => {
              modal(<AddHallModal locationId={locationId} />, {
                ...DEFAULT_MODAL,
                width: '60%',
              }).open();
            }}
          >
            Add New Hall
          </Button>
        }
      />
      <div className="flex justify-between pt-6 border-t border-slate-200 mt-3">
        <Button
          variant="outline"
          type="button"
          onClick={() => push('/dashboard/setup/show-facility')}
        >
          Cancel
        </Button>
        <Button
          variant="main"
          type="button"
          onClick={() =>
            push(`/dashboard/setup/show-facility/${locationId}/contact`)
          }
        >
          Save & Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default ShowHallTable;
