import AuthQuery from '@/services/queries/AuthQuery';
import { getQueryClient } from '@/utils/query-client';
import WelcomeBanner from '@/components/ui/welcome-banner';
import EventsTable from '@/components/ui/events-table';

export default async function Page() {
  const queryClient = getQueryClient();

  const user = await queryClient.fetchQuery({
    queryKey: [AuthQuery.tags],
    queryFn: AuthQuery.me,
  });

  return (
    <div className="flex flex-col w-full">
      <WelcomeBanner userName={user.name} showInstructions={true} />
      <EventsTable />
    </div>
  );
}
