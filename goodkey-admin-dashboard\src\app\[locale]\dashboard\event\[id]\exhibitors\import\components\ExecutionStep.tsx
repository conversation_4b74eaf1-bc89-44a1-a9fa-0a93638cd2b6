'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Users, 
  Building, 
  Mail,
  Loader2,
  Info
} from 'lucide-react';

interface ExecutionStepProps {
  onExecute: (sendEmailInvites: boolean) => void;
  isLoading: boolean;
}

const ExecutionStep: React.FC<ExecutionStepProps> = ({ onExecute, isLoading }) => {
  const [sendEmailInvites, setSendEmailInvites] = useState(false);

  const handleExecute = () => {
    onExecute(sendEmailInvites);
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Ready to Import</h2>
        <p className="text-muted-foreground">
          All validation checks have passed and duplicates have been resolved. 
          Configure your import settings and execute the import.
        </p>
      </div>

      {/* Success Alert */}
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Data is ready for import! All validation checks have passed and duplicate conflicts have been resolved.
        </AlertDescription>
      </Alert>

      {/* Import Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Import Process</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <Building className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Companies</h4>
                <p className="text-sm text-muted-foreground">
                  New companies will be created and existing companies will be updated as needed.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <Users className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Contacts</h4>
                <p className="text-sm text-muted-foreground">
                  New contacts will be created and linked to their respective companies.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <Users className="h-5 w-5 text-purple-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Exhibitors</h4>
                <p className="text-sm text-muted-foreground">
                  Exhibitor records will be created linking contacts to this show with booth assignments.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <Mail className="h-5 w-5 text-orange-600 mt-0.5" />
              <div>
                <h4 className="font-medium">User Accounts (Optional)</h4>
                <p className="text-sm text-muted-foreground">
                  User accounts can be created for contacts with email invitations sent automatically.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Import Options */}
      <Card>
        <CardHeader>
          <CardTitle>Import Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="sendEmailInvites"
              checked={sendEmailInvites}
              onCheckedChange={(checked) => setSendEmailInvites(checked as boolean)}
              disabled={isLoading}
            />
            <div className="space-y-1">
              <label 
                htmlFor="sendEmailInvites" 
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                Send Email Invitations
              </label>
              <p className="text-sm text-muted-foreground">
                Create user accounts for all contacts and send email invitations with login credentials. 
                Default password will be "blue".
              </p>
            </div>
          </div>
          
          {sendEmailInvites && (
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                Email invitations will be sent to all contacts with valid email addresses. 
                Make sure your email service is configured properly.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Warning */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> This action cannot be undone. Make sure you have reviewed all the data 
          and are ready to proceed with the import. The process may take a few minutes depending on the number of records.
        </AlertDescription>
      </Alert>

      {/* Execute Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleExecute}
          disabled={isLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Execute Import
            </>
          )}
        </Button>
      </div>

      {/* Additional Information */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-800 mb-2">What happens during import?</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Companies are created or updated in the database</li>
            <li>• Contacts are created and linked to their companies</li>
            <li>• Exhibitor records are created for this show</li>
            <li>• Booth numbers are parsed and assigned</li>
            {sendEmailInvites && (
              <>
                <li>• User accounts are created with default password "blue"</li>
                <li>• Email invitations are sent to all contacts</li>
              </>
            )}
            <li>• A detailed import report is generated</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExecutionStep;
