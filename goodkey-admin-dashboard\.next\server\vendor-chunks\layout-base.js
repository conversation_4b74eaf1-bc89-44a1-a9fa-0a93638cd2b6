/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/layout-base";
exports.ids = ["vendor-chunks/layout-base"];
exports.modules = {

/***/ "(ssr)/./node_modules/layout-base/layout-base.js":
/*!*************************************************!*\
  !*** ./node_modules/layout-base/layout-base.js ***!
  \*************************************************/
/***/ (function(module) {

eval("(function webpackUniversalModuleDefinition(root, factory) {\n\tif(true)\n\t\tmodule.exports = factory();\n\telse {}\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __nested_webpack_require_543__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __nested_webpack_require_543__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__nested_webpack_require_543__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__nested_webpack_require_543__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__nested_webpack_require_543__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__nested_webpack_require_543__.d = function(exports, name, getter) {\n/******/ \t\tif(!__nested_webpack_require_543__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__nested_webpack_require_543__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__nested_webpack_require_543__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__nested_webpack_require_543__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__nested_webpack_require_543__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __nested_webpack_require_543__(__nested_webpack_require_543__.s = 26);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __nested_webpack_require_4947__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __nested_webpack_require_4947__(2);\nvar IGeometry = __nested_webpack_require_4947__(8);\nvar IMath = __nested_webpack_require_4947__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __nested_webpack_require_8167__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __nested_webpack_require_8167__(2);\nvar Integer = __nested_webpack_require_8167__(10);\nvar RectangleD = __nested_webpack_require_8167__(13);\nvar LayoutConstants = __nested_webpack_require_8167__(0);\nvar RandomSeed = __nested_webpack_require_8167__(16);\nvar PointD = __nested_webpack_require_8167__(4);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth > width) {\n        this.rect.x -= (this.labelWidth - width) / 2;\n        this.setWidth(this.labelWidth);\n      }\n\n      if (this.labelHeight > height) {\n        if (this.labelPos == \"center\") {\n          this.rect.y -= (this.labelHeight - height) / 2;\n        } else if (this.labelPos == \"top\") {\n          this.rect.y -= this.labelHeight - height;\n        }\n        this.setHeight(this.labelHeight);\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __nested_webpack_require_17549__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __nested_webpack_require_17549__(2);\nvar Integer = __nested_webpack_require_17549__(10);\nvar LayoutConstants = __nested_webpack_require_17549__(0);\nvar LGraphManager = __nested_webpack_require_17549__(6);\nvar LNode = __nested_webpack_require_17549__(3);\nvar LEdge = __nested_webpack_require_17549__(1);\nvar RectangleD = __nested_webpack_require_17549__(13);\nvar Point = __nested_webpack_require_17549__(12);\nvar LinkedList = __nested_webpack_require_17549__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __nested_webpack_require_27617__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __nested_webpack_require_27617__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __nested_webpack_require_27617__(5); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __nested_webpack_require_38707__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __nested_webpack_require_38707__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __nested_webpack_require_40298__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __nested_webpack_require_40298__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __nested_webpack_require_64072__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __nested_webpack_require_64072__(0);\nvar LGraphManager = __nested_webpack_require_64072__(6);\nvar LNode = __nested_webpack_require_64072__(3);\nvar LEdge = __nested_webpack_require_64072__(1);\nvar LGraph = __nested_webpack_require_64072__(5);\nvar PointD = __nested_webpack_require_64072__(4);\nvar Transform = __nested_webpack_require_64072__(17);\nvar Emitter = __nested_webpack_require_64072__(27);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __nested_webpack_require_81860__) {\n\n\"use strict\";\n\n\nvar PointD = __nested_webpack_require_81860__(4);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __nested_webpack_require_84747__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __nested_webpack_require_84747__(15);\nvar FDLayoutConstants = __nested_webpack_require_84747__(7);\nvar LayoutConstants = __nested_webpack_require_84747__(0);\nvar IGeometry = __nested_webpack_require_84747__(8);\nvar IMath = __nested_webpack_require_84747__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.idealEdgeLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n  this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    edge.idealLength = this.idealEdgeLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += FDLayoutConstants.DEFAULT_EDGE_LENGTH * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = this.springConstant * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      repulsionForce = this.repulsionConstant * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __nested_webpack_require_100902__) {\n\n\"use strict\";\n\n\nvar LEdge = __nested_webpack_require_100902__(1);\nvar FDLayoutConstants = __nested_webpack_require_100902__(7);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __nested_webpack_require_101387__) {\n\n\"use strict\";\n\n\nvar LNode = __nested_webpack_require_101387__(3);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __nested_webpack_require_103173__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __nested_webpack_require_103173__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __nested_webpack_require_103901__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __nested_webpack_require_103901__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __nested_webpack_require_105138__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __nested_webpack_require_105138__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __nested_webpack_require_115611__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __nested_webpack_require_115611__(18);\nlayoutBase.FDLayoutConstants = __nested_webpack_require_115611__(7);\nlayoutBase.FDLayoutEdge = __nested_webpack_require_115611__(19);\nlayoutBase.FDLayoutNode = __nested_webpack_require_115611__(20);\nlayoutBase.DimensionD = __nested_webpack_require_115611__(21);\nlayoutBase.HashMap = __nested_webpack_require_115611__(22);\nlayoutBase.HashSet = __nested_webpack_require_115611__(23);\nlayoutBase.IGeometry = __nested_webpack_require_115611__(8);\nlayoutBase.IMath = __nested_webpack_require_115611__(9);\nlayoutBase.Integer = __nested_webpack_require_115611__(10);\nlayoutBase.Point = __nested_webpack_require_115611__(12);\nlayoutBase.PointD = __nested_webpack_require_115611__(4);\nlayoutBase.RandomSeed = __nested_webpack_require_115611__(16);\nlayoutBase.RectangleD = __nested_webpack_require_115611__(13);\nlayoutBase.Transform = __nested_webpack_require_115611__(17);\nlayoutBase.UniqueIDGeneretor = __nested_webpack_require_115611__(14);\nlayoutBase.Quicksort = __nested_webpack_require_115611__(24);\nlayoutBase.LinkedList = __nested_webpack_require_115611__(11);\nlayoutBase.LGraphObject = __nested_webpack_require_115611__(2);\nlayoutBase.LGraph = __nested_webpack_require_115611__(5);\nlayoutBase.LEdge = __nested_webpack_require_115611__(1);\nlayoutBase.LGraphManager = __nested_webpack_require_115611__(6);\nlayoutBase.LNode = __nested_webpack_require_115611__(3);\nlayoutBase.Layout = __nested_webpack_require_115611__(15);\nlayoutBase.LayoutConstants = __nested_webpack_require_115611__(0);\nlayoutBase.NeedlemanWunsch = __nested_webpack_require_115611__(25);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/layout-base/layout-base.js\n");

/***/ })

};
;