import type { Metadata } from 'next';
import { ReactNode } from 'react';

import { Provider } from '@/components/app_provider/app-provider';
import { OverlayContainer } from '@/components/ui/overlay/OverlayContainer';
import { Toaster } from '@/components/ui/toaster';
import '../globals.scss';
import { getQueryClient } from '@/utils/query-client';

import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { cn } from '@/lib/utils';

export const metadata: Metadata = {
  title: 'Goodkey Show Services Ltd',
  description: 'Goodkey Show Services Ltd',
  icons: {
    icon: '/favicon-good-key.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const queryClient = getQueryClient();
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn('flex flex-col h-[100vh] overflow-hidden')}>
        <Provider>
          <HydrationBoundary state={dehydrate(queryClient)}>
            {children}
            <Toaster />
            <OverlayContainer />
          </HydrationBoundary>
        </Provider>
      </body>
    </html>
  );
}
