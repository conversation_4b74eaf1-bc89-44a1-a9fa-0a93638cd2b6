'use client';

import { useQuery } from '@tanstack/react-query';
import { Control } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Field from '@/components/ui/inputs/field/field';
import CompanyQuery from '@/services/queries/CompanyQuery';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import { ShowExhibitorCreateFormData } from '@/schema/ShowExhibitorSchema';
import { ContactInList } from '@/models/Contact';
import { BriefData } from '@/models/BriefData';

interface ContactFormProps {
  control: Control<ShowExhibitorCreateFormData>;
  selectedCompanyId: number | null;
  useExistingContact: boolean;
}

export default function ContactForm({
  control,
  selectedCompanyId,
  useExistingContact,
}: ContactFormProps) {
  // Fetch contact types
  const { data: contactTypes } = useQuery<BriefData[]>({
    queryKey: ContactTypeQuery.tags,
    queryFn: ContactTypeQuery.getAll,
  });

  // Fetch contacts for selected company
  const { data: contacts } = useQuery<ContactInList[]>({
    queryKey: [CompanyQuery.tags, 'contacts', selectedCompanyId],
    queryFn: () => CompanyQuery.contacts.getAll(selectedCompanyId!),
    enabled: !!selectedCompanyId && useExistingContact,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Contact Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Field
          control={control}
          name="useExistingContact"
          label="Use existing contact"
          type="checkbox"
        />

        {useExistingContact ? (
          <Field
            control={control}
            name="contactId"
            label="Select Contact"
            type={{
              type: 'select',
              props: {
                options:
                  contacts?.map((contact) => ({
                    label: contact.fullName,
                    value: contact.id.toString(),
                  })) || [],
                placeholder: 'Select a contact',
              },
            }}
            required
          />
        ) : (
          <div className="grid grid-cols-2 gap-4">
            <Field
              control={control}
              name="contactTypeId"
              label="Contact Type"
              type={{
                type: 'select',
                props: {
                  options:
                    contactTypes?.map((type) => ({
                      label: type.name,
                      value: type.id.toString(),
                    })) || [],
                  placeholder: 'Select contact type',
                },
              }}
              required
            />
            <div />
            <Field
              control={control}
              name="firstName"
              label="First Name"
              type="text"
              required
            />
            <Field
              control={control}
              name="lastName"
              label="Last Name"
              type="text"
              required
            />
            <Field
              control={control}
              name="email"
              label="Email"
              type="email"
              required
            />
            <Field
              control={control}
              name="telephone"
              label="Telephone"
              type="text"
            />
            <Field
              control={control}
              name="ext"
              label="Extension"
              type="text"
            />
            <Field
              control={control}
              name="cellphone"
              label="Cellphone"
              type="text"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
