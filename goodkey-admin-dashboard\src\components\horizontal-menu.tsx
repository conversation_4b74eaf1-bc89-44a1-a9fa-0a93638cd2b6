'use client';

import { useQuery } from '@tanstack/react-query';
import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import type { MenuItem } from '@/models/Menu';
import { PermissionKey } from '@/models/Permission';
import AuthQuery from '@/services/queries/AuthQuery';
import MenuQuery from '@/services/queries/MenuQuery';
import UserDropdown from '@/components/app_header/components/user_dropdown/user-dropdown';
import Link from 'next/link';
import { icons } from '@/common/menu-icons';

interface HorizontalMenuProps {}

function processMenuItems(items: MenuItem[]): MenuItem[] {
  return items.map((item) => ({
    ...item,
    permission:
      PermissionKey[item.permissionKey as keyof typeof PermissionKey] ??
      item.permissionKey,
    submenu: item.submenu?.length ? processMenuItems(item.submenu) : undefined,
  }));
}

function filterMenuItemsByUserMenuItems(
  items: MenuItem[],
  userMenuItems: number[],
): MenuItem[] {
  return items
    .map((item) => ({
      ...item,
      submenu: item.submenu
        ? filterMenuItemsByUserMenuItems(item.submenu, userMenuItems)
        : undefined,
    }))
    .filter((item) => {
      if (!item.menuItemId) return true;
      const isAuthorized = userMenuItems.includes(item.menuItemId);
      return isAuthorized || (item.submenu && item.submenu.length > 0);
    });
}

function filterMenuItemsBySection(
  items: MenuItem[],
  section: string,
): MenuItem[] {
  return items
    .map((item) => ({
      ...item,
      submenu: item.submenu
        ? filterMenuItemsBySection(item.submenu, section)
        : undefined,
    }))
    .filter((item) => {
      return (
        item.section === section || (item.submenu && item.submenu.length > 0)
      );
    });
}

export default function HorizontalMenu() {
  const router = useRouter();
  const pathname = usePathname() || '';
  const [active, setActive] = useState('');

  const {
    data: userData,
    isLoading: isLoadingUserData,
    isError: isUserDataError,
  } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  const {
    data: rawItems,
    isLoading: isLoadingItems,
    isError: isItemsError,
  } = useQuery({
    queryKey: [MenuQuery.tags, 'brief'],
    queryFn: MenuQuery.getBrief,
  });

  const processedItems = useMemo(() => {
    return rawItems ? processMenuItems(rawItems) : [];
  }, [rawItems]);

  const menuItems = useMemo(() => {
    if (userData && processedItems.length > 0) {
      return filterMenuItemsBySection(
        filterMenuItemsByUserMenuItems(processedItems, userData.menuItems),
        'main',
      );
    }
    return [];
  }, [userData, processedItems]);

  useEffect(() => {
    if (pathname && menuItems.length > 0) {
      let bestMatch: MenuItem | null = null;
      let bestMatchSegmentCount = 0;

      for (const item of menuItems) {
        if (!item.path) continue;

        const itemPathSegments = item.path.split('/').filter(Boolean);
        const segmentCount = itemPathSegments.length;

        if (
          pathname.startsWith(item.path) &&
          segmentCount > bestMatchSegmentCount &&
          (pathname === item.path || pathname.startsWith(`${item.path}/`))
        ) {
          bestMatch = item;
          bestMatchSegmentCount = segmentCount;
        }
      }

      if (bestMatch) {
        setActive(bestMatch.title);
      } else {
        setActive(menuItems[0]?.title || '');
      }
    }
  }, [pathname, menuItems]);

  const handleMenuClick = (item: MenuItem) => {
    setActive(item.title);
  };

  if (isLoadingUserData || isLoadingItems) {
    return (
      <div className=" ">
        <div className="flex flex-wrap">
          {[1, 2, 3].map((i) => (
            <Button
              key={i}
              variant="ghost"
              className="h-12 px-6 rounded-none text-base font-medium transition-colors opacity-50"
              disabled
            >
              Loading...
            </Button>
          ))}
        </div>
      </div>
    );
  }

  if (isUserDataError || isItemsError) {
    return (
      <div className="">
        <div className="flex flex-wrap">
          <Button
            variant="ghost"
            className="h-12 px-6 rounded-none text-base font-medium transition-colors text-red-500"
            disabled
          >
            Error loading menu
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="flex flex-wrap justify-between">
        <div className="flex flex-wrap gap-7">
          {menuItems.map((item: MenuItem) => {
            const IconComponent = item.icon
              ? (icons as any)[item.icon as any]
              : undefined;

            return (
              <Link
                key={item.title}
                href={item.path}
                className={cn(
                  'flex flex-row items-center gap-2 px-0 text-slate-700 hover:text-slate-900  rounded-none text-base  font-medium transition-colors uppercase',
                  active === item.title
                    ? 'border-b-2 border-teal-500 text-teal-500 '
                    : ' hover:bg-slate-50',
                )}
                onClick={() => handleMenuClick(item)}
              >
                {IconComponent && <IconComponent className="h-5 w-5 " />}
                {item.title}
              </Link>
            );
          })}
        </div>

        <div className="flex items-center px-4">
          <UserDropdown />
        </div>
      </div>
    </div>
  );
}
