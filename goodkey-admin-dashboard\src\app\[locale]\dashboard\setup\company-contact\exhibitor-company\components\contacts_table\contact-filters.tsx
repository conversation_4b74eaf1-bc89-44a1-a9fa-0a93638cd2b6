import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Filter, X, ChevronDown, ChevronUp } from 'lucide-react';

export interface ContactFilters {
  name?: string;
  contactType?: string;
  showArchived: boolean;
}

interface ContactFiltersProps {
  filters: ContactFilters;
  onFilterChange: (filters: ContactFilters) => void;
  onResetFilters: () => void;
  compact?: boolean;
}

const ContactFiltersComponent = ({
  filters,
  onFilterChange,
  onResetFilters,
  compact = true,
}: ContactFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onFilterChange({ ...filters, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    onFilterChange({ ...filters, [name]: value });
  };

  const handleArchivedChange = (value: string) => {
    onFilterChange({ ...filters, showArchived: value === 'true' });
  };

  // Count active filters
  const activeFilterCount = [
    filters.name,
    filters.contactType,
    filters.showArchived,
  ].filter(Boolean).length;

  return (
    <div className={`${compact ? 'mb-4' : 'mb-6'}`}>
      <div className="flex items-center justify-between border-b pb-2">
        <div className="flex items-center gap-2">
          <Filter className="h-3.5 w-3.5 text-gray-500" />
          <h3 className="text-xs font-medium text-gray-700">Contact Filters</h3>
          {activeFilterCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 bg-brand-brown/10 text-brand-brown hover:bg-brand-brown/20 text-[10px] px-1.5 py-0"
            >
              {activeFilterCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-500 hover:text-gray-700 p-1 h-6 text-xs"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-3 w-3 mr-1" /> Hide
              </>
            ) : (
              <>
                <ChevronDown className="h-3 w-3 mr-1" /> Show
              </>
            )}
          </Button>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onResetFilters}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-6 text-xs"
            >
              <X className="h-3 w-3 mr-1" /> Clear
            </Button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-3 pt-1">
          <div>
            <Label
              htmlFor="contactName"
              className="text-xs font-medium text-gray-600 mb-1 block"
            >
              Contact Name
            </Label>
            <Input
              id="contactName"
              name="name"
              placeholder="Search by name"
              value={filters.name || ''}
              onChange={handleInputChange}
              className="h-8 text-sm"
            />
          </div>

          <div>
            <Label
              htmlFor="contactType"
              className="text-xs font-medium text-gray-600 mb-1 block"
            >
              Contact Type
            </Label>
            <Input
              id="contactType"
              name="contactType"
              placeholder="Filter by type"
              value={filters.contactType || ''}
              onChange={handleInputChange}
              className="h-8 text-sm"
            />
          </div>

          <div>
            <Label
              htmlFor="showContactArchived"
              className="text-xs font-medium text-gray-600 mb-1 block"
            >
              Show Archived
            </Label>
            <Select
              value={filters.showArchived ? 'true' : 'false'}
              onValueChange={(value) => handleArchivedChange(value)}
            >
              <SelectTrigger id="showContactArchived" className="h-8 text-sm">
                <SelectValue placeholder="Show archived?" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="false">No</SelectItem>
                <SelectItem value="true">Yes</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Active filter badges */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1.5 mt-3">
          {filters.name && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-0.5 px-1.5 text-xs"
            >
              Name: {filters.name}
              <X
                className="h-2.5 w-2.5 ml-1 cursor-pointer"
                onClick={() => onFilterChange({ ...filters, name: '' })}
              />
            </Badge>
          )}
          {filters.contactType && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-0.5 px-1.5 text-xs"
            >
              Type: {filters.contactType}
              <X
                className="h-2.5 w-2.5 ml-1 cursor-pointer"
                onClick={() => onFilterChange({ ...filters, contactType: '' })}
              />
            </Badge>
          )}
          {filters.showArchived && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-0.5 px-1.5 text-xs"
            >
              Archived
              <X
                className="h-2.5 w-2.5 ml-1 cursor-pointer"
                onClick={() =>
                  onFilterChange({ ...filters, showArchived: false })
                }
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default ContactFiltersComponent;
