import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import AppLayout from '@/components/ui/app_layout';
import GalleryQuery from '@/services/queries/GalleryQuery';
import { getQueryClient } from '@/utils/query-client';
import GalleryManagementClient from './components/GalleryManagementClient';

export default async function GalleryManagementPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: ['gallery', 'categories'],
    queryFn: GalleryQuery.getCategories,
  });
  await queryClient.prefetchQuery({
    queryKey: ['gallery', 'subcategories'],
    queryFn: GalleryQuery.getSubcategories,
  });
  await queryClient.prefetchQuery({
    queryKey: ['gallery', 'images'],
    queryFn: GalleryQuery.getImages,
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Gallery Management',
          link: '/dashboard/setup/public-site/gallery-management',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <GalleryManagementClient />
      </HydrationBoundary>
    </AppLayout>
  );
}
