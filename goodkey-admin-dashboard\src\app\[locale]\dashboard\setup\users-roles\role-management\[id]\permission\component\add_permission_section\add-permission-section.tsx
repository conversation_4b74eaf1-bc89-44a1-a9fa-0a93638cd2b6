'use client';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { Permission, PermissionKey } from '@/models/Permission';
import AuthQuery from '@/services/queries/AuthQuery';
import RoleQuery from '@/services/queries/RoleQuery';
import { getQueryClient } from '@/utils/query-client';

import SinglePermission from '../single_permission';

interface IAddPermissionSection {
  data: Permission[];
  selected: PermissionKey[];
  id: number;
}
function PermissionForm({ data, selected, id }: IAddPermissionSection) {
  const { toast } = useToast();

  const [selectedState, setSelectedState] = useState<PermissionKey[]>(
    selected || [],
  );
  const { mutate, isPending } = useMutation({
    mutationFn: (permissions: PermissionKey[]) =>
      RoleQuery.setPermissions(id)(permissions),
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });
      await getQueryClient().invalidateQueries({
        queryKey: [...RoleQuery.tags, { id }],
      });
      toast({ variant: 'success', title: 'Role updated' });
    },
  });

  const groupedPermissions = useMemo(() => {
    const groups: { [key: string]: Permission[] } = {};
    data.forEach((permission) => {
      const section = permission.code.split('.')[0];
      if (!groups[section]) {
        groups[section] = [];
      }
      groups[section].push(permission);
    });
    return groups;
  }, [data]);

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-end items-center">
          <div className="w-full space-y-2">
            <CardTitle> Permissions</CardTitle>
            <CardDescription>Set Role Permissions</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              disabled={isPending}
              onClick={() => {
                mutate(selectedState);
              }}
              className="w-fit self-end"
              iconName="SaveIcon"
              iconProps={{ className: 'text-success-foreground', size: 20 }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full text-sm flex flex-col gap-6">
          {Object.entries(groupedPermissions)
            .sort(([sectionA], [sectionB]) => sectionA.localeCompare(sectionB))
            .map(([section, permissions]) => (
              <div
                key={section}
                className="border-b pb-4 last:border-b-0 last:pb-0"
              >
                <h3 className="text-lg font-semibold mb-3 capitalize text-primary">
                  {capitalizeFirstLetter(section.replace(/_/g, ' '))}
                </h3>
                <ul className="space-y-2">
                  {permissions
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((item) => (
                      <SinglePermission
                        key={item.id}
                        permission={item}
                        isActivated={selectedState.includes(item.code)}
                        onChange={(isChecked) => {
                          if (isChecked && !selectedState.includes(item.code)) {
                            setSelectedState([...selectedState, item.code]);
                          } else {
                            setSelectedState(
                              selectedState.filter((key) => key !== item.code),
                            );
                          }
                        }}
                      />
                    ))}
                </ul>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  );
}
function AddPermissionSection({
  data,
  id,
}: {
  data: Permission[];
  id: number;
}) {
  const {
    data: selected,
    isLoading,
    isPaused,
  } = useQuery({
    queryKey: [...RoleQuery.tags, { id }],
    queryFn: () => RoleQuery.getOne(id!),
    enabled: id != undefined,
    select: (d): PermissionKey[] => {
      return (d.permissions as PermissionKey[]) || [];
    },
  });
  return (
    <Suspense isLoading={isLoading && !selected}>
      {selected && <PermissionForm data={data} selected={selected} id={id} />}
    </Suspense>
  );
}

export default AddPermissionSection;
