'use client';

import './style/index.scss';

import { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import useConnectionStore from '@/services/zustand/ConnectionStore';
import { useRouter } from '@/utils/navigation';
import SessionExpired from '../modals/session_expired';
import HorizontalMenu from '@/components/horizontal-menu';

import { modal, STOP_MODAL } from '../ui/overlay';
// Remove the Image import and import the SVG directly
import GssLogo from '../../../public/gss-logo.svg';

interface IAppHeader {}

function AppHeader({}: IAppHeader) {
  const [open, setOpen] = useState(false);
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });
  const isConnected = useConnectionStore((state) => state.isConnected);
  const { push } = useRouter();
  useEffect(() => {
    useConnectionStore.persist.rehydrate();
    if (
      !useConnectionStore.getState().isConnected &&
      useConnectionStore.getState().connectionStatus == 'expired'
    ) {
      modal(({ close }) => <SessionExpired close={close} />, STOP_MODAL).open();
    }
  }, [isConnected, push]);
  useEffect(() => {
    if (!isMobile) {
      setOpen(false);
    }
  }, [isMobile]);
  return (
    <div className="app-header w-full flex  flex-row sticky top-0 z-30  h-24 min-h-24   items-center justify-between border-b border-slate-200 bg-white px-6  ">
      <div className=" w-full flex  flex-row  container  px-2 mx-auto   items-center justify-between ">
        <GssLogo className=" h-[95px] " />

        <div className="flex items-center space-x-4 py-8  ">
          <HorizontalMenu />
        </div>
      </div>
    </div>
  );
}

export default AppHeader;
