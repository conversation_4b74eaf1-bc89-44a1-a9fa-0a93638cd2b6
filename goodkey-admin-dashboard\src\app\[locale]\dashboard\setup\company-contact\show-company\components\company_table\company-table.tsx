'use client';

import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { CompanyInList } from '@/models/Company';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import ContactsTable from '../contacts_table/contacts-table';
import { ChevronDownIcon, ChevronRightIcon } from '@/assets/Icons';

export const CompanyTable = () => {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());

  const { data, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, 'Show manager'],
    queryFn: () => CompanyQuery.getAll('Show manager'),
  });

  const toggleRow = (companyId: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(companyId)) {
      newExpanded.delete(companyId);
    } else {
      newExpanded.add(companyId);
    }
    setExpandedRows(newExpanded);
  };

  const { data: provinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const columns = generateTableColumns<CompanyInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      city: { name: 'City', type: 'text', sortable: true },
      province: { name: 'Province', type: 'text', sortable: true },
      country: { name: 'Country', type: 'text', sortable: true },
      numberOfShows: { name: 'Number of Shows', type: 'text', sortable: true },
      // phone: { name: 'Phone', type: 'text', sortable: true },
      isArchived: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                  Archived
                </span>
              ) : (
                <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 w-full ">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleRow(row.id)}
                title={
                  expandedRows.has(row.id) ? 'Hide Contacts' : 'Show Contacts'
                }
              >
                {expandedRows.has(row.id) ? (
                  <ChevronDownIcon size={16} />
                ) : (
                  <ChevronRightIcon size={16} />
                )}
              </Button>
              <Link
                href={`/dashboard/setup/company-contact/show-company/${row.id}/view`}
              >
                <Button
                  size="sm"
                  variant="outline"
                  iconName="EyeIcon"
                  title="View Details"
                ></Button>
              </Link>
              <Link
                href={`/dashboard/setup/company-contact/show-company/${row.id}`}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  title="Edit Company"
                ></Button>
              </Link>
            </div>
          ),
        },
      },
    },
    false,
    false,
    { column: 'name', direction: 'asc' },
  );

  const filters = generateTableFilters<CompanyInList>({
    name: {
      name: 'Name',
      type: 'text',
    },
    province: {
      name: 'Province',
      type: {
        type: 'select',
        options:
          provinces?.map((province) => ({
            label: province.name,
            value: province.name,
          })) || [],
      },
    },
    country: {
      name: 'Country',
      type: {
        type: 'select',
        options:
          countries?.map((country) => ({
            label: country.name,
            value: country.name,
          })) || [],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      expandedRows={expandedRows}
      disableStripedRows
      renderExpandedRow={(row) => (
        <div className="p-3">
          <ContactsTable companyId={row.id} companyName={row.name} />
        </div>
      )}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/company-contact/show-company/add">
            <Button variant="main" iconName="AddIcon">
              Add New Company
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default CompanyTable;
