import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  OnSiteMaterialHandlingSchema,
  OnSiteMaterialHandlingFormType,
} from '@/schema/ServiceFormSchemas';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

interface OnSiteMaterialHandlingFormProps {
  onSubmit: (data: OnSiteMaterialHandlingFormType) => void;
  initialData?: Partial<OnSiteMaterialHandlingFormType>;
  isLoading?: boolean;
}

export function OnSiteMaterialHandlingForm({
  onSubmit,
  initialData,
  isLoading = false,
}: OnSiteMaterialHandlingFormProps) {
  const form = useForm<OnSiteMaterialHandlingFormType>({
    resolver: zodResolver(OnSiteMaterialHandlingSchema),
    defaultValues: {
      receivingAddress:
        initialData?.receivingAddress ||
        'Vancouver Convention Centre East\n999 Canada Place Vancouver\nBritish Columbia Canada',
      daysHours: initialData?.daysHours || '',
      pricePerWeight: initialData?.pricePerWeight || 0,
      weight: initialData?.weight || 0,
      weightUnit: initialData?.weightUnit || 'lbs',
      minimumCharge: initialData?.minimumCharge || 0,
    },
  });

  const handleSubmit = (data: OnSiteMaterialHandlingFormType) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <Field
          control={form.control}
          name="receivingAddress"
          type="textarea"
          label="Receiving Address"
          className="min-h-[80px] bg-gray-50"
          readOnly
        />

        <Field
          control={form.control}
          name="daysHours"
          type="textarea"
          label="Please arrange shipments to arrive between"
          placeholder="monday"
          className="min-h-[80px]"
        />

        <div className="grid grid-cols-3 gap-4">
          <div className="relative">
            <Field
              control={form.control}
              name="pricePerWeight"
              type="number"
              label="Price/Weight"
              step="0.01"
              min="0"
              placeholder="1.00"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
          </div>

          <Field
            control={form.control}
            name="weight"
            type="number"
            label="Weight"
            step="0.01"
            min="0"
            placeholder="1"
          />

          <Field
            control={form.control}
            name="weightUnit"
            type={{
              type: 'select',
              props: {
                options: [
                  { label: 'lbs', value: 'lbs' },
                  { label: 'kg', value: 'kg' },
                ],
                placeholder: 'Select unit',
              },
            }}
            label="Unit"
          />
        </div>

        <div className="relative max-w-xs">
          <Field
            control={form.control}
            name="minimumCharge"
            type="number"
            label="Minimum charge"
            step="0.01"
            min="0"
            placeholder="94.00"
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
            $
          </span>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> There are currently orders on this service.
            Rate cannot be modified.
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isLoading}
            iconName="SaveIcon"
            iconProps={{ className: 'text-white' }}
          >
            {isLoading ? 'Saving...' : 'Save Information'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
