﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class Shows
    {
        public Shows()
        {
            ShowDocuments = new HashSet<ShowDocuments>();
            ShowExhibitors = new HashSet<ShowExhibitors>();
            ShowSchedules = new HashSet<ShowSchedules>();
            ShowsPromoters = new HashSet<ShowsPromoters>();
        }

        public int Id { get; set; }
        public bool Archive { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? DisplayDate { get; set; }
        public DateTime? OrderDeadlineDate { get; set; }
        public string LateChargePercentage { get; set; }
        public string Location { get; set; }
        public string Link { get; set; }
        public string Description { get; set; }
        public bool Display { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public int? LocationId { get; set; }
        public string MapLink { get; set; }
        public DateTime? KioskPrintingQueueDate { get; set; }
        public bool View { get; set; }
        public int? HallId { get; set; }
        public int? ContactId { get; set; }
        public int? ProvinceId { get; set; }

        public virtual Contact Contact { get; set; }
        public virtual AuthUser CreatedByNavigation { get; set; }
        public virtual ShowLocationHalls Hall { get; set; }
        public virtual ShowLocations LocationNavigation { get; set; }
        public virtual Provinces Province { get; set; }
        public virtual ICollection<ShowDocuments> ShowDocuments { get; set; }
        public virtual ICollection<ShowExhibitors> ShowExhibitors { get; set; }
        public virtual ICollection<ShowSchedules> ShowSchedules { get; set; }
        public virtual ICollection<ShowsPromoters> ShowsPromoters { get; set; }
    }
}