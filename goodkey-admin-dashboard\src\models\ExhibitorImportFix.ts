// =====================================================
// FIELD EDITING AND ERROR FIXING TYPES
// =====================================================

export interface ExhibitorImportFieldEditRequestDto {
  sessionId: string;
  fieldEdits: FieldEditDto[];
}

export interface FieldEditDto {
  rowNumber: number;
  fieldName: string;
  newValue: string;
  originalValue?: string;
  editReason?: string; // "UserEdit", "ErrorFix", "ValidationFix"
}

export interface ExhibitorImportFieldEditResponseDto {
  success: boolean;
  message: string;
  results: FieldEditResultDto[];
  updatedSummary: ExhibitorImportValidationSummaryDto;
}

export interface FieldEditResultDto {
  rowNumber: number;
  fieldName: string;
  success: boolean;
  errorMessage?: string;
  validationFixed: boolean;
  remainingErrors: string[];
}

// =====================================================
// BULK ERROR FIXING TYPES
// =====================================================

export interface ExhibitorImportBulkFixRequestDto {
  sessionId: string;
  errorFixes: ValidationErrorFixDto[];
  duplicateFixes: DuplicateFixDto[];
  revalidateAfterFix?: boolean;
}

export interface ValidationErrorFixDto {
  rowNumber: number;
  fieldName: string;
  fixedValue: string;
  originalValue?: string;
  validationRule?: string;
  messageCode?: string;
}

export interface DuplicateFixDto {
  duplicateId: number;
  resolution: string; // "UseExcel", "UseDatabase", "Merge", "Custom"
  fieldResolutions: FieldResolutionDto[];
}

export interface FieldResolutionDto {
  fieldName: string;
  selectedValue: string;
  excelValue?: string;
  databaseValue?: string;
  customValue?: string;
  source?: string; // "Excel", "Database", "Custom"
}

export interface ExhibitorImportBulkFixResponseDto {
  success: boolean;
  message: string;
  fixedErrorsCount: number;
  fixedDuplicatesCount: number;
  remainingErrorsCount: number;
  remainingDuplicatesCount: number;
  results: FieldEditResultDto[];
  updatedSummary: ExhibitorImportValidationSummaryDto;
}

// =====================================================
// ROW COMPARISON TYPES
// =====================================================

export interface ExhibitorImportRowComparisonRequestDto {
  sessionId: string;
  rowNumber: number;
  compareWithContactId?: number;
  compareWithCompanyId?: number;
}

export interface ExhibitorImportRowComparisonResponseDto {
  excelData: ExhibitorImportRowComparisonDto;
  databaseData?: ExhibitorImportRowComparisonDto;
  fieldComparisons: FieldComparisonDto[];
  validationMessages: ExhibitorImportValidationMessageDto[];
}

export interface ExhibitorImportRowComparisonDto {
  source: string; // "Excel", "Database"
  companyName: string;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  contactFirstName: string;
  contactLastName: string;
  contactEmail: string;
  contactPhone: string;
  contactMobile: string;
  boothNumbers: string;
  lastUpdated?: string;
  updatedBy?: string;
}

export interface FieldComparisonDto {
  fieldName: string;
  displayName: string;
  excelValue: string;
  databaseValue?: string;
  hasConflict: boolean;
  hasValidationError: boolean;
  validationErrors: string[];
  suggestedValue?: string;
  suggestionReason?: string;
}

// =====================================================
// VALIDATION RE-RUN TYPES
// =====================================================

export interface ExhibitorImportRevalidateRequestDto {
  sessionId: string;
  rowNumbers?: number[]; // If null, revalidate all rows
  clearExistingMessages?: boolean;
  checkDuplicates?: boolean;
}

export interface ExhibitorImportRevalidateResponseDto {
  success: boolean;
  message: string;
  updatedSummary: ExhibitorImportValidationSummaryDto;
  newValidationMessages: ExhibitorImportValidationMessageDto[];
  newDuplicates: ExhibitorImportDuplicateDto[];
}

// =====================================================
// FIELD SUGGESTIONS TYPES
// =====================================================

export interface ExhibitorImportFieldSuggestionsRequestDto {
  sessionId: string;
  rowNumber: number;
  fieldName: string;
  currentValue?: string;
}

export interface ExhibitorImportFieldSuggestionsResponseDto {
  suggestions: FieldSuggestionDto[];
}

export interface FieldSuggestionDto {
  suggestedValue: string;
  reason: string;
  confidence: number;
  source: string; // "Database", "AutoCorrect", "Pattern"
  metadata: Record<string, any>;
}

// =====================================================
// VALIDATION SUMMARY TYPE
// =====================================================

export interface ExhibitorImportValidationSummaryDto {
  totalRows: number;
  validRows: number;
  errorRows: number;
  warningRows: number;
  hasErrors: boolean;
  hasWarnings: boolean;
  hasDuplicates: boolean;
  unresolvedDuplicates: number;
}

// =====================================================
// SHARED TYPES FROM MAIN IMPORT
// =====================================================

export interface ExhibitorImportValidationMessageDto {
  rowNumber: number;
  fieldName: string;
  fieldValue: string;
  messageType: string; // "Error", "Warning"
  validationRule: string;
  messageCode: string;
  message: string;
}

export interface ExhibitorImportDuplicateDto {
  duplicateId: number;
  duplicateType: string; // "DatabaseConflict", "EmailDuplicate"
  duplicateValue: string;
  rowNumbers: number[];
  conflictDescription: string;
  requiresUserDecision: boolean;
  existingRecordId?: number;
  existingRecordType?: string;
  fieldConflicts: ExhibitorImportFieldConflictDto[];
}

export interface ExhibitorImportFieldConflictDto {
  fieldName: string;
  excelValue: string;
  databaseValue: string;
  hasConflict: boolean;
}

// =====================================================
// UI STATE TYPES
// =====================================================

export interface FieldEditState {
  rowNumber: number;
  fieldName: string;
  originalValue: string;
  currentValue: string;
  isModified: boolean;
  isValid: boolean;
  validationErrors: string[];
  suggestions: FieldSuggestionDto[];
  isEditing: boolean;
}

export interface RowEditState {
  rowNumber: number;
  fields: Record<string, FieldEditState>;
  hasModifications: boolean;
  hasErrors: boolean;
  isExpanded: boolean;
}

export interface DuplicateResolutionState {
  duplicateId: number;
  selectedResolution: 'excel' | 'database' | 'merge' | 'custom';
  fieldResolutions: Record<string, string>;
  isResolved: boolean;
}

export interface DataFixingSessionState {
  sessionId: string;
  rows: Record<number, RowEditState>;
  duplicates: Record<number, DuplicateResolutionState>;
  summary: ExhibitorImportValidationSummaryDto;
  hasUnsavedChanges: boolean;
  isLoading: boolean;
  selectedTab: 'errors' | 'warnings' | 'duplicates' | 'all';
  showOnlyModified: boolean;
  autoSave: boolean;
}

// =====================================================
// FILTER AND SEARCH TYPES
// =====================================================

export interface DataFixingFilters {
  showOnlyErrors: boolean;
  showOnlyWarnings: boolean;
  showOnlyModified: boolean;
  showOnlyUnresolved: boolean;
  fieldNameFilter?: string;
  rowNumberFilter?: number;
  messageTypeFilter?: 'Error' | 'Warning' | 'All';
  searchQuery?: string;
}

export interface DataFixingSortOptions {
  sortBy: 'rowNumber' | 'fieldName' | 'errorCount' | 'lastModified';
  sortDirection: 'asc' | 'desc';
}

// =====================================================
// BULK OPERATION TYPES
// =====================================================

export interface BulkFixOperation {
  type: 'fix-errors' | 'resolve-duplicates' | 'apply-suggestions' | 'standardize-format';
  targetRows: number[];
  targetFields?: string[];
  parameters: Record<string, any>;
}

export interface BulkFixResult {
  operation: BulkFixOperation;
  success: boolean;
  affectedRows: number[];
  fixedCount: number;
  errorCount: number;
  errors: string[];
  summary: string;
}
