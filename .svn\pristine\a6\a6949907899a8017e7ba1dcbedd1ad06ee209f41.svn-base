namespace goodkey_common.DTO
{
    public class ExcelRowData
    {
        public int RowNumber { get; set; }

        // Company Data
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress1 { get; set; }
        public string CompanyAddress2 { get; set; }
        public string CompanyCity { get; set; }
        public string CompanyProvince { get; set; }
        public string CompanyPostalCode { get; set; }
        public string CompanyCountry { get; set; }
        public string CompanyWebsite { get; set; }

        // Contact Data
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string ContactType { get; set; }

        // Booth Data
        public string BoothNumbers { get; set; }
    }

    public class ExcelValidationMessageDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
        public string MessageType { get; set; } // Error, Warning, Info
        public string ValidationRule { get; set; }
        public string MessageCode { get; set; }
        public string Message { get; set; }
    }

    public class ExcelTemplateInfoDto
    {
        public string TemplateName { get; set; }
        public string Description { get; set; }
        public List<string> RequiredColumns { get; set; }
        public List<string> OptionalColumns { get; set; }
        public Dictionary<string, string> ColumnMappings { get; set; }
        public List<ExcelColumnInfo> Columns { get; set; }
        public Dictionary<string, List<string>> ValidValues { get; set; }
    }

    public class ExcelColumnInfo
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string DataType { get; set; }
        public bool IsRequired { get; set; }
        public string Description { get; set; }
        public List<string> ValidValues { get; set; }
        public string ValidationPattern { get; set; }
        public string Format { get; set; }
        public string Example { get; set; }
    }

    public class FixSuggestionDto
    {
        public string FieldName { get; set; }
        public string OriginalValue { get; set; }
        public string SuggestedValue { get; set; }
        public string SuggestedDisplayValue { get; set; }
        public string SuggestionType { get; set; } // Format, Spelling, Validation
        public string Reason { get; set; }
        public string Description { get; set; }
        public string ActionType { get; set; }
        public double Confidence { get; set; }
        public Dictionary<string, object> Metadata { get; set; }
    }
}
