"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_terraform_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/terraform.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/terraform.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Terraform\\\",\\\"fileTypes\\\":[\\\"tf\\\",\\\"tfvars\\\"],\\\"name\\\":\\\"terraform\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#attribute_definition\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#expressions\\\"}],\\\"repository\\\":{\\\"attribute_access\\\":{\\\"begin\\\":\\\"\\\\\\\\.(?!\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.accessor.hcl\\\"}},\\\"comment\\\":\\\"Matches traversal attribute access such as .attr\\\",\\\"end\\\":\\\"[[:alpha:]][\\\\\\\\w-]*|\\\\\\\\d*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Attribute name\\\",\\\"match\\\":\\\"(?!null|false|true)[[:alpha:]][\\\\\\\\w-]*\\\",\\\"name\\\":\\\"variable.other.member.hcl\\\"},{\\\"comment\\\":\\\"Optional attribute index\\\",\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.integer.hcl\\\"}]}}},\\\"attribute_definition\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.readwrite.hcl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.assignment.hcl\\\"}},\\\"comment\\\":\\\"Identifier \\\\\\\"=\\\\\\\" with optional parens\\\",\\\"match\\\":\\\"(\\\\\\\\()?(\\\\\\\\b(?!null\\\\\\\\b|false\\\\\\\\b|true\\\\\\\\b)[[:alpha:]][[:alnum:]_-]*)(\\\\\\\\))?\\\\\\\\s*(\\\\\\\\=(?!\\\\\\\\=|\\\\\\\\>))\\\\\\\\s*\\\",\\\"name\\\":\\\"variable.declaration.hcl\\\"},\\\"attribute_splat\\\":{\\\"begin\\\":\\\"\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.accessor.hcl\\\"}},\\\"comment\\\":\\\"Legacy attribute-only splat\\\",\\\"end\\\":\\\"\\\\\\\\*\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.splat.hcl\\\"}}},\\\"block\\\":{\\\"begin\\\":\\\"([\\\\\\\\w][\\\\\\\\-\\\\\\\\w]*)([\\\\\\\\s\\\\\\\\\\\\\\\"\\\\\\\\-\\\\\\\\w]*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Known block type\\\",\\\"match\\\":\\\"\\\\\\\\bdata|check|import|locals|module|output|provider|resource|terraform|variable\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.terraform\\\"},{\\\"comment\\\":\\\"Unknown block type\\\",\\\"match\\\":\\\"\\\\\\\\b(?!null|false|true)[[:alpha:]][[:alnum:]_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.hcl\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Block label\\\",\\\"match\\\":\\\"[\\\\\\\\\\\\\\\"\\\\\\\\-\\\\\\\\w]+\\\",\\\"name\\\":\\\"variable.other.enummember.hcl\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.hcl\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.hcl\\\"}},\\\"comment\\\":\\\"This will match Terraform blocks like `resource \\\\\\\"aws_instance\\\\\\\" \\\\\\\"web\\\\\\\" {` or `module {`\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.hcl\\\"}},\\\"name\\\":\\\"meta.block.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#attribute_definition\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#expressions\\\"}]},\\\"block_inline_comments\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"comment\\\":\\\"Inline comments start with the /* sequence and end with the */ sequence, and may have any characters within except the ending sequence. An inline comment is considered equivalent to a whitespace sequence\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.hcl\\\"},\\\"brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.hcl\\\"}},\\\"patterns\\\":[{\\\"comment\\\":\\\"Splat operator\\\",\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.splat.hcl\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#inline_for_expression\\\"},{\\\"include\\\":\\\"#inline_if_expression\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"char_escapes\\\":{\\\"comment\\\":\\\"Character Escapes\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[nrt\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\u(\\\\\\\\h{8}|\\\\\\\\h{4})\\\",\\\"name\\\":\\\"constant.character.escape.hcl\\\"},\\\"comma\\\":{\\\"comment\\\":\\\"Commas - used in certain expressions\\\",\\\"match\\\":\\\"\\\\\\\\,\\\",\\\"name\\\":\\\"punctuation.separator.hcl\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#hash_line_comments\\\"},{\\\"include\\\":\\\"#double_slash_line_comments\\\"},{\\\"include\\\":\\\"#block_inline_comments\\\"}]},\\\"double_slash_line_comments\\\":{\\\"begin\\\":\\\"//\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"comment\\\":\\\"Line comments start with // sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.hcl\\\"},\\\"expressions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literal_values\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#tuple_for_expression\\\"},{\\\"include\\\":\\\"#object_for_expression\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#objects\\\"},{\\\"include\\\":\\\"#attribute_access\\\"},{\\\"include\\\":\\\"#attribute_splat\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#parens\\\"}]},\\\"for_expression_body\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"in keyword\\\",\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.word.hcl\\\"},{\\\"comment\\\":\\\"if keyword\\\",\\\"match\\\":\\\"\\\\\\\\bif\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\:\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"functions\\\":{\\\"begin\\\":\\\"([:\\\\\\\\-\\\\\\\\w]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(core::)?(abs|abspath|alltrue|anytrue|base64decode|base64encode|base64gzip|base64sha256|base64sha512|basename|bcrypt|can|ceil|chomp|chunklist|cidrhost|cidrnetmask|cidrsubnet|cidrsubnets|coalesce|coalescelist|compact|concat|contains|csvdecode|dirname|distinct|element|endswith|file|filebase64|filebase64sha256|filebase64sha512|fileexists|filemd5|fileset|filesha1|filesha256|filesha512|flatten|floor|format|formatdate|formatlist|indent|index|join|jsondecode|jsonencode|keys|length|log|lookup|lower|matchkeys|max|md5|merge|min|nonsensitive|one|parseint|pathexpand|plantimestamp|pow|range|regex|regexall|replace|reverse|rsadecrypt|sensitive|setintersection|setproduct|setsubtract|setunion|sha1|sha256|sha512|signum|slice|sort|split|startswith|strcontains|strrev|substr|sum|templatefile|textdecodebase64|textencodebase64|timeadd|timecmp|timestamp|title|tobool|tolist|tomap|tonumber|toset|tostring|transpose|trim|trimprefix|trimspace|trimsuffix|try|upper|urlencode|uuid|uuidv5|values|yamldecode|yamlencode|zipmap)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.terraform\\\"},{\\\"match\\\":\\\"\\\\\\\\bprovider::[[:alpha:]][\\\\\\\\w_-]*::[[:alpha:]][\\\\\\\\w_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.provider.terraform\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"comment\\\":\\\"Built-in function calls\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"}},\\\"name\\\":\\\"meta.function-call.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comma\\\"}]},\\\"hash_line_comments\\\":{\\\"begin\\\":\\\"#\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.hcl\\\"}},\\\"comment\\\":\\\"Line comments start with # sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.hcl\\\"},\\\"hcl_type_keywords\\\":{\\\"comment\\\":\\\"Type keywords known to HCL.\\\",\\\"match\\\":\\\"\\\\\\\\b(any|string|number|bool|list|set|map|tuple|object)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.hcl\\\"},\\\"heredoc\\\":{\\\"begin\\\":\\\"(\\\\\\\\<\\\\\\\\<\\\\\\\\-?)\\\\\\\\s*(\\\\\\\\w+)\\\\\\\\s*$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.heredoc.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.heredoc.hcl\\\"}},\\\"comment\\\":\\\"String Heredoc\\\",\\\"end\\\":\\\"^\\\\\\\\s*\\\\\\\\2\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.heredoc.hcl\\\"}},\\\"name\\\":\\\"string.unquoted.heredoc.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},\\\"inline_for_expression\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\=\\\\\\\\>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"},{\\\"include\\\":\\\"#for_expression_body\\\"}]}},\\\"match\\\":\\\"(for)\\\\\\\\b(.*)\\\\\\\\n\\\"},\\\"inline_if_expression\\\":{\\\"begin\\\":\\\"(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"language_constants\\\":{\\\"comment\\\":\\\"Language Constants\\\",\\\"match\\\":\\\"\\\\\\\\b(true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.hcl\\\"},\\\"literal_values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#language_constants\\\"},{\\\"include\\\":\\\"#string_literals\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#hcl_type_keywords\\\"},{\\\"include\\\":\\\"#named_value_references\\\"}]},\\\"local_identifiers\\\":{\\\"comment\\\":\\\"Local Identifiers\\\",\\\"match\\\":\\\"\\\\\\\\b(?!null|false|true)[[:alpha:]][[:alnum:]_-]*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.hcl\\\"},\\\"named_value_references\\\":{\\\"comment\\\":\\\"Constant values available only to Terraform.\\\",\\\"match\\\":\\\"\\\\\\\\b(var|local|module|data|path|terraform)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.terraform\\\"},\\\"numeric_literals\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.exponent.hcl\\\"}},\\\"comment\\\":\\\"Integer, no fraction, optional exponent\\\",\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+([Ee][+-]?)\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hcl\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.decimal.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.exponent.hcl\\\"}},\\\"comment\\\":\\\"Integer, fraction, optional exponent\\\",\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+(\\\\\\\\.)\\\\\\\\d+(?:([Ee][+-]?)\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hcl\\\"},{\\\"comment\\\":\\\"Integers\\\",\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.hcl\\\"}]},\\\"object_for_expression\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\\\\\\s?(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.hcl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\=\\\\\\\\>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"},{\\\"include\\\":\\\"#for_expression_body\\\"}]},\\\"object_key_values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#literal_values\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#tuple_for_expression\\\"},{\\\"include\\\":\\\"#object_for_expression\\\"},{\\\"include\\\":\\\"#heredoc\\\"},{\\\"include\\\":\\\"#functions\\\"}]},\\\"objects\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.hcl\\\"}},\\\"name\\\":\\\"meta.braces.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#objects\\\"},{\\\"include\\\":\\\"#inline_for_expression\\\"},{\\\"include\\\":\\\"#inline_if_expression\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.mapping.key.hcl variable.other.readwrite.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.hcl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\=\\\\\\\\>\\\",\\\"name\\\":\\\"storage.type.function.hcl\\\"}]}},\\\"comment\\\":\\\"Literal, named object key\\\",\\\"match\\\":\\\"\\\\\\\\b((?!null|false|true)[[:alpha:]][[:alnum:]_-]*)\\\\\\\\s*(\\\\\\\\=\\\\\\\\>?)\\\\\\\\s*\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#named_value_references\\\"}]},\\\"1\\\":{\\\"name\\\":\\\"meta.mapping.key.hcl string.quoted.double.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.hcl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.hcl\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.hcl\\\"}},\\\"comment\\\":\\\"String object key\\\",\\\"match\\\":\\\"\\\\\\\\b((\\\\\\\").*(\\\\\\\"))\\\\\\\\s*(\\\\\\\\=)\\\\\\\\s*\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"comment\\\":\\\"Computed object key (any expression between parens)\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*(=|:)\\\\\\\\s*\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hcl\\\"}},\\\"name\\\":\\\"meta.mapping.key.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#named_value_references\\\"},{\\\"include\\\":\\\"#attribute_access\\\"}]},{\\\"include\\\":\\\"#object_key_values\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\>\\\\\\\\=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\<\\\\\\\\=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\=\\\\\\\\=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\!\\\\\\\\=\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\-\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\/\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\%\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\&\\\\\\\\&\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\!\\\",\\\"name\\\":\\\"keyword.operator.logical.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\>\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\<\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\:\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"},{\\\"match\\\":\\\"\\\\\\\\=\\\\\\\\>\\\",\\\"name\\\":\\\"keyword.operator.hcl\\\"}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.hcl\\\"}},\\\"comment\\\":\\\"Parens - matched *after* function syntax\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.hcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expressions\\\"}]},\\\"string_interpolation\\\":{\\\"begin\\\":\\\"(?<![%$])([%$]{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.interpolation.begin.hcl\\\"}},\\\"comment\\\":\\\"String interpolation\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.interpolation.end.hcl\\\"}},\\\"name\\\":\\\"meta.interpolation.hcl\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Trim left whitespace\\\",\\\"match\\\":\\\"\\\\\\\\~\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.template.left.trim.hcl\\\"},{\\\"comment\\\":\\\"Trim right whitespace\\\",\\\"match\\\":\\\"\\\\\\\\s\\\\\\\\~\\\",\\\"name\\\":\\\"keyword.operator.template.right.trim.hcl\\\"},{\\\"comment\\\":\\\"if/else/endif and for/in/endfor directives\\\",\\\"match\\\":\\\"\\\\\\\\b(if|else|endif|for|in|endfor)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.hcl\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#local_identifiers\\\"}]},\\\"string_literals\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.hcl\\\"}},\\\"comment\\\":\\\"Strings\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.hcl\\\"}},\\\"name\\\":\\\"string.quoted.double.hcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"include\\\":\\\"#char_escapes\\\"}]},\\\"tuple_for_expression\\\":{\\\"begin\\\":\\\"(\\\\\\\\[)\\\\\\\\s?(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.hcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.hcl\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.hcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#for_expression_body\\\"}]}},\\\"scopeName\\\":\\\"source.hcl.terraform\\\",\\\"aliases\\\":[\\\"tf\\\",\\\"tfvars\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/terraform.mjs\n"));

/***/ })

}]);