﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportAppliedFixes
    {
        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int RowId { get; set; }
        public string FieldName { get; set; }
        public string OriginalValue { get; set; }
        public string FixedValue { get; set; }
        public string FixType { get; set; }
        public bool? SuggestionUsed { get; set; }
        public DateTime AppliedAt { get; set; }
        public string AppliedBy { get; set; }

        public virtual ExhibitorImportRows Row { get; set; }
        public virtual ExhibitorImportSessions Session { get; set; }
    }
}