// Used when fetching/displaying tax data
export interface Tax {
  id: number;
  taxProvince?: string;
  taxRate?: number;
  taxType?: string;
  taxTypeAbr?: string;
  displayOrder?: number;
}

// Used when creating a new tax entry
export interface TaxRequest {
  provinceId: number;
  taxTypeId: number;
  displayOrder: number;
  taxRate: number;
}

// Used when updating an existing tax entry
export interface TaxUpdate {
  displayOrder: number;
  taxRate: number;
}
