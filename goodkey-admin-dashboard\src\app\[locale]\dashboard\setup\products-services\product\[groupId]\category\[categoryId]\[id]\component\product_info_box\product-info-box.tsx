'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import { CheckCircle, XCircle } from 'lucide-react';
import OfferingQuery from '@/services/queries/OfferingQuery';
import { OfferingDto } from '@/models/Offering';
interface IProductInfoBox {
  id?: number;
}

function ProductInfoBox({ id }: IProductInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering', { id: id }],
    queryFn: () => OfferingQuery.getById(id!),
    enabled: !!id,
    select: (res): OfferingDto => ({
      id: res.id,
      name: res.name ?? '',
      code: res.code ?? '',
      isActive: res.isActive ?? false,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <div className="product-info-box">
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
            {data ? data.name : 'Setup a new product'}
            {data && (
              <span
                className={`inline-flex items-center gap-1 text-xs font-medium px-2 py-0.5 rounded-full`}
              >
                {data.isActive ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 text-red-600" />
                  </>
                )}
              </span>
            )}
          </h1>
          <p className="text-slate-600 text-sm">
            {data ? (
              'Edit the details below to update the product.'
            ) : (
              <span>
                Please fill out the form below to add a new product. Required
                fields are marked with a{' '}
                <span className="text-red-500 font-semibold">*</span>.
              </span>
            )}
          </p>
        </div>
      </div>
    </Suspense>
  );
}

export default ProductInfoBox;
