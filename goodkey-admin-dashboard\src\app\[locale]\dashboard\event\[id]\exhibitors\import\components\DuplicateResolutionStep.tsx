'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  Users,
  Building,
  Mail,
  Phone,
  ChevronRight,
  Loader2,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import ExhibitorImportQuery from '@/services/queries/ExhibitorImportQuery';
import type {
  ExhibitorImportDuplicateDto,
  ExhibitorImportResolveDto,
  ExhibitorImportDuplicateResolutionDto,
} from '@/models/ExhibitorImport';

interface DuplicateResolutionStepProps {
  sessionId: string;
  duplicates: ExhibitorImportDuplicateDto[];
  onResolved: () => void;
  isLoading: boolean;
}

interface FieldResolution {
  fieldName: string;
  selectedSource: 'Excel' | 'Database' | 'Custom';
  selectedValue: string;
  excelValue: string;
  databaseValue: string;
  customValue?: string;
}

const DuplicateResolutionStep: React.FC<DuplicateResolutionStepProps> = ({
  sessionId,
  duplicates,
  onResolved,
  isLoading: parentLoading,
}) => {
  const [isResolving, setIsResolving] = useState(false);
  const [resolutions, setResolutions] = useState<
    Record<number, FieldResolution[]>
  >({});

  const isLoading = parentLoading || isResolving;

  // Initialize resolutions for each duplicate
  React.useEffect(() => {
    const initialResolutions: Record<number, FieldResolution[]> = {};

    duplicates.forEach((duplicate) => {
      initialResolutions[duplicate.duplicateId] = duplicate.fieldConflicts.map(
        (conflict) => ({
          fieldName: conflict.fieldName,
          selectedSource: 'Excel',
          selectedValue: conflict.excelValue,
          excelValue: conflict.excelValue,
          databaseValue: conflict.databaseValue,
        }),
      );
    });

    setResolutions(initialResolutions);
  }, [duplicates]);

  const updateFieldResolution = (
    duplicateId: number,
    fieldName: string,
    source: 'Excel' | 'Database' | 'Custom',
    customValue?: string,
  ) => {
    setResolutions((prev) => {
      const duplicateResolutions = prev[duplicateId] || [];
      const fieldIndex = duplicateResolutions.findIndex(
        (r) => r.fieldName === fieldName,
      );

      if (fieldIndex >= 0) {
        const field = duplicateResolutions[fieldIndex];
        const updatedField: FieldResolution = {
          ...field,
          selectedSource: source,
          selectedValue:
            source === 'Excel'
              ? field.excelValue
              : source === 'Database'
                ? field.databaseValue
                : customValue || '',
          customValue: source === 'Custom' ? customValue : undefined,
        };

        const newResolutions = [...duplicateResolutions];
        newResolutions[fieldIndex] = updatedField;

        return {
          ...prev,
          [duplicateId]: newResolutions,
        };
      }

      return prev;
    });
  };

  const handleResolveAll = async () => {
    setIsResolving(true);

    try {
      const duplicateResolutions: ExhibitorImportDuplicateResolutionDto[] =
        duplicates.map((duplicate) => ({
          duplicateId: duplicate.duplicateId,
          fieldResolutions: (resolutions[duplicate.duplicateId] || []).map(
            (resolution) => ({
              fieldName: resolution.fieldName,
              selectedSource: resolution.selectedSource,
              selectedValue: resolution.selectedValue,
              excelValue: resolution.excelValue,
              databaseValue: resolution.databaseValue,
              customValue: resolution.customValue,
            }),
          ),
        }));

      const request: ExhibitorImportResolveDto = {
        sessionId,
        duplicateResolutions,
      };

      await ExhibitorImportQuery.resolve(request);

      toast({
        title: 'Duplicates resolved successfully',
        description:
          'All duplicate conflicts have been resolved. Ready to proceed with import.',
      });

      onResolved();
    } catch (error) {
      toast({
        title: 'Failed to resolve duplicates',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred while resolving duplicates',
        variant: 'destructive',
      });
    } finally {
      setIsResolving(false);
    }
  };

  const getFieldIcon = (fieldName: string) => {
    if (fieldName.toLowerCase().includes('email'))
      return <Mail className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('phone'))
      return <Phone className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('company'))
      return <Building className="h-4 w-4" />;
    return <Users className="h-4 w-4" />;
  };

  const formatFieldName = (fieldName: string) => {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  if (duplicates.length === 0) {
    return (
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-semibold">No Duplicates Found</h2>
        <p className="text-muted-foreground">
          No duplicate conflicts were detected. Ready to proceed with import.
        </p>
        <Button onClick={onResolved} size="lg">
          Proceed to Import
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Resolve Duplicate Conflicts</h2>
        <p className="text-muted-foreground">
          Choose how to handle each field conflict for the detected duplicates.
        </p>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {duplicates.length} duplicate conflict
          {duplicates.length > 1 ? 's' : ''} detected. For each conflict, choose
          whether to use the Excel value, existing database value, or enter a
          custom value.
        </AlertDescription>
      </Alert>

      <div className="space-y-6">
        {duplicates.map((duplicate, index) => (
          <Card key={duplicate.duplicateId} className="border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>
                  Conflict {index + 1}: {duplicate.duplicateType}
                </span>
                <Badge variant="outline">{duplicate.duplicateValue}</Badge>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {duplicate.conflictDescription}
              </p>
              <p className="text-sm">
                <strong>Affected Rows:</strong>{' '}
                {duplicate.rowNumbers.join(', ')}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {duplicate.fieldConflicts.map((conflict) => {
                const resolution = resolutions[duplicate.duplicateId]?.find(
                  (r) => r.fieldName === conflict.fieldName,
                );

                return (
                  <div
                    key={conflict.fieldName}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex items-center space-x-2 mb-3">
                      {getFieldIcon(conflict.fieldName)}
                      <h4 className="font-medium">
                        {formatFieldName(conflict.fieldName)}
                      </h4>
                    </div>

                    <RadioGroup
                      value={resolution?.selectedSource || 'Excel'}
                      onValueChange={(value) =>
                        updateFieldResolution(
                          duplicate.duplicateId,
                          conflict.fieldName,
                          value as any,
                        )
                      }
                      className="space-y-3"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="Excel"
                          id={`excel-${duplicate.duplicateId}-${conflict.fieldName}`}
                        />
                        <Label
                          htmlFor={`excel-${duplicate.duplicateId}-${conflict.fieldName}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex justify-between items-center">
                            <span>Use Excel Value</span>
                            <code className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                              {conflict.excelValue || '(empty)'}
                            </code>
                          </div>
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="Database"
                          id={`db-${duplicate.duplicateId}-${conflict.fieldName}`}
                        />
                        <Label
                          htmlFor={`db-${duplicate.duplicateId}-${conflict.fieldName}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex justify-between items-center">
                            <span>Use Database Value</span>
                            <code className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                              {conflict.databaseValue || '(empty)'}
                            </code>
                          </div>
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="Custom"
                          id={`custom-${duplicate.duplicateId}-${conflict.fieldName}`}
                        />
                        <Label
                          htmlFor={`custom-${duplicate.duplicateId}-${conflict.fieldName}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="space-y-2">
                            <span>Use Custom Value</span>
                            {resolution?.selectedSource === 'Custom' && (
                              <Input
                                placeholder="Enter custom value..."
                                value={resolution.customValue || ''}
                                onChange={(e) =>
                                  updateFieldResolution(
                                    duplicate.duplicateId,
                                    conflict.fieldName,
                                    'Custom',
                                    e.target.value,
                                  )
                                }
                                className="mt-2"
                              />
                            )}
                          </div>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-center">
        <Button
          onClick={handleResolveAll}
          disabled={isLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {isResolving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Resolving...
            </>
          ) : (
            <>
              Resolve All Conflicts
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default DuplicateResolutionStep;
