namespace goodkey_common.DTO
{
    // Simple DTOs for Excel processing in goodkey_common
    public class ExcelRowData
    {
        public int RowNumber { get; set; }

        // Company Information
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress1 { get; set; }
        public string CompanyAddress2 { get; set; }
        public string CompanyCity { get; set; }
        public string CompanyProvince { get; set; }
        public string CompanyPostalCode { get; set; }
        public string CompanyCountry { get; set; }
        public string CompanyWebsite { get; set; }

        // Contact Information
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string BoothNumbers { get; set; }
        public string ContactType { get; set; }
    }

    public class ExcelTemplateInfoDto
    {
        public List<ExcelColumnInfo> Columns { get; set; } = new();
        public List<string> RequiredColumns { get; set; } = new();
        public List<string> OptionalColumns { get; set; } = new();
        public Dictionary<string, List<string>> ValidValues { get; set; } = new();
        public string SampleDataUrl { get; set; }
    }

    public class ExcelColumnInfo
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsRequired { get; set; }
        public string DataType { get; set; }
        public string Format { get; set; }
        public string Example { get; set; }
    }

    public class ExcelValidationMessageDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
        public string MessageType { get; set; } // Error, Warning, Info
        public string ValidationRule { get; set; } // Required, Format, Duplicate, Lookup, Business
        public string MessageCode { get; set; }
        public string Message { get; set; }
    }
}
