"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_rel_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/rel.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/rel.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Rel\\\",\\\"name\\\":\\\"rel\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#single-line-comment-consuming-line-ending\\\"},{\\\"include\\\":\\\"#deprecated-temporary\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#symbols\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#otherkeywords\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#constants\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"name\\\":\\\"comment.block.documentation.rel\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docblock\\\"}]},{\\\"begin\\\":\\\"(/\\\\\\\\*)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|(\\\\\\\\*/)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"}},\\\"name\\\":\\\"comment.block.rel\\\"},{\\\"begin\\\":\\\"doc\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"comment.block.documentation.rel\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.rel\\\",\\\"end\\\":\\\"(?=$)\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(true|false)\\\\\\\\b)\\\",\\\"name\\\":\\\"constant.language.rel\\\"}]},\\\"deprecated-temporary\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"@inspect\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(def|entity|bound|include|ic|forall|exists|∀|∃|return|module|^end)\\\\\\\\b)|(((\\\\\\\\<)?\\\\\\\\|(\\\\\\\\>)?)|∀|∃)\\\",\\\"name\\\":\\\"keyword.control.rel\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(if|then|else|and|or|not|eq|neq|lt|lt_eq|gt|gt_eq)\\\\\\\\b)|(\\\\\\\\+|\\\\\\\\-|\\\\\\\\*|\\\\\\\\/|÷|\\\\\\\\^|\\\\\\\\%|\\\\\\\\=|\\\\\\\\!\\\\\\\\=|≠|\\\\\\\\<|\\\\\\\\<\\\\\\\\=|≤|\\\\\\\\>|\\\\\\\\>\\\\\\\\=|≥|\\\\\\\\&)|\\\\\\\\s+(end)\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"otherkeywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\s*(@inline)\\\\\\\\s*|\\\\\\\\s*(@auto_number)\\\\\\\\s*|\\\\\\\\s*(function)\\\\\\\\s|(\\\\\\\\b(implies|select|from|∈|where|for|in)\\\\\\\\b)|(((\\\\\\\\<)?\\\\\\\\|(\\\\\\\\>)?)|∈)\\\",\\\"name\\\":\\\"keyword.other.rel\\\"}]},\\\"single-line-comment-consuming-line-ending\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?((//)(?:\\\\\\\\s*((@)internal)(?=\\\\\\\\s|$))?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.rel\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.double-slash.rel\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.rel\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.internaldeclaration.rel\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.decorator.internaldeclaration.rel\\\"}},\\\"contentName\\\":\\\"comment.line.double-slash.rel\\\",\\\"end\\\":\\\"(?=^)\\\"},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.rel\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.rel\\\"}]},\\\"symbols\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(:[\\\\\\\\[_$[:alpha:]](\\\\\\\\]|[_$[:alnum:]]*))\\\",\\\"name\\\":\\\"variable.parameter.rel\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\b(Symbol|Char|Bool|Rational|FixedDecimal|Float16|Float32|Float64|Int8|Int16|Int32|Int64|Int128|UInt8|UInt16|UInt32|UInt64|UInt128|Date|DateTime|Day|Week|Month|Year|Nanosecond|Microsecond|Millisecond|Second|Minute|Hour|FilePos|HashValue|AutoNumberValue)\\\\\\\\b)\\\",\\\"name\\\":\\\"entity.name.type.rel\\\"}]}},\\\"scopeName\\\":\\\"source.rel\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/rel.mjs\n"));

/***/ })

}]);