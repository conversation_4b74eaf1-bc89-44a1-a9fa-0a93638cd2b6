"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_go_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/go.mjs":
/*!*************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/go.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Go\\\",\\\"name\\\":\\\"go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statements\\\"}],\\\"repository\\\":{\\\"after_control_variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"variable.other.go\\\"}]}},\\\"comment\\\":\\\"After control variables, to not highlight as a struct/interface (before formatting with gofmt)\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\brange\\\\\\\\b|\\\\\\\\bswitch\\\\\\\\b|\\\\\\\\;|\\\\\\\\bif\\\\\\\\b|\\\\\\\\bfor\\\\\\\\b|\\\\\\\\<|\\\\\\\\>|\\\\\\\\<\\\\\\\\=|\\\\\\\\>\\\\\\\\=|\\\\\\\\=\\\\\\\\=|\\\\\\\\!\\\\\\\\=|\\\\\\\\w(?:\\\\\\\\+|/|\\\\\\\\-|\\\\\\\\*|\\\\\\\\%)|\\\\\\\\w(?:\\\\\\\\+|/|\\\\\\\\-|\\\\\\\\*|\\\\\\\\%)\\\\\\\\=|\\\\\\\\|\\\\\\\\||\\\\\\\\&\\\\\\\\&)(?:\\\\\\\\s*)((?![\\\\\\\\[\\\\\\\\]]+)[[:alnum:]\\\\\\\\-\\\\\\\\_\\\\\\\\!\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\<\\\\\\\\>\\\\\\\\=\\\\\\\\*/\\\\\\\\+\\\\\\\\%\\\\\\\\:]+)(?:\\\\\\\\s*)(?=\\\\\\\\{))\\\"},\\\"brackets\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"built_in_functions\\\":{\\\"comment\\\":\\\"Built-in functions\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(append|cap|close|complex|copy|delete|imag|len|panic|print|println|real|recover|min|max|clear)\\\\\\\\b(?=\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.support.builtin.go\\\"},{\\\"begin\\\":\\\"(?:(\\\\\\\\bnew\\\\\\\\b)(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.support.builtin.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"comment\\\":\\\"new keyword\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?:(\\\\\\\\bmake\\\\\\\\b)(?:(\\\\\\\\()((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+(?:\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?)?(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?(?:(?!\\\\\\\\bmap\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.]+))?(\\\\\\\\[(?:(?:[\\\\\\\\S]+)(?:(?:\\\\\\\\,\\\\\\\\s*(?:[\\\\\\\\S]+))*))?\\\\\\\\])?(?:\\\\\\\\,)?)?))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.support.builtin.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"make keyword\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.go\\\"}},\\\"end\\\":\\\"(\\\\\\\\*\\\\\\\\/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.go\\\"}},\\\"name\\\":\\\"comment.block.go\\\"},{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.go\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\n|$)\\\",\\\"name\\\":\\\"comment.line.double-slash.go\\\"}]},\\\"const_assignment\\\":{\\\"comment\\\":\\\"constant assignment with const keyword\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.constant.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"single assignment\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\bconst\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.]+)*)(?:\\\\\\\\s*)((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+(?:\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|func|map)\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)*)?(?:\\\\\\\\s*)(?:\\\\\\\\=)?)?)\\\"},{\\\"begin\\\":\\\"(?:(?<=\\\\\\\\bconst\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"comment\\\":\\\"multi assignment\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.constant.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"match\\\":\\\"(?:(?:^\\\\\\\\s*)(\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.]+)*)(?:\\\\\\\\s*)((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+(?:\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|func|map)\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)*)?(?:\\\\\\\\s*)(?:\\\\\\\\=)?)?)\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"delimiters\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\,\\\",\\\"name\\\":\\\"punctuation.other.comma.go\\\"},{\\\"match\\\":\\\"\\\\\\\\.(?!\\\\\\\\.\\\\\\\\.)\\\",\\\"name\\\":\\\"punctuation.other.period.go\\\"},{\\\"match\\\":\\\":(?!=)\\\",\\\"name\\\":\\\"punctuation.other.colon.go\\\"}]},\\\"double_parentheses_types\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"double parentheses types\\\",\\\"match\\\":\\\"(?:(?<!\\\\\\\\w)(\\\\\\\\((?:[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*\\\\\\\\&]+)\\\\\\\\))(?=\\\\\\\\())\\\"},\\\"field_hover\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\binvalid\\\\\\\\b\\\\\\\\s+\\\\\\\\btype\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.field.go\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"struct field property and types when hovering with the mouse\\\",\\\"match\\\":\\\"(?:(?<=^\\\\\\\\bfield\\\\\\\\b)\\\\\\\\s+([\\\\\\\\w\\\\\\\\*\\\\\\\\.]+)\\\\\\\\s+([\\\\\\\\s\\\\\\\\S]+))\\\"},\\\"function_declaration\\\":{\\\"begin\\\":\\\"(?:^(\\\\\\\\bfunc\\\\\\\\b)(?:\\\\\\\\s*(\\\\\\\\([^\\\\\\\\)]+\\\\\\\\)\\\\\\\\s*)?(?:(\\\\\\\\w+)(?=\\\\\\\\(|\\\\\\\\[))?))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.function.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"match\\\":\\\"(?:(\\\\\\\\w+(?:\\\\\\\\s+))?((?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(?:\\\\\\\\[(?:(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(?:\\\\\\\\,\\\\\\\\s+)?)+)?\\\\\\\\])?))\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\d\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid.illegal.identifier.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.function.go\\\"}]}},\\\"comment\\\":\\\"Function declarations\\\",\\\"end\\\":\\\"(?:(?<=\\\\\\\\))\\\\\\\\s*((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?(?:\\\\\\\\bstruct\\\\\\\\b|\\\\\\\\binterface\\\\\\\\b))[\\\\\\\\w\\\\\\\\.\\\\\\\\-\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?\\\\\\\\s*(?=\\\\\\\\{))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"}]},{\\\"begin\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#generic_param_types\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"single function as a type returned type(s) declaration\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\))(?:\\\\\\\\s*)((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?[\\\\\\\\w\\\\\\\\*\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\<\\\\\\\\>\\\\\\\\-]+(?:\\\\\\\\s*)(?:\\\\\\\\/(?:\\\\\\\\/|\\\\\\\\*).*)?)$)\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"function_param_types\\\":{\\\"comment\\\":\\\"function parameter variables and types\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#interface_variables_types\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]}},\\\"comment\\\":\\\"struct/interface type declaration\\\",\\\"match\\\":\\\"((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?\\\\\\\\b\\\\\\\\w+)\\\\\\\\s+(?=(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|interface)\\\\\\\\b\\\\\\\\s*\\\\\\\\{)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]}},\\\"comment\\\":\\\"multiple parameters one type -with multilines\\\",\\\"match\\\":\\\"(?:(?:(?<=\\\\\\\\()|^\\\\\\\\s*)((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)(?:/(?:/|\\\\\\\\*).*)?)$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"multiple params and types | multiple params one type | one param one type\\\",\\\"match\\\":\\\"(?:((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?\\\\\\\\b\\\\\\\\w+)(?:\\\\\\\\s+)((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:(?:(?:[\\\\\\\\w\\\\\\\\[\\\\\\\\]\\\\\\\\.\\\\\\\\*]+)?(?:(?:\\\\\\\\bfunc\\\\\\\\b\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\))(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:\\\\\\\\s*))+(?:(?:(?:[\\\\\\\\w\\\\\\\\*\\\\\\\\.\\\\\\\\[\\\\\\\\]]+)|(?:\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\))))?)|(?:(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?[\\\\\\\\w\\\\\\\\*\\\\\\\\.]+(?:\\\\\\\\[(?:[^\\\\\\\\]]+)\\\\\\\\])?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?)+)))\\\"},{\\\"begin\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#generic_param_types\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"other types\\\",\\\"match\\\":\\\"([\\\\\\\\w\\\\\\\\.]+)\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"functions\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\bfunc\\\\\\\\b)(?=\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.function.go\\\"}},\\\"comment\\\":\\\"Functions\\\",\\\"end\\\":\\\"(?:(?<=\\\\\\\\))(\\\\\\\\s*(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?((?:(?:\\\\\\\\s*(?:(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(?:(?:\\\\\\\\[(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(?:\\\\\\\\[(?:[^\\\\\\\\]]+)?\\\\\\\\])?(?:\\\\\\\\,\\\\\\\\s+)?)+\\\\\\\\])|(?:\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\)))?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?)(?:\\\\\\\\s*)(?=\\\\\\\\{))|(?:\\\\\\\\s*(?:(?:(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?(?!\\\\\\\\bfunc\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(?:\\\\\\\\[(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(?:\\\\\\\\[(?:[^\\\\\\\\]]+)?\\\\\\\\])?(?:\\\\\\\\,\\\\\\\\s+)?)+\\\\\\\\])?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?)|(?:\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\)))))?)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-variable-types\\\"}]},\\\"functions_inline\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.function.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"functions in-line with multi return types\\\",\\\"match\\\":\\\"(?:(\\\\\\\\bfunc\\\\\\\\b)((?:\\\\\\\\((?:[^/]*?)\\\\\\\\))(?:\\\\\\\\s+)(?:\\\\\\\\((?:[^/]*?)\\\\\\\\)))(?:\\\\\\\\s+)(?=\\\\\\\\{))\\\"},\\\"generic_param_types\\\":{\\\"comment\\\":\\\"generic parameter variables and types\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#interface_variables_types\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]}},\\\"comment\\\":\\\"struct/interface type declaration\\\",\\\"match\\\":\\\"((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?\\\\\\\\b\\\\\\\\w+)\\\\\\\\s+(?=(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|interface)\\\\\\\\b\\\\\\\\s*\\\\\\\\{)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]}},\\\"comment\\\":\\\"multiple parameters one type -with multilines\\\",\\\"match\\\":\\\"(?:(?:(?<=\\\\\\\\()|^\\\\\\\\s*)((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)(?:/(?:/|\\\\\\\\*).*)?)$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"multiple params and types | multiple types one param\\\",\\\"match\\\":\\\"(?:((?:(?:\\\\\\\\b\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?\\\\\\\\b\\\\\\\\w+)(?:\\\\\\\\s+)((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:(?:(?:[\\\\\\\\w\\\\\\\\[\\\\\\\\]\\\\\\\\.\\\\\\\\*]+)?(?:(?:\\\\\\\\bfunc\\\\\\\\b\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\))(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:\\\\\\\\s*))+(?:(?:(?:[\\\\\\\\w\\\\\\\\*\\\\\\\\.]+)|(?:\\\\\\\\((?:[^\\\\\\\\)]+)?\\\\\\\\))))?)|(?:(?:(?:[\\\\\\\\w\\\\\\\\*\\\\\\\\.\\\\\\\\~]+)|(?:\\\\\\\\[(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(?:\\\\\\\\[(?:[^\\\\\\\\]]+)?\\\\\\\\])?(?:\\\\\\\\,\\\\\\\\s+)?)+\\\\\\\\]))(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?)+)))\\\"},{\\\"begin\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#generic_param_types\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"other types\\\",\\\"match\\\":\\\"(?:\\\\\\\\b([\\\\\\\\w\\\\\\\\.]+))\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"generic_types\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-variable-types\\\"}]}},\\\"comment\\\":\\\"Generic support for all types\\\",\\\"match\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(\\\\\\\\[(?:[^\\\\\\\\]]+)?\\\\\\\\]))\\\"},\\\"group-functions\\\":{\\\"comment\\\":\\\"all statements related to functions\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_declaration\\\"},{\\\"include\\\":\\\"#functions_inline\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#built_in_functions\\\"},{\\\"include\\\":\\\"#support_functions\\\"}]},\\\"group-types\\\":{\\\"comment\\\":\\\"all statements related to types\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#other_struct_interface_expressions\\\"},{\\\"include\\\":\\\"#type_assertion_inline\\\"},{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#interface_variables_types\\\"},{\\\"include\\\":\\\"#single_type\\\"},{\\\"include\\\":\\\"#multi_types\\\"},{\\\"include\\\":\\\"#struct_interface_declaration\\\"},{\\\"include\\\":\\\"#double_parentheses_types\\\"},{\\\"include\\\":\\\"#switch_types\\\"},{\\\"include\\\":\\\"#type-declarations\\\"}]},\\\"group-variables\\\":{\\\"comment\\\":\\\"all statements related to variables\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#const_assignment\\\"},{\\\"include\\\":\\\"#var_assignment\\\"},{\\\"include\\\":\\\"#variable_assignment\\\"},{\\\"include\\\":\\\"#label_loop_variables\\\"},{\\\"include\\\":\\\"#slice_index_variables\\\"},{\\\"include\\\":\\\"#property_variables\\\"},{\\\"include\\\":\\\"#switch_select_case_variables\\\"},{\\\"include\\\":\\\"#other_variables\\\"}]},\\\"import\\\":{\\\"comment\\\":\\\"import\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(import)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.import.go\\\"}},\\\"comment\\\":\\\"import\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#imports\\\"}]}]},\\\"imports\\\":{\\\"comment\\\":\\\"import package(s)\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"variable.other.import.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.go\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.import.go\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.go\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*[\\\\\\\\w\\\\\\\\.]+)?\\\\\\\\s*((\\\\\\\")([^\\\\\\\"]*)(\\\\\\\"))\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.imports.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.imports.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#imports\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"interface_variables_types\\\":{\\\"begin\\\":\\\"(\\\\\\\\binterface\\\\\\\\b)\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interface.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"comment\\\":\\\"interface variable types\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_variables_types_field\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"interface_variables_types_field\\\":{\\\"comment\\\":\\\"interface variable type fields\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#support_functions\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"begin\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#generic_param_types\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"other types\\\",\\\"match\\\":\\\"([\\\\\\\\w\\\\\\\\.]+)\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Flow control keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(break|case|continue|default|defer|else|fallthrough|for|go|goto|if|range|return|select|switch)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bchan\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.channel.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bconst\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.const.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bvar\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.var.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bfunc\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.function.go\\\"},{\\\"match\\\":\\\"\\\\\\\\binterface\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.interface.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bmap\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.map.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bstruct\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.struct.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bimport\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.go\\\"},{\\\"match\\\":\\\"\\\\\\\\btype\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.go\\\"}]},\\\"label_loop_variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.label.go\\\"}]}},\\\"comment\\\":\\\"labeled loop variable name\\\",\\\"match\\\":\\\"((?:^\\\\\\\\s*\\\\\\\\w+:\\\\\\\\s*$)|(?:^\\\\\\\\s*(?:\\\\\\\\bbreak\\\\\\\\b|\\\\\\\\bgoto\\\\\\\\b|\\\\\\\\bcontinue\\\\\\\\b)\\\\\\\\s+\\\\\\\\w+(?:\\\\\\\\s*/(?:/|\\\\\\\\*)\\\\\\\\s*.*)?$))\\\"},\\\"language_constants\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.boolean.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.null.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.language.iota.go\\\"}},\\\"comment\\\":\\\"Language constants\\\",\\\"match\\\":\\\"\\\\\\\\b(?:(true|false)|(nil)|(iota))\\\\\\\\b\\\"},\\\"map_types\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\bmap\\\\\\\\b)(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.map.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"comment\\\":\\\"map types\\\",\\\"end\\\":\\\"(?:(\\\\\\\\])((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:func|struct|map)\\\\\\\\b)(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:[\\\\\\\\w\\\\\\\\.]+)(?:\\\\\\\\[(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}]+)(?:(?:\\\\\\\\,\\\\\\\\s*(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}]+))*))?\\\\\\\\])?)?)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"multi_types\\\":{\\\"begin\\\":\\\"(\\\\\\\\btype\\\\\\\\b)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"comment\\\":\\\"multi type declaration\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#interface_variables_types\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"numeric_literals\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=.)\\\",\\\"end\\\":\\\"(?:\\\\\\\\n|$)\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.go\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.go\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.go\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.go\\\"},\\\"9\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"13\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.go\\\"},\\\"14\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.go\\\"},\\\"15\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.go\\\"},\\\"16\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"17\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"18\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.go\\\"},\\\"19\\\":{\\\"name\\\":\\\"constant.numeric.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"20\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"21\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.go\\\"},\\\"22\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.go\\\"},\\\"23\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.go\\\"},\\\"24\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"25\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"26\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.go\\\"},\\\"27\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"28\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"29\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\"},\\\"30\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"31\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"32\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.go\\\"},\\\"33\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.go\\\"},\\\"34\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.go\\\"},\\\"35\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"36\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"37\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.go\\\"},\\\"38\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"39\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"40\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.go\\\"},\\\"41\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.go\\\"},\\\"42\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.go\\\"},\\\"43\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"44\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"45\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.go\\\"},\\\"46\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\"},\\\"47\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"48\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"49\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.go\\\"},\\\"50\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.go\\\"},\\\"51\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.go\\\"},\\\"52\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"53\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"}},\\\"match\\\":\\\"(?:(?:(?:(?:(?:\\\\\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?(?:(?<!_)([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$)|\\\\\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|\\\\\\\\G((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?:(?<!_)([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|(\\\\\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?(?<!_)([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|(\\\\\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|(\\\\\\\\G0[xX])((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.go\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.binary.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.go\\\"},\\\"9\\\":{\\\"name\\\":\\\"constant.numeric.octal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.go\\\"},\\\"13\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"}]},\\\"14\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.go\\\"},\\\"15\\\":{\\\"name\\\":\\\"keyword.other.unit.imaginary.go\\\"}},\\\"match\\\":\\\"(?:(?:(?:\\\\\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$)|(\\\\\\\\G0[bB])_?([01](?:[01]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|(\\\\\\\\G0[oO]?)_?((?:[0-7]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))+)(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))|(\\\\\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\\\\\w))?(?:\\\\\\\\n|$))\\\"},{\\\"match\\\":\\\"(?:(?:[0-9a-zA-Z_\\\\\\\\.])|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric.go\\\"}]}]}},\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\.?\\\\\\\\d(?:(?:[0-9a-zA-Z_\\\\\\\\.])|(?<=[eEpP])[+-])*\\\"},\\\"operators\\\":{\\\"comment\\\":\\\"Note that the order here is very important!\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"((?:\\\\\\\\*|\\\\\\\\&)+)(?:(?!\\\\\\\\d)(?=(?:[\\\\\\\\w\\\\\\\\[\\\\\\\\]])|(?:\\\\\\\\<\\\\\\\\-)))\\\",\\\"name\\\":\\\"keyword.operator.address.go\\\"},{\\\"match\\\":\\\"<\\\\\\\\-\\\",\\\"name\\\":\\\"keyword.operator.channel.go\\\"},{\\\"match\\\":\\\"\\\\\\\\-\\\\\\\\-\\\",\\\"name\\\":\\\"keyword.operator.decrement.go\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.go\\\"},{\\\"match\\\":\\\"(==|!=|<=|>=|<(?!<)|>(?!>))\\\",\\\"name\\\":\\\"keyword.operator.comparison.go\\\"},{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\||!)\\\",\\\"name\\\":\\\"keyword.operator.logical.go\\\"},{\\\"match\\\":\\\"(=|\\\\\\\\+=|\\\\\\\\-=|\\\\\\\\|=|\\\\\\\\^=|\\\\\\\\*=|/=|:=|%=|<<=|>>=|&\\\\\\\\^=|&=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.go\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|\\\\\\\\-|\\\\\\\\*|/|%)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.go\\\"},{\\\"match\\\":\\\"(&(?!\\\\\\\\^)|\\\\\\\\||\\\\\\\\^|&\\\\\\\\^|<<|>>|\\\\\\\\~)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.bitwise.go\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.ellipsis.go\\\"}]},\\\"other_struct_interface_expressions\\\":{\\\"comment\\\":\\\"struct and interface expression in-line (before curly bracket)\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"after control variables must be added exactly here, do not move it! (changing may not affect tests, so be careful!)\\\",\\\"include\\\":\\\"#after_control_variables\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"},{\\\"include\\\":\\\"$self\\\"}]}]}},\\\"match\\\":\\\"(\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+)(\\\\\\\\[(?:[^\\\\\\\\]]+)?\\\\\\\\])?(?=\\\\\\\\{)(?<!\\\\\\\\bstruct\\\\\\\\b|\\\\\\\\binterface\\\\\\\\b)\\\"}]},\\\"other_variables\\\":{\\\"comment\\\":\\\"all other variables\\\",\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.go\\\"},\\\"package_name\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(package)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.package.go\\\"}},\\\"comment\\\":\\\"package name\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\d\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid.illegal.identifier.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.package.go\\\"}]}]},\\\"parameter-variable-types\\\":{\\\"comment\\\":\\\"function and generic parameter types\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"},{\\\"begin\\\":\\\"(?:([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)?(\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#generic_param_types\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"}]}]},\\\"property_variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]}},\\\"comment\\\":\\\"Property variables in struct\\\",\\\"match\\\":\\\"((?:\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+)(?:\\\\\\\\:(?!\\\\\\\\=)))\\\"},\\\"raw_string_literals\\\":{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.go\\\"}},\\\"comment\\\":\\\"Raw string literals\\\",\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.go\\\"}},\\\"name\\\":\\\"string.quoted.raw.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_placeholder\\\"}]},\\\"runes\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.go\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.go\\\"}},\\\"name\\\":\\\"string.quoted.rune.go\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\G(\\\\\\\\\\\\\\\\([0-7]{3}|[abfnrtv\\\\\\\\\\\\\\\\'\\\\\\\"]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})|.)(?=')\\\",\\\"name\\\":\\\"constant.other.rune.go\\\"},{\\\"match\\\":\\\"[^']+\\\",\\\"name\\\":\\\"invalid.illegal.unknown-rune.go\\\"}]}]},\\\"single_type\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"single type declaration\\\",\\\"match\\\":\\\"(?:(?:^\\\\\\\\s*)(\\\\\\\\btype\\\\\\\\b)(?:\\\\\\\\s*)([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(?:\\\\\\\\s+)(?!(?:\\\\\\\\=\\\\\\\\s*)?(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|interface)\\\\\\\\b)([\\\\\\\\s\\\\\\\\S]+))\\\"},{\\\"begin\\\":\\\"(?:(?:^|\\\\\\\\s+)(\\\\\\\\btype\\\\\\\\b)(?:\\\\\\\\s*)([\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)(?=\\\\\\\\[))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"single type declaration with generics\\\",\\\"end\\\":\\\"(?:(?<=\\\\\\\\])((?:\\\\\\\\s+)(?:\\\\\\\\=\\\\\\\\s*)?(?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?(?:\\\\\\\\bstruct\\\\\\\\b|\\\\\\\\binterface\\\\\\\\b|\\\\\\\\bfunc\\\\\\\\b))[\\\\\\\\w\\\\\\\\.\\\\\\\\-\\\\\\\\*\\\\\\\\[\\\\\\\\]]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)*))?)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types\\\"},{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}]},\\\"slice_index_variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.go\\\"}]}},\\\"comment\\\":\\\"slice index and capacity variables, to not scope them as property variables\\\",\\\"match\\\":\\\"(?<=\\\\\\\\w\\\\\\\\[)((?:(?:\\\\\\\\b[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\+/\\\\\\\\-\\\\\\\\%\\\\\\\\<\\\\\\\\>\\\\\\\\|\\\\\\\\&]+\\\\\\\\:)|(?:\\\\\\\\:\\\\\\\\b[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\+/\\\\\\\\-\\\\\\\\%\\\\\\\\<\\\\\\\\>\\\\\\\\|\\\\\\\\&]+))(?:\\\\\\\\b[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\+/\\\\\\\\-\\\\\\\\%\\\\\\\\<\\\\\\\\>\\\\\\\\|\\\\\\\\&]+)?(?:\\\\\\\\:\\\\\\\\b[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\+/\\\\\\\\-\\\\\\\\%\\\\\\\\<\\\\\\\\>\\\\\\\\|\\\\\\\\&]+)?)(?=\\\\\\\\])\\\"},\\\"statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#package_name\\\"},{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#syntax_errors\\\"},{\\\"include\\\":\\\"#group-functions\\\"},{\\\"include\\\":\\\"#group-types\\\"},{\\\"include\\\":\\\"#group-variables\\\"},{\\\"include\\\":\\\"#field_hover\\\"}]},\\\"storage_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bbool\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.boolean.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bbyte\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.byte.go\\\"},{\\\"match\\\":\\\"\\\\\\\\berror\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.error.go\\\"},{\\\"match\\\":\\\"\\\\\\\\b(complex(64|128)|float(32|64)|u?int(8|16|32|64)?)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.numeric.go\\\"},{\\\"match\\\":\\\"\\\\\\\\brune\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.rune.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bstring\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.string.go\\\"},{\\\"match\\\":\\\"\\\\\\\\buintptr\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.uintptr.go\\\"},{\\\"match\\\":\\\"\\\\\\\\bany\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.any.go\\\"}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-7]{3}|[abfnrtv\\\\\\\\\\\\\\\\'\\\\\\\"]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})\\\",\\\"name\\\":\\\"constant.character.escape.go\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[^0-7xuUabfnrtv\\\\\\\\'\\\\\\\"]\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.go\\\"}]},\\\"string_literals\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.go\\\"}},\\\"comment\\\":\\\"Interpreted string literals\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.go\\\"}},\\\"name\\\":\\\"string.quoted.double.go\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"}]}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\[\\\\\\\\d+\\\\\\\\])?([\\\\\\\\+#\\\\\\\\-0\\\\\\\\x20]{,2}((\\\\\\\\d+|\\\\\\\\*)?(\\\\\\\\.?(\\\\\\\\d+|\\\\\\\\*|(\\\\\\\\[\\\\\\\\d+\\\\\\\\])\\\\\\\\*?)?(\\\\\\\\[\\\\\\\\d+\\\\\\\\])?)?))?[vT%tbcdoqxXUbeEfFgGspw]\\\",\\\"name\\\":\\\"constant.other.placeholder.go\\\"}]},\\\"struct_interface_declaration\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"struct, interface type declarations (related to: struct_variables_types, interface_variables_types)\\\",\\\"match\\\":\\\"(?:(?:^\\\\\\\\s*)(\\\\\\\\btype\\\\\\\\b)(?:\\\\\\\\s*)([\\\\\\\\w\\\\\\\\.]+))\\\"},\\\"struct_variable_types_fields_multi\\\":{\\\"comment\\\":\\\"struct variable and type fields with multi lines\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:((?:\\\\\\\\w+(?:\\\\\\\\,\\\\\\\\s*\\\\\\\\w+)*)(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:\\\\\\\\s+)(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?)(\\\\\\\\bstruct\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\{))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.struct.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"comment\\\":\\\"struct in struct types\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types_fields\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?:((?:\\\\\\\\w+(?:\\\\\\\\,\\\\\\\\s*\\\\\\\\w+)*)(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:\\\\\\\\s+)(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?)(\\\\\\\\binterface\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\{))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.interface.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"comment\\\":\\\"interface in struct types\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_variables_types_field\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?:((?:\\\\\\\\w+(?:\\\\\\\\,\\\\\\\\s*\\\\\\\\w+)*)(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:\\\\\\\\s+)(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?)(\\\\\\\\bfunc\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.function.go\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"comment\\\":\\\"function in struct types\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_param_types\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#parameter-variable-types\\\"}]},\\\"struct_variables_types\\\":{\\\"begin\\\":\\\"(\\\\\\\\bstruct\\\\\\\\b)\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.struct.go\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"comment\\\":\\\"Struct variable type\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variables_types_fields\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"struct_variables_types_fields\\\":{\\\"comment\\\":\\\"Struct variable type fields\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#struct_variable_types_fields_multi\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"one line - single type\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\{)\\\\\\\\s*((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]]+))\\\\\\\\s*(?=\\\\\\\\}))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"one line - property variables and types\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\{)\\\\\\\\s*((?:(?:\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?(?:\\\\\\\\w+\\\\\\\\s+))((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]]+))\\\\\\\\s*(?=\\\\\\\\}))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"match\\\":\\\"(?:((?:(?:\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?(?:\\\\\\\\w+\\\\\\\\s+))?((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\S]+)(?:\\\\\\\\;)?))\\\"}]}},\\\"comment\\\":\\\"one line with semicolon(;) without formatting gofmt - single type | property variables and types\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\{)((?:\\\\\\\\s*(?:(?:(?:\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?(?:\\\\\\\\w+\\\\\\\\s+))?(?:(?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\S]+)(?:\\\\\\\\;)?))+)\\\\\\\\s*(?=\\\\\\\\}))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"one type only\\\",\\\"match\\\":\\\"(?:((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)\\\\\\\\s*)(?:(?=\\\\\\\\`|\\\\\\\\/|\\\\\\\")|$))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"variable.other.property.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#parameter-variable-types\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"property variables and types\\\",\\\"match\\\":\\\"(?:((?:(?:\\\\\\\\w+\\\\\\\\,\\\\\\\\s*)+)?(?:\\\\\\\\w+\\\\\\\\s+))([^\\\\\\\\`\\\\\\\"\\\\\\\\/]+))\\\"}]},\\\"support_functions\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.support.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\d\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid.illegal.identifier.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.function.support.go\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"Support Functions\\\",\\\"match\\\":\\\"(?:(?:((?<=\\\\\\\\.)\\\\\\\\b\\\\\\\\w+)|(\\\\\\\\b\\\\\\\\w+))(\\\\\\\\[(?:(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\"\\\\\\\\']+)(?:(?:\\\\\\\\,\\\\\\\\s*(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}]+))*))?\\\\\\\\])?(?=\\\\\\\\())\\\"},\\\"switch_select_case_variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"include\\\":\\\"#support_functions\\\"},{\\\"include\\\":\\\"#variable_assignment\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.go\\\"}]}},\\\"comment\\\":\\\"variables after case control keyword in switch/select expression, to not scope them as property variables\\\",\\\"match\\\":\\\"(?:(?:^\\\\\\\\s*(\\\\\\\\bcase\\\\\\\\b))(?:\\\\\\\\s+)([\\\\\\\\s\\\\\\\\S]+(?:\\\\\\\\:)\\\\\\\\s*(?:/(?:/|\\\\\\\\*).*)?)$)\\\"},\\\"switch_types\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\bswitch\\\\\\\\b)(?:\\\\\\\\s*)(?:(\\\\\\\\w+\\\\\\\\s*\\\\\\\\:\\\\\\\\=)?\\\\\\\\s*([\\\\\\\\w\\\\\\\\.\\\\\\\\*\\\\\\\\(\\\\\\\\)\\\\\\\\[\\\\\\\\]\\\\\\\\+/\\\\\\\\-\\\\\\\\%\\\\\\\\<\\\\\\\\>\\\\\\\\|\\\\\\\\&]+))(\\\\\\\\.\\\\\\\\(\\\\\\\\btype\\\\\\\\b\\\\\\\\)\\\\\\\\s*)(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.assignment.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#support_functions\\\"},{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.go\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"match\\\":\\\"\\\\\\\\btype\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.go\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.curly.go\\\"}},\\\"comment\\\":\\\"switch type assertions, only highlights types after case keyword\\\",\\\"end\\\":\\\"(?:\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.curly.go\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.other.colon.go\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"}]}},\\\"comment\\\":\\\"types after case keyword with single line\\\",\\\"match\\\":\\\"(?:^\\\\\\\\s*(\\\\\\\\bcase\\\\\\\\b))(?:\\\\\\\\s+)([\\\\\\\\w\\\\\\\\.\\\\\\\\,\\\\\\\\*\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\!\\\\\\\\s]+)(:)(\\\\\\\\s*/(?:/|\\\\\\\\*)\\\\\\\\s*.*)?$\\\"},{\\\"begin\\\":\\\"\\\\\\\\bcase\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.go\\\"}},\\\"comment\\\":\\\"types after case keyword with multi lines\\\",\\\"end\\\":\\\"\\\\\\\\:\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.other.colon.go\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"syntax_errors\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.slice.go\\\"}},\\\"comment\\\":\\\"Syntax error using slices\\\",\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\](\\\\\\\\s+)\\\"},{\\\"comment\\\":\\\"Syntax error numeric literals\\\",\\\"match\\\":\\\"\\\\\\\\b0[0-7]*[89]\\\\\\\\d*\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.numeric.go\\\"}]},\\\"terminators\\\":{\\\"comment\\\":\\\"Terminators\\\",\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.go\\\"},\\\"type-declarations\\\":{\\\"comment\\\":\\\"includes all type declarations\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#language_constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#map_types\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#delimiters\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#runes\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#raw_string_literals\\\"},{\\\"include\\\":\\\"#string_literals\\\"},{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#terminators\\\"}]},\\\"type-declarations-without-brackets\\\":{\\\"comment\\\":\\\"includes all type declarations without brackets (in some cases, brackets need to be captured manually)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#language_constants\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#map_types\\\"},{\\\"include\\\":\\\"#delimiters\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#runes\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#raw_string_literals\\\"},{\\\"include\\\":\\\"#string_literals\\\"},{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#terminators\\\"}]},\\\"type_assertion_inline\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.go\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\w+)\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"struct/interface types in-line (type assertion) | switch type keyword\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\.\\\\\\\\()(?:(\\\\\\\\btype\\\\\\\\b)|((?:(?:\\\\\\\\s*(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+)?[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+))(?=\\\\\\\\)))\\\"},\\\"var_assignment\\\":{\\\"comment\\\":\\\"variable assignment with var keyword\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.assignment.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"comment\\\":\\\"single assignment\\\",\\\"match\\\":\\\"(?:(?<=\\\\\\\\bvar\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.]+)*)(?:\\\\\\\\s*)((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+(?:\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|func|map)\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)*)?(?:\\\\\\\\s*)(?:\\\\\\\\=)?)?)\\\"},{\\\"begin\\\":\\\"(?:(?<=\\\\\\\\bvar\\\\\\\\b)(?:\\\\\\\\s*)(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"}},\\\"comment\\\":\\\"multi assignment\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.assignment.go\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-declarations-without-brackets\\\"},{\\\"include\\\":\\\"#generic_types\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.round.go\\\"},{\\\"match\\\":\\\"\\\\\\\\[\\\",\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.end.bracket.square.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.type.go\\\"}]}},\\\"match\\\":\\\"(?:(?:^\\\\\\\\s*)(\\\\\\\\b[\\\\\\\\w\\\\\\\\.]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.]+)*)(?:\\\\\\\\s*)((?:(?:(?:[\\\\\\\\*\\\\\\\\[\\\\\\\\]]+)?(?:\\\\\\\\<\\\\\\\\-\\\\\\\\s*)?\\\\\\\\bchan\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\<\\\\\\\\-)?\\\\\\\\s*)+(?:\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?)?(?!(?:[\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)?\\\\\\\\b(?:struct|func|map)\\\\\\\\b)(?:[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\[\\\\\\\\]\\\\\\\\*]+)*)?(?:\\\\\\\\s*)(?:\\\\\\\\=)?)?)\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"variable_assignment\\\":{\\\"comment\\\":\\\"variable assignment\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"match\\\":\\\"\\\\\\\\d\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid.illegal.identifier.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.assignment.go\\\"}]}},\\\"comment\\\":\\\"variable assignment with :=\\\",\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(?:\\\\\\\\,\\\\\\\\s*\\\\\\\\w+)*(?=\\\\\\\\s*:=)\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#delimiters\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\d\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid.illegal.identifier.go\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.assignment.go\\\"}]}},\\\"comment\\\":\\\"variable assignment with =\\\",\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+(?:\\\\\\\\,\\\\\\\\s*[\\\\\\\\w\\\\\\\\.\\\\\\\\*]+)*(?=\\\\\\\\s*=(?!=))\\\"}]}},\\\"scopeName\\\":\\\"source.go\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/go.mjs\n"));

/***/ })

}]);