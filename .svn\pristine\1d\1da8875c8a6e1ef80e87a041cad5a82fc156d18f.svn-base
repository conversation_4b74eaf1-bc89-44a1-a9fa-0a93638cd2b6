﻿using goodkey_cms.DTO.Menu;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.DTO.Menu;
using goodkey_common.Models;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class MenuController : ControllerBase
    {
        private readonly IMenuRepository _repo;
        public MenuController(IMenuRepository repo)
        {
            _repo = repo;
        }

        [HttpGet]
        public GenericRespond<IEnumerable<SectionDetail>> GetBrief()
        {
            return new()
            {
                Data = _repo.GetMenus().Select(item => new SectionDetail()
                {
                    Id = item.Id,
                    Name = item.Name,
                    IsDashboard = item.IsDashboard
                })
            };
        }

        [HttpGet("[action]")]
        public GenericRespond<IEnumerable<MenuBasicDetail>> Items()
        {
            return new()
            {
                Data = _repo.GetAll().Select(item => GetMenu(item))
            };
        }

        [HttpGet("[action]/{sectionName}")]
        public GenericRespond<IEnumerable<MenuBasicDetail>> ItemsBySection(string sectionName)
        {
            var username = Request.HttpContext.GetUsername();

            return new()
            {
                Data = _repo.GetAll()
                    .Where(c => c.Section != null && c.Section.Name == sectionName)
                    .Where(c => c.IsVisible == true)
                    .Where(c => c.Parent == null || c.Parent.Section?.Name != sectionName)
                    .OrderBy(c => c.DisplayOrder)
                    .Select(item => GetMenu(item))
            };
        }
        [HttpGet("[action]/{id:int}")]
        public GenericRespond<MenuData> Items(int id)
        {
            var item = _repo.Get(id);

            return new()
            {
                Data = item == null ? null : new()
                {
                    Name = item.Name,
                    Description = item.Description,
                    IconName = item.IconName,
                    IsVisible = item.IsVisible == true,
                    DisplayOrder = item.DisplayOrder,
                    PermissionKey = item.PermissionKey,
                    ParentId = item.ParentId,
                    Url = item.Url,
                    SectionId = item.SectionId ?? 1,
                    Target = item.Target,
                    ImagePath = item.ImagePath,
                    Level = item.Level,
                    IsParent = item.IsParent == true,
                    MetaDescription = item.MetaDescription,
                    Keywords = item.MetaKeywords,
                    IsStatic = true,
                    IsDashboard = item.IsDashboard == true,
                    Direction = item.Direction
                }
            };
        }
        [HttpPost("[action]")]
        public GenericRespond<int?> Items([FromForm] MenuData data)
        {
            // Ensure permissionKey is not null
            string permissionKey = data.PermissionKey ?? "";
            var result = _repo.Create(data.SectionId, data.ParentId, data.Name, data.Description, data.Keywords, data.MetaDescription, data.Url, data.DisplayOrder, permissionKey, data.IconName, data.Target, data.IsVisible, data.IsParent, data.IsDashboard, data.IsStatic, data.Direction, data.Image, data.RoleId, data.Level, Request.HttpContext.GetUsername() ?? "");
            return new()
            {
                Data = result
            };
        }
        [HttpPatch("[action]/{id:int}")]
        public GenericRespond<bool> Items(int id, [FromForm] MenuData data)
        {
            // Ensure permissionKey is not null
            string permissionKey = data.PermissionKey ?? "";
            var result = _repo.Update(data.SectionId, id, data.ParentId, data.Name, data.Description, data.Keywords, data.MetaDescription, data.Url, data.DisplayOrder, permissionKey, data.IconName, data.Target, data.IsVisible, data.IsParent, data.IsDashboard, data.IsStatic, data.Direction, data.Image, data.RoleId, data.Level) && _repo.UpdatedStatus(id, Request.HttpContext.GetUsername() ?? "");
            return new()
            {
                Data = result
            };
        }
        [HttpPatch("[action]/{id:int}")]
        public GenericRespond<bool> MoveDown(int id)
        {
            var username = Request.HttpContext.GetUsername() ?? "";
            var result = _repo.MoveDown(id, username);
            return new()
            {
                Data = result
            };
        }

        [HttpPatch("[action]/{id:int}")]
        public GenericRespond<bool> MoveUp(int id)
        {
            var username = Request.HttpContext.GetUsername() ?? "";
            var result = _repo.MoveUp(id, username);
            return new()
            {
                Data = result
            };
        }
        private static MenuBasicDetail GetMenu(MenuItem item, bool withChildren = true, bool withParent = true)
        {
            return new MenuBasicDetail()
            {
                Id = item.MenuItemId,
                Name = item.Name,
                Icon = item.IconName,
                IsVisible = item.IsVisible == true,
                DisplayOrder = item.DisplayOrder,
                PermissionKey = item.PermissionKey,
                Url = item.Url,
                Section = item.Section?.Name,
                IsParent = item.IsParent == true,
                Parent = withParent ? item.Parent == null ? null : GetMenu(item.Parent, false) : null,
                Image = item.ImagePath,
                Description = item.Description,
                Target = item.Target,
                IsDashboard = item.IsDashboard == true,
                Children = withChildren ? item.InverseParent.Select(c => GetMenu(c, true, false)).ToList() : new()
            };
        }
    }
}
