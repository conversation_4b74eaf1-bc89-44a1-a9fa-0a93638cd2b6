'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  AlertCircle,
  Upload,
  FileCheck,
  Edit3,
  Play,
} from 'lucide-react';
import ExhibitorImportQuery from '@/services/queries/ExhibitorImportQuery';
import type {
  ExhibitorImportValidationResponseDto,
  ExhibitorImportExecutionResponseDto,
} from '@/models/ExhibitorImport';

// Import the step components
import FileUploadStep from './FileUploadStep';
import ValidationStep from './ValidationStep';
import DuplicateResolutionStep from './DuplicateResolutionStep';
import ComprehensiveDataFixingStep from './ComprehensiveDataFixingStep';
import ExecutionStep from './ExecutionStep';
import CompletionStep from './CompletionStep';
import { toast } from '@/components/ui/use-toast';

interface ExhibitorImportClientProps {
  showId: number;
}

type ImportStep =
  | 'upload'
  | 'validation'
  | 'fixing'
  | 'duplicates'
  | 'execution'
  | 'completed';

interface ImportState {
  currentStep: ImportStep;
  sessionId?: string;
  validationData?: ExhibitorImportValidationResponseDto;
  executionData?: ExhibitorImportExecutionResponseDto;
  isLoading: boolean;
  error?: string;
}

const ExhibitorImportClient: React.FC<ExhibitorImportClientProps> = ({
  showId,
}) => {
  const [state, setState] = useState<ImportState>({
    currentStep: 'upload',
    isLoading: false,
  });

  const steps = [
    { key: 'upload', label: 'Upload File', icon: Upload },
    { key: 'validation', label: 'Review Data', icon: FileCheck },
    { key: 'fixing', label: 'Fix Issues', icon: Edit3 },
    { key: 'duplicates', label: 'Resolve Duplicates', icon: AlertCircle },
    { key: 'execution', label: 'Import Data', icon: Play },
    { key: 'completed', label: 'Complete', icon: CheckCircle },
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.key === state.currentStep);
  };

  const getProgressPercentage = () => {
    const currentIndex = getCurrentStepIndex();
    return ((currentIndex + 1) / steps.length) * 100;
  };

  // Phase 1: Handle file upload and validation
  const handleFileUpload = async (file: File) => {
    setState((prev) => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await ExhibitorImportQuery.upload(file, showId);

      setState((prev) => ({
        ...prev,
        sessionId: response.sessionId,
        validationData: response,
        currentStep: 'validation',
        isLoading: false,
      }));

      toast({
        title: 'File uploaded successfully',
        description: `Processed ${response.summary.totalRows} rows with ${response.summary.errorRows} errors and ${response.summary.warningRows} warnings.`,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      }));

      toast({
        title: 'Upload failed',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred during upload',
        variant: 'destructive',
      });
    }
  };

  // Phase 2: Handle validation completion - move to data fixing
  const handleValidationComplete = async () => {
    // Always go to data fixing step first to allow users to fix errors and review data
    setState((prev) => ({ ...prev, currentStep: 'fixing' }));
  };

  // Phase 3: Handle data fixing completion
  const handleDataFixingComplete = async (fixedData: any) => {
    // After data fixing, check if there are still duplicates to resolve
    if (state.validationData?.duplicates?.length) {
      setState((prev) => ({ ...prev, currentStep: 'duplicates' }));
      toast({
        title: 'Data fixes applied',
        description: 'Now resolving duplicate conflicts.',
      });
    } else {
      // No duplicates, proceed to execution
      setState((prev) => ({ ...prev, currentStep: 'execution' }));
      toast({
        title: 'Data fixes applied',
        description: 'Ready for import execution.',
      });
    }
  };

  const handleDuplicatesResolved = async () => {
    setState((prev) => ({ ...prev, currentStep: 'execution' }));

    toast({
      title: 'Duplicates resolved',
      description:
        'All duplicate conflicts have been resolved. Ready to import.',
    });
  };

  // Phase 3: Handle import execution
  const handleExecuteImport = async (sendEmailInvites: boolean = false) => {
    if (!state.sessionId) return;

    setState((prev) => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await ExhibitorImportQuery.execute({
        sessionId: state.sessionId,
        sendEmailInvites,
      });
      console.log('Execution response:', response);

      setState((prev) => ({
        ...prev,
        executionData: response,
        currentStep: 'completed',
        isLoading: false,
      }));

      toast({
        title: 'Import completed successfully',
        description: `Processed ${response.summary.processedRows} rows. Created ${response.summary.companiesCreated} companies and ${response.summary.contactsCreated} contacts.`,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Import failed',
      }));

      toast({
        title: 'Import failed',
        description:
          error instanceof Error
            ? error.message
            : 'An error occurred during import',
        variant: 'destructive',
      });
    }
  };

  const handleStartOver = () => {
    setState({
      currentStep: 'upload',
      isLoading: false,
    });
  };

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <CardTitle>Exhibitor Import</CardTitle>
          <div className="space-y-4">
            <Progress value={getProgressPercentage()} className="w-full" />
            <div className="flex justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = state.currentStep === step.key;
                const isCompleted = getCurrentStepIndex() > index;

                return (
                  <div
                    key={step.key}
                    className={`flex flex-col items-center space-y-2 ${
                      isActive
                        ? 'text-primary'
                        : isCompleted
                          ? 'text-green-600'
                          : 'text-muted-foreground'
                    }`}
                  >
                    <div
                      className={`p-2 rounded-full ${
                        isActive
                          ? 'bg-primary text-primary-foreground'
                          : isCompleted
                            ? 'bg-green-100 text-green-600'
                            : 'bg-muted'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                    </div>
                    <span className="text-xs font-medium">{step.label}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display */}
      {state.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {state.currentStep === 'upload' && (
            <FileUploadStep
              onFileUpload={handleFileUpload}
              isLoading={state.isLoading}
            />
          )}

          {state.currentStep === 'validation' && state.validationData && (
            <ValidationStep
              validationData={state.validationData}
              onProceed={handleValidationComplete}
              isLoading={state.isLoading}
            />
          )}

          {state.currentStep === 'fixing' && state.validationData && (
            <ComprehensiveDataFixingStep
              validationData={state.validationData}
              onDataFixed={handleDataFixingComplete}
              isLoading={state.isLoading}
            />
          )}

          {state.currentStep === 'duplicates' && state.validationData && (
            <DuplicateResolutionStep
              sessionId={state.sessionId!}
              duplicates={state.validationData.duplicates}
              onResolved={handleDuplicatesResolved}
              isLoading={state.isLoading}
            />
          )}

          {state.currentStep === 'execution' && (
            <ExecutionStep
              onExecute={handleExecuteImport}
              isLoading={state.isLoading}
            />
          )}

          {state.currentStep === 'completed' && state.executionData && (
            <CompletionStep
              executionData={state.executionData}
              onStartOver={handleStartOver}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ExhibitorImportClient;
