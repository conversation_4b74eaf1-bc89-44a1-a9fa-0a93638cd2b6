'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { modal } from '@/components/ui/overlay';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { addSchema, ImageFormValues } from './ImageModal.schema';
import GalleryQuery from '@/services/queries/GalleryQuery';

interface IImageModal {
  imgId?: number;
  subcategoryId?: number;
}

function Content({
  defaultValue,
  imgId,
  subcategoryId,
}: {
  defaultValue?: Partial<ImageFormValues & { url?: string }>;
  imgId?: number;
  subcategoryId?: number;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch subcategories
  const { data: subcategories, isLoading: loadingSubcategories } = useQuery({
    queryKey: ['gallery', 'subcategories'],
    queryFn: GalleryQuery.getSubcategories,
  });

  const { mutate, isPending } = useMutation({
    mutationFn:
      defaultValue && imgId
        ? GalleryQuery.updateImage(Number(imgId))
        : GalleryQuery.createImage,
    onSuccess: () => {
      // Invalidate queries to refresh the table data
      queryClient.invalidateQueries({ queryKey: ['gallery', 'images'] });
      queryClient.invalidateQueries({ queryKey: ['gallery', 'image'] });
      modal.close();
      toast({
        title: 'Success',
        description:
          defaultValue && imgId
            ? 'Image updated successfully'
            : 'Image added successfully',
        variant: 'success',
      });
    },
  });

  const form = useForm<ImageFormValues>({
    resolver: zodResolver(addSchema),
    defaultValues: defaultValue
      ? {
          subcategoryId: String(defaultValue.subcategoryId),
          name: defaultValue.name,
          alt: defaultValue.alt,
          image: defaultValue.image,
          displayOrder: defaultValue.displayOrder,
        }
      : {
          subcategoryId: subcategoryId ? String(subcategoryId) : '',
          name: '',
          alt: '',
          image: undefined,
          displayOrder: 1,
        },
  });

  if (loadingSubcategories) {
    return (
      <ModalContainer
        title={(defaultValue ? 'Update' : 'Add') + ' Image'}
        description="Loading..."
        controls={
          <div className="flex justify-end w-full items-center gap-4">
            <Button variant={'primary'} disabled>
              Loading...
            </Button>
          </div>
        }
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading subcategories...</p>
          </div>
        </div>
      </ModalContainer>
    );
  }

  return (
    <Form {...form}>
      <ModalContainer
        onSubmit={form.handleSubmit((data) => {
          const formData = new FormData();
          formData.append('subcategoryId', data.subcategoryId);
          formData.append('name', data.name);
          formData.append('alt', data.alt || '');
          if (data.image && data.image.length > 0) {
            formData.append('image', data.image[0]);
          }
          formData.append('displayOrder', String(data.displayOrder));
          mutate(formData);
        })}
        title={(defaultValue ? 'Update' : 'Add') + ' Image'}
        description="Please fill in the image details"
        controls={
          <div className="flex justify-end w-full items-center gap-4">
            <Button
              variant={'primary'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{
                className: 'text-white',
              }}
            >
              {isPending
                ? defaultValue
                  ? 'Updating...'
                  : 'Adding...'
                : defaultValue
                  ? 'Update'
                  : 'Add'}
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-6 w-full rounded-[12px] ">
          <Field
            control={form.control}
            name="subcategoryId"
            label="Subcategory"
            required={true}
            type={{
              type: 'select',
              props: {
                options: (subcategories || []).map((sub) => ({
                  label: sub.name,
                  value: String(sub.id),
                })),
                placeholder: 'Select subcategory',
              },
            }}
          />
          <Field
            control={form.control}
            name="name"
            label="Name"
            required={true}
            type="text"
          />
          <Field
            control={form.control}
            name="alt"
            label="Alt Text"
            required={true}
            type="text"
          />
          <Field
            control={form.control}
            name="image"
            label="Image"
            required={!defaultValue?.image}
            type={{
              type: 'file',
              props: {
                dropzoneOptions: {
                  accept: { 'image/*': ['.jpg', '.jpeg', '.png'] },
                  multiple: false,
                  maxFiles: 1,
                },
              },
            }}
            aspect={[1.5, 4 / 3]}
          />
          <Field
            control={form.control}
            name="displayOrder"
            label="Display Order"
            required={true}
            type="number"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

function ImageModal({ imgId, subcategoryId }: IImageModal) {
  const { data, isLoading, isPaused } = useQuery({
    queryFn: () => (imgId ? GalleryQuery.getImage(Number(imgId)) : undefined),
    queryKey: ['gallery', 'image', { imgId }],
    select: (d) =>
      d
        ? {
            subcategoryId: String(d.subcategoryId),
            name: d.name,
            alt: d.alt,
            image: d.image,
            url: d.url,
            displayOrder: d.displayOrder,
          }
        : undefined,
    enabled: !!imgId,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <Content
        defaultValue={data}
        imgId={imgId}
        subcategoryId={subcategoryId}
      />
    </Suspense>
  );
}

export default ImageModal;
