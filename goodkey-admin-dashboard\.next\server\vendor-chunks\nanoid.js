"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nanoid";
exports.ids = ["vendor-chunks/nanoid"];
exports.modules = {

/***/ "(ssr)/./node_modules/nanoid/index.js":
/*!**************************************!*\
  !*** ./node_modules/nanoid/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customAlphabet: () => (/* binding */ customAlphabet),\n/* harmony export */   customRandom: () => (/* binding */ customRandom),\n/* harmony export */   nanoid: () => (/* binding */ nanoid),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   urlAlphabet: () => (/* reexport safe */ _url_alphabet_index_js__WEBPACK_IMPORTED_MODULE_1__.urlAlphabet)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _url_alphabet_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./url-alphabet/index.js */ \"(ssr)/./node_modules/nanoid/url-alphabet/index.js\");\n\n\n\nconst POOL_SIZE_MULTIPLIER = 128\nlet pool, poolOffset\nfunction fillPool(bytes) {\n  if (!pool || pool.length < bytes) {\n    pool = Buffer.allocUnsafe(bytes * POOL_SIZE_MULTIPLIER)\n    node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto.getRandomValues(pool)\n    poolOffset = 0\n  } else if (poolOffset + bytes > pool.length) {\n    node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto.getRandomValues(pool)\n    poolOffset = 0\n  }\n  poolOffset += bytes\n}\nfunction random(bytes) {\n  fillPool((bytes |= 0))\n  return pool.subarray(poolOffset - bytes, poolOffset)\n}\nfunction customRandom(alphabet, defaultSize, getRandom) {\n  let mask = (2 << (31 - Math.clz32((alphabet.length - 1) | 1))) - 1\n  let step = Math.ceil((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let i = step\n      while (i--) {\n        id += alphabet[bytes[i] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nfunction customAlphabet(alphabet, size = 21) {\n  return customRandom(alphabet, size, random)\n}\nfunction nanoid(size = 21) {\n  fillPool((size |= 0))\n  let id = ''\n  for (let i = poolOffset - size; i < poolOffset; i++) {\n    id += _url_alphabet_index_js__WEBPACK_IMPORTED_MODULE_1__.urlAlphabet[pool[i] & 63]\n  }\n  return id\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nanoid/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nanoid/url-alphabet/index.js":
/*!***************************************************!*\
  !*** ./node_modules/nanoid/url-alphabet/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   urlAlphabet: () => (/* binding */ urlAlphabet)\n/* harmony export */ });\nconst urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmFub2lkL3VybC1hbHBoYWJldC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuYW5vaWRcXHVybC1hbHBoYWJldFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHVybEFscGhhYmV0ID1cbiAgJ3VzZWFuZG9tLTI2VDE5ODM0MFBYNzVweEpBQ0tWRVJZTUlOREJVU0hXT0xGX0dRWmJmZ2hqa2xxdnd5enJpY3QnXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nanoid/url-alphabet/index.js\n");

/***/ })

};
;