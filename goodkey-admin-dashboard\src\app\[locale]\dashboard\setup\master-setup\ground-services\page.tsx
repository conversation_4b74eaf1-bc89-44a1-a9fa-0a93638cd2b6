import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import AppLayout from '@/components/ui/app_layout';
import GroundServiceQuery from '@/services/queries/GroundServiceQuery';
import { getQueryClient } from '@/utils/query-client';
import GroundServicesManagementSection from './components/GroundServicesManagementSection';

export default async function GroundServicesManagementPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: ['ground-services'],
    queryFn: GroundServiceQuery.getAll,
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Ground Services',
          link: '/dashboard/setup/master-setup/ground-services',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <GroundServicesManagementSection />
      </HydrationBoundary>
    </AppLayout>
  );
}
