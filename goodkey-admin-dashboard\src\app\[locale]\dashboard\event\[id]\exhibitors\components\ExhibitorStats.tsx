'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { ShowExhibitorStats } from '@/models/ShowExhibitor';

interface ExhibitorStatsProps {
  stats?: ShowExhibitorStats;
  isLoading: boolean;
}

export function ExhibitorStats({ stats, isLoading }: ExhibitorStatsProps) {
  if (isLoading) {
    return (
      <div className="flex flex-wrap gap-2">
        {Array.from({ length: 3 }).map((_, i) => (
          <div
            key={i}
            className="bg-gray-100 rounded-full px-3 py-1.5 flex items-center gap-1.5"
          >
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-6" />
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const statBadges = [
    {
      title: 'Total',
      value: stats.totalExhibitors,
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      borderColor: 'border-blue-200',
    },
    {
      title: 'Active',
      value: stats.activeExhibitors,
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      borderColor: 'border-green-200',
    },
    {
      title: 'With Booths',
      value: stats.exhibitorsWithBooths,
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      borderColor: 'border-purple-200',
    },
  ];

  return (
    <div className="flex flex-wrap gap-2 justify-end">
      {statBadges.map((stat, index) => (
        <div
          key={index}
          className={`${stat.bgColor} ${stat.textColor} border ${stat.borderColor} rounded-full px-3 py-1.5 flex items-center gap-1.5`}
        >
          <span className="text-xs font-medium">{stat.title}:</span>
          <span className="text-xs font-bold">{stat.value}</span>
        </div>
      ))}
    </div>
  );
}
