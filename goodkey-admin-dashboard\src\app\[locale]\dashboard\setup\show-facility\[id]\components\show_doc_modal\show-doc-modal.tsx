'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import Field from '@/components/ui/inputs/field';
import { Spinner } from '@/components/ui/spinner';
import { DropzoneOptions } from 'react-dropzone';
import {
  ShowDocUpload,
  ShowDocUpdate,
  ShowDocUploadSchema,
  ShowDocUpdateSchema,
} from '@/schema/ShowDocSchema';
import ShowDocQuery from '@/services/queries/ShowDocQuery';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { getQueryClient } from '@/utils/query-client';

type ShowDocModalFormProps = {
  locationId: number;
  docId?: number;
};

const ShowDocModalForm = ({ locationId, docId }: ShowDocModalFormProps) => {
  const isEdit = !!docId;
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'application/pdf': ['.pdf'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const { data: halls, isLoading } = useQuery({
    queryKey: ['ShowHall', { location: Number(locationId) }],
    queryFn: () => ShowHallQuery.getBriefByLocation(locationId),
    enabled: !!locationId,
  });

  const { data: docType, isLoading: isLoadingDocType } = useQuery({
    queryKey: ['Show Doc Type'],
    queryFn: () => DocumentFileTypeQuery.getAll(),
    enabled: !!locationId,
  });

  const form = useForm<ShowDocUpload | ShowDocUpdate>({
    resolver: zodResolver(isEdit ? ShowDocUpdateSchema : ShowDocUploadSchema),
    defaultValues: {
      docTypeId: undefined,
      locationId: locationId,
      hallId: undefined,
      showId: undefined,
      file: undefined,
      note: '',
      validUntil: undefined,
    },
  });

  const { isLoading: isFetchingDoc } = useQuery({
    queryKey: ['ShowDoc', docId],
    queryFn: () => ShowDocQuery.getById(docId!),
    enabled: isEdit,
    select: (data: ShowDocUpdate) => {
      form.reset({
        ...data,
        validUntil: data.validUntil ? new Date(data.validUntil) : undefined,
        locationId: data.locationId ?? locationId,
      });
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (data: ShowDocUpload | ShowDocUpdate) =>
      isEdit
        ? ShowDocQuery.update(data as ShowDocUpdate)
        : ShowDocQuery.upload(data as ShowDocUpload),

    onSuccess: async () => {
      toast({
        title: 'Success',
        description: `Document ${isEdit ? 'updated' : 'uploaded'} successfully`,
        variant: 'success',
      });

      await getQueryClient().invalidateQueries({
        queryKey: ['ShowDoc', { locationId }],
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Something went wrong',
        variant: 'destructive',
      });
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        className="w-full max-w-3xl"
        title={docId ? 'Update Document' : 'Add Document'}
        description={docId ? 'Update a document' : 'Upload a new document'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end items-center gap-4">
            <Button
              variant={'main'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: isPending ? 'animate-spin' : '' }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        }
      >
        {isFetchingDoc ? (
          <Spinner />
        ) : (
          <>
            <div className="grid grid-cols-1 gap-4">
              <Field
                name="docTypeId"
                control={form.control}
                label="Document Type"
                required
                type={{
                  type: 'select',
                  props: {
                    options:
                      docType?.map((c) => ({
                        label: c.name,
                        value: c.id.toString(),
                      })) ?? [],
                    placeholder: 'Select a Document Type',
                  },
                }}
              />
              <Field
                name="hallId"
                control={form.control}
                label="Hall"
                type={{
                  type: 'select',
                  props: {
                    options:
                      halls?.map((c) => ({
                        label: c.name,
                        value: c.id.toString(),
                      })) ?? [],
                    placeholder: 'Select a Hall',
                  },
                }}
              />
              <Field
                name="showId"
                control={form.control}
                label="Show ID"
                type="number"
              />
              <Field
                name="validUntil"
                control={form.control}
                label="Valid Until"
                type="date"
              />
              <Field
                name="file"
                control={form.control}
                label={isEdit ? 'Replace File (optional)' : 'Upload File'}
                required={!isEdit}
                type={{
                  type: 'file',
                  props: {
                    dropzoneOptions: dropzone,
                  },
                }}
              />
              <Field
                name="note"
                control={form.control}
                label="Note"
                type="textarea"
              />
            </div>
          </>
        )}
      </ModalContainer>
    </Form>
  );
};

export default ShowDocModalForm;
