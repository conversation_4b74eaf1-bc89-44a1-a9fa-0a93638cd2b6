'use client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import GroundConditionQuery from '@/services/queries/GroundConditionQuery';
import { GroundConditionCreate } from '@/models/GroundCondition';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { Spinner } from '@/components/ui/spinner';
import { GroundConditionCreateSchema } from '@/schema/GroundConditionSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import Field from '@/components/ui/inputs/field';
import { useRouter } from 'next/navigation';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: GroundConditionCreate;
  id?: number;
}) {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const form = useForm<GroundConditionCreate>({
    resolver: zodResolver(GroundConditionCreateSchema),
    defaultValues: defaultValues || {
      name: '',
      text: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (f: GroundConditionCreate) =>
      id ? GroundConditionQuery.update(id)(f) : GroundConditionQuery.create(f),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ground-conditions'] });
      toast({
        title: 'Success',
        description: id
          ? 'Ground condition updated'
          : 'Ground condition created',
        variant: 'success',
      });
      push('/dashboard/setup/master-setup/ground-conditions');
    },
    onError: (e: any) =>
      toast({ title: e.message || 'Failed to save', variant: 'destructive' }),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((formData) => mutate(formData))}
        className="space-y-4"
      >
        <div className="flex flex-col gap-4 overflow-y-auto scrollbar-hide pr-1 pb-1">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required
            type="text"
          />
          <Field
            control={form.control}
            name="text"
            label="Description"
            required
            type="LightRichText"
          />
        </div>
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              push('/dashboard/setup/master-setup/ground-conditions')
            }
          >
            Cancel
          </Button>
          <div className="flex justify-end w-full gap-4">
            <Button
              variant="main"
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: 'text-white' }}
              type="submit"
            >
              {isPending ? 'Please wait...' : id ? 'Update' : 'Add'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

export default function GroundConditionForm({
  conditionId,
}: {
  conditionId?: number;
}) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ground-conditions', conditionId],
    queryFn: () =>
      conditionId ? GroundConditionQuery.getById(conditionId) : undefined,
    enabled: !!conditionId,
  });

  let defaultValues: GroundConditionCreate | undefined = undefined;
  if (conditionId && data) {
    defaultValues = {
      name: data.name,
      text: data.text,
    };
  }

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent defaultValues={defaultValues} id={conditionId} />
      )}
    </Suspense>
  );
}
