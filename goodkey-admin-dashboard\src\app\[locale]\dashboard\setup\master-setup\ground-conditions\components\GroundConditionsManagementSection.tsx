'use client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import GroundConditionQuery from '@/services/queries/GroundConditionQuery';
import { GroundCondition } from '@/models/GroundCondition';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import GroundConditionModal from './GroundConditionModal';
import { useMemo } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import Link from 'next/link';

function DescriptionCell({ html }: { html: string }) {
  const handleViewMore = (e: React.MouseEvent) => {
    e.stopPropagation();
    modal(
      ({ close }) => (
        <ModalContainer title="Description" onClose={close}>
          <div
            style={{ whiteSpace: 'pre-line' }}
            dangerouslySetInnerHTML={{ __html: html || '' }}
          />
        </ModalContainer>
      ),
      { ...DEFAULT_MODAL, width: '40%' },
    ).open();
  };
  return (
    <div style={{ maxWidth: 400 }}>
      <div
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'normal',
        }}
        dangerouslySetInnerHTML={{ __html: html || '' }}
      />
      <button
        type="button"
        style={{
          marginTop: 4,
          background: 'none',
          border: 'none',
          color: '#0070f3',
          cursor: 'pointer',
          fontSize: '13px',
          padding: 0,
          textDecoration: 'underline',
        }}
        onClick={handleViewMore}
      >
        View More
      </button>
    </div>
  );
}

export default function GroundConditionsManagementSection() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['ground-conditions'],
    queryFn: GroundConditionQuery.getAll,
  });

  const handleAdd = () => {
    modal(<GroundConditionModal />, { ...DEFAULT_MODAL, width: '75%' }).open();
  };
  const handleEdit = (condition: GroundCondition) => {
    modal(<GroundConditionModal conditionId={condition.id} />, {
      ...DEFAULT_MODAL,
      width: '75%',
    }).open();
  };

  const columns = useMemo(
    () =>
      generateTableColumns<GroundCondition>(
        {
          name: { name: 'Name', type: 'text', sortable: true },
          text: {
            name: 'Description',
            type: {
              type: 'node',
              render: ({ cell }: { cell: string }) => (
                <DescriptionCell html={cell} />
              ),
            },
          },
        },
        {
          action: {
            name: 'Actions',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex gap-2">
                  <Link
                    href={`/dashboard/setup/master-setup/ground-conditions/${row.id ?? 'add'}`}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      iconName="EditIcon"
                    ></Button>
                  </Link>
                  <Button
                    variant="remove"
                    size="sm"
                    iconName="RemoveIcon"
                    onClick={() =>
                      modal(
                        ({ close }) => (
                          <MutationConfirmModal
                            close={close}
                            mutateFn={async () =>
                              GroundConditionQuery.delete(row.id)
                            }
                            mutationKey={GroundConditionQuery.tags}
                            title="Delete Ground Condition"
                            description={`Are you sure you want to delete ${row.name}?`}
                            variant="destructive"
                            confirmButtonText="Delete"
                            confirmIconName="DeleteIcon"
                            loadingIconName="LoadingIcon"
                            onSuccess={() => {
                              toast({
                                title: 'Success',
                                description:
                                  'Ground condition deleted successfully',
                                variant: 'success',
                              });
                              queryClient.invalidateQueries({
                                queryKey: GroundConditionQuery.tags,
                              });
                            }}
                            onError={(error) => {
                              toast({
                                title: 'Error',
                                description:
                                  error.message ||
                                  'Failed to delete ground condition',
                                variant: 'destructive',
                              });
                            }}
                          />
                        ),
                        DEFAULT_MODAL,
                      ).open()
                    }
                  ></Button>
                </div>
              ),
            },
          },
        },
        false,
      ),
    [queryClient, toast],
  );

  const filters = useMemo(
    () =>
      generateTableFilters<GroundCondition>({
        name: { name: 'Name', type: 'text' },
        text: { name: 'Description', type: 'text' },
      }),
    [],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Link href="/dashboard/setup/master-setup/ground-conditions/add">
            <Button
              variant={'main'}
              iconName="AddIcon"
              iconProps={{ className: 'text-white' }}
            >
              Add Ground Service
            </Button>
          </Link>
        }
      />
    </div>
  );
}
