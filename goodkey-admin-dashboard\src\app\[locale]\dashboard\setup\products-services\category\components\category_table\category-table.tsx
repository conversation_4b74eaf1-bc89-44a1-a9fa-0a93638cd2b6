'use client';

import { useQuery } from '@tanstack/react-query';
import { CheckCircle, XCircle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import CategoryQuery from '@/services/queries/CategoryQuery';
import { CategoryDto } from '@/models/Category';
import GroupQuery from '@/services/queries/GroupQuery';

export const CategoryTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: CategoryQuery.tags,
    queryFn: CategoryQuery.getAll,
  });

  const { data: groups, isLoading: isLoadingGroups } = useQuery({
    queryKey: GroupQuery.tags,
    queryFn: GroupQuery.getBrief,
  });

  const columns = generateTableColumns<CategoryDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      group: { name: 'Group', type: 'text', sortable: true },
      displayOrder: { name: 'Display Order', type: 'text', sortable: true },
      isAvailable: {
        name: 'Available',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircle className="text-green-600 w-4 h-4 mr-1" />
                  <span className="text-green-600 hidden">Yes</span>
                </>
              ) : (
                <>
                  <XCircle className="text-red-600 w-4 h-4 mr-1" />
                  <span className="text-red-600 hidden">No</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link
              href={`/dashboard/setup/products-services/category/${row.id ?? 'add'}`}
            >
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
              ></Button>
            </Link>
          ),
        },
      },
    },
    false,
    false,
    { column: 'name', direction: 'asc' },
  );

  const filters = generateTableFilters<CategoryDto>({
    name: { name: 'Name', type: 'text' },
    code: { name: 'Code', type: 'text' },
    group: {
      name: 'Group',
      type: {
        type: 'select',
        options:
          isLoadingGroups || !groups
            ? []
            : (groups &&
                groups.map((o) => ({
                  label: o.name,
                  value: o.name,
                }))) ||
              [],
      },
    },
    isAvailable: {
      name: 'Available',
      type: {
        type: 'select',
        options: [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <Link href={`/dashboard/setup/products-services/category/add`}>
          <Button variant="primary" iconName="AddIcon">
            Add New Category
          </Button>
        </Link>
      }
    />
  );
};

export default CategoryTable;
