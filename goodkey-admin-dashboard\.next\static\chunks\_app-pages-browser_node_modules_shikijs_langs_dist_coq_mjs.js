"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_coq_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/coq.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/coq.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Coq\\\",\\\"fileTypes\\\":[\\\"v\\\"],\\\"name\\\":\\\"coq\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Vernacular import keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(From|Require|Import|Export|Local|Global|Include)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.coq\\\"},{\\\"comment\\\":\\\"Vernacular scope keywords\\\",\\\"match\\\":\\\"\\\\\\\\b((Open|Close|Delimit|Undelimit|Bind)\\\\\\\\s+Scope)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.coq\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.theorem.coq\\\"}},\\\"comment\\\":\\\"Theorem declarations\\\",\\\"match\\\":\\\"\\\\\\\\b(Theorem|Lemma|Remark|Fact|Corollary|Property|Proposition)\\\\\\\\s+((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"match\\\":\\\"\\\\\\\\bGoal\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.assumption.coq\\\"}},\\\"comment\\\":\\\"Assumptions\\\",\\\"match\\\":\\\"\\\\\\\\b(Parameters?|Axioms?|Conjectures?|Variables?|Hypothesis|Hypotheses)(\\\\\\\\s+Inline)?\\\\\\\\b\\\\\\\\s*\\\\\\\\(?\\\\\\\\s*((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.assumption.coq\\\"}},\\\"comment\\\":\\\"Context\\\",\\\"match\\\":\\\"\\\\\\\\b(Context)\\\\\\\\b\\\\\\\\s*`?\\\\\\\\s*(\\\\\\\\(|\\\\\\\\{)?\\\\\\\\s*((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.coq\\\"}},\\\"comment\\\":\\\"Definitions\\\",\\\"match\\\":\\\"(\\\\\\\\b(?:Program|Local)\\\\\\\\s+)?\\\\\\\\b(Definition|Fixpoint|CoFixpoint|Function|Example|Let(?:\\\\\\\\s+Fixpoint|\\\\\\\\s+CoFixpoint)?|Instance|Equations|Equations?)\\\\\\\\s+((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"}},\\\"comment\\\":\\\"Obligations\\\",\\\"match\\\":\\\"\\\\\\\\b((Show\\\\\\\\s+)?Obligation\\\\\\\\s+Tactic|Obligations\\\\\\\\s+of|Obligation|Next\\\\\\\\s+Obligation(\\\\\\\\s+of)?|Solve\\\\\\\\s+Obligations(\\\\\\\\s+of)?|Solve\\\\\\\\s+All\\\\\\\\s+Obligations|Admit\\\\\\\\s+Obligations(\\\\\\\\s+of)?|Instance)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.coq\\\"}},\\\"comment\\\":\\\"Type declarations\\\",\\\"match\\\":\\\"\\\\\\\\b(CoInductive|Inductive|Variant|Record|Structure|Class)\\\\\\\\s+(>\\\\\\\\s*)?((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.source.coq\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.ltac\\\"}},\\\"comment\\\":\\\"Ltac declarations\\\",\\\"match\\\":\\\"\\\\\\\\b(Ltac)\\\\\\\\s+((\\\\\\\\p{L}|[_\\\\\\\\u00A0])(\\\\\\\\p{L}|[0-9_\\\\\\\\u00A0'])*)\\\"},{\\\"comment\\\":\\\"Vernacular keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(Hint|Constructors|Resolve|Rewrite|Ltac|Implicit(\\\\\\\\s+Types)?|Set|Unset|Remove\\\\\\\\s+Printing|Arguments|Tactic\\\\\\\\s+Notation|Notation|Infix|Reserved\\\\\\\\s+Notation|Section|Module\\\\\\\\s+Type|Module|End|Check|Print|Eval|Search|Universe|Coercions?|Generalizable\\\\\\\\s+All|Generalizable\\\\\\\\s+Variable?|Existing\\\\\\\\s+Instance|Existing\\\\\\\\s+Class|Canonical|About|Locate|Collection|Typeclasses\\\\\\\\s+(Opaque|Transparent))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"comment\\\":\\\"Proof keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(Proof|Qed|Defined|Save|Abort(\\\\\\\\s+All)?|Undo(\\\\\\\\s+To)?|Restart|Focus|Unfocus|Unfocused|Show\\\\\\\\s+Proof|Show\\\\\\\\s+Existentials|Show|Unshelve)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.source.coq\\\"},{\\\"comment\\\":\\\"Vernacular Debug keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(Quit|Drop|Time|Redirect|Timeout|Fail)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.debug.coq\\\"},{\\\"comment\\\":\\\"Admits are bad\\\",\\\"match\\\":\\\"\\\\\\\\b(admit|Admitted)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.admit.coq\\\"},{\\\"comment\\\":\\\"Operators\\\",\\\"match\\\":\\\":|\\\\\\\\||=|<|>|\\\\\\\\*|\\\\\\\\+|-|\\\\\\\\{|\\\\\\\\}|≠|∨|∧|↔|¬|→|≤|≥\\\",\\\"name\\\":\\\"keyword.operator.coq\\\"},{\\\"comment\\\":\\\"Type keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(forall|exists|Type|Set|Prop|nat|bool|option|list|unit|sum|prod|comparison|Empty_set)\\\\\\\\b|∀|∃\\\",\\\"name\\\":\\\"support.type.coq\\\"},{\\\"comment\\\":\\\"Ltac keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(try|repeat|rew|progress|fresh|solve|now|first|tryif|at|once|do|only)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.ltac\\\"},{\\\"comment\\\":\\\"Common Ltac connectors\\\",\\\"match\\\":\\\"\\\\\\\\b(into|with|eqn|by|move|as|using)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.ltac\\\"},{\\\"comment\\\":\\\"Gallina keywords\\\",\\\"match\\\":\\\"\\\\\\\\b(match|lazymatch|multimatch|fun|with|return|end|let|in|if|then|else|fix|for|where|and)\\\\\\\\b|λ\\\",\\\"name\\\":\\\"keyword.control.gallina\\\"},{\\\"comment\\\":\\\"Ltac builtins\\\",\\\"match\\\":\\\"\\\\\\\\b(intro|intros|revert|induction|destruct|auto|eauto|tauto|eassumption|apply|eapply|assumption|constructor|econstructor|reflexivity|inversion|injection|assert|split|esplit|omega|fold|unfold|specialize|rewrite|erewrite|change|symmetry|refine|simpl|intuition|firstorder|generalize|idtac|exist|exists|eexists|elim|eelim|rename|subst|congruence|trivial|left|right|set|pose|discriminate|clear|clearbody|contradict|contradiction|exact|dependent|remember|case|easy|unshelve|pattern|transitivity|etransitivity|f_equal|exfalso|replace|abstract|cycle|swap|revgoals|shelve|unshelve)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.ltac\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*(?!#)\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.coq\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#block_double_quoted_string\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b((0(x|X)[0-9a-fA-F]+)|([0-9]+(\\\\\\\\.[0-9]+)?))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.gallina\\\"},{\\\"comment\\\":\\\"Gallina builtin constructors\\\",\\\"match\\\":\\\"\\\\\\\\b(True|False|tt|false|true|Some|None|nil|cons|pair|inl|inr|O|S|Eq|Lt|Gt|id|ex|all|unique)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.constructor.gallina\\\"},{\\\"match\\\":\\\"\\\\\\\\b_\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.wildcard.coq\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.coq\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.coq\\\"}},\\\"name\\\":\\\"string.quoted.double.coq\\\"}],\\\"repository\\\":{\\\"block_comment\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*(?!#)\\\",\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.coq\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#block_double_quoted_string\\\"}]},\\\"block_double_quoted_string\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.coq\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.coq\\\"}},\\\"name\\\":\\\"string.quoted.double.coq\\\"}},\\\"scopeName\\\":\\\"source.coq\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/coq.mjs\n"));

/***/ })

}]);