import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

import { Tokens } from '@/models/Auth';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ slug: string[]; locale: string }> },
) {
  const params = await props.params;

  const { slug } = params;

  const requestHeaders = new Headers(request.headers);
  const searchParams = Array.from(new URL(request.url).searchParams.entries())
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  // Fix: await the cookies() call
  const cookiesStore = await cookies();
  const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')
    ?.state as Tokens | undefined;
  const headers = {
    'content-type': requestHeaders.get('content-type'),
    Authorization: requestHeaders.get('Authorization'),
    'accept-language': 'en',
    ...(auth?.accessToken
      ? { Authorization: `Bearer ${auth.accessToken}` }
      : {}),
  };
  const fullUrl = `${process.env.API_BASE_URL}/storage/doc/${slug.join('/')}?${searchParams}`;
  const respond = await fetch(fullUrl, {
    ...request,
    headers: Object.fromEntries(
      Object.entries(headers).filter(
        ([_, value]) => value !== undefined && value !== '' && value !== null,
      ),
    ) as Record<string, string>,
  });
  return respond;
}
