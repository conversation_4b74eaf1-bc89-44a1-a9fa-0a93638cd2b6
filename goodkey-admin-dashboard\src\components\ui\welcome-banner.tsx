interface WelcomeBannerProps {
  userName: string;
  showInstructions?: boolean;
  showOnlyOnMainPage?: boolean;
  currentPath?: string;
}

export default function WelcomeBanner({
  userName,
  showInstructions = true,
  showOnlyOnMainPage = false,
  currentPath = '',
}: WelcomeBannerProps) {
  // If showOnlyOnMainPage is true and we're not on the main page, don't render anything
  if (showOnlyOnMainPage && currentPath !== '/' && currentPath !== '') {
    return null;
  }

  return (
    <div className="mb-6 py-2">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-semibold text-slate-800">
            Welcome {userName} to the administrative section of Goodkey.
          </h1>
          {showInstructions && (
            <p className="text-slate-600 mt-1">
              Please click on the name of the show you wish to access
            </p>
          )}
        </div>
        <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 via-red-500 to-blue-500 rounded"></div>
      </div>
    </div>
  );
}
