/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/doc/[...slug]/route";
exports.ids = ["app/doc/[...slug]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdoc%2F%5B...slug%5D%2Froute&page=%2Fdoc%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fdoc%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdoc%2F%5B...slug%5D%2Froute&page=%2Fdoc%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fdoc%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_developer2_source_repos_Project_goodkey_admin_dashboard_src_app_doc_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/doc/[...slug]/route.ts */ \"(rsc)/./src/app/doc/[...slug]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/doc/[...slug]/route\",\n        pathname: \"/doc/[...slug]\",\n        filename: \"route\",\n        bundlePath: \"app/doc/[...slug]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\app\\\\doc\\\\[...slug]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_developer2_source_repos_Project_goodkey_admin_dashboard_src_app_doc_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdoc%2F%5B...slug%5D%2Froute&page=%2Fdoc%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fdoc%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/doc/[...slug]/route.ts":
/*!****************************************!*\
  !*** ./src/app/doc/[...slug]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\nasync function GET(request, props) {\n    const params = await props.params;\n    const { slug } = params;\n    const requestHeaders = new Headers(request.headers);\n    const searchParams = Array.from(new URL(request.url).searchParams.entries()).map(([key, value])=>`${key}=${value}`).join('&');\n    // Fix: await the cookies() call\n    const cookiesStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')?.state;\n    const headers = {\n        'content-type': requestHeaders.get('content-type'),\n        Authorization: requestHeaders.get('Authorization'),\n        'accept-language': 'en',\n        ...auth?.accessToken ? {\n            Authorization: `Bearer ${auth.accessToken}`\n        } : {}\n    };\n    const fullUrl = `${process.env.API_BASE_URL}/storage/doc/${slug.join('/')}?${searchParams}`;\n    const respond = await fetch(fullUrl, {\n        ...request,\n        headers: Object.fromEntries(Object.entries(headers).filter(([_, value])=>value !== undefined && value !== '' && value !== null))\n    });\n    return respond;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/doc/[...slug]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdoc%2F%5B...slug%5D%2Froute&page=%2Fdoc%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fdoc%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();