import type React from 'react';
import SetupSidebar from '@/components/setup-sidebar';

interface SetupLayoutProps {
  children: React.ReactNode;
}

export default function SetupLayout({ children }: SetupLayoutProps) {
  return (
    <div className="w-full flex  py-8 px-4">
      <div className="flex flex-col md:flex-row gap-6 w-full">
        <SetupSidebar />
        <main className="flex-1 bg-white rounded-md border border-slate-200 min-h-[600px]">
          {children}
        </main>
      </div>
    </div>
  );
}
