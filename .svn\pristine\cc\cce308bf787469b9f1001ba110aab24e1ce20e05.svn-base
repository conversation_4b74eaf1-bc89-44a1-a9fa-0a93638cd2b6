﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportSessions
    {
        public ExhibitorImportSessions()
        {
            ExhibitorImportRows = new HashSet<ExhibitorImportRows>();
        }

        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int ShowId { get; set; }
        public string FileName { get; set; }
        public string OriginalFileName { get; set; }
        public string FilePath { get; set; }
        public long? FileSize { get; set; }
        public string MimeType { get; set; }
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public int WarningRows { get; set; }
        public string Status { get; set; }
        public bool CanProceed { get; set; }
        public string ValidationSummary { get; set; }
        public DateTime? ProcessingStartedAt { get; set; }
        public DateTime? ProcessingCompletedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public int CreatedById { get; set; }
        public DateTime? ExecutedAt { get; set; }
        public int? ExecutedById { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual AuthUser ExecutedBy { get; set; }
        public virtual Shows Show { get; set; }
        public virtual ICollection<ExhibitorImportRows> ExhibitorImportRows { get; set; }
    }
}