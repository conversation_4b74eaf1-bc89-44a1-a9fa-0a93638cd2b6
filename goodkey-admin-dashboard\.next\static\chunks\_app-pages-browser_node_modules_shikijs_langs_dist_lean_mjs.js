"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_lean_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/lean.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/lean.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Lean 4\\\",\\\"fileTypes\\\":[],\\\"name\\\":\\\"lean\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Prop|Type|Sort)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\battribute\\\\\\\\b\\\\\\\\s*\\\\\\\\[[^\\\\\\\\]]*\\\\\\\\]\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"@\\\\\\\\[[^\\\\\\\\]]*\\\\\\\\]\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(global|local|scoped|partial|unsafe|private|protected|noncomputable)(?!\\\\\\\\.)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sorry|admit|stop)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.lean4\\\"},{\\\"match\\\":\\\"#(print|eval|reduce|check|check_failure)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\bderiving\\\\\\\\s+instance\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.command.lean4\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(inductive|coinductive|structure|theorem|axiom|abbrev|lemma|def|instance|class|constant)\\\\\\\\b\\\\\\\\s+(\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.definitioncommand.lean4\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\bwith\\\\\\\\b|\\\\\\\\bextends\\\\\\\\b|\\\\\\\\bwhere\\\\\\\\b|[:\\\\\\\\|\\\\\\\\(\\\\\\\\[\\\\\\\\{⦃<>])\\\",\\\"name\\\":\\\"meta.definitioncommand.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#definitionName\\\"},{\\\"match\\\":\\\",\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(theorem|show|have|from|suffices|nomatch|def|class|structure|instance|set_option|initialize|builtin_initialize|example|inductive|coinductive|axiom|constant|universe|universes|variable|variables|import|open|export|theory|prelude|renaming|hiding|exposing|do|by|let|extends|mutual|mut|where|rec|syntax|macro_rules|macro|deriving|fun|section|namespace|end|infix|infixl|infixr|postfix|prefix|notation|abbrev|if|then|else|calc|match|with|for|in|unless|try|catch|finally|return|continue|break)(?!\\\\\\\\.)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.lean4\\\"},{\\\"begin\\\":\\\"«\\\",\\\"contentName\\\":\\\"entity.name.lean4\\\",\\\"end\\\":\\\"»\\\"},{\\\"begin\\\":\\\"(s!)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.interpolated.lean4\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.lean4\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"ntr']\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x[0-9A-Fa-f][0-9A-Fa-f]\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.lean4\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"ntr']\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x[0-9A-Fa-f][0-9A-Fa-f]\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]\\\",\\\"name\\\":\\\"constant.character.escape.lean4\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.lean4\\\"},{\\\"match\\\":\\\"'[^\\\\\\\\\\\\\\\\']'\\\",\\\"name\\\":\\\"string.quoted.single.lean4\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.lean4\\\"}},\\\"match\\\":\\\"'(\\\\\\\\\\\\\\\\(x[0-9A-Fa-f][0-9A-Fa-f]|u[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]|.))'\\\",\\\"name\\\":\\\"string.quoted.single.lean4\\\"},{\\\"match\\\":\\\"`+[^\\\\\\\\[(]\\\\\\\\S+\\\",\\\"name\\\":\\\"entity.name.lean4\\\"},{\\\"match\\\":\\\"\\\\\\\\b([0-9]+|0([xX][0-9a-fA-F]+)|[-]?(0|[1-9][0-9]*)(\\\\\\\\.[0-9]+)?([eE][+-]?[0-9]+)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.lean4\\\"}],\\\"repository\\\":{\\\"blockComment\\\":{\\\"begin\\\":\\\"/-\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#dashComment\\\"},{\\\"include\\\":\\\"#docComment\\\"},{\\\"include\\\":\\\"#stringBlock\\\"},{\\\"include\\\":\\\"#modDocComment\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"dashComment\\\":{\\\"begin\\\":\\\"--\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-dash.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"}]},\\\"definitionName\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[^:«»\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}[:space:]=→λ∀?][^:«»\\\\\\\\(\\\\\\\\)\\\\\\\\{\\\\\\\\}[:space:]]*\\\",\\\"name\\\":\\\"entity.name.function.lean4\\\"},{\\\"begin\\\":\\\"«\\\",\\\"contentName\\\":\\\"entity.name.function.lean4\\\",\\\"end\\\":\\\"»\\\"}]},\\\"docComment\\\":{\\\"begin\\\":\\\"/--\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.documentation.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]},\\\"modDocComment\\\":{\\\"begin\\\":\\\"/-!\\\",\\\"end\\\":\\\"-/\\\",\\\"name\\\":\\\"comment.block.documentation.lean4\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.lean4.markdown\\\"},{\\\"include\\\":\\\"#blockComment\\\"}]}},\\\"scopeName\\\":\\\"source.lean4\\\",\\\"aliases\\\":[\\\"lean4\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/lean.mjs\n"));

/***/ })

}]);