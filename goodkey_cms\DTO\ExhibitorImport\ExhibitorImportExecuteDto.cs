namespace goodkey_cms.DTO.ExhibitorImport
{
    public class ExhibitorImportExecuteDto
    {
        public string SessionId { get; set; }
        public bool SendEmailInvites { get; set; } = false;
    }

    public class ExhibitorImportExecutionResponseDto
    {
        public string SessionId { get; set; }
        public string Status { get; set; }
        public ExhibitorImportExecutionSummaryDto Summary { get; set; }
        public List<ExhibitorImportExecutionResultDto> Results { get; set; }
        public DateTime CompletedAt { get; set; }
    }

    public class ExhibitorImportExecutionSummaryDto
    {
        public int TotalRows { get; set; }
        public int ProcessedRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int FailedRows { get; set; }
        public int CompaniesCreated { get; set; }
        public int CompaniesUpdated { get; set; }
        public int ContactsCreated { get; set; }
        public int ContactsUpdated { get; set; }
        public int ExhibitorsCreated { get; set; }
        public int UsersCreated { get; set; }
        public int EmailsSent { get; set; }
    }

    public class ExhibitorImportExecutionResultDto
    {
        public int RowNumber { get; set; }
        public string Status { get; set; } // Processed, Skipped, Failed
        public string CompanyName { get; set; }
        public string ContactName { get; set; }
        public string ContactEmail { get; set; }

        // What was created/updated
        public int? CreatedCompanyId { get; set; }
        public int? UpdatedCompanyId { get; set; }
        public int? CreatedContactId { get; set; }
        public int? UpdatedContactId { get; set; }
        public int? CreatedExhibitorId { get; set; }
        public int? CreatedUserId { get; set; }

        // Actions taken
        public string CompanyAction { get; set; } // Created, Updated, UsedExisting
        public string ContactAction { get; set; } // Created, Updated, UsedExisting
        public string ExhibitorAction { get; set; } // Created
        public string UserAction { get; set; } // Created, UsedExisting, Skipped

        // Error handling
        public string ErrorMessage { get; set; }
        public string SkipReason { get; set; }
        public bool EmailInviteSent { get; set; }

        public DateTime ProcessedAt { get; set; }
    }
}
