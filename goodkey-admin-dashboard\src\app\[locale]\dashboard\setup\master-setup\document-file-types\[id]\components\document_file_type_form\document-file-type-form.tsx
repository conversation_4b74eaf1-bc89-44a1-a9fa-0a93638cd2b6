'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';

import {
  DocumentFileTypeCreateSchema,
  DocumentFileTypeUpdateSchema,
  DocumentFileTypeCreateData,
  DocumentFileTypeUpdateData,
} from '@/schema/DocumentFileTypeSchema';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { DocumentFileTypeDetail } from '@/models/DocumentFileType';

interface DocumentFileTypeFormProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: DocumentFileTypeCreateData | DocumentFileTypeUpdateData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();
  const isEdit = !!id;

  const form = useForm<any>({
    resolver: zodResolver(
      isEdit ? DocumentFileTypeUpdateSchema : DocumentFileTypeCreateSchema,
    ),
    defaultValues: defaultValues ?? {
      name: '',
      extensionCode: '',
      extension: '',
      isImage: false,
      ...(isEdit && { isAvailable: true }),
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (
      data: DocumentFileTypeCreateData | DocumentFileTypeUpdateData,
    ) => {
      if (isEdit) {
        await DocumentFileTypeQuery.update(id!)(
          data as DocumentFileTypeUpdateData,
        );
      } else {
        await DocumentFileTypeQuery.create(data as DocumentFileTypeCreateData);
      }
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: isEdit
          ? 'Document file type updated successfully.'
          : 'Document file type created successfully.',
        variant: 'success',
      });
      push('/dashboard/setup/master-setup/document-file-types');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Something went wrong.',
        variant: 'destructive',
      });
    },
  });

  const extensionCodeValue = form.watch('extensionCode');

  useEffect(() => {
    if (extensionCodeValue) {
      const upperValue = extensionCodeValue.toUpperCase();
      form.setValue('extensionCode', upperValue);
      form.setValue('extension', upperValue.toLowerCase());
    }
  }, [extensionCodeValue, form]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
          <Field
            control={form.control}
            name="name"
            label="Name"
            placeholder="Enter file type name"
            type="text"
            required
          />
          <Field
            control={form.control}
            name="extensionCode"
            label="Extension Code"
            placeholder="Enter 3-letter code (e.g., PDF)"
            type="text"
            required
            maxLength={3}
          />
          <Field
            control={form.control}
            name="extension"
            label="Extension"
            placeholder="File extension"
            type="text"
            required
            disabled
          />
          <Field
            control={form.control}
            name="isImage"
            label="Is Image Type"
            type="checkbox"
          />
          {isEdit && (
            <Field
              control={form.control}
              name="isAvailable"
              label="Is Available"
              type="checkbox"
            />
          )}
        </div>

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              push('/dashboard/setup/master-setup/document-file-types')
            }
          >
            Cancel
          </Button>
          <Button
            variant={'main'}
            disabled={isPending}
            iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
            iconProps={{ className: isPending ? 'animate-spin' : '' }}
            type="submit"
          >
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function DocumentFileTypeForm({
  id,
}: DocumentFileTypeFormProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...DocumentFileTypeQuery.tags, { id }],
    queryFn: () => DocumentFileTypeQuery.getOne(id!),
    enabled: !!id,
    select: (res: DocumentFileTypeDetail) =>
      ({
        name: res.name,
        extensionCode: res.extensionCode,
        extension: res.extension,
        isImage: res.isImage,
        isAvailable: res.isAvailable,
      }) as DocumentFileTypeUpdateData,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}
