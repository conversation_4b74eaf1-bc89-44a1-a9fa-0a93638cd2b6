import { useSetupSidebarStore } from '@/stores/setup-sidebar-store';

/**
 * Hook to control the setup sidebar state from any component
 * Provides simple functions to collapse, expand, or toggle the sidebar
 */
export function useSetupSidebarControl() {
  const { collapsed, setCollapsed, toggleCollapsed, resetUserToggled } =
    useSetupSidebarStore();

  return {
    collapsed,
    collapse: () => {
      setCollapsed(true);
      resetUserToggled();
    },
    expand: () => {
      setCollapsed(false);
      resetUserToggled();
    },
    toggle: toggleCollapsed,
    setCollapsed,
  };
}
