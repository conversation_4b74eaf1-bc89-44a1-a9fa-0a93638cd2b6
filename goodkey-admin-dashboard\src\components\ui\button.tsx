import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import * as ImportedIcons from '@/assets/Icons';
import { IconProps } from '@/assets/Icons';

const Icons: Record<
  keyof typeof ImportedIcons,
  React.ComponentType<IconProps>
> = ImportedIcons;

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  iconName?: keyof typeof Icons;
  iconProps?: IconProps;
  iconPosition?: 'left' | 'right';
}

export type IconName = keyof typeof ImportedIcons;

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none ',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        remove:
          'bg-secondary text-destructive hover:text-destructive-foreground border hover:border-destructive hover:text-destructive-foreground',
        outline:
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary:
          'bg-secondary text-secondary-foreground  border hover:border-main hover:bg-secondary/90',
        primary: 'bg-main gap-2 text-white hover:bg-main/90 ',
        main: 'bg-main gap-2 text-white hover:bg-main/90',
        success: 'bg-success text-success-foreground hover:bg-success/90',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        text: 'text-foreground shadow-none',
        info: 'bg-blue-500 text-white hover:bg-blue-500/90',
        transparent:
          'text-foreground bg-transparent border-transparent shadow-none ',
        blackWhite: 'bg-black text-white hover:bg-black/90 border border-black',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-9 rounded-md p-2',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      iconName,
      iconProps,
      iconPosition = 'left',
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';
    const IconComponent = iconName ? Icons[iconName] : null;

    const content = (
      <>
        {iconPosition === 'left' && IconComponent && (
          <IconComponent {...iconProps} />
        )}
        {props.children}
        {iconPosition === 'right' && IconComponent && (
          <IconComponent {...iconProps} />
        )}
      </>
    );

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {content}
      </Comp>
    );
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
