"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_applescript_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/applescript.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/applescript.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"AppleScript\\\",\\\"fileTypes\\\":[\\\"applescript\\\",\\\"scpt\\\",\\\"script editor\\\"],\\\"firstLineMatch\\\":\\\"^#!.*(osascript)\\\",\\\"name\\\":\\\"applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#blocks\\\"},{\\\"include\\\":\\\"#inline\\\"}],\\\"repository\\\":{\\\"attributes.considering-ignoring\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.attributes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(and)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.attributes.and.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:case|diacriticals|hyphens|numeric\\\\\\\\s+strings|punctuation|white\\\\\\\\s+space)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.attributes.text.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:application\\\\\\\\s+responses)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.attributes.application.applescript\\\"}]},\\\"blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(script)\\\\\\\\s+(\\\\\\\\w+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.script.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.script-object.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+script)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.script.applescript\\\"}},\\\"name\\\":\\\"meta.block.script.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(to|on)\\\\\\\\s+(\\\\\\\\w+)(\\\\\\\\()((?:[\\\\\\\\s,:\\\\\\\\{\\\\\\\\}]*(?:\\\\\\\\w+)?)*)(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.handler.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.applescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.handler.applescript\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.applescript\\\"}},\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tThis is not a very well-designed rule.  For now,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\twe can leave it like this though, as it sorta works.\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end)(?:\\\\\\\\s+(\\\\\\\\2))?(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"}},\\\"name\\\":\\\"meta.function.positional.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(to|on)\\\\\\\\s+(\\\\\\\\w+)(?:\\\\\\\\s+(of|in)\\\\\\\\s+(\\\\\\\\w+))?(?=\\\\\\\\s+(above|against|apart\\\\\\\\s+from|around|aside\\\\\\\\s+from|at|below|beneath|beside|between|by|for|from|instead\\\\\\\\s+of|into|on|onto|out\\\\\\\\s+of|over|thru|under)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.handler.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.handler.direct.applescript\\\"}},\\\"comment\\\":\\\"TODO: match `given` parameters\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end)(?:\\\\\\\\s+(\\\\\\\\2))?(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"}},\\\"name\\\":\\\"meta.function.prepositional.applescript\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preposition.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.handler.applescript\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?i:above|against|apart\\\\\\\\s+from|around|aside\\\\\\\\s+from|at|below|beneath|beside|between|by|for|from|instead\\\\\\\\s+of|into|on|onto|out\\\\\\\\s+of|over|thru|under)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\b\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(to|on)\\\\\\\\s+(\\\\\\\\w+)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.handler.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end)(?:\\\\\\\\s+(\\\\\\\\2))?(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.function.applescript\\\"}},\\\"name\\\":\\\"meta.function.parameterless.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#blocks.tell\\\"},{\\\"include\\\":\\\"#blocks.repeat\\\"},{\\\"include\\\":\\\"#blocks.statement\\\"},{\\\"include\\\":\\\"#blocks.other\\\"}]},\\\"blocks.other\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(considering)\\\\\\\\b\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+considering)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.considering.applescript\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=considering)\\\",\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.array.attributes.considering.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes.considering-ignoring\\\"}]},{\\\"begin\\\":\\\"(?<=ignoring)\\\",\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.array.attributes.ignoring.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes.considering-ignoring\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(but)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.but.applescript\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(ignoring)\\\\\\\\b\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+ignoring)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.ignoring.applescript\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=considering)\\\",\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.array.attributes.considering.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes.considering-ignoring\\\"}]},{\\\"begin\\\":\\\"(?<=ignoring)\\\",\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.array.attributes.ignoring.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes.considering-ignoring\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(but)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.but.applescript\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+if)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.if.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(then)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.then.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(else\\\\\\\\s+if)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.else-if.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(else)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.else.applescript\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(try)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.try.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+(try|error))?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.try.applescript\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(on\\\\\\\\s+error)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.exception.on-error.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.property.error.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?i:number|partial|from|to)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.exception.modifier.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(using\\\\\\\\s+terms\\\\\\\\s+from)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.terms.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+using\\\\\\\\s+terms\\\\\\\\s+from)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.terms.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(with\\\\\\\\s+timeout(\\\\\\\\s+of)?)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.timeout.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+timeout)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.timeout.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(with\\\\\\\\s+transaction(\\\\\\\\s+of)?)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.transaction.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+transaction)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.transaction.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"blocks.repeat\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(repeat)\\\\\\\\s+(until)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.repeat.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.until.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+repeat)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.repeat.until.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(repeat)\\\\\\\\s+(while)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.repeat.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.while.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+repeat)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.repeat.while.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(repeat)\\\\\\\\s+(with)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.repeat.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.until.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.loop.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+repeat)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.repeat.with.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(from|to|by)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.modifier.range.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(in)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.modifier.list.applescript\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(repeat)\\\\\\\\b(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.repeat.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+repeat)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.repeat.forever.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(repeat)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.repeat.applescript\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+repeat)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.applescript\\\"}},\\\"name\\\":\\\"meta.block.repeat.times.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(times)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.times.applescript\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"blocks.statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(prop(?:erty)?)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.property.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.property.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.property.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.property.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(set)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s+(to)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.set.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.readwrite.set.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.def.set.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.set.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(local)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.local.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.local.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.variables.local.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.readwrite.local.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(global)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.def.global.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.global.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.variables.global.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.other.readwrite.global.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(error)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.exception.error.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.error.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(number|partial|from|to)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.exception.modifier.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(if)\\\\\\\\b(?=.*\\\\\\\\bthen\\\\\\\\b(?!\\\\\\\\s*(--.*?)?$))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.applescript\\\"}},\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.statement.if-then.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inline\\\"}]}]},\\\"blocks.tell\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\s+\\\\\\\"(?i:textmate)\\\\\\\")(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell Textmate\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application.textmate.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#textmate\\\"},{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\s+\\\\\\\"(?i:finder)\\\\\\\")(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell Finder\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application.finder.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#finder\\\"},{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\s+\\\\\\\"(?i:system events)\\\\\\\")(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell System Events\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application.system-events.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#system-events\\\"},{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\s+\\\\\\\"(?i:itunes)\\\\\\\")(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell iTunes\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application.itunes.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#itunes\\\"},{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\s+process\\\\\\\\b)(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell generic application process\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application-process.generic.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=app(lication)?\\\\\\\\b)(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell generic application\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.application.generic.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#standard-suite\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?!.*\\\\\\\\bto(?!\\\\\\\\s+tell)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"generic tell block\\\",\\\"end\\\":\\\"^\\\\\\\\s*(end(?:\\\\\\\\s+tell)?)(?=\\\\\\\\s*(--.*?)?$)\\\",\\\"name\\\":\\\"meta.block.tell.generic.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(tell)\\\\\\\\s+(?=.*\\\\\\\\bto\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tell.applescript\\\"}},\\\"comment\\\":\\\"tell … to statement\\\",\\\"end\\\":\\\"(?<!¬)$\\\",\\\"name\\\":\\\"meta.block.tell.generic.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"built-in\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#built-in.constant\\\"},{\\\"include\\\":\\\"#built-in.keyword\\\"},{\\\"include\\\":\\\"#built-in.support\\\"},{\\\"include\\\":\\\"#built-in.punctuation\\\"}]},\\\"built-in.constant\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"yes/no can’t always be used as booleans, e.g. in an if() expression. But they work e.g. for boolean arguments.\\\",\\\"match\\\":\\\"\\\\\\\\b(?i:true|false|yes|no)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:null|missing\\\\\\\\s+value)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.null.applescript\\\"},{\\\"match\\\":\\\"-?\\\\\\\\b\\\\\\\\d+((\\\\\\\\.(\\\\\\\\d+\\\\\\\\b)?)?(?i:e\\\\\\\\+?\\\\\\\\d*\\\\\\\\b)?|\\\\\\\\b)\\\",\\\"name\\\":\\\"constant.numeric.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:space|tab|return|linefeed|quote)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.text.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:all\\\\\\\\s+(caps|lowercase)|bold|condensed|expanded|hidden|italic|outline|plain|shadow|small\\\\\\\\s+caps|strikethrough|(sub|super)script|underline)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.styles.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Jan(uary)?|Feb(ruary)?|Mar(ch)?|Apr(il)?|May|Jun(e)?|Jul(y)?|Aug(ust)?|Sep(tember)?|Oct(ober)?|Nov(ember)?|Dec(ember)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.time.month.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:Mon(day)?|Tue(sday)?|Wed(nesday)?|Thu(rsday)?|Fri(day)?|Sat(urday)?|Sun(day)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.time.weekday.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:AppleScript|pi|result|version|current\\\\\\\\s+application|its?|m[ey])\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.miscellaneous.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:text\\\\\\\\s+item\\\\\\\\s+delimiters|print\\\\\\\\s+(length|depth))\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.applescript\\\"}]},\\\"built-in.keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(&|\\\\\\\\*|\\\\\\\\+|-|/|÷|\\\\\\\\^)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.applescript\\\"},{\\\"match\\\":\\\"(=|≠|>|<|≥|>=|≤|<=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.applescript\\\"},{\\\"match\\\":\\\"(?ix)\\\\\\\\b\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(and|or|div|mod|as|not\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|(a\\\\\\\\s+)?(ref(\\\\\\\\s+to)?|reference\\\\\\\\s+to)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|equal(s|\\\\\\\\s+to)|contains?|comes\\\\\\\\s+(after|before)|(start|begin|end)s?\\\\\\\\s+with\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.word.applescript\\\"},{\\\"comment\\\":\\\"In double quotes so we can use a single quote in the keywords.\\\",\\\"match\\\":\\\"(?ix)\\\\\\\\b\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(is(n't|\\\\\\\\s+not)?(\\\\\\\\s+(equal(\\\\\\\\s+to)?|(less|greater)\\\\\\\\s+than(\\\\\\\\s+or\\\\\\\\s+equal(\\\\\\\\s+to)?)?|in|contained\\\\\\\\s+by))?\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|does(n't|\\\\\\\\s+not)\\\\\\\\s+(equal|come\\\\\\\\s+(before|after)|contain)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.word.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:some|every|whose|where|that|id|index|\\\\\\\\d+(st|nd|rd|th)|first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth|last|front|back|middle|named|beginning|end|from|to|thr(u|ough)|before|(front|back|beginning|end)\\\\\\\\s+of|after|behind|in\\\\\\\\s+(front|back|beginning|end)\\\\\\\\s+of)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.reference.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:continue|return|exit(\\\\\\\\s+repeat)?)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.loop.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:about|above|after|against|and|apart\\\\\\\\s+from|around|as|aside\\\\\\\\s+from|at|back|before|beginning|behind|below|beneath|beside|between|but|by|considering|contain|contains|contains|copy|div|does|eighth|else|end|equal|equals|error|every|false|fifth|first|for|fourth|from|front|get|given|global|if|ignoring|in|instead\\\\\\\\s+of|into|is|it|its|last|local|me|middle|mod|my|ninth|not|of|on|onto|or|out\\\\\\\\s+of|over|prop|property|put|ref|reference|repeat|returning|script|second|set|seventh|since|sixth|some|tell|tenth|that|the|then|third|through|thru|timeout|times|to|transaction|true|try|until|where|while|whose|with|without)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.applescript\\\"}]},\\\"built-in.punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"¬\\\",\\\"name\\\":\\\"punctuation.separator.continuation.line.applescript\\\"},{\\\"comment\\\":\\\"the : in property assignments\\\",\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.property.applescript\\\"},{\\\"comment\\\":\\\"the parentheses in groups\\\",\\\"match\\\":\\\"[()]\\\",\\\"name\\\":\\\"punctuation.section.group.applescript\\\"}]},\\\"built-in.support\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?i:POSIX\\\\\\\\s+path|frontmost|id|name|running|version|days?|weekdays?|months?|years?|time|date\\\\\\\\s+string|time\\\\\\\\s+string|length|rest|reverse|items?|contents|quoted\\\\\\\\s+form|characters?|paragraphs?|words?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.built-in.property.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:activate|log|clipboard\\\\\\\\s+info|set\\\\\\\\s+the\\\\\\\\s+clipboard\\\\\\\\s+to|the\\\\\\\\s+clipboard|info\\\\\\\\s+for|list\\\\\\\\s+(disks|folder)|mount\\\\\\\\s+volume|path\\\\\\\\s+to(\\\\\\\\s+resource)?|close\\\\\\\\s+access|get\\\\\\\\s+eof|open\\\\\\\\s+for\\\\\\\\s+access|read|set\\\\\\\\s+eof|write|open\\\\\\\\s+location|current\\\\\\\\s+date|do\\\\\\\\s+shell\\\\\\\\s+script|get\\\\\\\\s+volume\\\\\\\\s+settings|random\\\\\\\\s+number|round|set\\\\\\\\s+volume|system\\\\\\\\s+(attribute|info)|time\\\\\\\\s+to\\\\\\\\s+GMT|load\\\\\\\\s+script|run\\\\\\\\s+script|scripting\\\\\\\\s+components|store\\\\\\\\s+script|copy|count|get|launch|run|set|ASCII\\\\\\\\s+(character|number)|localized\\\\\\\\s+string|offset|summarize|beep|choose\\\\\\\\s+(application|color|file(\\\\\\\\s+name)?|folder|from\\\\\\\\s+list|remote\\\\\\\\s+application|URL)|delay|display\\\\\\\\s+(alert|dialog)|say)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.built-in.command.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:get|run)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.built-in.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:anything|data|text|upper\\\\\\\\s+case|propert(y|ies))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.built-in.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:alias|class)(es)?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.built-in.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:app(lication)?|boolean|character|constant|date|event|file(\\\\\\\\s+specification)?|handler|integer|item|keystroke|linked\\\\\\\\s+list|list|machine|number|picture|preposition|POSIX\\\\\\\\s+file|real|record|reference(\\\\\\\\s+form)?|RGB\\\\\\\\s+color|script|sound|text\\\\\\\\s+item|type\\\\\\\\s+class|vector|writing\\\\\\\\s+code(\\\\\\\\s+info)?|zone|((international|styled(\\\\\\\\s+(Clipboard|Unicode))?|Unicode)\\\\\\\\s+)?text|((C|encoded|Pascal)\\\\\\\\s+)?string)s?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.built-in.applescript\\\"},{\\\"match\\\":\\\"(?ix)\\\\\\\\b\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(\\\\t(cubic\\\\\\\\s+(centi)?|square\\\\\\\\s+(kilo)?|centi|kilo)met(er|re)s\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|\\\\tsquare\\\\\\\\s+(yards|feet|miles)|cubic\\\\\\\\s+(yards|feet|inches)|miles|inches\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|\\\\tlit(re|er)s|gallons|quarts\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|\\\\t(kilo)?grams|ounces|pounds\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t|\\\\tdegrees\\\\\\\\s+(Celsius|Fahrenheit|Kelvin)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.built-in.unit.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:seconds|minutes|hours|days)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.built-in.time.applescript\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#!)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.applescript\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.applescript\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.applescript\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.applescript\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.applescript\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.applescript\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.applescript\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.applescript\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.applescript\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments.nested\\\"}]}]},\\\"comments.nested\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.applescript\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.applescript\\\"}},\\\"name\\\":\\\"comment.block.applescript\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments.nested\\\"}]}]},\\\"data-structures\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.applescript\\\"}},\\\"comment\\\":\\\"We cannot necessarily distinguish \\\\\\\"records\\\\\\\" from \\\\\\\"arrays\\\\\\\", and so this could be either.\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.applescript\\\"}},\\\"name\\\":\\\"meta.array.applescript\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.key.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.identifier.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.identifier.applescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.identifier.applescript\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.applescript\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+|((\\\\\\\\|)[^|\\\\\\\\n]*(\\\\\\\\|)))\\\\\\\\s*(:)\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.applescript\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.applescript\\\"},{\\\"include\\\":\\\"#inline\\\"}]},{\\\"begin\\\":\\\"(?:(?<=application )|(?<=app ))(\\\\\\\")\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.applescript\\\"}},\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.application-name.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.applescript\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.applescript\\\"}},\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.applescript\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.applescript\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.identifier.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.identifier.applescript\\\"}},\\\"match\\\":\\\"(\\\\\\\\|)[^|\\\\\\\\n]*(\\\\\\\\|)\\\",\\\"name\\\":\\\"meta.identifier.applescript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.class.built-in.applescript\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.utxt.applescript\\\"},\\\"4\\\":{\\\"name\\\":\\\"string.unquoted.data.applescript\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.applescript\\\"},\\\"7\\\":{\\\"name\\\":\\\"support.class.built-in.applescript\\\"}},\\\"match\\\":\\\"(«)(data) (utxt|utf8)([[:xdigit:]]*)(»)(?:\\\\\\\\s+(as)\\\\\\\\s+(?i:Unicode\\\\\\\\s+text))?\\\",\\\"name\\\":\\\"constant.other.data.utxt.applescript\\\"},{\\\"begin\\\":\\\"(«)(\\\\\\\\w+)\\\\\\\\b(?=\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.class.built-in.applescript\\\"}},\\\"end\\\":\\\"(»)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"}},\\\"name\\\":\\\"constant.other.data.raw.applescript\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.data.applescript\\\"}},\\\"match\\\":\\\"(«)[^»]*(»)\\\",\\\"name\\\":\\\"invalid.illegal.data.applescript\\\"}]},\\\"finder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(item|container|(computer|disk|trash)-object|disk|folder|((alias|application|document|internet location) )?file|clipping|package)s?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.finder.items.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b((Finder|desktop|information|preferences|clipping) )windows?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.finder.window-classes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(preferences|(icon|column|list) view options|(label|column|alias list)s?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.finder.type-definitions.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(copy|find|sort|clean up|eject|empty( trash)|erase|reveal|update)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.finder.items.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(insertion location|product version|startup disk|desktop|trash|home|computer container|finder preferences)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.finder.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(visible)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.finder.applescript\\\"}]},\\\"inline\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#data-structures\\\"},{\\\"include\\\":\\\"#built-in\\\"},{\\\"include\\\":\\\"#standardadditions\\\"}]},\\\"itunes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(artwork|application|encoder|EQ preset|item|source|visual|(EQ |browser )?window|((audio CD|device|shared|URL|file) )?track|playlist window|((audio CD|device|radio tuner|library|folder|user) )?playlist)s?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.itunes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(add|back track|convert|fast forward|(next|previous) track|pause|play(pause)?|refresh|resume|rewind|search|stop|update|eject|subscribe|update(Podcast|AllPodcasts)|download)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.itunes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(current (playlist|stream (title|URL)|track)|player state)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.itunes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(current (encoder|EQ preset|visual)|EQ enabled|fixed indexing|full screen|mute|player position|sound volume|visuals enabled|visual size)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.itunes.applescript\\\"}]},\\\"standard-suite\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(colors?|documents?|items?|windows?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.standard-suite.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(close|count|delete|duplicate|exists|make|move|open|print|quit|save|activate|select|data size)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standard-suite.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(name|frontmost|version)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.standard-suite.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(selection)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.standard-suite.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(attachments?|attribute runs?|characters?|paragraphs?|texts?|words?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.text-suite.applescript\\\"}]},\\\"standardadditions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((alert|dialog) reply)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.standardadditions.user-interaction.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(file information)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.standardadditions.file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(POSIX files?|system information|volume settings)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.standardadditions.miscellaneous.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(URLs?|internet address(es)?|web pages?|FTP items?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.standardadditions.internet.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(info for|list (disks|folder)|mount volume|path to( resource)?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(beep|choose (application|color|file( name)?|folder|from list|remote application|URL)|delay|display (alert|dialog)|say)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.user-interaction.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ASCII (character|number)|localized string|offset|summarize)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.string.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(set the clipboard to|the clipboard|clipboard info)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.clipboard.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(open for access|close access|read|write|get eof|set eof)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.file-i-o.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b((load|store|run) script|scripting components)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.scripting.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(current date|do shell script|get volume settings|random number|round|set volume|system attribute|system info|time to GMT)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.miscellaneous.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(opening folder|(closing|moving) folder window for|adding folder items to|removing folder items from)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.folder-actions.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(open location|handle CGI request)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.standardadditions.internet.applescript\\\"}]},\\\"system-events\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(audio (data|file))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.audio-file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(alias(es)?|(Classic|local|network|system|user) domain objects?|disk( item)?s?|domains?|file( package)?s?|folders?|items?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.disk-folder-file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(delete|open|move)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system-events.disk-folder-file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(folder actions?|scripts?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.folder-actions.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(attach action to|attached scripts|edit action of|remove action from)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system-events.folder-actions.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(movie data|movie file)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.movie-file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(log out|restart|shut down|sleep)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system-events.power.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(((application |desk accessory )?process|(check|combo )?box)(es)?|(action|attribute|browser|(busy|progress|relevance) indicator|color well|column|drawer|group|grow area|image|incrementor|list|menu( bar)?( item)?|(menu |pop up |radio )?button|outline|(radio|tab|splitter) group|row|scroll (area|bar)|sheet|slider|splitter|static text|table|text (area|field)|tool bar|UI element|window)s?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.processes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(click|key code|keystroke|perform|select)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system-events.processes.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(property list (file|item))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.property-list.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(annotation|QuickTime (data|file)|track)s?\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.quicktime-file.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b((abort|begin|end) transaction)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system-events.system-events.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(XML (attribute|data|element|file)s?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.system-events.xml.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(print settings|users?|login items?)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.sytem-events.other.applescript\\\"}]},\\\"textmate\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(print settings)\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.textmate.applescript\\\"},{\\\"match\\\":\\\"\\\\\\\\b(get url|insert|reload bundles)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.textmate.applescript\\\"}]}},\\\"scopeName\\\":\\\"source.applescript\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/applescript.mjs\n"));

/***/ })

}]);