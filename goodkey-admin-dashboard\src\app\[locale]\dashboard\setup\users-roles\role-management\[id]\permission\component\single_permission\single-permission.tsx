import { Switch } from '@/components/ui/switch';
import { Permission } from '@/models/Permission';

interface ISinglePermission {
  permission: Permission;
  isActivated: boolean;
  onChange?: (isChecked: boolean) => void;
}

function SinglePermission({
  permission,
  isActivated,
  onChange,
}: ISinglePermission) {
  return (
    <li className="flex flex-col py-2 border-b border-border gap-2">
      <div className="flex flex-row justify-between">
        <p className="font-bold">{permission.name}</p>
        <Switch checked={isActivated} onCheckedChange={onChange} />
      </div>
      <p className="text-gray-400">{permission.description}</p>
    </li>
  );
}

export default SinglePermission;
