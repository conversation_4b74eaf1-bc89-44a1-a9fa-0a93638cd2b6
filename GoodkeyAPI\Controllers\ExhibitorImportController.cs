using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Infrastructure.Utils;
using goodkey_common.Repositories;
using goodkey_common.Services;
using goodkey_common.Models;
using goodkey_common.DTO;
using goodkey_cms.Services;
using goodkey_cms.DTO.User;
using goodkey_cms.Repositories;
using goodkey_common.DTO.ExhibitorImport;

namespace goodkey_cms.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class ExhibitorImportController : ControllerBase
    {
        private readonly IExhibitorImportRepository _importRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IShowExhibitorRepository _exhibitorRepository;
        private readonly IExcelImportService _excelService;
        private readonly goodkey_cms.Services.StorageService _storageService;
        private readonly IUserRepository _userRepository;
        private readonly AuthService _authService;
        private readonly ICountryRepository _countryRepository;
        private readonly IProvinceRepository _provinceRepository;

        public ExhibitorImportController(
            IExhibitorImportRepository importRepository,
            ICompanyRepository companyRepository,
            IShowExhibitorRepository exhibitorRepository,
            IExcelImportService excelService,
            goodkey_cms.Services.StorageService storageService,
            IUserRepository userRepository,
            AuthService authService,
            ICountryRepository countryRepository,
            IProvinceRepository provinceRepository)
        {
            _importRepository = importRepository;
            _companyRepository = companyRepository;
            _exhibitorRepository = exhibitorRepository;
            _excelService = excelService;
            _storageService = storageService;
            _userRepository = userRepository;
            _authService = authService;
            _countryRepository = countryRepository;
            _provinceRepository = provinceRepository;
        }

        // =====================================================
        // PHASE 1: UPLOAD AND VALIDATION
        // =====================================================

        /// <summary>
        /// Upload Excel file and validate exhibitor data
        /// </summary>
        [HttpPost("upload")]
        public GenericRespond<ExhibitorImportValidationResponseDto> UploadAndValidate([FromForm] ExhibitorImportUploadDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);
            if (user == null)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            // Validate file
            if (dto.ExcelFile == null || dto.ExcelFile.Length == 0)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Excel file is required"
                };
            }

            // Validate file type
            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(dto.ExcelFile.FileName).ToLower();
            if (!allowedExtensions.Contains(fileExtension))
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Only Excel files (.xlsx, .xls) are allowed"
                };
            }

            // Validate file size (max 10MB)
            if (dto.ExcelFile.Length > 10 * 1024 * 1024)
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "File size cannot exceed 10MB"
                };
            }

            // Save file to storage
            var fileName = $"{Guid.NewGuid()}_{dto.ExcelFile.FileName}";
            var uploadResult = _storageService.UploadFile(dto.ExcelFile, goodkey_cms.Services.FileType.Document, "imports");
            var filePath = uploadResult.RelativePath;

            // Create import session
            var session = _importRepository.CreateSession(
                dto.ShowId,
                user.UserId,
                fileName,
                filePath,
                dto.ExcelFile.FileName,
                dto.ExcelFile.ContentType,
                dto.ExcelFile.Length
            );

            // Read Excel file
            List<goodkey_common.DTO.ExcelRowData> excelRows;
            using (var stream = dto.ExcelFile.OpenReadStream())
            {
                excelRows = _excelService.ReadExcelFile(stream, dto.ExcelFile.FileName);
            }

            if (!excelRows.Any())
            {
                return new GenericRespond<ExhibitorImportValidationResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Excel file contains no data rows"
                };
            }

            // Process and validate each row
            var validationMessages = new List<ExhibitorImportValidationMessageDto>();
            var duplicates = new List<ExhibitorImportDuplicateDto>();
            var importRows = new List<ExhibitorImportRowDto>();

            foreach (var excelRow in excelRows)
            {
                // Create import row record
                var row = _importRepository.CreateRow(
                    session.SessionId,
                    excelRow.RowNumber,
                    excelRow.CompanyName,
                    excelRow.CompanyPhone,
                    excelRow.CompanyEmail,
                    excelRow.CompanyAddress1,
                    excelRow.CompanyAddress2,
                    excelRow.CompanyCity,
                    excelRow.CompanyProvince,
                    excelRow.CompanyPostalCode,
                    excelRow.CompanyCountry,
                    excelRow.CompanyWebsite,
                    excelRow.ContactFirstName,
                    excelRow.ContactLastName,
                    excelRow.ContactEmail,
                    excelRow.ContactPhone,
                    excelRow.ContactMobile,
                    excelRow.ContactExt,
                    excelRow.ContactType,
                    excelRow.BoothNumbers
                );

                // Validate row data
                var rowMessages = ValidateRowData(excelRow, row.Id);
                validationMessages.AddRange(rowMessages);

                // Update row status based on validation
                var hasErrors = rowMessages.Any(m => m.MessageType == "Error");
                var hasWarnings = rowMessages.Any(m => m.MessageType == "Warning");
                var errorCount = rowMessages.Count(m => m.MessageType == "Error");
                var warningCount = rowMessages.Count(m => m.MessageType == "Warning");

                _importRepository.UpdateRowStatus(row.Id,
                    hasErrors ? "Error" : hasWarnings ? "Warning" : "Valid",
                    hasErrors, hasWarnings, errorCount, warningCount);

                // Create DTO for response
                var importRow = new ExhibitorImportRowDto
                {
                    RowNumber = excelRow.RowNumber,
                    Status = hasErrors ? "Error" : hasWarnings ? "Warning" : "Valid",
                    CompanyName = excelRow.CompanyName,
                    CompanyPhone = excelRow.CompanyPhone,
                    CompanyEmail = excelRow.CompanyEmail,
                    CompanyAddress1 = excelRow.CompanyAddress1,
                    CompanyAddress2 = excelRow.CompanyAddress2,
                    CompanyCity = excelRow.CompanyCity,
                    CompanyProvince = excelRow.CompanyProvince,
                    CompanyPostalCode = excelRow.CompanyPostalCode,
                    CompanyCountry = excelRow.CompanyCountry,
                    CompanyWebsite = excelRow.CompanyWebsite,
                    ContactFirstName = excelRow.ContactFirstName,
                    ContactLastName = excelRow.ContactLastName,
                    ContactEmail = excelRow.ContactEmail,
                    ContactPhone = excelRow.ContactPhone,
                    ContactMobile = excelRow.ContactMobile,
                    ContactExt = excelRow.ContactExt,
                    ContactType = excelRow.ContactType,
                    BoothNumbers = excelRow.BoothNumbers,
                    HasErrors = hasErrors,
                    HasWarnings = hasWarnings,
                    ErrorCount = errorCount,
                    WarningCount = warningCount
                };

                importRows.Add(importRow);
            }

            // Detect duplicates
            duplicates = DetectDuplicates(session.SessionId, importRows);

            // Calculate summary
            var validRows = importRows.Count(r => r.Status == "Valid");
            var errorRows = importRows.Count(r => r.Status == "Error");
            var warningRows = importRows.Count(r => r.Status == "Warning");
            var unresolvedDuplicates = duplicates.Count(d => d.RequiresUserDecision);
            var canProceed = errorRows == 0 && unresolvedDuplicates == 0;

            // Update session with counts
            _importRepository.UpdateSessionCounts(session.SessionId, importRows.Count, validRows, errorRows, warningRows, unresolvedDuplicates);
            _importRepository.UpdateSessionStatus(session.SessionId, "Validated", canProceed);

            var summary = new ExhibitorImportSummaryDto
            {
                TotalRows = importRows.Count,
                ValidRows = validRows,
                ErrorRows = errorRows,
                WarningRows = warningRows,
                UnresolvedDuplicates = unresolvedDuplicates,
                HasErrors = errorRows > 0,
                HasWarnings = warningRows > 0,
                HasDuplicates = duplicates.Any()
            };

            var response = new ExhibitorImportValidationResponseDto
            {
                SessionId = session.SessionId.ToString(),
                ShowId = dto.ShowId,
                FileName = dto.ExcelFile.FileName,
                Summary = summary,
                Rows = importRows,
                ValidationMessages = validationMessages,
                Duplicates = duplicates,
                CanProceed = canProceed,
                ExpiresAt = session.ExpiresAt.Value
            };

            return new GenericRespond<ExhibitorImportValidationResponseDto>
            {
                Data = response,
                StatusCode = 200,
                Message = canProceed ? "Validation completed successfully" : "Validation completed with issues that need resolution"
            };
        }

        // =====================================================
        // VALIDATION METHODS
        // =====================================================

        private List<ExhibitorImportValidationMessageDto> ValidateRowData(goodkey_common.DTO.ExcelRowData excelRow, int rowId)
        {
            var messages = new List<ExhibitorImportValidationMessageDto>();

            // Required field validation
            ValidateRequiredField(messages, excelRow.RowNumber, rowId, "CompanyName", excelRow.CompanyName, "Company name is required");
            ValidateRequiredField(messages, excelRow.RowNumber, rowId, "ContactFirstName", excelRow.ContactFirstName, "Contact first name is required");
            ValidateRequiredField(messages, excelRow.RowNumber, rowId, "ContactLastName", excelRow.ContactLastName, "Contact last name is required");
            ValidateRequiredField(messages, excelRow.RowNumber, rowId, "ContactEmail", excelRow.ContactEmail, "Contact email is required");

            // Email format validation
            if (!string.IsNullOrWhiteSpace(excelRow.ContactEmail) && !IsValidEmail(excelRow.ContactEmail))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "ContactEmail", excelRow.ContactEmail,
                    "Error", "EmailFormat", "INVALID_EMAIL", "Invalid email format");
            }

            if (!string.IsNullOrWhiteSpace(excelRow.CompanyEmail) && !IsValidEmail(excelRow.CompanyEmail))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "CompanyEmail", excelRow.CompanyEmail,
                    "Warning", "EmailFormat", "INVALID_EMAIL", "Invalid company email format");
            }

            // Phone format validation
            if (!string.IsNullOrWhiteSpace(excelRow.ContactPhone) && !IsValidPhone(excelRow.ContactPhone))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "ContactPhone", excelRow.ContactPhone,
                    "Warning", "PhoneFormat", "INVALID_PHONE", "Invalid phone format");
            }

            if (!string.IsNullOrWhiteSpace(excelRow.ContactMobile) && !IsValidPhone(excelRow.ContactMobile))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "ContactMobile", excelRow.ContactMobile,
                    "Warning", "PhoneFormat", "INVALID_PHONE", "Invalid mobile phone format");
            }

            // Website URL validation
            if (!string.IsNullOrWhiteSpace(excelRow.CompanyWebsite) && !IsValidUrl(excelRow.CompanyWebsite))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "CompanyWebsite", excelRow.CompanyWebsite,
                    "Warning", "UrlFormat", "INVALID_URL", "Invalid website URL format");
            }

            // Postal code validation
            if (!string.IsNullOrWhiteSpace(excelRow.CompanyPostalCode) && !IsValidPostalCode(excelRow.CompanyPostalCode))
            {
                AddValidationMessage(messages, excelRow.RowNumber, rowId, "CompanyPostalCode", excelRow.CompanyPostalCode,
                    "Warning", "PostalCodeFormat", "INVALID_POSTAL_CODE", "Invalid postal code format");
            }

            return messages;
        }

        private void ValidateRequiredField(List<ExhibitorImportValidationMessageDto> messages, int rowNumber, int rowId,
            string fieldName, string fieldValue, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(fieldValue))
            {
                AddValidationMessage(messages, rowNumber, rowId, fieldName, fieldValue,
                    "Error", "Required", "REQUIRED_FIELD", errorMessage);
            }
        }

        private void AddValidationMessage(List<ExhibitorImportValidationMessageDto> messages, int rowNumber, int rowId,
            string fieldName, string fieldValue, string messageType, string validationRule, string messageCode, string message)
        {
            var validationMessage = new ExhibitorImportValidationMessageDto
            {
                RowNumber = rowNumber,
                FieldName = fieldName,
                FieldValue = fieldValue,
                MessageType = messageType,
                ValidationRule = validationRule,
                MessageCode = messageCode,
                Message = message
            };

            messages.Add(validationMessage);

            // Save to database
            _importRepository.AddValidationMessage(
                Guid.Parse(Request.HttpContext.Items["SessionId"]?.ToString() ?? Guid.NewGuid().ToString()),
                rowId, rowNumber, fieldName, fieldValue, messageType, validationRule, messageCode, message);
        }

        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email)) return false;

            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }

        private bool IsValidPhone(string phone)
        {
            if (string.IsNullOrWhiteSpace(phone)) return true;

            // Remove common phone formatting characters
            var cleanPhone = phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace("+", "").Replace(".", "");

            // Check if it's all digits and reasonable length
            return cleanPhone.All(char.IsDigit) && cleanPhone.Length >= 7 && cleanPhone.Length <= 15;
        }

        private bool IsValidUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url)) return true;

            // Add http:// if no protocol specified
            if (!url.StartsWith("http://") && !url.StartsWith("https://"))
            {
                url = "http://" + url;
            }

            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }

        private bool IsValidPostalCode(string postalCode)
        {
            if (string.IsNullOrWhiteSpace(postalCode)) return true;

            // Basic postal code validation - adjust based on your requirements
            var cleanPostalCode = postalCode.Replace(" ", "").Replace("-", "");
            return cleanPostalCode.Length >= 3 && cleanPostalCode.Length <= 10;
        }

        // =====================================================
        // DUPLICATE DETECTION METHODS
        // =====================================================

        private List<ExhibitorImportDuplicateDto> DetectDuplicates(Guid sessionId, List<ExhibitorImportRowDto> rows)
        {
            var duplicates = new List<ExhibitorImportDuplicateDto>();

            // 1. Check for database conflicts (existing contacts with same email but different data)
            foreach (var row in rows.Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail) && r.Status != "Error"))
            {
                var existingContacts = _importRepository.FindContactsByEmail(row.ContactEmail);

                foreach (var existingContact in existingContacts)
                {
                    var fieldConflicts = GenerateFieldConflicts(row, existingContact);

                    if (fieldConflicts.Any(c => c.HasConflict))
                    {
                        var duplicate = _importRepository.CreateDuplicate(
                            sessionId,
                            "DatabaseConflict",
                            row.ContactEmail,
                            new List<int> { row.RowNumber },
                            $"Contact with email {row.ContactEmail} already exists in database with different information",
                            existingContact.ContactId,
                            "Contact"
                        );

                        duplicates.Add(new ExhibitorImportDuplicateDto
                        {
                            DuplicateId = duplicate.Id,
                            DuplicateType = "DatabaseConflict",
                            DuplicateValue = row.ContactEmail,
                            RowNumbers = new List<int> { row.RowNumber },
                            ConflictDescription = $"Contact with email {row.ContactEmail} already exists in database with different information",
                            RequiresUserDecision = true,
                            ExistingRecordId = existingContact.ContactId,
                            ExistingRecordType = "Contact",
                            FieldConflicts = fieldConflicts
                        });
                    }
                }
            }

            // 2. Check for Excel duplicates (same email appears multiple times in Excel with different data)
            var emailGroups = rows
                .Where(r => !string.IsNullOrWhiteSpace(r.ContactEmail) && r.Status != "Error")
                .GroupBy(r => r.ContactEmail.ToLower())
                .Where(g => g.Count() > 1);

            foreach (var group in emailGroups)
            {
                var rowsList = group.ToList();
                var fieldConflicts = GenerateExcelFieldConflicts(rowsList);

                if (fieldConflicts.Any(c => c.HasConflict))
                {
                    var rowNumbers = rowsList.Select(r => r.RowNumber).ToList();

                    var duplicate = _importRepository.CreateDuplicate(
                        sessionId,
                        "EmailDuplicate",
                        group.Key,
                        rowNumbers,
                        $"Email {group.Key} appears multiple times in Excel with different information"
                    );

                    duplicates.Add(new ExhibitorImportDuplicateDto
                    {
                        DuplicateId = duplicate.Id,
                        DuplicateType = "EmailDuplicate",
                        DuplicateValue = group.Key,
                        RowNumbers = rowNumbers,
                        ConflictDescription = $"Email {group.Key} appears multiple times in Excel with different information",
                        RequiresUserDecision = true,
                        ExistingRecordId = null,
                        ExistingRecordType = null,
                        FieldConflicts = fieldConflicts
                    });
                }
            }

            return duplicates;
        }

        private List<ExhibitorImportFieldConflictDto> GenerateFieldConflicts(ExhibitorImportRowDto importRow, Contact existingContact)
        {
            var conflicts = new List<ExhibitorImportFieldConflictDto>();

            // Compare contact fields
            AddFieldConflict(conflicts, "ContactFirstName", importRow.ContactFirstName, existingContact.FirstName);
            AddFieldConflict(conflicts, "ContactLastName", importRow.ContactLastName, existingContact.LastName);
            AddFieldConflict(conflicts, "ContactEmail", importRow.ContactEmail, existingContact.Email);
            AddFieldConflict(conflicts, "ContactPhone", importRow.ContactPhone, existingContact.Telephone);
            AddFieldConflict(conflicts, "ContactMobile", importRow.ContactMobile, existingContact.Cellphone);

            // Get company info if contact has a company
            if (existingContact.CompanyId.HasValue)
            {
                var existingCompany = _importRepository.GetCompany(existingContact.CompanyId.Value);
                if (existingCompany != null)
                {
                    AddFieldConflict(conflicts, "CompanyName", importRow.CompanyName, existingCompany.CompanyName);
                    AddFieldConflict(conflicts, "CompanyPhone", importRow.CompanyPhone, existingCompany.Phone);
                    AddFieldConflict(conflicts, "CompanyEmail", importRow.CompanyEmail, existingCompany.Email);
                    AddFieldConflict(conflicts, "CompanyAddress1", importRow.CompanyAddress1, existingCompany.Address1);
                    AddFieldConflict(conflicts, "CompanyAddress2", importRow.CompanyAddress2, existingCompany.Address2);
                    AddFieldConflict(conflicts, "CompanyCity", importRow.CompanyCity, existingCompany.City);
                    AddFieldConflict(conflicts, "CompanyWebsite", importRow.CompanyWebsite, existingCompany.WebsiteUrl);
                }
            }

            return conflicts;
        }

        private List<ExhibitorImportFieldConflictDto> GenerateExcelFieldConflicts(List<ExhibitorImportRowDto> rows)
        {
            var conflicts = new List<ExhibitorImportFieldConflictDto>();
            var firstRow = rows.First();

            foreach (var row in rows.Skip(1))
            {
                AddFieldConflict(conflicts, "ContactFirstName", firstRow.ContactFirstName, row.ContactFirstName);
                AddFieldConflict(conflicts, "ContactLastName", firstRow.ContactLastName, row.ContactLastName);
                AddFieldConflict(conflicts, "ContactPhone", firstRow.ContactPhone, row.ContactPhone);
                AddFieldConflict(conflicts, "ContactMobile", firstRow.ContactMobile, row.ContactMobile);
                AddFieldConflict(conflicts, "CompanyName", firstRow.CompanyName, row.CompanyName);
                AddFieldConflict(conflicts, "CompanyPhone", firstRow.CompanyPhone, row.CompanyPhone);
                AddFieldConflict(conflicts, "CompanyEmail", firstRow.CompanyEmail, row.CompanyEmail);
                AddFieldConflict(conflicts, "CompanyAddress1", firstRow.CompanyAddress1, row.CompanyAddress1);
                AddFieldConflict(conflicts, "CompanyAddress2", firstRow.CompanyAddress2, row.CompanyAddress2);
                AddFieldConflict(conflicts, "CompanyCity", firstRow.CompanyCity, row.CompanyCity);
                AddFieldConflict(conflicts, "CompanyProvince", firstRow.CompanyProvince, row.CompanyProvince);
                AddFieldConflict(conflicts, "CompanyPostalCode", firstRow.CompanyPostalCode, row.CompanyPostalCode);
                AddFieldConflict(conflicts, "CompanyCountry", firstRow.CompanyCountry, row.CompanyCountry);
                AddFieldConflict(conflicts, "CompanyWebsite", firstRow.CompanyWebsite, row.CompanyWebsite);
                AddFieldConflict(conflicts, "BoothNumbers", firstRow.BoothNumbers, row.BoothNumbers);
            }

            return conflicts.Where(c => c.HasConflict).ToList();
        }

        private void AddFieldConflict(List<ExhibitorImportFieldConflictDto> conflicts, string fieldName, string value1, string value2)
        {
            var normalizedValue1 = (value1 ?? "").Trim();
            var normalizedValue2 = (value2 ?? "").Trim();
            var hasConflict = !string.Equals(normalizedValue1, normalizedValue2, StringComparison.OrdinalIgnoreCase);

            // Only add if there's an actual conflict or if we haven't added this field yet
            var existingConflict = conflicts.FirstOrDefault(c => c.FieldName == fieldName);
            if (existingConflict == null)
            {
                conflicts.Add(new ExhibitorImportFieldConflictDto
                {
                    FieldName = fieldName,
                    ExcelValue = normalizedValue1,
                    DatabaseValue = normalizedValue2,
                    HasConflict = hasConflict
                });
            }
            else if (hasConflict && !existingConflict.HasConflict)
            {
                existingConflict.HasConflict = true;
            }
        }

        // =====================================================
        // PHASE 2: RESOLVE DUPLICATES
        // =====================================================

        /// <summary>
        /// Resolve duplicate conflicts with user's field-by-field choices
        /// </summary>
        [HttpPost("resolve")]
        public GenericRespond<string> ResolveDuplicates([FromBody] ExhibitorImportResolveDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<string>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);
            if (user == null)
            {
                return new GenericRespond<string>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var sessionId = Guid.Parse(dto.SessionId);
            var session = _importRepository.GetSession(sessionId);
            if (session == null)
            {
                return new GenericRespond<string>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            if (session.Status != "Validated")
            {
                return new GenericRespond<string>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session is not in validated state"
                };
            }

            // Process each duplicate resolution
            foreach (var duplicateResolution in dto.DuplicateResolutions)
            {
                var duplicate = _importRepository.GetDuplicate(duplicateResolution.DuplicateId);
                if (duplicate == null) continue;

                // Save field resolutions
                foreach (var fieldResolution in duplicateResolution.FieldResolutions)
                {
                    // Get the row IDs for this duplicate
                    var rowNumbers = System.Text.Json.JsonSerializer.Deserialize<List<int>>(duplicate.RowNumbers);
                    var rows = _importRepository.GetSessionRows(sessionId)
                        .Where(r => rowNumbers.Contains(r.RowNumber))
                        .ToList();

                    foreach (var row in rows)
                    {
                        // Save the field resolution
                        _importRepository.SaveFieldResolution(
                            sessionId,
                            duplicateResolution.DuplicateId,
                            row.Id,
                            fieldResolution.FieldName,
                            fieldResolution.SelectedSource,
                            fieldResolution.SelectedValue,
                            fieldResolution.ExcelValue,
                            fieldResolution.DatabaseValue,
                            fieldResolution.CustomValue
                        );

                        // Update the row with the selected value
                        _importRepository.UpdateRowFieldValue(row.Id, fieldResolution.FieldName, fieldResolution.SelectedValue);
                    }
                }

                // Mark duplicate as resolved
                _importRepository.ResolveDuplicate(duplicateResolution.DuplicateId, user.UserId);
            }

            // Update session status to allow execution
            _importRepository.UpdateSessionStatus(sessionId, "Validated", true);

            return new GenericRespond<string>
            {
                Data = "Duplicates resolved successfully",
                StatusCode = 200,
                Message = "All duplicate conflicts have been resolved. You can now proceed with the import."
            };
        }

        // =====================================================
        // FIELD EDITING AND ERROR FIXING ENDPOINTS
        // =====================================================

        /// <summary>
        /// Edit individual field values to fix validation errors
        /// </summary>
        [HttpPost("edit-fields")]
        public GenericRespond<ExhibitorImportFieldEditResponseDto> EditFields([FromBody] ExhibitorImportFieldEditRequestDto request)
        {
            try
            {
                var user = GetCurrentUser();
                if (user == null)
                    return new GenericRespond<ExhibitorImportFieldEditResponseDto>
                    {
                        Data = null,
                        StatusCode = 401,
                        Message = "User not authenticated"
                    };

                var sessionId = Guid.Parse(request.SessionId);
                var session = _importRepository.GetSession(sessionId);
                if (session == null)
                    return new GenericRespond<ExhibitorImportFieldEditResponseDto>
                    {
                        Data = null,
                        StatusCode = 404,
                        Message = "Import session not found"
                    };

                var results = new List<FieldEditResultDto>();

                foreach (var edit in request.FieldEdits)
                {
                    try
                    {
                        // Get the row
                        var rows = _importRepository.GetSessionRows(sessionId);
                        var row = rows.FirstOrDefault(r => r.RowNumber == edit.RowNumber);
                        if (row == null)
                        {
                            results.Add(new FieldEditResultDto
                            {
                                RowNumber = edit.RowNumber,
                                FieldName = edit.FieldName,
                                Success = false,
                                ErrorMessage = "Row not found"
                            });
                            continue;
                        }

                        // Update the field value
                        _importRepository.UpdateRowFieldValue(row.Id, edit.FieldName, edit.NewValue);

                        // Re-validate the field
                        var validationErrors = ValidateField(edit.FieldName, edit.NewValue);
                        bool validationFixed = validationErrors.Count == 0;

                        // Add new validation messages if any
                        foreach (var error in validationErrors)
                        {
                            _importRepository.AddValidationMessage(
                                sessionId, row.Id, row.RowNumber, edit.FieldName,
                                edit.NewValue, "Error", "FieldValidation", "FIELD_INVALID", error);
                        }

                        results.Add(new FieldEditResultDto
                        {
                            RowNumber = edit.RowNumber,
                            FieldName = edit.FieldName,
                            Success = true,
                            ValidationFixed = validationFixed,
                            RemainingErrors = validationErrors
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new FieldEditResultDto
                        {
                            RowNumber = edit.RowNumber,
                            FieldName = edit.FieldName,
                            Success = false,
                            ErrorMessage = ex.Message
                        });
                    }
                }

                // Update session summary
                var updatedSummary = RecalculateValidationSummary(sessionId);

                var response = new ExhibitorImportFieldEditResponseDto
                {
                    Success = true,
                    Message = $"Updated {results.Count(r => r.Success)} field(s) successfully",
                    Results = results,
                    UpdatedSummary = updatedSummary
                };

                return new GenericRespond<ExhibitorImportFieldEditResponseDto>
                {
                    Data = response,
                    StatusCode = 200,
                    Message = "Fields updated successfully"
                };
            }
            catch (Exception ex)
            {
                return new GenericRespond<ExhibitorImportFieldEditResponseDto>
                {
                    Data = null,
                    StatusCode = 500,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// Compare a row with existing database records for conflict resolution
        /// </summary>
        [HttpPost("compare-row")]
        public GenericRespond<ExhibitorImportRowComparisonResponseDto> CompareRow([FromBody] ExhibitorImportRowComparisonRequestDto request)
        {
            try
            {
                var sessionId = Guid.Parse(request.SessionId);
                var session = _importRepository.GetSession(sessionId);
                if (session == null)
                    return new GenericRespond<ExhibitorImportRowComparisonResponseDto>
                    {
                        Data = null,
                        StatusCode = 404,
                        Message = "Import session not found"
                    };

                var rows = _importRepository.GetSessionRows(sessionId);
                var row = rows.FirstOrDefault(r => r.RowNumber == request.RowNumber);
                if (row == null)
                    return new GenericRespond<ExhibitorImportRowComparisonResponseDto>
                    {
                        Data = null,
                        StatusCode = 404,
                        Message = "Row not found"
                    };

                // Get Excel data
                var excelData = new ExhibitorImportRowComparisonDto
                {
                    Source = "Excel",
                    CompanyName = row.CompanyName ?? "",
                    CompanyEmail = row.CompanyEmail ?? "",
                    CompanyPhone = row.CompanyPhone ?? "",
                    CompanyAddress = $"{row.CompanyAddress1} {row.CompanyAddress2}".Trim(),
                    ContactFirstName = row.ContactFirstName ?? "",
                    ContactLastName = row.ContactLastName ?? "",
                    ContactEmail = row.ContactEmail ?? "",
                    ContactPhone = row.ContactPhone ?? "",
                    ContactMobile = row.ContactMobile ?? "",
                    BoothNumbers = row.BoothNumbers ?? "",
                    LastUpdated = row.CreatedAt
                };

                // Get database data if comparison IDs provided
                ExhibitorImportRowComparisonDto? databaseData = null;
                if (request.CompareWithContactId.HasValue)
                {
                    var contact = _importRepository.GetContact(request.CompareWithContactId.Value);
                    if (contact != null)
                    {
                        var company = contact.CompanyId.HasValue ? _importRepository.GetCompany(contact.CompanyId.Value) : null;
                        databaseData = new ExhibitorImportRowComparisonDto
                        {
                            Source = "Database",
                            CompanyName = company?.CompanyName ?? "",
                            CompanyEmail = company?.Email ?? "",
                            CompanyPhone = company?.Phone ?? "",
                            CompanyAddress = $"{company?.Address1} {company?.Address2}".Trim(),
                            ContactFirstName = contact.FirstName ?? "",
                            ContactLastName = contact.LastName ?? "",
                            ContactEmail = contact.Email ?? "",
                            ContactPhone = contact.Telephone ?? "",
                            ContactMobile = contact.Cellphone ?? "",
                            BoothNumbers = "", // Would need to get from exhibitor record
                            LastUpdated = contact.UpdatedAt
                        };
                    }
                }

                // Generate field comparisons
                var fieldComparisons = GenerateFieldComparisons(excelData, databaseData);

                // Get validation messages for this row
                var validationMessages = _importRepository.GetRowValidationMessages(row.Id)
                    .Select(m => new ExhibitorImportValidationMessageDto
                    {
                        RowNumber = m.RowNumber,
                        FieldName = m.FieldName ?? "",
                        FieldValue = m.FieldValue ?? "",
                        MessageType = m.MessageType ?? "",
                        ValidationRule = m.ValidationRule ?? "",
                        MessageCode = m.MessageCode ?? "",
                        Message = m.Message ?? ""
                    }).ToList();

                var response = new ExhibitorImportRowComparisonResponseDto
                {
                    ExcelData = excelData,
                    DatabaseData = databaseData,
                    FieldComparisons = fieldComparisons,
                    ValidationMessages = validationMessages
                };

                return new GenericRespond<ExhibitorImportRowComparisonResponseDto>
                {
                    Data = response,
                    StatusCode = 200,
                    Message = "Row comparison data retrieved successfully"
                };
            }
            catch (Exception ex)
            {
                return new GenericRespond<ExhibitorImportRowComparisonResponseDto>
                {
                    Data = null,
                    StatusCode = 500,
                    Message = ex.Message
                };
            }
        }

        // =====================================================
        // PHASE 3: EXECUTE IMPORT
        // =====================================================

        /// <summary>
        /// Execute the import after validation and duplicate resolution
        /// </summary>
        [HttpPost("execute")]
        public GenericRespond<ExhibitorImportExecutionResponseDto> ExecuteImport([FromBody] ExhibitorImportExecuteDto dto)
        {
            var username = Request.HttpContext.GetUsername();
            if (string.IsNullOrEmpty(username))
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    StatusCode = 401,
                    Message = "User not authenticated"
                };
            }

            var user = _userRepository.GetUserByUsername(username);
            if (user == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "User not found"
                };
            }

            var sessionId = Guid.Parse(dto.SessionId);
            var session = _importRepository.GetSession(sessionId);
            if (session == null)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Import session not found"
                };
            }

            if (session.Status != "Validated" || !session.CanProceed.GetValueOrDefault())
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session is not ready for execution"
                };
            }

            if (session.ExpiresAt < DateTime.Now)
            {
                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 400,
                    Message = "Import session has expired"
                };
            }

            try
            {
                // Start execution
                _importRepository.UpdateSessionStatus(sessionId, "Executing");

                var rows = _importRepository.GetSessionRows(sessionId);
                var results = new List<ExhibitorImportExecutionResultDto>();
                var summary = new ExhibitorImportExecutionSummaryDto
                {
                    TotalRows = rows.Count
                };

                foreach (var row in rows.Where(r => r.Status == "Valid" || r.Status == "Warning"))
                {
                    try
                    {
                        var result = ProcessImportRow(row, dto.SendEmailInvites, username);
                        results.Add(result);

                        // Update summary based on result
                        if (result.Status == "Processed")
                        {
                            summary.ProcessedRows++;
                            summary.SuccessfulRows++;
                            if (result.CompanyAction == "Created") summary.CompaniesCreated++;
                            if (result.CompanyAction == "Updated") summary.CompaniesUpdated++;
                            if (result.ContactAction == "Created") summary.ContactsCreated++;
                            if (result.ContactAction == "Updated") summary.ContactsUpdated++;
                            if (result.CreatedExhibitorId.HasValue) summary.ExhibitorsCreated++;
                            if (result.CreatedUserId.HasValue) summary.UsersCreated++;
                            if (result.EmailInviteSent) summary.EmailsSent++;
                        }
                        else if (result.Status == "Skipped")
                        {
                            // Skipped rows are not counted in current implementation
                        }
                        else if (result.Status == "Failed")
                        {
                            summary.FailedRows++;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Handle individual row processing errors
                        var errorResult = new ExhibitorImportExecutionResultDto
                        {
                            RowNumber = row.RowNumber,
                            Status = "Failed",
                            CompanyName = row.CompanyName,
                            ContactName = $"{row.ContactFirstName} {row.ContactLastName}".Trim(),
                            ContactEmail = row.ContactEmail,
                            ErrorMessage = ex.Message,
                            ProcessedAt = DateTime.Now
                        };

                        results.Add(errorResult);
                        summary.FailedRows++;

                        // Save execution result
                        _importRepository.SaveExecutionResult(
                            sessionId, row.Id, row.RowNumber, "Failed",
                            null, null, null, null, null, null,
                            "Failed", "Failed", "Failed", "Failed",
                            row.CompanyName, $"{row.ContactFirstName} {row.ContactLastName}".Trim(), row.ContactEmail,
                            ex.Message, null);
                    }
                }

                // Complete execution
                _importRepository.UpdateSessionStatus(sessionId, "Completed");

                var response = new ExhibitorImportExecutionResponseDto
                {
                    SessionId = dto.SessionId,
                    Status = "Completed",
                    Summary = summary,
                    Results = results,
                    CompletedAt = DateTime.Now
                };

                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = response,
                    StatusCode = 200,
                    Message = $"Import completed successfully. Processed {summary.ProcessedRows} of {summary.TotalRows} rows."
                };
            }
            catch (Exception ex)
            {
                _importRepository.UpdateSessionStatus(sessionId, "Failed");

                return new GenericRespond<ExhibitorImportExecutionResponseDto>
                {
                    Data = null,
                    StatusCode = 500,
                    Message = $"Error executing import: {ex.Message}"
                };
            }
        }

        // =====================================================
        // ROW PROCESSING METHOD
        // =====================================================

        private ExhibitorImportExecutionResultDto ProcessImportRow(ExhibitorImportRows row, bool sendEmailInvites, string username)
        {
            var result = new ExhibitorImportExecutionResultDto
            {
                RowNumber = row.RowNumber,
                CompanyName = row.CompanyName,
                ContactName = $"{row.ContactFirstName} {row.ContactLastName}".Trim(),
                ContactEmail = row.ContactEmail,
                ProcessedAt = DateTime.Now
            };

            try
            {
                // Step 1: Handle Company
                int companyId;
                var existingCompanies = _importRepository.FindCompaniesByName(row.CompanyName);
                var existingCompany = existingCompanies.FirstOrDefault();

                if (existingCompany != null)
                {
                    // Use existing company
                    companyId = existingCompany.CompanyId;
                    result.UpdatedCompanyId = companyId;
                    result.CompanyAction = "UsedExisting";
                }
                else
                {
                    // Create new company using repository
                    var success = _companyRepository.Add(
                        row.CompanyName ?? "",
                        row.CompanyPhone ?? "",
                        row.CompanyEmail ?? "",
                        row.CompanyAddress1 ?? "",
                        row.CompanyAddress2 ?? "",
                        row.CompanyPostalCode ?? "",
                        row.CompanyCity ?? "",
                        ResolveProvinceId(row.CompanyProvince) ?? 1,
                        ResolveCountryId(row.CompanyCountry) ?? 1,
                        row.CompanyWebsite ?? "",
                        "", // accountNumber
                        "Exhibitor", // companyGroup
                        "", // note
                        false, // isArchived
                        username
                    );

                    if (success)
                    {
                        // Get the created company ID by finding it by name
                        var createdCompany = _importRepository.FindCompaniesByName(row.CompanyName ?? "").FirstOrDefault();
                        companyId = createdCompany?.CompanyId ?? 0;
                    }
                    else
                    {
                        companyId = 0; // Failed to create company
                    }

                    result.CreatedCompanyId = companyId;
                    result.CompanyAction = "Created";
                }

                // Step 2: Handle Contact
                int contactId;
                var existingContacts = _importRepository.FindContactsByEmail(row.ContactEmail);
                var existingContact = existingContacts.FirstOrDefault();

                if (existingContact != null)
                {
                    // Update existing contact with resolved field values
                    _companyRepository.UpdateContact(
                        existingContact.ContactId,
                        existingContact.ContactTypeId,
                        companyId,
                        row.ContactFirstName ?? "",
                        row.ContactLastName ?? "",
                        row.ContactEmail ?? "",
                        row.ContactPhone ?? "",
                        row.ContactExt ?? "",
                        row.ContactMobile ?? "",
                        false,
                        username
                    );

                    contactId = existingContact.ContactId;
                    result.UpdatedContactId = contactId;
                    result.ContactAction = "Updated";
                }
                else
                {
                    // Create new contact
                    var newContactId = _companyRepository.AddContact(
                        1, // contactTypeId - default
                        companyId,
                        row.ContactFirstName ?? "",
                        row.ContactLastName ?? "",
                        row.ContactEmail ?? "",
                        row.ContactPhone ?? "",
                        row.ContactExt ?? "",
                        row.ContactMobile ?? "",
                        false,
                        username
                    );
                    contactId = newContactId ?? 0;

                    result.CreatedContactId = contactId;
                    result.ContactAction = "Created";
                }

                // Step 3: Create User Account (if needed and requested)
                int? userId = null;
                if (sendEmailInvites && !string.IsNullOrWhiteSpace(row.ContactEmail))
                {
                    var existingUser = _userRepository.GetUserByEmail(row.ContactEmail);
                    if (existingUser == null)
                    {
                        // Create user account
                        var userDto = new CreateUserDto
                        {
                            Username = GenerateUsername(row.ContactEmail),
                            Password = "blue", // Default password
                            FirstName = row.ContactFirstName ?? "",
                            LastName = row.ContactLastName ?? "",
                            Email = row.ContactEmail,
                            WorkEmail = row.ContactEmail,
                            VerificationEmail = row.ContactEmail,
                            WorkPhoneNumber = row.ContactPhone ?? "",
                            MobileNumber = row.ContactMobile ?? "",
                            StatusId = 1,
                            RoleId = 4, // Exhibitor role
                            SalutationId = 1,
                            DepartmentId = 1
                        };

                        userId = _userRepository.CreateUser(username, userDto);
                        result.CreatedUserId = userId;
                        result.UserAction = "Created";

                        // Link user to contact
                        if (userId.HasValue)
                        {
                            var contact = _companyRepository.GetContact(contactId);
                            if (contact != null)
                            {
                                contact.Authuserid = userId.Value;
                                _companyRepository.UpdateContact(
                                    contactId,
                                    contact.ContactTypeId,
                                    contact.CompanyId,
                                    contact.FirstName,
                                    contact.LastName,
                                    contact.Email,
                                    contact.Telephone,
                                    contact.Ext,
                                    contact.Cellphone,
                                    contact.IsArchived ?? false,
                                    username
                                );
                            }
                        }

                        // Send email invite
                        try
                        {
                            // TODO: Implement email sending logic
                            result.EmailInviteSent = true;
                        }
                        catch
                        {
                            result.EmailInviteSent = false;
                        }
                    }
                    else
                    {
                        result.UserAction = "UsedExisting";
                    }
                }
                else
                {
                    result.UserAction = "Skipped";
                }

                // Step 4: Create Show Exhibitor
                var exhibitor = new ShowExhibitors
                {
                    ShowId = row.Session.ShowId,
                    CompanyId = companyId,
                    ContactId = contactId,
                    BoothNumber = ParseBoothNumbers(row.BoothNumbers),
                    CreatedById = _userRepository.GetUserByUsername(username)?.UserId ?? 0
                };

                var createdExhibitor = _exhibitorRepository.CreateShowExhibitor(exhibitor);
                result.CreatedExhibitorId = createdExhibitor.Id;
                result.ExhibitorAction = "Created";

                // Step 5: Update row with processing results
                _importRepository.UpdateRowProcessingResults(
                    row.Id,
                    result.CreatedCompanyId,
                    result.CreatedContactId ?? result.UpdatedContactId,
                    result.CreatedExhibitorId,
                    result.CreatedUserId
                );

                // Step 6: Save execution result
                _importRepository.SaveExecutionResult(
                    row.SessionId, row.Id, row.RowNumber, "Processed",
                    result.CreatedCompanyId, result.UpdatedCompanyId,
                    result.CreatedContactId, result.UpdatedContactId,
                    result.CreatedExhibitorId, result.CreatedUserId,
                    result.CompanyAction, result.ContactAction, result.ExhibitorAction, result.UserAction,
                    row.CompanyName ?? "", $"{row.ContactFirstName} {row.ContactLastName}".Trim(), row.ContactEmail ?? "");

                result.Status = "Processed";
                return result;
            }
            catch (Exception ex)
            {
                result.Status = "Failed";
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        // =====================================================
        // HELPER METHODS
        // =====================================================

        private string GenerateUsername(string email)
        {
            var baseUsername = email.Split('@')[0].ToLower();
            var username = baseUsername;
            var counter = 1;

            while (_userRepository.GetUserByUsername(username) != null)
            {
                username = $"{baseUsername}{counter}";
                counter++;
            }

            return username;
        }

        private string[] ParseBoothNumbers(string boothNumbers)
        {
            if (string.IsNullOrWhiteSpace(boothNumbers))
                return new string[0];

            return boothNumbers
                .Split(new char[] { ',', ';', '|' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(b => b.Trim())
                .Where(b => !string.IsNullOrWhiteSpace(b))
                .ToArray();
        }

        private int? ResolveProvinceId(string provinceName)
        {
            if (string.IsNullOrWhiteSpace(provinceName)) return null;
            return _provinceRepository.FindProvince(provinceName);
        }

        private int? ResolveCountryId(string countryName)
        {
            if (string.IsNullOrWhiteSpace(countryName)) return null;
            return _countryRepository.FindCountry(countryName);
        }

        // =====================================================
        // HELPER METHODS FOR FIELD EDITING
        // =====================================================

        private AuthUser? GetCurrentUser()
        {
            // Get current user from context
            var username = Request.HttpContext.GetUsername();
            if (!string.IsNullOrEmpty(username))
            {
                return _userRepository.GetUserByUsername(username);
            }
            return null;
        }

        private List<string> ValidateField(string fieldName, string value)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(value))
            {
                // Check if field is required
                var requiredFields = new[] { "companyName", "contactFirstName", "contactLastName", "contactEmail" };
                if (requiredFields.Contains(fieldName, StringComparer.OrdinalIgnoreCase))
                {
                    errors.Add($"{fieldName} is required");
                }
                return errors;
            }

            switch (fieldName.ToLower())
            {
                case "contactemail":
                case "companyemail":
                    if (!IsValidEmail(value))
                        errors.Add("Invalid email format");
                    break;

                case "companyphone":
                case "contactphone":
                case "contactmobile":
                    if (!IsValidPhone(value))
                        errors.Add("Invalid phone number format");
                    break;

                case "companypostalcode":
                    if (!IsValidPostalCode(value))
                        errors.Add("Invalid postal code format");
                    break;

                case "companywebsite":
                    if (!IsValidUrl(value))
                        errors.Add("Invalid website URL format");
                    break;
            }

            return errors;
        }

        private ExhibitorImportValidationSummaryDto RecalculateValidationSummary(Guid sessionId)
        {
            var rows = _importRepository.GetSessionRows(sessionId);
            var messages = _importRepository.GetSessionValidationMessages(sessionId);
            var duplicates = _importRepository.GetSessionDuplicates(sessionId);

            var errorRows = rows.Count(r => r.HasErrors == true);
            var warningRows = rows.Count(r => r.HasWarnings == true && r.HasErrors != true);
            var validRows = rows.Count - errorRows - warningRows;
            var unresolvedDuplicates = duplicates.Count(d => d.IsResolved != true);

            // Update session counts
            _importRepository.UpdateSessionCounts(sessionId, rows.Count, validRows, errorRows, warningRows, unresolvedDuplicates);

            return new ExhibitorImportValidationSummaryDto
            {
                TotalRows = rows.Count,
                ValidRows = validRows,
                ErrorRows = errorRows,
                WarningRows = warningRows,
                HasErrors = errorRows > 0,
                HasWarnings = warningRows > 0,
                HasDuplicates = duplicates.Any(),
                UnresolvedDuplicates = unresolvedDuplicates
            };
        }



        private List<FieldComparisonDto> GenerateFieldComparisons(ExhibitorImportRowComparisonDto excelData, ExhibitorImportRowComparisonDto? databaseData)
        {
            var comparisons = new List<FieldComparisonDto>();

            var fields = new[]
            {
                ("CompanyName", "Company Name", excelData.CompanyName, databaseData?.CompanyName),
                ("CompanyEmail", "Company Email", excelData.CompanyEmail, databaseData?.CompanyEmail),
                ("CompanyPhone", "Company Phone", excelData.CompanyPhone, databaseData?.CompanyPhone),
                ("CompanyAddress", "Company Address", excelData.CompanyAddress, databaseData?.CompanyAddress),
                ("ContactFirstName", "Contact First Name", excelData.ContactFirstName, databaseData?.ContactFirstName),
                ("ContactLastName", "Contact Last Name", excelData.ContactLastName, databaseData?.ContactLastName),
                ("ContactEmail", "Contact Email", excelData.ContactEmail, databaseData?.ContactEmail),
                ("ContactPhone", "Contact Phone", excelData.ContactPhone, databaseData?.ContactPhone),
                ("ContactMobile", "Contact Mobile", excelData.ContactMobile, databaseData?.ContactMobile),
                ("BoothNumbers", "Booth Numbers", excelData.BoothNumbers, databaseData?.BoothNumbers)
            };

            foreach (var (fieldName, displayName, excelValue, dbValue) in fields)
            {
                var hasConflict = databaseData != null && !string.Equals(excelValue, dbValue, StringComparison.OrdinalIgnoreCase);
                var validationErrors = ValidateField(fieldName, excelValue);

                comparisons.Add(new FieldComparisonDto
                {
                    FieldName = fieldName,
                    DisplayName = displayName,
                    ExcelValue = excelValue ?? "",
                    DatabaseValue = dbValue,
                    HasConflict = hasConflict,
                    HasValidationError = validationErrors.Any(),
                    ValidationErrors = validationErrors
                });
            }

            return comparisons;
        }
    }
}
