namespace goodkey_cms.DTO.ExhibitorImport
{
    public class ExhibitorImportResolveDto
    {
        public string SessionId { get; set; }
        public List<ExhibitorImportDuplicateResolutionDto> DuplicateResolutions { get; set; }
    }

    public class ExhibitorImportDuplicateResolutionDto
    {
        public int DuplicateId { get; set; }
        public List<ExhibitorImportFieldResolutionDto> FieldResolutions { get; set; }
    }

    public class ExhibitorImportFieldResolutionDto
    {
        public string FieldName { get; set; }
        public string SelectedSource { get; set; } // Excel, Database, Custom
        public string SelectedValue { get; set; }
        public string ExcelValue { get; set; }
        public string DatabaseValue { get; set; }
        public string CustomValue { get; set; }
    }

    public class ExhibitorImportFieldConflictDto
    {
        public string FieldName { get; set; }
        public string ExcelValue { get; set; }
        public string DatabaseValue { get; set; }
        public bool HasConflict { get; set; }
    }
}
