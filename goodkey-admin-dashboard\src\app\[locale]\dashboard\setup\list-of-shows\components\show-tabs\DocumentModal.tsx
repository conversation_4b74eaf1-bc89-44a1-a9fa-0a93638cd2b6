'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { modal } from '@/components/ui/overlay';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import DocumentQuery from '@/services/queries/DocumentQuery';
import { z } from 'zod';
import { ShowDocumentType } from '@/models/Document';

const addSchema = z.object({
  name: z.string().min(1, { message: 'Document name is required' }),
  description: z.string().optional(),
  documentTypeId: z.string().min(1, { message: 'Document type is required' }),
  file: z
    .any()
    .refine((files) => files?.length > 0, 'Document file is required')
    .optional(), // Make file optional for updates
  isRequired: z.boolean().optional(),
  isPublic: z.boolean().optional(),
  isArchived: z.boolean().optional(),
});

type DocumentFormValues = z.infer<typeof addSchema>;

interface IDocumentModal {
  docId?: number;
  showId?: number;
}

function Content({
  defaultValue,
  docId,
  showId,
}: {
  defaultValue?: Partial<DocumentFormValues & { filePath?: string }>;
  docId?: number;
  showId?: number;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: documentTypes, isLoading: loadingDocumentTypes } = useQuery<
    ShowDocumentType[]
  >({
    queryKey: ['documents', 'available-types'],
    queryFn: DocumentQuery.getAvailableDocumentTypes,
  });

  const { mutate, isPending } = useMutation({
    mutationFn:
      defaultValue && docId
        ? DocumentQuery.updateDocument(Number(docId))
        : DocumentQuery.createDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', { showId }] });
      queryClient.invalidateQueries({ queryKey: ['documents', 'document'] });
      modal.close();
      toast({
        title: 'Success',
        description:
          defaultValue && docId
            ? 'Document updated successfully'
            : 'Document added successfully',
        variant: 'success',
      });
    },
    onError: (e: any) => {
      toast({
        title: e.message || 'Failed to save document',
        variant: 'destructive',
      });
    },
  });

  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(addSchema),
    defaultValues: defaultValue
      ? {
          documentTypeId: String(defaultValue.documentTypeId),
          name: defaultValue.name,
          description: defaultValue.description,
          file: defaultValue.file,
          isRequired: defaultValue.isRequired,
          isPublic: defaultValue.isPublic,
          isArchived: defaultValue.isArchived,
        }
      : {
          documentTypeId: '',
          name: '',
          description: '',
          file: undefined,
          isRequired: false,
          isPublic: false,
          isArchived: false,
        },
  });

  if (loadingDocumentTypes) {
    return (
      <ModalContainer
        title={(defaultValue ? 'Update' : 'Add') + ' Document'}
        description="Loading..."
        controls={
          <div className="flex justify-end w-full items-center gap-4">
            <Button variant={'primary'} disabled>
              Loading...
            </Button>
          </div>
        }
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading document types...</p>
          </div>
        </div>
      </ModalContainer>
    );
  }

  return (
    <Form {...form}>
      <ModalContainer
        onSubmit={form.handleSubmit((data) => {
          const formData = new FormData();
          if (data.name) formData.append('name', data.name);
          if (data.description)
            formData.append('description', data.description);
          if (data.documentTypeId)
            formData.append('documentTypeId', data.documentTypeId);
          if (data.file && data.file.length > 0) {
            formData.append('file', data.file[0]);
          }
          if (data.isRequired !== undefined)
            formData.append('isRequired', String(data.isRequired));
          if (data.isPublic !== undefined)
            formData.append('isPublic', String(data.isPublic));
          if (data.isArchived !== undefined)
            formData.append('isArchived', String(data.isArchived));

          if (!docId && showId) {
            formData.append('showId', String(showId));
          }
          mutate(formData);
        })}
        title={(defaultValue ? 'Update' : 'Add') + ' Document'}
        description="Please fill in the document details"
        controls={
          <div className="flex justify-end w-full items-center gap-4">
            <Button
              variant={'primary'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{
                className: 'text-white',
              }}
            >
              {isPending
                ? defaultValue
                  ? 'Updating...'
                  : 'Adding...'
                : defaultValue
                  ? 'Update'
                  : 'Add'}
            </Button>
          </div>
        }
      >
        <div className="flex flex-col gap-6 w-full rounded-[12px] ">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required={true}
            type="text"
          />
          <Field
            control={form.control}
            name="description"
            label="Description"
            type="text"
          />
          {!docId && (
            <Field
              control={form.control}
              name="documentTypeId"
              label="Document Type"
              required={true}
              type={{
                type: 'select',
                props: {
                  options: (documentTypes || []).map((type) => ({
                    label: type.name,
                    value: String(type.id),
                  })),
                  placeholder: 'Select document type',
                },
              }}
            />
          )}
          <Field
            control={form.control}
            name="file"
            label="Document File"
            required={!defaultValue?.filePath} // Use filePath to check if a file already exists
            type={{
              type: 'file',
              props: {
                dropzoneOptions: {
                  // You can add specific document types here if needed
                  // accept: { 'application/pdf': ['.pdf'] },
                  multiple: false,
                  maxFiles: 1,
                },
              },
            }}
          />
          <Field
            control={form.control}
            name="isRequired"
            label="Is Required"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="isPublic"
            label="Is Public"
            type="checkbox"
          />
        </div>
      </ModalContainer>
    </Form>
  );
}

function DocumentModal({ docId, showId }: IDocumentModal) {
  const { data, isLoading, isPaused } = useQuery({
    queryFn: () =>
      docId ? DocumentQuery.getDocument(Number(docId)) : undefined,
    queryKey: ['documents', 'document', { docId }],
    select: (d) =>
      d
        ? {
            documentTypeId: String(d.documentTypeId),
            name: d.name,
            description: d.description,
            file: d.file,
            isRequired: d.isRequired,
            isPublic: d.isPublic,
            isArchived: d.isArchived,
          }
        : undefined,
    enabled: !!docId,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <Content defaultValue={data} docId={docId} showId={showId} />
    </Suspense>
  );
}

export default DocumentModal;
