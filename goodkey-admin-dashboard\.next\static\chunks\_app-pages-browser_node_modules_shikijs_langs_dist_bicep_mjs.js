"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_bicep_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/bicep.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/bicep.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Bicep\\\",\\\"fileTypes\\\":[\\\".bicep\\\"],\\\"name\\\":\\\"bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}],\\\"repository\\\":{\\\"array-literal\\\":{\\\"begin\\\":\\\"\\\\\\\\[(?!(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\bfor\\\\\\\\b)\\\",\\\"end\\\":\\\"]\\\",\\\"name\\\":\\\"meta.array-literal.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"block-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.bicep\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"@(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*(?=\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\",\\\"name\\\":\\\"meta.decorator.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"directive\\\":{\\\"begin\\\":\\\"#\\\\\\\\b[_a-zA-Z-0-9]+\\\\\\\\b\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"meta.directive.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#directive-variable\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"directive-variable\\\":{\\\"match\\\":\\\"\\\\\\\\b[_a-zA-Z-0-9]+\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.declaration.bicep\\\"},\\\"escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\\\\\\\\\|'|\\\\\\\\${)\\\",\\\"name\\\":\\\"constant.character.escape.bicep\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#string-verbatim\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"},{\\\"include\\\":\\\"#named-literal\\\"},{\\\"include\\\":\\\"#object-literal\\\"},{\\\"include\\\":\\\"#array-literal\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#function-call\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#lambda-start\\\"},{\\\"include\\\":\\\"#directive\\\"}]},\\\"function-call\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b)(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.bicep\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.function-call.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"identifier\\\":{\\\"match\\\":\\\"\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b(?!(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\()\\\",\\\"name\\\":\\\"variable.other.readwrite.bicep\\\"},\\\"keyword\\\":{\\\"match\\\":\\\"\\\\\\\\b(metadata|targetScope|resource|module|param|var|output|for|in|if|existing|import|as|type|with|using|extends|func|assert|extension)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.declaration.bicep\\\"},\\\"lambda-start\\\":{\\\"begin\\\":\\\"(\\\\\\\\((?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*(,(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*)*\\\\\\\\)|\\\\\\\\((?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\)|(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*)(?=(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*=>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.undefined.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"end\\\":\\\"(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*=>\\\",\\\"name\\\":\\\"meta.lambda-start.bicep\\\"},\\\"line-comment\\\":{\\\"match\\\":\\\"//.*(?=$)\\\",\\\"name\\\":\\\"comment.line.double-slash.bicep\\\"},\\\"named-literal\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.bicep\\\"},\\\"numeric-literal\\\":{\\\"match\\\":\\\"[0-9]+\\\",\\\"name\\\":\\\"constant.numeric.bicep\\\"},\\\"object-literal\\\":{\\\"begin\\\":\\\"{\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.object-literal.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#object-property-key\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"object-property-key\\\":{\\\"match\\\":\\\"\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b(?=(?:[ \\\\\\\\t\\\\\\\\r\\\\\\\\n]|\\\\\\\\/\\\\\\\\*(?:\\\\\\\\*(?!\\\\\\\\/)|[^*])*\\\\\\\\*\\\\\\\\/)*:)\\\",\\\"name\\\":\\\"variable.other.property.bicep\\\"},\\\"string-literal\\\":{\\\"begin\\\":\\\"'(?!'')\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-character\\\"},{\\\"include\\\":\\\"#string-literal-subst\\\"}]},\\\"string-literal-subst\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\${)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.bicep\\\"}},\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.bicep\\\"}},\\\"name\\\":\\\"meta.string-literal-subst.bicep\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"string-verbatim\\\":{\\\"begin\\\":\\\"'''\\\",\\\"end\\\":\\\"'''(?!')\\\",\\\"name\\\":\\\"string.quoted.multi.bicep\\\",\\\"patterns\\\":[]}},\\\"scopeName\\\":\\\"source.bicep\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/bicep.mjs\n"));

/***/ })

}]);