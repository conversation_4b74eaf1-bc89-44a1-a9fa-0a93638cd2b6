"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_scala_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/scala.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/scala.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Scala\\\",\\\"fileTypes\\\":[\\\"scala\\\"],\\\"firstLineMatch\\\":\\\"^#!/.*\\\\\\\\b\\\\\\\\w*scala\\\\\\\\b\\\",\\\"foldingStartMarker\\\":\\\"/\\\\\\\\*\\\\\\\\*|\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"\\\\\\\\*\\\\\\\\*/|^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}],\\\"repository\\\":{\\\"backQuotedVariable\\\":{\\\"match\\\":\\\"`[^`]+`\\\"},\\\"block-comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"}},\\\"match\\\":\\\"/\\\\\\\\*\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.empty.scala\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*(/\\\\\\\\*\\\\\\\\*)(?!/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"}},\\\"name\\\":\\\"comment.block.documentation.scala\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.documentation.scaladoc.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.scala\\\"}},\\\"match\\\":\\\"(@param)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.documentation.scaladoc.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class\\\"}},\\\"match\\\":\\\"(@(?:tparam|throws))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"@(return|see|note|example|constructor|usecase|author|version|since|todo|deprecated|migration|define|inheritdoc|groupname|groupprio|groupdesc|group|contentDiagram|documentable|syntax)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.documentation.scaladoc.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.documentation.link.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.other.link.title.markdown\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.documentation.link.scala\\\"}},\\\"match\\\":\\\"(\\\\\\\\[\\\\\\\\[)([^\\\\\\\\]]+)(\\\\\\\\]\\\\\\\\])\\\"},{\\\"include\\\":\\\"#block-comments\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-comments\\\"}]}]},\\\"char-literal\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.character.begin.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.character.end.scala\\\"}},\\\"match\\\":\\\"(')'(')\\\",\\\"name\\\":\\\"string.quoted.other constant.character.literal.scala\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.character.begin.scala\\\"}},\\\"end\\\":\\\"'|$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.character.end.scala\\\"}},\\\"name\\\":\\\"string.quoted.other constant.character.literal.scala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[btnfr\\\\\\\\\\\\\\\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-character-escape.scala\\\"},{\\\"match\\\":\\\"[^']{2,}\\\",\\\"name\\\":\\\"invalid.illegal.character-literal-too-long\\\"},{\\\"match\\\":\\\"(?<!')[^']\\\",\\\"name\\\":\\\"invalid.illegal.character-literal-too-long\\\"}]}]},\\\"code\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#using-directive\\\"},{\\\"include\\\":\\\"#script-header\\\"},{\\\"include\\\":\\\"#storage-modifiers\\\"},{\\\"include\\\":\\\"#declarations\\\"},{\\\"include\\\":\\\"#inheritance\\\"},{\\\"include\\\":\\\"#extension\\\"},{\\\"include\\\":\\\"#imports\\\"},{\\\"include\\\":\\\"#exports\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#initialization\\\"},{\\\"include\\\":\\\"#xml-literal\\\"},{\\\"include\\\":\\\"#namedBounds\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#using\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#singleton-type\\\"},{\\\"include\\\":\\\"#inline\\\"},{\\\"include\\\":\\\"#scala-quoted-or-symbol\\\"},{\\\"include\\\":\\\"#char-literal\\\"},{\\\"include\\\":\\\"#empty-parentheses\\\"},{\\\"include\\\":\\\"#parameter-list\\\"},{\\\"include\\\":\\\"#qualifiedClassName\\\"},{\\\"include\\\":\\\"#backQuotedVariable\\\"},{\\\"include\\\":\\\"#curly-braces\\\"},{\\\"include\\\":\\\"#meta-brackets\\\"},{\\\"include\\\":\\\"#meta-bounds\\\"},{\\\"include\\\":\\\"#meta-colons\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#block-comments\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.scala\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.scala\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(false|null|true)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b(0[xX][0-9a-fA-F_]*)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b(([0-9][0-9_]*(\\\\\\\\.[0-9][0-9_]*)?)([eE](\\\\\\\\+|-)?[0-9][0-9_]*)?|[0-9][0-9_]*)[LlFfDd]?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.scala\\\"},{\\\"match\\\":\\\"(\\\\\\\\.[0-9][0-9_]*)([eE](\\\\\\\\+|-)?[0-9][0-9_]*)?[LlFfDd]?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b0[bB][01]([01_]*[01])?[Ll]?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b(this|super)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.scala\\\"}]},\\\"curly-braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.scala\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.scala\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}]},\\\"declarations\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.declaration\\\"}},\\\"match\\\":\\\"\\\\\\\\b(def)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.declaration\\\"}},\\\"match\\\":\\\"\\\\\\\\b(trait)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.declaration\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(case)\\\\\\\\s+)?(class|object|enum)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.declaration\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(type)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))?\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.stable.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.volatile.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(val)|(var))\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)(?=(?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`)?\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.stable.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.stable.declaration.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\b(val)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`)(?:\\\\\\\\s*,\\\\\\\\s*(?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))*)?(?!\\\\\\\")\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.volatile.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.volatile.declaration.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\b(var)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`)(?:\\\\\\\\s*,\\\\\\\\s*(?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))*)?(?!\\\\\\\")\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.package.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.declaration\\\"}},\\\"match\\\":\\\"\\\\\\\\b(package)\\\\\\\\s+(object)\\\\\\\\b\\\\\\\\s*(?!//|/\\\\\\\\*)((?:(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`))?\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(package)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.package.scala\\\"}},\\\"end\\\":\\\"(?<=[\\\\\\\\n;])\\\",\\\"name\\\":\\\"meta.package.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+))\\\",\\\"name\\\":\\\"entity.name.package.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.definition.package\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.given.declaration\\\"}},\\\"match\\\":\\\"\\\\\\\\b(given)\\\\\\\\b\\\\\\\\s*([_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|`[^`]+`)?\\\"}]},\\\"empty-parentheses\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"match\\\":\\\"(\\\\\\\\(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.parentheses.scala\\\"},\\\"exports\\\":{\\\"begin\\\":\\\"\\\\\\\\b(export)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.export.scala\\\"}},\\\"end\\\":\\\"(?<=[\\\\\\\\n;])\\\",\\\"name\\\":\\\"meta.export.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(given)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.export.given.scala\\\"},{\\\"match\\\":\\\"[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?\\\",\\\"name\\\":\\\"entity.name.class.export.scala\\\"},{\\\"match\\\":\\\"(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+))\\\",\\\"name\\\":\\\"entity.name.export.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.definition.export\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"name\\\":\\\"meta.export.selector.scala\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.export.given.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.export.renamed-from.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.export.renamed-from.scala\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.arrow.scala\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.class.export.renamed-to.scala\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.export.renamed-to.scala\\\"}},\\\"match\\\":\\\"(given\\\\\\\\s)?\\\\\\\\s*(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\\\\\\s*(=>)\\\\\\\\s*(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\\\\\\s*\\\"},{\\\"match\\\":\\\"\\\\\\\\b(given)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.export.given.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.export.given.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.export.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.export.scala\\\"}},\\\"match\\\":\\\"(given\\\\\\\\s+)?(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\"}]}]},\\\"extension\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(extension)\\\\\\\\s+(?=[\\\\\\\\[\\\\\\\\(])\\\"}]},\\\"imports\\\":{\\\"begin\\\":\\\"\\\\\\\\b(import)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.import.scala\\\"}},\\\"end\\\":\\\"(?<=[\\\\\\\\n;])\\\",\\\"name\\\":\\\"meta.import.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(given)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.import.given.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\s(as)\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.other.import.as.scala\\\"},{\\\"match\\\":\\\"[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?\\\",\\\"name\\\":\\\"entity.name.class.import.scala\\\"},{\\\"match\\\":\\\"(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+))\\\",\\\"name\\\":\\\"entity.name.import.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.definition.import\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"name\\\":\\\"meta.import.selector.scala\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.import.given.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.import.renamed-from.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.import.renamed-from.scala\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.arrow.scala\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.class.import.renamed-to.scala\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.import.renamed-to.scala\\\"}},\\\"match\\\":\\\"(given\\\\\\\\s)?\\\\\\\\s*(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\\\\\\s*(=>)\\\\\\\\s*(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\\\\\\s*\\\"},{\\\"match\\\":\\\"\\\\\\\\b(given)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.import.given.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.import.given.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.import.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.import.scala\\\"}},\\\"match\\\":\\\"(given\\\\\\\\s+)?(?:([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)|(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))\\\"}]}]},\\\"inheritance\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class\\\"}},\\\"match\\\":\\\"\\\\\\\\b(extends|with|derives)\\\\\\\\b\\\\\\\\s*([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|`[^`]+`|(?=\\\\\\\\([^\\\\\\\\)]+=>)|(?=(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+))|(?=\\\\\\\"))?\\\"}]},\\\"initialization\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\b(new)\\\\\\\\b\\\"},\\\"inline\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(inline)(?=\\\\\\\\s+((?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)|`[^`]+`)\\\\\\\\s*:)\\\",\\\"name\\\":\\\"storage.modifier.other\\\"},{\\\"match\\\":\\\"\\\\\\\\b(inline)\\\\\\\\b(?=(?:.(?!\\\\\\\\b(?:val|def|given)\\\\\\\\b))*\\\\\\\\b(if|match)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.control.flow.scala\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(return|throw)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.jump.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b(classOf|isInstanceOf|asInstanceOf)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.type-of.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\b(else|if|then|do|while|for|yield|match|case)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.scala\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(if|while|for|match)(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\",\\\"name\\\":\\\"keyword.control.flow.end.scala\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(val)(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\",\\\"name\\\":\\\"keyword.declaration.stable.end.scala\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(var)(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\",\\\"name\\\":\\\"keyword.declaration.volatile.end.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.declaration.end.scala\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.declaration\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(?:(new|extension)|([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?))(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(catch|finally|try)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.exception.scala\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(try)(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\",\\\"name\\\":\\\"keyword.control.exception.end.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.declaration\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(end)\\\\\\\\s+(`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+))?(?=\\\\\\\\s*(//.*|/\\\\\\\\*(?!.*\\\\\\\\*/\\\\\\\\s*\\\\\\\\S.*).*)?$)\\\"},{\\\"match\\\":\\\"([!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]|[\\\\\\\\\\\\\\\\]){3,}\\\",\\\"name\\\":\\\"keyword.operator.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\|\\\\\\\\||&&)\\\",\\\"name\\\":\\\"keyword.operator.logical.scala\\\"},{\\\"match\\\":\\\"(\\\\\\\\!=|==|\\\\\\\\<=|>=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.scala\\\"},{\\\"match\\\":\\\"..\\\",\\\"name\\\":\\\"keyword.operator.scala\\\"}]}},\\\"match\\\":\\\"((?:[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]|[\\\\\\\\\\\\\\\\]){2,}|_\\\\\\\\*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\!)\\\",\\\"name\\\":\\\"keyword.operator.logical.scala\\\"},{\\\"match\\\":\\\"(\\\\\\\\*|-|\\\\\\\\+|/|%|~)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.scala\\\"},{\\\"match\\\":\\\"(=|\\\\\\\\<|>)\\\",\\\"name\\\":\\\"keyword.operator.comparison.scala\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"keyword.operator.scala\\\"}]}},\\\"match\\\":\\\"(?<!_)([!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]|\\\\\\\\\\\\\\\\)\\\"}]},\\\"meta-bounds\\\":{\\\"comment\\\":\\\"For themes: Matching view bounds\\\",\\\"match\\\":\\\"<%|=:=|<:<|<%<|>:|<:\\\",\\\"name\\\":\\\"meta.bounds.scala\\\"},\\\"meta-brackets\\\":{\\\"comment\\\":\\\"For themes: Brackets look nice when colored.\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"The punctuation.section.*.begin is needed for return snippet in source bundle\\\",\\\"match\\\":\\\"\\\\\\\\{\\\",\\\"name\\\":\\\"punctuation.section.block.begin.scala\\\"},{\\\"comment\\\":\\\"The punctuation.section.*.end is needed for return snippet in source bundle\\\",\\\"match\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"punctuation.section.block.end.scala\\\"},{\\\"match\\\":\\\"{|}|\\\\\\\\(|\\\\\\\\)|\\\\\\\\[|\\\\\\\\]\\\",\\\"name\\\":\\\"meta.bracket.scala\\\"}]},\\\"meta-colons\\\":{\\\"comment\\\":\\\"For themes: Matching type colons\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!:):(?!:)\\\",\\\"name\\\":\\\"meta.colon.scala\\\"}]},\\\"namedBounds\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.import.as.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.stable.declaration.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\s+(as)\\\\\\\\s+([_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)\\\\\\\\b\\\"}]},\\\"parameter-list\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.colon.scala\\\"}},\\\"match\\\":\\\"(?<=[^\\\\\\\\._$a-zA-Z0-9])(`[^`]+`|[_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)\\\\\\\\s*(:)\\\\\\\\s+\\\"}]},\\\"qualifiedClassName\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class\\\"}},\\\"match\\\":\\\"(\\\\\\\\b([A-Z][\\\\\\\\w]*)(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?)\\\"},\\\"scala-quoted-or-symbol\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.staging.scala constant.other.symbol.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.symbol.scala\\\"}},\\\"match\\\":\\\"(')((?>(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)))(?!')\\\"},{\\\"match\\\":\\\"'(?=\\\\\\\\s*\\\\\\\\{(?!'))\\\",\\\"name\\\":\\\"keyword.control.flow.staging.scala\\\"},{\\\"match\\\":\\\"'(?=\\\\\\\\s*\\\\\\\\[(?!'))\\\",\\\"name\\\":\\\"keyword.control.flow.staging.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\$(?=\\\\\\\\s*\\\\\\\\{)\\\",\\\"name\\\":\\\"keyword.control.flow.staging.scala\\\"}]},\\\"script-header\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.unquoted.shebang.scala\\\"}},\\\"match\\\":\\\"^#!(.*)$\\\",\\\"name\\\":\\\"comment.block.shebang.scala\\\"},\\\"singleton-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.type.scala\\\"}},\\\"match\\\":\\\"\\\\\\\\.(type)(?![A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[0-9])\\\"},\\\"storage-modifiers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(private\\\\\\\\[\\\\\\\\S+\\\\\\\\]|protected\\\\\\\\[\\\\\\\\S+\\\\\\\\]|private|protected)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.access\\\"},{\\\"match\\\":\\\"\\\\\\\\b(synchronized|@volatile|abstract|final|lazy|sealed|implicit|override|@transient|@native)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.other\\\"},{\\\"match\\\":\\\"(?<=^|\\\\\\\\s)\\\\\\\\b(transparent|opaque|infix|open|inline)\\\\\\\\b(?=[a-z\\\\\\\\s]*\\\\\\\\b(def|val|var|given|type|class|trait|object|enum)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.modifier.other\\\"}]},\\\"string-interpolation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$\\\\\\\\$\\\",\\\"name\\\":\\\"constant.character.escape.interpolation.scala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.scala\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)([A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*)\\\",\\\"name\\\":\\\"meta.template.expression.scala\\\"},{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.scala\\\"}},\\\"contentName\\\":\\\"meta.embedded.line.scala\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.scala\\\"}},\\\"name\\\":\\\"meta.template.expression.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}]}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scala\\\"}},\\\"name\\\":\\\"string.quoted.triple.scala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\|\\\\\\\\\\\\\\\\u[0-9A-Fa-f]{4}\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(raw)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.triple.interpolated.scala punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")(?!\\\\\\\")|\\\\\\\\$\\\\n|(\\\\\\\\$[^\\\\\\\\$\\\\\\\"_{A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.quoted.triple.interpolated.scala punctuation.definition.string.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\$\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"include\\\":\\\"#string-interpolation\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.triple.interpolated.scala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?))(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.triple.interpolated.scala punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")(?!\\\\\\\")|\\\\\\\\$\\\\n|(\\\\\\\\$[^\\\\\\\\$\\\\\\\"_{A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.quoted.triple.interpolated.scala punctuation.definition.string.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#string-interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\|\\\\\\\\\\\\\\\\u[0-9A-Fa-f]{4}\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.triple.interpolated.scala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scala\\\"}},\\\"name\\\":\\\"string.quoted.double.scala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[btnfr\\\\\\\\\\\\\\\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(raw)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.interpolated.scala punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"(\\\\\\\")|\\\\\\\\$\\\\n|(\\\\\\\\$[^\\\\\\\\$\\\\\\\"_{A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.quoted.double.interpolated.scala punctuation.definition.string.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\$\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"include\\\":\\\"#string-interpolation\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.interpolated.scala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?))(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.interpolated.scala punctuation.definition.string.begin.scala\\\"}},\\\"end\\\":\\\"(\\\\\\\")|\\\\\\\\$\\\\n|(\\\\\\\\$[^\\\\\\\\$\\\\\\\"_{A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.quoted.double.interpolated.scala punctuation.definition.string.end.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\$\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"include\\\":\\\"#string-interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[btnfr\\\\\\\\\\\\\\\\\\\\\\\"']|[0-7]{1,3}|u[0-9A-Fa-f]{4})\\\",\\\"name\\\":\\\"constant.character.escape.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.scala\\\"},{\\\"match\\\":\\\".\\\",\\\"name\\\":\\\"string.quoted.double.interpolated.scala\\\"}]}]},\\\"using\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.declaration.scala\\\"}},\\\"match\\\":\\\"(?<=\\\\\\\\()\\\\\\\\s*(using)\\\\\\\\s\\\"}]},\\\"using-directive\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(//>)\\\\\\\\s*(using)[^\\\\\\\\S\\\\\\\\n]+(?:(\\\\\\\\S+))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scala\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.import.scala\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|`[^`]+`|(?:[A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}][A-Z\\\\\\\\p{Lt}\\\\\\\\p{Lu}_a-z\\\\\\\\$\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Ll}0-9]*(?:(?<=_)[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)?|[!#%&*+\\\\\\\\-\\\\\\\\/:<>=?@^|~\\\\\\\\p{Sm}\\\\\\\\p{So}]+)\\\",\\\"name\\\":\\\"entity.name.import.scala\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.definition.import\\\"}]}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.shebang.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]+\\\",\\\"name\\\":\\\"string.quoted.double.scala\\\"}]},\\\"xml-doublequotedString\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.xml\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.xml\\\"}},\\\"name\\\":\\\"string.quoted.double.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#xml-entity\\\"}]},\\\"xml-embedded-content\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.scala\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.source.embedded.scala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.namespace.xml\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.xml\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.xml\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.other.attribute-name.localname.xml\\\"}},\\\"match\\\":\\\" (?:([-_a-zA-Z0-9]+)((:)))?([_a-zA-Z-]+)=\\\"},{\\\"include\\\":\\\"#xml-doublequotedString\\\"},{\\\"include\\\":\\\"#xml-singlequotedString\\\"}]},\\\"xml-entity\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.xml\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.constant.xml\\\"}},\\\"match\\\":\\\"(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#[0-9]+|#x[0-9a-fA-F]+)(;)\\\",\\\"name\\\":\\\"constant.character.entity.xml\\\"},\\\"xml-literal\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(<)((?:([_a-zA-Z0-9][_a-zA-Z0-9]*)((:)))?([_a-zA-Z0-9][-_a-zA-Z0-9:]*))(?=(\\\\\\\\s[^>]*)?></\\\\\\\\2>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.xml\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.xml\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.xml\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.xml\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.tag.localname.xml\\\"}},\\\"comment\\\":\\\"We do not allow a tag name to start with a - since this would likely conflict with the <- operator. This is not very common for tag names anyway.  Also code such as -- if (val <val2 || val> val3) will falsly be recognized as an xml tag.  The solution is to put a space on either side of the comparison operator\\\",\\\"end\\\":\\\"(>(<))/(?:([-_a-zA-Z0-9]+)((:)))?([-_a-zA-Z0-9:]*[_a-zA-Z0-9])(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.xml\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.scope.between-tag-pair.xml\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.xml\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.xml\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.xml\\\"},\\\"6\\\":{\\\"name\\\":\\\"entity.name.tag.localname.xml\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.tag.xml\\\"}},\\\"name\\\":\\\"meta.tag.no-content.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#xml-embedded-content\\\"}]},{\\\"begin\\\":\\\"(</?)(?:([_a-zA-Z0-9][-_a-zA-Z0-9]*)((:)))?([_a-zA-Z0-9][-_a-zA-Z0-9:]*)(?=[^>]*?>)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.xml\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.namespace.xml\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.tag.xml\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.xml\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.tag.localname.xml\\\"}},\\\"end\\\":\\\"(/?>)\\\",\\\"name\\\":\\\"meta.tag.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#xml-embedded-content\\\"}]},{\\\"include\\\":\\\"#xml-entity\\\"}]},\\\"xml-singlequotedString\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.xml\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.xml\\\"}},\\\"name\\\":\\\"string.quoted.single.xml\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#xml-entity\\\"}]}},\\\"scopeName\\\":\\\"source.scala\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/scala.mjs\n"));

/***/ })

}]);