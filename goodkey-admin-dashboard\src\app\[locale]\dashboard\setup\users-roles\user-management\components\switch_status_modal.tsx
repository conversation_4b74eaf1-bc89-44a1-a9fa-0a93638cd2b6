import { useMutation } from '@tanstack/react-query';

import UsersQuery from '@/services/queries/UsersQuery';
import { getQueryClient } from '@/utils/query-client';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';

interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  userId: number;
}
export default function SwitchStatusModal({
  close,
  isChecked,
  userId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: UsersQuery.tags,
    mutationFn: UsersQuery.switchStatus,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });
      close();
    },
  });

  const confirmText = isChecked ? 'Archive' : 'Unarchive';

  return (
    <AlertDialog open={true} onOpenChange={close}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isChecked ? 'Archive User' : 'Unarchive User'}
          </AlertDialogTitle>
        </AlertDialogHeader>
        <div className="py-2 text-slate-700">
          {isChecked
            ? "This user will be archived and won't appear in the main list."
            : 'This user will be unarchived and will appear in the main list.'}
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button variant="outline" disabled={isPending} iconName="ClearIcon">
              Cancel
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={() => mutate(userId)}
              disabled={isPending}
              variant={isChecked ? 'destructive' : 'primary'}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{
                className: 'text-white',
              }}
            >
              {isPending ? 'Please wait...' : confirmText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
