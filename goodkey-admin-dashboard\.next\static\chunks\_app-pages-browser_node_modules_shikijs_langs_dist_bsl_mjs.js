"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_bsl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/bsl.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/bsl.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sdbl_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sdbl.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Enterprise)\\\",\\\"fileTypes\\\":[\\\"bsl\\\",\\\"os\\\"],\\\"name\\\":\\\"bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"},{\\\"begin\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Процедура|Procedure|Функция|Function)\\\\\\\\s+([a-zа-яё0-9_]+)\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.bsl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"}},\\\"comment\\\":\\\"Proc and function definition\\\",\\\"end\\\":\\\"(?i:(\\\\\\\\))\\\\\\\\s*((Экспорт|Export)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))?)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.bsl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#annotations\\\"},{\\\"include\\\":\\\"#basic\\\"},{\\\"match\\\":\\\"(=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Знач|Val)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"storage.modifier.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)((?<==)(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)((?<==\\\\\\\\s)\\\\\\\\s*(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?i:[a-zа-яё0-9_]+)\\\",\\\"name\\\":\\\"variable.parameter.bsl\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Перем|Var)\\\\\\\\s+([a-zа-яё0-9_]+)\\\\\\\\s*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.var.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.bsl\\\"}},\\\"comment\\\":\\\"Define of variable\\\",\\\"end\\\":\\\"(;)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.bsl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Экспорт|Export)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"storage.modifier.bsl\\\"},{\\\"match\\\":\\\"(?i:[a-zа-яё0-9_]+)\\\",\\\"name\\\":\\\"variable.bsl\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=;|^)\\\\\\\\s*(Если|If))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"}},\\\"comment\\\":\\\"Conditional\\\",\\\"end\\\":\\\"(?i:(Тогда|Then))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"}},\\\"name\\\":\\\"meta.conditional.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"}]},{\\\"begin\\\":\\\"(?i:(?<=;|^)\\\\\\\\s*([\\\\\\\\wа-яё]+))\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.assignment.bsl\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"}},\\\"comment\\\":\\\"Variable assignment\\\",\\\"end\\\":\\\"(?i:(?=(;|Иначе|Конец|Els|End)))\\\",\\\"name\\\":\\\"meta.var-single-variable.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"include\\\":\\\"#miscellaneous\\\"}]},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(КонецПроцедуры|EndProcedure|КонецФункции|EndFunction)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"storage.type.bsl\\\"},{\\\"match\\\":\\\"(?i)#(Использовать|Use)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.control.import.bsl\\\"},{\\\"match\\\":\\\"(?i)#native\\\",\\\"name\\\":\\\"keyword.control.native.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Прервать|Break|Продолжить|Continue|Возврат|Return)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"keyword.control.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Если|If|Иначе|Else|ИначеЕсли|ElsIf|Тогда|Then|КонецЕсли|EndIf)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"keyword.control.conditional.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Попытка|Try|Исключение|Except|КонецПопытки|EndTry|ВызватьИсключение|Raise)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"keyword.control.exception.bsl\\\"},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Пока|While|(Для|For)(\\\\\\\\s+(Каждого|Each))?|Из|In|По|To|Цикл|Do|КонецЦикла|EndDo)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"keyword.control.repeat.bsl\\\"},{\\\"match\\\":\\\"(?i:&(НаКлиенте((НаСервере(БезКонтекста)?)?)|AtClient((AtServer(NoContext)?)?)|НаСервере(БезКонтекста)?|AtServer(NoContext)?))\\\",\\\"name\\\":\\\"storage.modifier.directive.bsl\\\"},{\\\"include\\\":\\\"#annotations\\\"},{\\\"match\\\":\\\"(?i:#(Если|If|ИначеЕсли|ElsIf|Иначе|Else|КонецЕсли|EndIf).*(Тогда|Then)?)\\\",\\\"name\\\":\\\"keyword.other.preprocessor.bsl\\\"},{\\\"begin\\\":\\\"(?i)(#(Область|Region))(\\\\\\\\s+([\\\\\\\\wа-яё]+))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.section.bsl\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.section.bsl\\\"}},\\\"comment\\\":\\\"Region start\\\",\\\"end\\\":\\\"$\\\"},{\\\"comment\\\":\\\"Region end\\\",\\\"match\\\":\\\"(?i)#(КонецОбласти|EndRegion)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"comment\\\":\\\"Delete start\\\",\\\"match\\\":\\\"(?i)#(Удаление|Delete)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"comment\\\":\\\"Delete end\\\",\\\"match\\\":\\\"(?i)#(КонецУдаления|EndDelete)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"comment\\\":\\\"Inster start\\\",\\\"match\\\":\\\"(?i)#(Вставка|Insert)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"},{\\\"comment\\\":\\\"Insert end\\\",\\\"match\\\":\\\"(?i)#(КонецВставки|EndInsert)\\\",\\\"name\\\":\\\"keyword.other.section.bsl\\\"}],\\\"repository\\\":{\\\"annotations\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(&([a-zа-яё0-9_]+))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.annotation.bsl\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"}},\\\"comment\\\":\\\"Annotations with parameters\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#basic\\\"},{\\\"match\\\":\\\"(=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)((?<==)(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)((?<==\\\\\\\\s)\\\\\\\\s*(?i)[a-zа-яё0-9_]+)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"invalid.illegal.bsl\\\"},{\\\"match\\\":\\\"(?i)[a-zа-яё0-9_]+\\\",\\\"name\\\":\\\"variable.annotation.bsl\\\"}]},{\\\"comment\\\":\\\"Annotations without parameters\\\",\\\"match\\\":\\\"(?i)(&([a-zа-яё0-9_]+))\\\",\\\"name\\\":\\\"storage.type.annotation.bsl\\\"}]},\\\"basic\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\\\\\\\\\"(?![\\\\\\\\\\\\\\\"])\\\",\\\"name\\\":\\\"string.quoted.double.bsl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#query\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.bsl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"}]},{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"constant.language.bsl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"constant.numeric.bsl\\\"},{\\\"match\\\":\\\"\\\\\\\\'((\\\\\\\\d{4}[^\\\\\\\\d\\\\\\\\']*\\\\\\\\d{2}[^\\\\\\\\d\\\\\\\\']*\\\\\\\\d{2})([^\\\\\\\\d\\\\\\\\']*\\\\\\\\d{2}[^\\\\\\\\d\\\\\\\\']*\\\\\\\\d{2}([^\\\\\\\\d\\\\\\\\']*\\\\\\\\d{2})?)?)\\\\\\\\'\\\",\\\"name\\\":\\\"constant.other.date.bsl\\\"},{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\()\\\",\\\"name\\\":\\\"punctuation.bracket.begin.bsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\))\\\",\\\"name\\\":\\\"punctuation.bracket.end.bsl\\\"}]},\\\"miscellaneous\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(НЕ|NOT|И|AND|ИЛИ|OR)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"keyword.operator.logical.bsl\\\"},{\\\"match\\\":\\\"<=|>=|=|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.bsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|-|\\\\\\\\*|/|%)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.bsl\\\"},{\\\"match\\\":\\\"(;|\\\\\\\\?)\\\",\\\"name\\\":\\\"keyword.operator.bsl\\\"},{\\\"comment\\\":\\\"Functions w/o brackets\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Новый|New)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$))\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции работы со значениями типа Строка\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(СтрДлина|StrLen|СокрЛ|TrimL|СокрП|TrimR|СокрЛП|TrimAll|Лев|Left|Прав|Right|Сред|Mid|СтрНайти|StrFind|ВРег|Upper|НРег|Lower|ТРег|Title|Символ|Char|КодСимвола|CharCode|ПустаяСтрока|IsBlankString|СтрЗаменить|StrReplace|СтрЧислоСтрок|StrLineCount|СтрПолучитьСтроку|StrGetLine|СтрЧислоВхождений|StrOccurrenceCount|СтрСравнить|StrCompare|СтрНачинаетсяС|StrStartWith|СтрЗаканчиваетсяНа|StrEndsWith|СтрРазделить|StrSplit|СтрСоединить|StrConcat)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции работы со значениями типа Число\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Цел|Int|Окр|Round|ACos|ASin|ATan|Cos|Exp|Log|Log10|Pow|Sin|Sqrt|Tan)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции работы со значениями типа Дата\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Год|Year|Месяц|Month|День|Day|Час|Hour|Минута|Minute|Секунда|Second|НачалоГода|BegOfYear|НачалоДня|BegOfDay|НачалоКвартала|BegOfQuarter|НачалоМесяца|BegOfMonth|НачалоМинуты|BegOfMinute|НачалоНедели|BegOfWeek|НачалоЧаса|BegOfHour|КонецГода|EndOfYear|КонецДня|EndOfDay|КонецКвартала|EndOfQuarter|КонецМесяца|EndOfMonth|КонецМинуты|EndOfMinute|КонецНедели|EndOfWeek|КонецЧаса|EndOfHour|НеделяГода|WeekOfYear|ДеньГода|DayOfYear|ДеньНедели|WeekDay|ТекущаяДата|CurrentDate|ДобавитьМесяц|AddMonth)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции работы со значениями типа Тип\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Тип|Type|ТипЗнч|TypeOf)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции преобразования значений\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Булево|Boolean|Число|Number|Строка|String|Дата|Date)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - процедуры и функции интерактивной работы\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПоказатьВопрос|ShowQueryBox|Вопрос|DoQueryBox|ПоказатьПредупреждение|ShowMessageBox|Предупреждение|DoMessageBox|Сообщить|Message|ОчиститьСообщения|ClearMessages|ОповеститьОбИзменении|NotifyChanged|Состояние|Status|Сигнал|Beep|ПоказатьЗначение|ShowValue|ОткрытьЗначение|OpenValue|Оповестить|Notify|ОбработкаПрерыванияПользователя|UserInterruptProcessing|ОткрытьСодержаниеСправки|OpenHelpContent|ОткрытьИндексСправки|OpenHelpIndex|ОткрытьСправку|OpenHelp|ПоказатьИнформациюОбОшибке|ShowErrorInfo|КраткоеПредставлениеОшибки|BriefErrorDescription|ПодробноеПредставлениеОшибки|DetailErrorDescription|ПолучитьФорму|GetForm|ЗакрытьСправку|CloseHelp|ПоказатьОповещениеПользователя|ShowUserNotification|ОткрытьФорму|OpenForm|ОткрытьФормуМодально|OpenFormModal|АктивноеОкно|ActiveWindow|ВыполнитьОбработкуОповещения|ExecuteNotifyProcessing)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции для вызова диалога ввода данных\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПоказатьВводЗначения|ShowInputValue|ВвестиЗначение|InputValue|ПоказатьВводЧисла|ShowInputNumber|ВвестиЧисло|InputNumber|ПоказатьВводСтроки|ShowInputString|ВвестиСтроку|InputString|ПоказатьВводДаты|ShowInputDate|ВвестиДату|InputDate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции форматирования\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Формат|Format|ЧислоПрописью|NumberInWords|НСтр|NStr|ПредставлениеПериода|PeriodPresentation|СтрШаблон|StrTemplate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - функции обращения к конфигурации\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПолучитьОбщийМакет|GetCommonTemplate|ПолучитьОбщуюФорму|GetCommonForm|ПредопределенноеЗначение|PredefinedValue|ПолучитьПолноеИмяПредопределенногоЗначения|GetPredefinedValueFullName)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - процедуры и функции сеанса работы\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПолучитьЗаголовокСистемы|GetCaption|ПолучитьСкоростьКлиентскогоСоединения|GetClientConnectionSpeed|ПодключитьОбработчикОжидания|AttachIdleHandler|УстановитьЗаголовокСистемы|SetCaption|ОтключитьОбработчикОжидания|DetachIdleHandler|ИмяКомпьютера|ComputerName|ЗавершитьРаботуСистемы|Exit|ИмяПользователя|UserName|ПрекратитьРаботуСистемы|Terminate|ПолноеИмяПользователя|UserFullName|ЗаблокироватьРаботуПользователя|LockApplication|КаталогПрограммы|BinDir|КаталогВременныхФайлов|TempFilesDir|ПравоДоступа|AccessRight|РольДоступна|IsInRole|ТекущийЯзык|CurrentLanguage|ТекущийКодЛокализации|CurrentLocaleCode|СтрокаСоединенияИнформационнойБазы|InfoBaseConnectionString|ПодключитьОбработчикОповещения|AttachNotificationHandler|ОтключитьОбработчикОповещения|DetachNotificationHandler|ПолучитьСообщенияПользователю|GetUserMessages|ПараметрыДоступа|AccessParameters|ПредставлениеПриложения|ApplicationPresentation|ТекущийЯзыкСистемы|CurrentSystemLanguage|ЗапуститьСистему|RunSystem|ТекущийРежимЗапуска|CurrentRunMode|УстановитьЧасовойПоясСеанса|SetSessionTimeZone|ЧасовойПоясСеанса|SessionTimeZone|ТекущаяДатаСеанса|CurrentSessionDate|УстановитьКраткийЗаголовокПриложения|SetShortApplicationCaption|ПолучитьКраткийЗаголовокПриложения|GetShortApplicationCaption|ПредставлениеПрава|RightPresentation|ВыполнитьПроверкуПравДоступа|VerifyAccessRights|РабочийКаталогДанныхПользователя|UserDataWorkDir|КаталогДокументов|DocumentsDir|ПолучитьИнформациюЭкрановКлиента|GetClientDisplaysInformation|ТекущийВариантОсновногоШрифтаКлиентскогоПриложения|ClientApplicationBaseFontCurrentVariant|ТекущийВариантИнтерфейсаКлиентскогоПриложения|ClientApplicationInterfaceCurrentVariant|УстановитьЗаголовокКлиентскогоПриложения|SetClientApplicationCaption|ПолучитьЗаголовокКлиентскогоПриложения|GetClientApplicationCaption|НачатьПолучениеКаталогаВременныхФайлов|BeginGettingTempFilesDir|НачатьПолучениеКаталогаДокументов|BeginGettingDocumentsDir|НачатьПолучениеРабочегоКаталогаДанныхПользователя|BeginGettingUserDataWorkDir|ПодключитьОбработчикЗапросаНастроекКлиентаЛицензирования|AttachLicensingClientParametersRequestHandler|ОтключитьОбработчикЗапросаНастроекКлиентаЛицензирования|DetachLicensingClientParametersRequestHandler|КаталогБиблиотекиМобильногоУстройства|MobileDeviceLibraryDir)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - процедуры и функции сохранения значений\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЗначениеВСтрокуВнутр|ValueToStringInternal|ЗначениеИзСтрокиВнутр|ValueFromStringInternal|ЗначениеВФайл|ValueToFile|ЗначениеИзФайла|ValueFromFile)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с операционной системой\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(КомандаСистемы|System|ЗапуститьПриложение|RunApp|ПолучитьCOMОбъект|GetCOMObject|ПользователиОС|OSUsers|НачатьЗапускПриложения|BeginRunningApplication)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с внешними компонентами\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПодключитьВнешнююКомпоненту|AttachAddIn|НачатьУстановкуВнешнейКомпоненты|BeginInstallAddIn|УстановитьВнешнююКомпоненту|InstallAddIn|НачатьПодключениеВнешнейКомпоненты|BeginAttachingAddIn)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с файлами\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(КопироватьФайл|FileCopy|ПереместитьФайл|MoveFile|УдалитьФайлы|DeleteFiles|НайтиФайлы|FindFiles|СоздатьКаталог|CreateDirectory|ПолучитьИмяВременногоФайла|GetTempFileName|РазделитьФайл|SplitFile|ОбъединитьФайлы|MergeFiles|ПолучитьФайл|GetFile|НачатьПомещениеФайла|BeginPutFile|ПоместитьФайл|PutFile|ЭтоАдресВременногоХранилища|IsTempStorageURL|УдалитьИзВременногоХранилища|DeleteFromTempStorage|ПолучитьИзВременногоХранилища|GetFromTempStorage|ПоместитьВоВременноеХранилище|PutToTempStorage|ПодключитьРасширениеРаботыСФайлами|AttachFileSystemExtension|НачатьУстановкуРасширенияРаботыСФайлами|BeginInstallFileSystemExtension|УстановитьРасширениеРаботыСФайлами|InstallFileSystemExtension|ПолучитьФайлы|GetFiles|ПоместитьФайлы|PutFiles|ЗапроситьРазрешениеПользователя|RequestUserPermission|ПолучитьМаскуВсеФайлы|GetAllFilesMask|ПолучитьМаскуВсеФайлыКлиента|GetClientAllFilesMask|ПолучитьМаскуВсеФайлыСервера|GetServerAllFilesMask|ПолучитьРазделительПути|GetPathSeparator|ПолучитьРазделительПутиКлиента|GetClientPathSeparator|ПолучитьРазделительПутиСервера|GetServerPathSeparator|НачатьПодключениеРасширенияРаботыСФайлами|BeginAttachingFileSystemExtension|НачатьЗапросРазрешенияПользователя|BeginRequestingUserPermission|НачатьПоискФайлов|BeginFindingFiles|НачатьСозданиеКаталога|BeginCreatingDirectory|НачатьКопированиеФайла|BeginCopyingFile|НачатьПеремещениеФайла|BeginMovingFile|НачатьУдалениеФайлов|BeginDeletingFiles|НачатьПолучениеФайлов|BeginGettingFiles|НачатьПомещениеФайлов|BeginPuttingFiles|НачатьСозданиеДвоичныхДанныхИзФайла|BeginCreateBinaryDataFromFile)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с информационной базой\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(НачатьТранзакцию|BeginTransaction|ЗафиксироватьТранзакцию|CommitTransaction|ОтменитьТранзакцию|RollbackTransaction|УстановитьМонопольныйРежим|SetExclusiveMode|МонопольныйРежим|ExclusiveMode|ПолучитьОперативнуюОтметкуВремени|GetRealTimeTimestamp|ПолучитьСоединенияИнформационнойБазы|GetInfoBaseConnections|НомерСоединенияИнформационнойБазы|InfoBaseConnectionNumber|КонфигурацияИзменена|ConfigurationChanged|КонфигурацияБазыДанныхИзмененаДинамически|DataBaseConfigurationChangedDynamically|УстановитьВремяОжиданияБлокировкиДанных|SetLockWaitTime|ОбновитьНумерациюОбъектов|RefreshObjectsNumbering|ПолучитьВремяОжиданияБлокировкиДанных|GetLockWaitTime|КодЛокализацииИнформационнойБазы|InfoBaseLocaleCode|УстановитьМинимальнуюДлинуПаролейПользователей|SetUserPasswordMinLength|ПолучитьМинимальнуюДлинуПаролейПользователей|GetUserPasswordMinLength|ИнициализироватьПредопределенныеДанные|InitializePredefinedData|УдалитьДанныеИнформационнойБазы|EraseInfoBaseData|УстановитьПроверкуСложностиПаролейПользователей|SetUserPasswordStrengthCheck|ПолучитьПроверкуСложностиПаролейПользователей|GetUserPasswordStrengthCheck|ПолучитьСтруктуруХраненияБазыДанных|GetDBStorageStructureInfo|УстановитьПривилегированныйРежим|SetPrivilegedMode|ПривилегированныйРежим|PrivilegedMode|ТранзакцияАктивна|TransactionActive|НеобходимостьЗавершенияСоединения|ConnectionStopRequest|НомерСеансаИнформационнойБазы|InfoBaseSessionNumber|ПолучитьСеансыИнформационнойБазы|GetInfoBaseSessions|ЗаблокироватьДанныеДляРедактирования|LockDataForEdit|УстановитьСоединениеСВнешнимИсточникомДанных|ConnectExternalDataSource|РазблокироватьДанныеДляРедактирования|UnlockDataForEdit|РазорватьСоединениеСВнешнимИсточникомДанных|DisconnectExternalDataSource|ПолучитьБлокировкуСеансов|GetSessionsLock|УстановитьБлокировкуСеансов|SetSessionsLock|ОбновитьПовторноИспользуемыеЗначения|RefreshReusableValues|УстановитьБезопасныйРежим|SetSafeMode|БезопасныйРежим|SafeMode|ПолучитьДанныеВыбора|GetChoiceData|УстановитьЧасовойПоясИнформационнойБазы|SetInfoBaseTimeZone|ПолучитьЧасовойПоясИнформационнойБазы|GetInfoBaseTimeZone|ПолучитьОбновлениеКонфигурацииБазыДанных|GetDataBaseConfigurationUpdate|УстановитьБезопасныйРежимРазделенияДанных|SetDataSeparationSafeMode|БезопасныйРежимРазделенияДанных|DataSeparationSafeMode|УстановитьВремяЗасыпанияПассивногоСеанса|SetPassiveSessionHibernateTime|ПолучитьВремяЗасыпанияПассивногоСеанса|GetPassiveSessionHibernateTime|УстановитьВремяЗавершенияСпящегоСеанса|SetHibernateSessionTerminateTime|ПолучитьВремяЗавершенияСпящегоСеанса|GetHibernateSessionTerminateTime|ПолучитьТекущийСеансИнформационнойБазы|GetCurrentInfoBaseSession|ПолучитьИдентификаторКонфигурации|GetConfigurationID|УстановитьНастройкиКлиентаЛицензирования|SetLicensingClientParameters|ПолучитьИмяКлиентаЛицензирования|GetLicensingClientName|ПолучитьДополнительныйПараметрКлиентаЛицензирования|GetLicensingClientAdditionalParameter|ПолучитьОтключениеБезопасногоРежима|GetSafeModeDisabled|УстановитьОтключениеБезопасногоРежима|SetSafeModeDisabled)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с данными информационной базы\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(НайтиПомеченныеНаУдаление|FindMarkedForDeletion|НайтиПоСсылкам|FindByRef|УдалитьОбъекты|DeleteObjects|УстановитьОбновлениеПредопределенныхДанныхИнформационнойБазы|SetInfoBasePredefinedDataUpdate|ПолучитьОбновлениеПредопределенныхДанныхИнформационнойБазы|GetInfoBasePredefinedData)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с XML\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(XMLСтрока|XMLString|XMLЗначение|XMLValue|XMLТип|XMLType|XMLТипЗнч|XMLTypeOf|ИзXMLТипа|FromXMLType|ВозможностьЧтенияXML|CanReadXML|ПолучитьXMLТип|GetXMLType|ПрочитатьXML|ReadXML|ЗаписатьXML|WriteXML|НайтиНедопустимыеСимволыXML|FindDisallowedXMLCharacters|ИмпортМоделиXDTO|ImportXDTOModel|СоздатьФабрикуXDTO|CreateXDTOFactory)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с JSON\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЗаписатьJSON|WriteJSON|ПрочитатьJSON|ReadJSON|ПрочитатьДатуJSON|ReadJSONDate|ЗаписатьДатуJSON|WriteJSONDate)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с журналом регистрации\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЗаписьЖурналаРегистрации|WriteLogEvent|ПолучитьИспользованиеЖурналаРегистрации|GetEventLogUsing|УстановитьИспользованиеЖурналаРегистрации|SetEventLogUsing|ПредставлениеСобытияЖурналаРегистрации|EventLogEventPresentation|ВыгрузитьЖурналРегистрации|UnloadEventLog|ПолучитьЗначенияОтбораЖурналаРегистрации|GetEventLogFilterValues|УстановитьИспользованиеСобытияЖурналаРегистрации|SetEventLogEventUse|ПолучитьИспользованиеСобытияЖурналаРегистрации|GetEventLogEventUse|СкопироватьЖурналРегистрации|CopyEventLog|ОчиститьЖурналРегистрации|ClearEventLog)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с универсальными объектами\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЗначениеВДанныеФормы|ValueToFormData|ДанныеФормыВЗначение|FormDataToValue|КопироватьДанныеФормы|CopyFormData|УстановитьСоответствиеОбъектаИФормы|SetObjectAndFormConformity|ПолучитьСоответствиеОбъектаИФормы|GetObjectAndFormConformity)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с функциональными опциями\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПолучитьФункциональнуюОпцию|GetFunctionalOption|ПолучитьФункциональнуюОпциюИнтерфейса|GetInterfaceFunctionalOption|УстановитьПараметрыФункциональныхОпцийИнтерфейса|SetInterfaceFunctionalOptionParameters|ПолучитьПараметрыФункциональныхОпцийИнтерфейса|GetInterfaceFunctionalOptionParameters|ОбновитьИнтерфейс|RefreshInterface)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с криптографией\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(УстановитьРасширениеРаботыСКриптографией|InstallCryptoExtension|НачатьУстановкуРасширенияРаботыСКриптографией|BeginInstallCryptoExtension|ПодключитьРасширениеРаботыСКриптографией|AttachCryptoExtension|НачатьПодключениеРасширенияРаботыСКриптографией|BeginAttachingCryptoExtension)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы со стандартным интерфейсом OData\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(УстановитьСоставСтандартногоИнтерфейсаOData|SetStandardODataInterfaceContent|ПолучитьСоставСтандартногоИнтерфейсаOData|GetStandardODataInterfaceContent)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Процедуры и функции работы с двоичными данными\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(СоединитьБуферыДвоичныхДанных|ConcatBinaryDataBuffers)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Прочие процедуры и функции\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Мин|Min|Макс|Max|ОписаниеОшибки|ErrorDescription|Вычислить|Eval|ИнформацияОбОшибке|ErrorInfo|Base64Значение|Base64Value|Base64Строка|Base64String|ЗаполнитьЗначенияСвойств|FillPropertyValues|ЗначениеЗаполнено|ValueIsFilled|ПолучитьПредставленияНавигационныхСсылок|GetURLsPresentations|НайтиОкноПоНавигационнойСсылке|FindWindowByURL|ПолучитьОкна|GetWindows|ПерейтиПоНавигационнойСсылке|GotoURL|ПолучитьНавигационнуюСсылку|GetURL|ПолучитьДопустимыеКодыЛокализации|GetAvailableLocaleCodes|ПолучитьНавигационнуюСсылкуИнформационнойБазы|GetInfoBaseURL|ПредставлениеКодаЛокализации|LocaleCodePresentation|ПолучитьДопустимыеЧасовыеПояса|GetAvailableTimeZones|ПредставлениеЧасовогоПояса|TimeZonePresentation|ТекущаяУниверсальнаяДата|CurrentUniversalDate|ТекущаяУниверсальнаяДатаВМиллисекундах|CurrentUniversalDateInMilliseconds|МестноеВремя|ToLocalTime|УниверсальноеВремя|ToUniversalTime|ЧасовойПояс|TimeZone|СмещениеЛетнегоВремени|DaylightTimeOffset|СмещениеСтандартногоВремени|StandardTimeOffset|КодироватьСтроку|EncodeString|РаскодироватьСтроку|DecodeString|Найти|Find|ПродолжитьВызов|ProceedWithCall)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - События приложения и сеанса\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ПередНачаломРаботыСистемы|BeforeStart|ПриНачалеРаботыСистемы|OnStart|ПередЗавершениемРаботыСистемы|BeforeExit|ПриЗавершенииРаботыСистемы|OnExit|ОбработкаВнешнегоСобытия|ExternEventProcessing|УстановкаПараметровСеанса|SessionParametersSetting|ПриИзмененииПараметровЭкрана|OnChangeDisplaySettings)\\\\\\\\s*(?=\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Свойства (классы)\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(WSСсылки|WSReferences|БиблиотекаКартинок|PictureLib|БиблиотекаМакетовОформленияКомпоновкиДанных|DataCompositionAppearanceTemplateLib|БиблиотекаСтилей|StyleLib|БизнесПроцессы|BusinessProcesses|ВнешниеИсточникиДанных|ExternalDataSources|ВнешниеОбработки|ExternalDataProcessors|ВнешниеОтчеты|ExternalReports|Документы|Documents|ДоставляемыеУведомления|DeliverableNotifications|ЖурналыДокументов|DocumentJournals|Задачи|Tasks|ИнформацияОбИнтернетСоединении|InternetConnectionInformation|ИспользованиеРабочейДаты|WorkingDateUse|ИсторияРаботыПользователя|UserWorkHistory|Константы|Constants|КритерииОтбора|FilterCriteria|Метаданные|Metadata|Обработки|DataProcessors|ОтправкаДоставляемыхУведомлений|DeliverableNotificationSend|Отчеты|Reports|ПараметрыСеанса|SessionParameters|Перечисления|Enums|ПланыВидовРасчета|ChartsOfCalculationTypes|ПланыВидовХарактеристик|ChartsOfCharacteristicTypes|ПланыОбмена|ExchangePlans|ПланыСчетов|ChartsOfAccounts|ПолнотекстовыйПоиск|FullTextSearch|ПользователиИнформационнойБазы|InfoBaseUsers|Последовательности|Sequences|РасширенияКонфигурации|ConfigurationExtensions|РегистрыБухгалтерии|AccountingRegisters|РегистрыНакопления|AccumulationRegisters|РегистрыРасчета|CalculationRegisters|РегистрыСведений|InformationRegisters|РегламентныеЗадания|ScheduledJobs|СериализаторXDTO|XDTOSerializer|Справочники|Catalogs|СредстваГеопозиционирования|LocationTools|СредстваКриптографии|CryptoToolsManager|СредстваМультимедиа|MultimediaTools|СредстваОтображенияРекламы|AdvertisingPresentationTools|СредстваПочты|MailTools|СредстваТелефонии|TelephonyTools|ФабрикаXDTO|XDTOFactory|ФайловыеПотоки|FileStreams|ФоновыеЗадания|BackgroundJobs|ХранилищаНастроек|SettingsStorages|ВстроенныеПокупки|InAppPurchases|ОтображениеРекламы|AdRepresentation|ПанельЗадачОС|OSTaskbar|ПроверкаВстроенныхПокупок|InAppPurchasesValidation)(?=[^\\\\\\\\wа-яё]|$))\\\",\\\"name\\\":\\\"support.class.bsl\\\"},{\\\"comment\\\":\\\"Глобальный контекст - Свойства (переменные)\\\",\\\"match\\\":\\\"(?i:(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ГлавныйИнтерфейс|MainInterface|ГлавныйСтиль|MainStyle|ПараметрЗапуска|LaunchParameter|РабочаяДата|WorkingDate|ХранилищеВариантовОтчетов|ReportsVariantsStorage|ХранилищеНастроекДанныхФорм|FormDataSettingsStorage|ХранилищеОбщихНастроек|CommonSettingsStorage|ХранилищеПользовательскихНастроекДинамическихСписков|DynamicListsUserSettingsStorage|ХранилищеПользовательскихНастроекОтчетов|ReportsUserSettingsStorage|ХранилищеСистемныхНастроек|SystemSettingsStorage)(?=[^\\\\\\\\wа-яё]|$))\\\",\\\"name\\\":\\\"support.variable.bsl\\\"}]},\\\"query\\\":{\\\"begin\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sdbl\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\\\\\\\\"[^\\\\\\\\\\\\\\\"])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.bsl\\\"},{\\\"match\\\":\\\"(//((\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\")|[^\\\\\\\\\\\\\\\"])*)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"[^\\\\\\\"]*\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\"},{\\\"include\\\":\\\"source.sdbl\\\"}]}},\\\"scopeName\\\":\\\"source.bsl\\\",\\\"embeddedLangs\\\":[\\\"sdbl\\\"],\\\"aliases\\\":[\\\"1c\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._sdbl_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/bsl.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sdbl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Query)\\\",\\\"fileTypes\\\":[\\\"sdbl\\\",\\\"query\\\"],\\\"firstLineMatch\\\":\\\"(?i)Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?.*\\\",\\\"name\\\":\\\"sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\\\\\\\\\"(?![\\\\\\\\\\\\\\\"])\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.sdbl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"}]},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"constant.language.sdbl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"constant.numeric.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.control.conditional.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<!КАК\\\\\\\\s|AS\\\\\\\\s)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\\\\\s+ИЕРАРХИИ|IN\\\\\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\\\\\s+НЕ)?\\\\\\\\s+NULL|Is(\\\\\\\\s+NOT)?\\\\\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.sdbl\\\"},{\\\"match\\\":\\\"<=|>=|=|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.sdbl\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|-|\\\\\\\\*|/|%)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sdbl\\\"},{\\\"match\\\":\\\"(,|;)\\\",\\\"name\\\":\\\"keyword.operator.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\\\\\s+(Внешнее\\\\\\\\s+|Outer\\\\\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\\\\\s+По(\\\\\\\\s+Группирующим\\\\\\\\s+Наборам)?)|(Group\\\\\\\\s+By(\\\\\\\\s+Grouping\\\\\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\\\\\s+Все)?|Union(\\\\\\\\s+All)?|(Упорядочить\\\\\\\\s+По)|(Order\\\\\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\\\\\s+Общие)?|By(\\\\\\\\s+Overall)?|(Только\\\\\\\\s+)?Иерархия|(Only\\\\\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\\\\\s+Изменения|(For\\\\\\\\s+Update(\\\\\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.control.sdbl\\\"},{\\\"comment\\\":\\\"Функции языка запросов\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы со строками\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы с датами\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы с числами\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Агрегатные функции\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Прочие функции\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"support.type.sdbl\\\"},{\\\"match\\\":\\\"(&[\\\\\\\\wа-яё]+)\\\",\\\"name\\\":\\\"variable.parameter.sdbl\\\"}],\\\"scopeName\\\":\\\"source.sdbl\\\",\\\"aliases\\\":[\\\"1c-query\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\n"));

/***/ })

}]);