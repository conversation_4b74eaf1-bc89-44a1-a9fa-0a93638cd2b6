'use client';

import { useQuery } from '@tanstack/react-query';
import { Building2 } from 'lucide-react';
import CompanyQuery from '@/services/queries/CompanyQuery';

interface CompanyInfoCardProps {
  companyId: number;
}

export default function CompanyInfoCard({ companyId }: CompanyInfoCardProps) {
  const { data: company, isLoading } = useQuery({
    queryKey: [...CompanyQuery.tags, { id: companyId }],
    queryFn: () => CompanyQuery.getOne(companyId),
  });

  if (isLoading) {
    return (
      <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
        <div className="animate-pulse flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
          <div className="flex-1">
            <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <Building2 className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h3 className="font-medium text-red-800">Company Not Found</h3>
            <p className="text-sm text-red-600">
              Unable to load company information
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-brand-brown/10 rounded-full flex items-center justify-center flex-shrink-0">
          <Building2 className="h-6 w-6 text-brand-brown" />
        </div>

        <div className="flex-1 min-w-0">
          <h2 className="text-xl font-bold text-gray-900 truncate">
            {company.name}
          </h2>
          <div className="flex items-center gap-4 mt-1">
            {company.city && (
              <span className="text-sm text-gray-600">📍 {company.city}</span>
            )}
            {company.phone && (
              <span className="text-sm text-gray-600">📞 {company.phone}</span>
            )}
          </div>
        </div>

        <div className="flex-shrink-0">
          <span
            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
              company.isArchived
                ? 'bg-red-100 text-red-800'
                : 'bg-green-100 text-green-800'
            }`}
          >
            {company.isArchived ? 'Archived' : 'Active'}
          </span>
        </div>
      </div>
    </div>
  );
}
