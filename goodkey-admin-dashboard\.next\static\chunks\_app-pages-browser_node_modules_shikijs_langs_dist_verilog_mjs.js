"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_verilog_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/verilog.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/verilog.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Verilog\\\",\\\"fileTypes\\\":[\\\"v\\\",\\\"vh\\\"],\\\"name\\\":\\\"verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module_pattern\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#operators\\\"}],\\\"repository\\\":{\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.verilog\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.verilog\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.c-style.verilog\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"`(?!(celldefine|endcelldefine|default_nettype|define|undef|ifdef|ifndef|else|endif|include|resetall|timescale|unconnected_drive|nounconnected_drive))[a-z_A-Z][a-zA-Z0-9_$]*\\\",\\\"name\\\":\\\"variable.other.constant.verilog\\\"},{\\\"match\\\":\\\"[0-9]*'[bBoOdDhH][a-fA-F0-9_xXzZ]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sized_integer.verilog\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.range.verilog\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"}},\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)(:)(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.block.numeric.range.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d[\\\\\\\\d_]*(?i:e\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+(?i:e\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.real.verilog\\\"},{\\\"match\\\":\\\"#\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.delay.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\b[01xXzZ]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.logic.verilog\\\"}]},\\\"instantiation_patterns\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*(?!always|and|assign|output|input|inout|wire|module)([a-zA-Z][a-zA-Z0-9_]*)\\\\\\\\s+([a-zA-Z][a-zA-Z0-9_]*)(?<!begin|if)\\\\\\\\s*(?=\\\\\\\\(|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.module.reference.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.module.identifier.verilog\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.expression.verilog\\\"}},\\\"name\\\":\\\"meta.block.instantiation.parameterless.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*([a-zA-Z][a-zA-Z0-9_]*)\\\\\\\\s*(#)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.module.reference.verilog\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.expression.verilog\\\"}},\\\"name\\\":\\\"meta.block.instantiation.with.parameters.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parenthetical_list\\\"},{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"entity.name.tag.module.identifier.verilog\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(always|and|assign|attribute|begin|buf|bufif0|bufif1|case[xz]?|cmos|deassign|default|defparam|disable|edge|else|end(attribute|case|function|generate|module|primitive|specify|table|task)?|event|for|force|forever|fork|function|generate|genvar|highz(01)|if(none)?|initial|inout|input|integer|join|localparam|medium|module|large|macromodule|nand|negedge|nmos|nor|not|notif(01)|or|output|parameter|pmos|posedge|primitive|pull0|pull1|pulldown|pullup|rcmos|real|realtime|reg|release|repeat|rnmos|rpmos|rtran|rtranif(01)|scalared|signed|small|specify|specparam|strength|strong0|strong1|supply0|supply1|table|task|time|tran|tranif(01)|tri(01)?|tri(and|or|reg)|unsigned|vectored|wait|wand|weak(01)|while|wire|wor|xnor|xor)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.verilog\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*`((cell)?define|default_(decay_time|nettype|trireg_strength)|delay_mode_(path|unit|zero)|ifdef|ifndef|include|end(if|celldefine)|else|(no)?unconnected_drive|resetall|timescale|undef)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.compiler.directive.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(f(open|close)|readmem(b|h)|timeformat|printtimescale|stop|finish|(s|real)?time|realtobits|bitstoreal|rtoi|itor|(f)?(display|write(h|b)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.console.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(random|dist_(chi_square|erlang|exponential|normal|poisson|t|uniform))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.random_number.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$((a)?sync\\\\\\\\$((n)?and|(n)or)\\\\\\\\$(array|plane))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.pld_modeling.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(q_(initialize|add|remove|full|exam))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.stochastic.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(hold|nochange|period|recovery|setup(hold)?|skew|width)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.timing.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(dump(file|vars|off|on|all|limit|flush))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.system.vcd.tasks.verilog\\\"},{\\\"match\\\":\\\"\\\\\\\\$(countdrivers|list|input|scope|showscopes|(no)?(key|log)|reset(_count|_value)?|(inc)?save|restart|showvars|getpattern|sreadmem(b|h)|scale)\\\",\\\"name\\\":\\\"support.function.non-standard.tasks.verilog\\\"}]},\\\"module_pattern\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(module)\\\\\\\\s+([a-zA-Z][a-zA-Z0-9_]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.module.verilog\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.module.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\bendmodule\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"storage.type.module.verilog\\\"}},\\\"name\\\":\\\"meta.block.module.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#instantiation_patterns\\\"},{\\\"include\\\":\\\"#operators\\\"}]}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\+|-|\\\\\\\\*|/|%|(<|>)=?|(!|=)?==?|!|&&?|\\\\\\\\|\\\\\\\\|?|\\\\\\\\^?~|~\\\\\\\\^?\\\",\\\"name\\\":\\\"keyword.operator.verilog\\\"}]},\\\"parenthetical_list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.list.verilog\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.list.verilog\\\"}},\\\"name\\\":\\\"meta.block.parenthetical_list.verilog\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parenthetical_list\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"}]}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.verilog\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.verilog\\\"}]}]}},\\\"scopeName\\\":\\\"source.verilog\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/verilog.mjs\n"));

/***/ })

}]);