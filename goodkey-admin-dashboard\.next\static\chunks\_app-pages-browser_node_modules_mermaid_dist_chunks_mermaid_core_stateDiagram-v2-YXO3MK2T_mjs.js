"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_stateDiagram-v2-YXO3MK2T_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StateDB: () => (/* binding */ StateDB),\n/* harmony export */   stateDiagram_default: () => (/* binding */ stateDiagram_default),\n/* harmony export */   stateRenderer_v3_unified_default: () => (/* binding */ stateRenderer_v3_unified_default),\n/* harmony export */   styles_default: () => (/* binding */ styles_default)\n/* harmony export */ });\n/* harmony import */ var _chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-RZ5BOZE2.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs\");\n/* harmony import */ var _chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-TYCBKAJE.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-TYCBKAJE.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n\n\n\n\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 32], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 34], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 33], $Vr = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vs = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vt = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"classDef\": 38, \"CLASSDEF_ID\": 39, \"CLASSDEF_STYLEOPTS\": 40, \"DEFAULT\": 41, \"style\": 42, \"STYLE_IDS\": 43, \"STYLEDEF_STYLEOPTS\": 44, \"class\": 45, \"CLASSENTITY_IDS\": 46, \"STYLECLASS\": 47, \"direction_tb\": 48, \"direction_bt\": 49, \"direction_rl\": 50, \"direction_lr\": 51, \"eol\": 52, \";\": 53, \"EDGE_STATE\": 54, \"STYLE_SEPARATOR\": 55, \"left_of\": 56, \"right_of\": 57, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"classDef\", 39: \"CLASSDEF_ID\", 40: \"CLASSDEF_STYLEOPTS\", 41: \"DEFAULT\", 42: \"style\", 43: \"STYLE_IDS\", 44: \"STYLEDEF_STYLEOPTS\", 45: \"class\", 46: \"CLASSENTITY_IDS\", 47: \"STYLECLASS\", 48: \"direction_tb\", 49: \"direction_bt\", 50: \"direction_rl\", 51: \"direction_lr\", 53: \";\", 54: \"EDGE_STATE\", 55: \"STYLE_SEPARATOR\", 56: \"left_of\", 57: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [52, 1], [52, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 34:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 35:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 36:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 37:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 38:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 39:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 42:\n        case 43:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 44:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 5]), { 9: 38, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 7]), o($Vr, [2, 8]), o($Vr, [2, 9]), o($Vr, [2, 10]), o($Vr, [2, 11]), o($Vr, [2, 12], { 14: [1, 39], 15: [1, 40] }), o($Vr, [2, 16]), { 18: [1, 41] }, o($Vr, [2, 18], { 20: [1, 42] }), { 23: [1, 43] }, o($Vr, [2, 22]), o($Vr, [2, 23]), o($Vr, [2, 24]), o($Vr, [2, 25]), { 30: 44, 31: [1, 45], 56: [1, 46], 57: [1, 47] }, o($Vr, [2, 28]), { 34: [1, 48] }, { 36: [1, 49] }, o($Vr, [2, 31]), { 39: [1, 50], 41: [1, 51] }, { 43: [1, 52] }, { 46: [1, 53] }, o($Vs, [2, 42], { 55: [1, 54] }), o($Vs, [2, 43], { 55: [1, 55] }), o($Vr, [2, 36]), o($Vr, [2, 37]), o($Vr, [2, 38]), o($Vr, [2, 39]), o($Vr, [2, 6]), o($Vr, [2, 13]), { 13: 56, 24: $Va, 54: $Vq }, o($Vr, [2, 17]), o($Vt, $V3, { 7: 57 }), { 24: [1, 58] }, { 24: [1, 59] }, { 23: [1, 60] }, { 24: [2, 46] }, { 24: [2, 47] }, o($Vr, [2, 29]), o($Vr, [2, 30]), { 40: [1, 61] }, { 40: [1, 62] }, { 44: [1, 63] }, { 47: [1, 64] }, { 24: [1, 65] }, { 24: [1, 66] }, o($Vr, [2, 14], { 14: [1, 67] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 68], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 20], { 20: [1, 69] }), { 31: [1, 70] }, { 24: [1, 71] }, o($Vr, [2, 32]), o($Vr, [2, 33]), o($Vr, [2, 34]), o($Vr, [2, 35]), o($Vs, [2, 44]), o($Vs, [2, 45]), o($Vr, [2, 15]), o($Vr, [2, 19]), o($Vt, $V3, { 7: 72 }), o($Vr, [2, 26]), o($Vr, [2, 27]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 73], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 46: [2, 46], 47: [2, 47] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 41;\n            break;\n          case 1:\n            return 48;\n            break;\n          case 2:\n            return 49;\n            break;\n          case 3:\n            return 50;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            break;\n          case 6:\n            {\n            }\n            break;\n          case 7:\n            return 5;\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            break;\n          case 12:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 13:\n            return 18;\n            break;\n          case 14:\n            this.popState();\n            break;\n          case 15:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 16:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 17:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 18:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 19:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 20:\n            this.popState();\n            break;\n          case 21:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 22:\n            this.pushState(\"CLASSDEF\");\n            return 38;\n            break;\n          case 23:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 24:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 39;\n            break;\n          case 25:\n            this.popState();\n            return 40;\n            break;\n          case 26:\n            this.pushState(\"CLASS\");\n            return 45;\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 46;\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.pushState(\"STYLE\");\n            return 42;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 43;\n            break;\n          case 31:\n            this.popState();\n            return 44;\n            break;\n          case 32:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 33:\n            return 18;\n            break;\n          case 34:\n            this.popState();\n            break;\n          case 35:\n            this.pushState(\"STATE\");\n            break;\n          case 36:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 37:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 38:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            return 48;\n            break;\n          case 43:\n            return 49;\n            break;\n          case 44:\n            return 50;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 47:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 48:\n            this.popState();\n            return \"ID\";\n            break;\n          case 49:\n            this.popState();\n            break;\n          case 50:\n            return \"STATE_DESCR\";\n            break;\n          case 51:\n            return 19;\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 54:\n            break;\n          case 55:\n            this.popState();\n            return 21;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 58:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 56;\n            break;\n          case 59:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 57;\n            break;\n          case 60:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 62:\n            break;\n          case 63:\n            return \"NOTE_TEXT\";\n            break;\n          case 64:\n            this.popState();\n            return \"ID\";\n            break;\n          case 65:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 66:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 67:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 68:\n            return 6;\n            break;\n          case 69:\n            return 6;\n            break;\n          case 70:\n            return 16;\n            break;\n          case 71:\n            return 54;\n            break;\n          case 72:\n            return 24;\n            break;\n          case 73:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 74:\n            return 15;\n            break;\n          case 75:\n            return 28;\n            break;\n          case 76:\n            return 55;\n            break;\n          case 77:\n            return 5;\n            break;\n          case 78:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [9, 10], \"inclusive\": false }, \"struct\": { \"rules\": [9, 10, 22, 26, 29, 35, 42, 43, 44, 45, 54, 55, 56, 57, 71, 72, 73, 74, 75], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [64], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [66, 67], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [65], \"inclusive\": false }, \"NOTE\": { \"rules\": [58, 59, 60], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [31], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [30], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [28], \"inclusive\": false }, \"CLASS\": { \"rules\": [27], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [25], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [20, 21], \"inclusive\": false }, \"acc_descr\": { \"rules\": [18], \"inclusive\": false }, \"acc_title\": { \"rules\": [16], \"inclusive\": false }, \"SCALE\": { \"rules\": [13, 14, 33, 34], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [48], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [49, 50], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [9, 10, 36, 37, 38, 39, 40, 41, 46, 47, 51, 52, 53], \"inclusive\": false }, \"ID\": { \"rules\": [9, 10], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 15, 17, 19, 22, 26, 29, 32, 35, 53, 57, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"TB\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_DIRECTION = \"dir\";\nvar STMT_STATE = \"state\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(text, id, _version, diag) {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"REF0:\");\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = (0,_chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramElement)(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await (0,_chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_1__.render)(data4Layout, svg);\n  const padding = 8;\n  _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  (0,_chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__.setupViewPortForSVG)(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.js\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parentParsedItem, doc, diagramStates, nodes, edges, altFlag, look, classes) => {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(item.description, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes, nodeData, classes) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      if (classes.get(cssClass)) {\n        const classDef = classes.get(cssClass);\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles, ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((parent, parsedItem, diagramStates, nodes, edges, altFlag, look, classes) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(itemId, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeTextOrArray(newNode.description, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompilesStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().flowchart.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes, groupData, classes);\n      insertOrUpdateNode(nodes, noteData, classes);\n      insertOrUpdateNode(nodes, nodeData, classes);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.js\nvar START_NODE = \"[*]\";\nvar START_TYPE = \"start\";\nvar END_NODE = START_NODE;\nvar END_TYPE = \"end\";\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nfunction newClassesList() {\n  return /* @__PURE__ */ new Map();\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(newClassesList, \"newClassesList\");\nvar newDoc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => {\n  return {\n    /** @type {{ id1: string, id2: string, relationTitle: string }[]} */\n    relations: [],\n    states: /* @__PURE__ */ new Map(),\n    documents: {}\n  };\n}, \"newDoc\");\nvar clone = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar StateDB = class {\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"StateDB\");\n  }\n  /**\n   * @param {1 | 2} version - v1 renderer or v2 renderer.\n   */\n  constructor(version) {\n    this.clear();\n    this.version = version;\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n  /**\n   * @private\n   * @type {1 | 2}\n   */\n  version;\n  /**\n   * @private\n   * @type {Array}\n   */\n  nodes = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  edges = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  rootDoc = [];\n  /**\n   * @private\n   * @type {Map<string, any>}\n   */\n  classes = newClassesList();\n  // style classes defined by a classDef\n  /**\n   * @private\n   * @type {Object}\n   */\n  documents = {\n    root: newDoc()\n  };\n  /**\n   * @private\n   * @type {Object}\n   */\n  currentDocument = this.documents.root;\n  /**\n   * @private\n   * @type {number}\n   */\n  startEndCount = 0;\n  /**\n   * @private\n   * @type {number}\n   */\n  dividerCnt = 0;\n  static relationType = {\n    AGGREGATION: 0,\n    EXTENSION: 1,\n    COMPOSITION: 2,\n    DEPENDENCY: 3\n  };\n  setRootDoc(o) {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting root doc\", o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n  getRootDoc() {\n    return this.rootDoc;\n  }\n  /**\n   * @private\n   * @param {Object} parent\n   * @param {Object} node\n   * @param {boolean} first\n   */\n  docTranslator(parent, node, first) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n    } else {\n      if (node.stmt === STMT_STATE) {\n        if (node.id === \"[*]\") {\n          node.id = first ? parent.id + \"_start\" : parent.id + \"_end\";\n          node.start = first;\n        } else {\n          node.id = node.id.trim();\n        }\n      }\n      if (node.doc) {\n        const doc = [];\n        let currentDoc = [];\n        let i;\n        for (i = 0; i < node.doc.length; i++) {\n          if (node.doc[i].type === DIVIDER_TYPE) {\n            const newNode = clone(node.doc[i]);\n            newNode.doc = clone(currentDoc);\n            doc.push(newNode);\n            currentDoc = [];\n          } else {\n            currentDoc.push(node.doc[i]);\n          }\n        }\n        if (doc.length > 0 && currentDoc.length > 0) {\n          const newNode = {\n            stmt: STMT_STATE,\n            id: (0,_chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.generateId)(),\n            type: \"divider\",\n            doc: clone(currentDoc)\n          };\n          doc.push(clone(newNode));\n          node.doc = doc;\n        }\n        node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n      }\n    }\n  }\n  /**\n   * @private\n   */\n  getRootDocV2() {\n    this.docTranslator({ id: \"root\" }, { id: \"root\", doc: this.rootDoc }, true);\n    return { id: \"root\", doc: this.rootDoc };\n  }\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   * @private\n   * @param _doc\n   */\n  extract(_doc) {\n    let doc;\n    if (_doc.doc) {\n      doc = _doc.doc;\n    } else {\n      doc = _doc;\n    }\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(doc);\n    this.clear(true);\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Extract initial document:\", doc);\n    doc.forEach((item) => {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.warn(\"Statement\", item.stmt);\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(\n            item.id.trim(),\n            item.type,\n            item.doc,\n            item.description,\n            item.note,\n            item.classes,\n            item.styles,\n            item.textStyles\n          );\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          {\n            const ids = item.id.trim().split(\",\");\n            const styles = item.styleClass.split(\",\");\n            ids.forEach((id) => {\n              let foundState = this.getState(id);\n              if (foundState === void 0) {\n                const trimmedId = id.trim();\n                this.addState(trimmedId);\n                foundState = this.getState(trimmedId);\n              }\n              foundState.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n            });\n          }\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n      }\n    });\n    const diagramStates = this.getStates();\n    const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n    const look = config.look;\n    reset();\n    dataFetcher(\n      void 0,\n      this.getRootDocV2(),\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      look,\n      this.classes\n    );\n    this.nodes.forEach((node) => {\n      if (Array.isArray(node.label)) {\n        node.description = node.label.slice(1);\n        if (node.isGroup && node.description.length > 0) {\n          throw new Error(\n            \"Group nodes can only have label. Remove the additional description for node [\" + node.id + \"]\"\n          );\n        }\n        node.label = node.label[0];\n      }\n    });\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param {null | string} id\n   * @param {null | string} type\n   * @param {null | string} doc\n   * @param {null | string | string[]} descr - description for the state. Can be a string or a list or strings\n   * @param {null | string} note\n   * @param {null | string | string[]} classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param {null | string | string[]} styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param {null | string | string[]} textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(id, type = DEFAULT_STATE_TYPE, doc = null, descr = null, note = null, classes = null, styles = null, textStyles = null) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Adding state \", trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: []\n      });\n    } else {\n      if (!this.currentDocument.states.get(trimmedId).doc) {\n        this.currentDocument.states.get(trimmedId).doc = doc;\n      }\n      if (!this.currentDocument.states.get(trimmedId).type) {\n        this.currentDocument.states.get(trimmedId).type = type;\n      }\n    }\n    if (descr) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state description\", trimmedId, descr);\n      if (typeof descr === \"string\") {\n        this.addDescription(trimmedId, descr.trim());\n      }\n      if (typeof descr === \"object\") {\n        descr.forEach((des) => this.addDescription(trimmedId, des.trim()));\n      }\n    }\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      doc2.note = note;\n      doc2.note.text = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(doc2.note.text, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n    }\n    if (classes) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state classes\", trimmedId, classes);\n      const classesList = typeof classes === \"string\" ? [classes] : classes;\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n    if (styles) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state styles\", trimmedId, styles);\n      const stylesList = typeof styles === \"string\" ? [styles] : styles;\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n    if (textStyles) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Setting state styles\", trimmedId, styles);\n      const textStylesList = typeof textStyles === \"string\" ? [textStyles] : textStyles;\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n  clear(saveCommon) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = {\n      root: newDoc()\n    };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.clear)();\n    }\n  }\n  getState(id) {\n    return this.currentDocument.states.get(id);\n  }\n  getStates() {\n    return this.currentDocument.states;\n  }\n  logDocuments() {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.info(\"Documents = \", this.documents);\n  }\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  startIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === START_NODE) {\n      this.startEndCount++;\n      fixedId = `${START_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === START_NODE ? START_TYPE : type;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  endIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === END_NODE) {\n      this.startEndCount++;\n      fixedId = `${END_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === END_NODE ? END_TYPE : type;\n  }\n  /**\n   *\n   * @param item1\n   * @param item2\n   * @param relationTitle\n   */\n  addRelationObjs(item1, item2, relationTitle) {\n    let id1 = this.startIdIfNeeded(item1.id.trim());\n    let type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    let id2 = this.startIdIfNeeded(item2.id.trim());\n    let type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(relationTitle, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)())\n    });\n  }\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   *\n   * @param {string | object} item1\n   * @param {string | object} item2\n   * @param {string} title\n   */\n  addRelation(item1, item2, title) {\n    if (typeof item1 === \"object\") {\n      this.addRelationObjs(item1, item2, title);\n    } else {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        title: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(title, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)())\n      });\n    }\n  }\n  addDescription(id, descr) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n    theState.descriptions.push(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.sanitizeText(_descr, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()));\n  }\n  cleanupLabel(label) {\n    if (label.substring(0, 1) === \":\") {\n      return label.substr(2).trim();\n    } else {\n      return label.trim();\n    }\n  }\n  getDividerId() {\n    this.dividerCnt++;\n    return \"divider-id-\" + this.dividerCnt;\n  }\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param {string} id - the id of this (style) class\n   * @param  {string | null} styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id, styleAttributes = \"\") {\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes !== void 0 && styleAttributes !== null) {\n      styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n        if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n          const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n  /**\n   * Return all of the style classes\n   * @returns {{} | any | classes}\n   */\n  getClasses() {\n    return this.classes;\n  }\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param {string | string[]} itemIds The id or a list of ids of the item(s) to apply the css class to\n   * @param {string} cssClassName CSS class name\n   */\n  setCssClass(itemIds, cssClassName) {\n    itemIds.split(\",\").forEach((id) => {\n      let foundState = this.getState(id);\n      if (foundState === void 0) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState.classes.push(cssClassName);\n    });\n  }\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId, styleText) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.styles.push(styleText);\n    }\n  }\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId The id of item to apply the css class to\n   * @param cssClassName CSS class name\n   */\n  setTextStyle(itemId, cssClassName) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.textStyles.push(cssClassName);\n    }\n  }\n  /**\n   * Finds the direction statement in the root document.\n   * @private\n   * @returns {{ value: string } | undefined} - the direction statement if present\n   */\n  getDirectionStatement() {\n    return this.rootDoc.find((doc) => doc.stmt === STMT_DIRECTION);\n  }\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n  setDirection(dir) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n  trimColon(str) {\n    return str && str[0] === \":\" ? str.substr(1).trim() : str.trim();\n  }\n  getData() {\n    const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2())\n    };\n  }\n  getConfig() {\n    return (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().state;\n  }\n  getAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccTitle;\n  setAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle;\n  getAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccDescription;\n  setAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccDescription;\n  setDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setDiagramTitle;\n  getDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle;\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDiagramElement: () => (/* binding */ getDiagramElement),\n/* harmony export */   setupViewPortForSVG: () => (/* binding */ setupViewPortForSVG)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n// src/rendering-util/insertElementsForSize.js\n\nvar getDiagramElement = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_AEK57VVT_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-AEK57VVT.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs\");\n/* harmony import */ var _chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-RZ5BOZE2.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs\");\n/* harmony import */ var _chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-TYCBKAJE.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-TYCBKAJE.mjs\");\n/* harmony import */ var _chunk_IIMUDSI4_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-IIMUDSI4.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-IIMUDSI4.mjs\");\n/* harmony import */ var _chunk_VV3M67IP_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-VV3M67IP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-VV3M67IP.mjs\");\n/* harmony import */ var _chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-HRU6DDCH.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-HRU6DDCH.mjs\");\n/* harmony import */ var _chunk_K557N5IZ_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-K557N5IZ.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs\");\n/* harmony import */ var _chunk_H2D2JQ3I_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-H2D2JQ3I.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-H2D2JQ3I.mjs\");\n/* harmony import */ var _chunk_C3MQ5ANM_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-C3MQ5ANM.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: _chunk_AEK57VVT_mjs__WEBPACK_IMPORTED_MODULE_0__.stateDiagram_default,\n  get db() {\n    return new _chunk_AEK57VVT_mjs__WEBPACK_IMPORTED_MODULE_0__.StateDB(2);\n  },\n  renderer: _chunk_AEK57VVT_mjs__WEBPACK_IMPORTED_MODULE_0__.stateRenderer_v3_unified_default,\n  styles: _chunk_AEK57VVT_mjs__WEBPACK_IMPORTED_MODULE_0__.styles_default,\n  init: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_10__.__name)((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs\n"));

/***/ })

}]);