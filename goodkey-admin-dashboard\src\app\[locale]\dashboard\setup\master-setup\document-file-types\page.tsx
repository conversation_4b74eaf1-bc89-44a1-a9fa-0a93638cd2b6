import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import DocumentFileTypeTable from './components/document_file_type_table';

export const metadata: Metadata = {
  title: 'Goodkey | Document File Types',
};

export default async function DocumentFileTypesPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: DocumentFileTypeQuery.tags,
    queryFn: () => DocumentFileTypeQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Document File Types',
          link: '/dashboard/setup/master-setup/document-file-types',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <DocumentFileTypeTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
