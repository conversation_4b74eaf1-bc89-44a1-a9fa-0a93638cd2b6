"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_nextflow_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/nextflow.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/nextflow.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Nextflow\\\",\\\"name\\\":\\\"nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nextflow\\\"}],\\\"repository\\\":{\\\"enum-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(enum)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.groovy\\\"}},\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#comments\\\"},{\\\"include\\\":\\\"#enum-values\\\"}]},\\\"enum-values\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=;|^)\\\\\\\\s*\\\\\\\\b([A-Z0-9_]+)(?=\\\\\\\\s*(?:,|}|\\\\\\\\(|$))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.enum.name.groovy\\\"}},\\\"end\\\":\\\",|(?=})|^(?!\\\\\\\\s*\\\\\\\\w+\\\\\\\\s*(?:,|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.enum.value.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.parameter.groovy\\\"},{\\\"include\\\":\\\"#groovy-code\\\"}]}]}]},\\\"function-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\s\\\"},{\\\"begin\\\":\\\"(?=(?:\\\\\\\\w|<)[^\\\\\\\\(]*\\\\\\\\s+(?:[\\\\\\\\w$]|<)+\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?=[\\\\\\\\w$]+\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"meta.method.return-type.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"}]},{\\\"begin\\\":\\\"([\\\\\\\\w$]+)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.definition.method.signature.java\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^)])\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.method.parameters.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^,)])\\\",\\\"end\\\":\\\"(?=,|\\\\\\\\))\\\",\\\"name\\\":\\\"meta.method.parameter.groovy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.separator.groovy\\\"},{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.groovy\\\"}},\\\"end\\\":\\\"(?=,|\\\\\\\\))\\\",\\\"name\\\":\\\"meta.parameter.default.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]},{\\\"include\\\":\\\"source.nextflow-groovy#parameters\\\"}]}]}]},{\\\"begin\\\":\\\"(?=<)\\\",\\\"end\\\":\\\"(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"meta.method.paramerised-type.groovy\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"<\\\",\\\"end\\\":\\\">\\\",\\\"name\\\":\\\"storage.type.parameters.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#types\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.definition.seperator.groovy\\\"}]}]},{\\\"begin\\\":\\\"{\\\",\\\"end\\\":\\\"(?=})\\\",\\\"name\\\":\\\"meta.method.body.java\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy-code\\\"}]}]},\\\"function-def\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?:(?<=;|^|{)(?=\\\\\\\\s*(?:(?:def)|(?:(?:(?:boolean|byte|char|short|int|float|long|double)|(?:@?(?:[a-zA-Z]\\\\\\\\w*\\\\\\\\.)*[A-Z]+\\\\\\\\w*))[\\\\\\\\[\\\\\\\\]]*(?:<.*>)?)n)\\\\\\\\s+([^=]+\\\\\\\\s+)?\\\\\\\\w+\\\\\\\\s*\\\\\\\\())\\\",\\\"end\\\":\\\"}|(?=[^{])\\\",\\\"name\\\":\\\"meta.definition.method.groovy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-body\\\"}]},\\\"include-decl\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^\\\\\\\\b(include)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(from)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.nextflow\\\"}]},\\\"nextflow\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#enum-def\\\"},{\\\"include\\\":\\\"#function-def\\\"},{\\\"include\\\":\\\"#process-def\\\"},{\\\"include\\\":\\\"#workflow-def\\\"},{\\\"include\\\":\\\"#output-def\\\"},{\\\"include\\\":\\\"#include-decl\\\"},{\\\"include\\\":\\\"source.nextflow-groovy\\\"}]},\\\"output-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(output)\\\\\\\\s*{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"output.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:input|output|when|script|shell|exec):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"match\\\":\\\"\\\\\\\\b(val|env|file|path|stdin|stdout|tuple)(\\\\\\\\(|\\\\\\\\s)\\\",\\\"name\\\":\\\"entity.name.function.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"process-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(process)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s*{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"process.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#process-body\\\"}]},\\\"workflow-body\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:take|main|emit|publish):\\\",\\\"name\\\":\\\"constant.block.nextflow\\\"},{\\\"include\\\":\\\"source.nextflow-groovy#groovy\\\"}]},\\\"workflow-def\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(workflow)(?:\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\s*{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.nextflow\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.nextflow\\\"}},\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"workflow.nextflow\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#workflow-body\\\"}]}},\\\"scopeName\\\":\\\"source.nextflow\\\",\\\"aliases\\\":[\\\"nf\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/nextflow.mjs\n"));

/***/ })

}]);