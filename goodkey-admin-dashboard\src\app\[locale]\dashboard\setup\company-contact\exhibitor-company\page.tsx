import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { CompanySearch } from './components/company-search';

export const metadata: Metadata = {
  title: 'Goodkey | Exhibitor Companies',
};

export default function ExhibitorCompanyPage() {
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Company-Contact',
          link: '/dashboard/setup/company-contact/exhibitor-company',
        },
        {
          title: 'Exhibitor Companies',
          link: '/dashboard/setup/company-contact/exhibitor-company',
        },
      ]}
      childrenClassName="bg-transparent border-none py-0 px-0"
    >
      <CompanySearch />
    </AppLayout>
  );
}
