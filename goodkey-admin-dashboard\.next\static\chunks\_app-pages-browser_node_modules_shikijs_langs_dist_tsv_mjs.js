"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tsv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tsv.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tsv.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TSV\\\",\\\"fileTypes\\\":[\\\"tsv\\\",\\\"tab\\\"],\\\"name\\\":\\\"tsv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"rainbow1\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.rainbow2\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.rainbow3\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.rainbow4\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.rainbow5\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.rainbow6\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.rainbow7\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.type.rainbow8\\\"},\\\"9\\\":{\\\"name\\\":\\\"markup.bold.rainbow9\\\"},\\\"10\\\":{\\\"name\\\":\\\"invalid.rainbow10\\\"}},\\\"match\\\":\\\"([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)([^\\\\\\\\t]*\\\\\\\\t?)\\\",\\\"name\\\":\\\"rainbowgroup\\\"}],\\\"scopeName\\\":\\\"text.tsv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tsv.mjs\n"));

/***/ })

}]);