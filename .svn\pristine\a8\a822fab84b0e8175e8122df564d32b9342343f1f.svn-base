﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{

    public interface IProvinceRepository
    {
        public IEnumerable<Provinces> GetAllProvince();
        public Provinces? Get(int id);
        public int? FindProvince(string province);
    }

    public class ProvinceRepository : IProvinceRepository
    {
        private readonly GoodkeyContext _context;
        public ProvinceRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<Provinces> GetAllProvince()
        {
            return _context.Provinces.Include(x => x.Country);
        }

        public Provinces? Get(int id)
        {
            return _context.Provinces.FirstOrDefault(i => i.ProvinceId == id);
        }

        public int? FindProvince(string province)
        {
            if (string.IsNullOrWhiteSpace(province)) return null;

            var provinceEntity = _context.Provinces.FirstOrDefault(p =>
                p.ProvinceCode.Trim().ToLower() == province.Trim().ToLower());

            if (provinceEntity == null)
            {
                provinceEntity = _context.Provinces.FirstOrDefault(p =>
                    p.ProvinceName.Trim().ToLower() == province.Trim().ToLower() ||
                    p.ProvinceNameFr.Trim().ToLower() == province.Trim().ToLower());
            }

            return provinceEntity?.ProvinceId;
        }
    }
}
