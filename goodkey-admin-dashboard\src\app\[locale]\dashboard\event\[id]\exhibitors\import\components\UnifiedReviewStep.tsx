/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle2,
  Edit3,
  Save,
  Search,
  ChevronRight,
  Copy,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import ExhibitorImportQuery from '@/services/queries/ExhibitorImportQuery';
import type { ExhibitorImportValidationResponseDto } from '@/models/ExhibitorImport';
import type {
  FieldEditDto,
  DataFixingSessionState,
} from '@/models/ExhibitorImportFix';

interface UnifiedReviewStepProps {
  validationData: ExhibitorImportValidationResponseDto;
  onReviewComplete: (data: any) => void;
  isLoading?: boolean;
}

interface IssueType {
  type: 'error' | 'warning' | 'duplicate';
  rowNumber: number;
  fieldName?: string;
  message: string;
  severity: 'high' | 'medium' | 'low';
  canAutoFix: boolean;
  suggestions?: string[];
}

const UnifiedReviewStep: React.FC<UnifiedReviewStepProps> = ({
  validationData,
  onReviewComplete,
  isLoading = false,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // URL-based state management
  const activeTab = searchParams.get('tab') || 'all';
  const showOnlyUnresolved = searchParams.get('showUnresolved') === 'true';
  const searchQuery = searchParams.get('search') || '';

  // Local state
  const [sessionState, setSessionState] = useState<DataFixingSessionState>({
    sessionId: validationData.sessionId,
    rows: {},
    duplicates: {},
    summary: validationData.summary,
    hasUnsavedChanges: false,
    isLoading: false,
    autoSave: false,
  });

  const [allIssues, setAllIssues] = useState<IssueType[]>([]);
  const [fieldEdits, setFieldEdits] = useState<Record<string, FieldEditDto>>(
    {},
  );

  // Initialize issues from validation data
  useEffect(() => {
    const issues: IssueType[] = [];

    // Add validation errors and warnings
    validationData.validationMessages.forEach((msg) => {
      issues.push({
        type: msg.messageType.toLowerCase() as 'error' | 'warning',
        rowNumber: msg.rowNumber,
        fieldName: msg.fieldName,
        message: msg.message,
        severity: msg.messageType === 'Error' ? 'high' : 'medium',
        canAutoFix: isAutoFixable(msg.fieldName, msg.message),
        suggestions: generateSuggestions(msg.fieldName, msg.message),
      });
    });

    // Add duplicates
    validationData.duplicates.forEach((dup) => {
      issues.push({
        type: 'duplicate',
        rowNumber: dup.rowNumber,
        message: `Duplicate ${dup.duplicateType}: ${dup.conflictingValue}`,
        severity: 'medium',
        canAutoFix: false,
        suggestions: ['Keep Excel data', 'Keep database data', 'Merge both'],
      });
    });

    setAllIssues(issues);
  }, [validationData]);

  // Helper functions
  const isAutoFixable = (fieldName: string, message: string): boolean => {
    const autoFixablePatterns = [
      'email format',
      'phone format',
      'postal code format',
      'required field',
    ];
    return autoFixablePatterns.some((pattern) =>
      message.toLowerCase().includes(pattern),
    );
  };

  const generateSuggestions = (
    fieldName: string,
    message: string,
  ): string[] => {
    if (message.toLowerCase().includes('email')) {
      return ['Add @domain.com', 'Fix format', 'Use company email'];
    }
    if (message.toLowerCase().includes('phone')) {
      return ['Add area code', 'Remove spaces', 'Use standard format'];
    }
    if (message.toLowerCase().includes('required')) {
      return ['Use company name', 'Use contact name', 'Enter manually'];
    }
    return [];
  };

  const updateUrlParams = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });

    router.push(`?${newSearchParams.toString()}`, { scroll: false });
  };

  // Handle field edits
  const handleFieldEdit = (
    rowNumber: number,
    fieldName: string,
    newValue: string,
  ) => {
    const editKey = `${rowNumber}-${fieldName}`;
    const originalRow = validationData.rows.find(
      (r) => r.rowNumber === rowNumber,
    );

    if (!originalRow) return;

    // Get original value
    let originalValue = '';
    switch (fieldName.toLowerCase()) {
      case 'companyname':
        originalValue = originalRow.companyName || '';
        break;
      case 'companyemail':
        originalValue = originalRow.companyEmail || '';
        break;
      case 'companyphone':
        originalValue = originalRow.companyPhone || '';
        break;
      case 'companyaddress1':
        originalValue = originalRow.companyAddress1 || '';
        break;
      case 'companyaddress2':
        originalValue = originalRow.companyAddress2 || '';
        break;
      case 'companycity':
        originalValue = originalRow.companyCity || '';
        break;
      case 'companyprovince':
        originalValue = originalRow.companyProvince || '';
        break;
      case 'companypostalcode':
        originalValue = originalRow.companyPostalCode || '';
        break;
      case 'companycountry':
        originalValue = originalRow.companyCountry || '';
        break;
      case 'companywebsite':
        originalValue = originalRow.companyWebsite || '';
        break;
      case 'contactfirstname':
        originalValue = originalRow.contactFirstName || '';
        break;
      case 'contactlastname':
        originalValue = originalRow.contactLastName || '';
        break;
      case 'contactemail':
        originalValue = originalRow.contactEmail || '';
        break;
      case 'contactphone':
        originalValue = originalRow.contactPhone || '';
        break;
      case 'contactmobile':
        originalValue = originalRow.contactMobile || '';
        break;
      case 'contactext':
        originalValue = originalRow.contactExt || '';
        break;
      case 'contacttype':
        originalValue = originalRow.contactType || '';
        break;
      case 'boothnumbers':
        originalValue = originalRow.boothNumbers || '';
        break;
      default:
        originalValue = '';
    }

    const fieldEdit: FieldEditDto = {
      rowNumber,
      fieldName,
      newValue,
      originalValue,
      editReason: 'UserEdit',
    };

    setFieldEdits((prev) => ({
      ...prev,
      [editKey]: fieldEdit,
    }));

    setSessionState((prev) => ({
      ...prev,
      hasUnsavedChanges: true,
    }));

    console.log('🔧 Field edit added:', fieldEdit);
  };

  // Filter issues based on current tab and filters
  const filteredIssues = allIssues.filter((issue) => {
    // Tab filter
    if (activeTab !== 'all' && issue.type !== activeTab) return false;

    // Search filter
    if (
      searchQuery &&
      !issue.message.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    // Unresolved filter
    if (showOnlyUnresolved) {
      // Add logic to check if issue is resolved
      return true; // For now, show all
    }

    return true;
  });

  // Group issues by row
  const issuesByRow = filteredIssues.reduce(
    (acc, issue) => {
      if (!acc[issue.rowNumber]) {
        acc[issue.rowNumber] = [];
      }
      acc[issue.rowNumber].push(issue);
      return acc;
    },
    {} as Record<number, IssueType[]>,
  );

  const handleSaveAllChanges = async () => {
    try {
      setSessionState((prev) => ({ ...prev, isLoading: true }));

      // Collect all field edits from state
      const fieldEditsArray = Object.values(fieldEdits);

      console.log('🔧 Saving field edits:', fieldEditsArray);

      if (fieldEditsArray.length === 0) {
        toast({
          title: 'No changes to save',
          description: "You haven't made any modifications to the data.",
        });
        setSessionState((prev) => ({ ...prev, isLoading: false }));
        return;
      }

      const response = await ExhibitorImportQuery.editFields({
        sessionId: sessionState.sessionId,
        fieldEdits: fieldEditsArray,
      });

      if (response.success) {
        // Invalidate queries to refresh data
        await queryClient.invalidateQueries({
          queryKey: ExhibitorImportQuery.tags,
        });

        // Clear saved field edits
        setFieldEdits({});

        // Remove resolved issues from the issues list
        const resolvedFieldKeys = fieldEditsArray.map(
          (edit) => `${edit.rowNumber}-${edit.fieldName}`,
        );
        setAllIssues((prev) =>
          prev.filter((issue) => {
            if (issue.type === 'error' && issue.fieldName) {
              const issueKey = `${issue.rowNumber}-${issue.fieldName}`;
              return !resolvedFieldKeys.includes(issueKey);
            }
            return true;
          }),
        );

        // Update session state
        setSessionState((prev) => ({
          ...prev,
          hasUnsavedChanges: false,
          summary: response.updatedSummary,
          isLoading: false,
        }));

        // Check if ready to proceed
        if (
          response.updatedSummary.errorRows === 0 &&
          response.updatedSummary.unresolvedDuplicates === 0
        ) {
          toast({
            title: 'All issues resolved!',
            description: 'Ready to proceed with import.',
            variant: 'success',
          });

          onReviewComplete({
            fieldEdits: fieldEditsArray,
            summary: response.updatedSummary,
          });
        } else {
          toast({
            title: 'Issues still remain',
            description: `${response.updatedSummary.errorRows} errors and ${response.updatedSummary.unresolvedDuplicates} duplicates need attention.`,
            variant: 'destructive',
            duration: 8000,
          });
        }
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      toast({
        title: 'Error saving changes',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setSessionState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const getTabCounts = () => {
    const errorCount = allIssues.filter((i) => i.type === 'error').length;
    const warningCount = allIssues.filter((i) => i.type === 'warning').length;
    const duplicateCount = allIssues.filter(
      (i) => i.type === 'duplicate',
    ).length;

    return { errorCount, warningCount, duplicateCount };
  };

  const { errorCount, warningCount, duplicateCount } = getTabCounts();
  const totalIssues = errorCount + warningCount + duplicateCount;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">
          {totalIssues === 0 ? 'Review Complete!' : 'Review & Fix Issues'}
        </h2>
        <p className="text-muted-foreground">
          {totalIssues === 0
            ? 'All data looks good! Ready to proceed with import.'
            : `Review and resolve ${totalIssues} issue${totalIssues > 1 ? 's' : ''} before importing.`}
        </p>
      </div>

      {/* Summary Alert */}
      {totalIssues > 0 && (
        <Alert className="border-orange-500 bg-orange-50">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <strong>Action Required:</strong> {errorCount} error
            {errorCount !== 1 ? 's' : ''}, {warningCount} warning
            {warningCount !== 1 ? 's' : ''}, and {duplicateCount} duplicate
            {duplicateCount !== 1 ? 's' : ''} need your attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Issues Overview</CardTitle>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search issues..."
                  value={searchQuery}
                  onChange={(e) =>
                    updateUrlParams({ search: e.target.value || null })
                  }
                  className="pl-10 w-64"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-unresolved"
                  checked={showOnlyUnresolved}
                  onCheckedChange={(checked) =>
                    updateUrlParams({ showUnresolved: checked ? 'true' : null })
                  }
                />
                <Label htmlFor="show-unresolved" className="text-sm">
                  Unresolved Only
                </Label>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={(value) => updateUrlParams({ tab: value })}
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <span>All Issues</span>
                <Badge variant="secondary">{totalIssues}</Badge>
              </TabsTrigger>
              <TabsTrigger
                value="error"
                className="flex items-center space-x-2"
              >
                <AlertCircle className="h-4 w-4" />
                <span>Errors</span>
                <Badge variant="destructive">{errorCount}</Badge>
              </TabsTrigger>
              <TabsTrigger
                value="warning"
                className="flex items-center space-x-2"
              >
                <AlertTriangle className="h-4 w-4" />
                <span>Warnings</span>
                <Badge variant="warning">{warningCount}</Badge>
              </TabsTrigger>
              <TabsTrigger
                value="duplicate"
                className="flex items-center space-x-2"
              >
                <Copy className="h-4 w-4" />
                <span>Duplicates</span>
                <Badge variant="secondary">{duplicateCount}</Badge>
              </TabsTrigger>
            </TabsList>

            {/* Issues List */}
            <TabsContent value={activeTab} className="mt-6">
              {filteredIssues.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-green-800 mb-2">
                    {activeTab === 'all'
                      ? 'No Issues Found!'
                      : `No ${activeTab}s Found!`}
                  </h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'all'
                      ? 'All data looks good and ready for import.'
                      : `No ${activeTab}s to display with current filters.`}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(issuesByRow).map(([rowNumber, rowIssues]) => (
                    <IssueRowCard
                      key={rowNumber}
                      rowNumber={parseInt(rowNumber)}
                      issues={rowIssues}
                      validationData={validationData}
                      fieldEdits={fieldEdits}
                      onFieldEdit={handleFieldEdit}
                      onDuplicateResolve={(rowNum, resolution) => {
                        // Handle duplicate resolution
                        console.log('Duplicate resolve:', {
                          rowNum,
                          resolution,
                        });
                      }}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.back()}>
          Back to Upload
        </Button>

        <div className="flex space-x-3">
          {sessionState.hasUnsavedChanges && (
            <Button
              onClick={handleSaveAllChanges}
              disabled={sessionState.isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          )}

          <Button
            onClick={() => onReviewComplete({})}
            disabled={totalIssues > 0 || sessionState.isLoading}
            className={
              totalIssues === 0 ? 'bg-green-600 hover:bg-green-700' : ''
            }
          >
            <ChevronRight className="h-4 w-4 mr-2" />
            {totalIssues === 0
              ? 'Proceed to Import'
              : `Fix ${totalIssues} Issue${totalIssues > 1 ? 's' : ''} First`}
          </Button>
        </div>
      </div>
    </div>
  );
};

// Issue Row Card Component
interface IssueRowCardProps {
  rowNumber: number;
  issues: IssueType[];
  validationData: ExhibitorImportValidationResponseDto;
  fieldEdits: Record<string, FieldEditDto>;
  onFieldEdit: (rowNumber: number, fieldName: string, newValue: string) => void;
  onDuplicateResolve: (rowNumber: number, resolution: string) => void;
}

const IssueRowCard: React.FC<IssueRowCardProps> = ({
  rowNumber,
  issues,
  validationData,
  fieldEdits,
  onFieldEdit,
  onDuplicateResolve,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [fieldValues, setFieldValues] = useState<Record<string, string>>({});

  // Get row data
  const rowData = validationData.rows.find((r) => r.rowNumber === rowNumber);

  // Separate issues by type
  const errors = issues.filter((i) => i.type === 'error');
  const warnings = issues.filter((i) => i.type === 'warning');
  const duplicates = issues.filter((i) => i.type === 'duplicate');

  const handleFieldSave = (fieldName: string) => {
    const newValue = fieldValues[fieldName] || '';
    onFieldEdit(rowNumber, fieldName, newValue);
    setEditingField(null);
  };

  const getFieldValue = (fieldName: string): string => {
    // Check for local editing state first
    if (fieldValues[fieldName] !== undefined) {
      return fieldValues[fieldName];
    }

    // Check for pending field edits
    const editKey = `${rowNumber}-${fieldName}`;
    if (fieldEdits[editKey]) {
      return fieldEdits[editKey].newValue;
    }

    // Get original value from row data
    if (!rowData) return '';

    switch (fieldName.toLowerCase()) {
      case 'companyname':
        return rowData.companyName || '';
      case 'companyemail':
        return rowData.companyEmail || '';
      case 'companyphone':
        return rowData.companyPhone || '';
      case 'companyaddress':
        return `${rowData.companyAddress1 || ''} ${rowData.companyAddress2 || ''}`.trim();
      case 'contactfirstname':
        return rowData.contactFirstName || '';
      case 'contactlastname':
        return rowData.contactLastName || '';
      case 'contactemail':
        return rowData.contactEmail || '';
      case 'contactphone':
        return rowData.contactPhone || '';
      default:
        return '';
    }
  };

  return (
    <Card
      className={`border-l-4 ${
        errors.length > 0
          ? 'border-l-red-500'
          : warnings.length > 0
            ? 'border-l-yellow-500'
            : 'border-l-blue-500'
      }`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                errors.length > 0
                  ? 'bg-red-500'
                  : warnings.length > 0
                    ? 'bg-yellow-500'
                    : 'bg-blue-500'
              }`}
            >
              {rowNumber}
            </div>
            <div>
              <h3 className="font-semibold">Row {rowNumber}</h3>
              <p className="text-sm text-muted-foreground">
                {rowData?.companyName || 'Unknown Company'} •{' '}
                {rowData?.contactFirstName} {rowData?.contactLastName}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {errors.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {errors.length} Error{errors.length > 1 ? 's' : ''}
              </Badge>
            )}
            {warnings.length > 0 && (
              <Badge variant="warning" className="text-xs">
                {warnings.length} Warning{warnings.length > 1 ? 's' : ''}
              </Badge>
            )}
            {duplicates.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {duplicates.length} Duplicate{duplicates.length > 1 ? 's' : ''}
              </Badge>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Error Fields */}
            {errors.map((error, index) => (
              <div
                key={`error-${index}`}
                className="border border-red-200 rounded-lg p-4 bg-red-50"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="font-medium text-red-800">
                      {error.fieldName ? `${error.fieldName}: ` : ''}
                      {error.message}
                    </span>
                  </div>
                  {error.fieldName && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingField(error.fieldName!)}
                    >
                      <Edit3 className="h-3 w-3 mr-1" />
                      Fix
                    </Button>
                  )}
                </div>

                {editingField === error.fieldName && (
                  <div className="mt-3 space-y-2">
                    <Input
                      value={
                        fieldValues[error.fieldName!] ??
                        getFieldValue(error.fieldName!)
                      }
                      onChange={(e) =>
                        setFieldValues((prev) => ({
                          ...prev,
                          [error.fieldName!]: e.target.value,
                        }))
                      }
                      placeholder={`Enter ${error.fieldName}`}
                      className="border-red-300 focus:border-red-500"
                    />
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        onClick={() => handleFieldSave(error.fieldName!)}
                      >
                        Save
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingField(null)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* Warning Fields */}
            {warnings.map((warning, index) => (
              <div
                key={`warning-${index}`}
                className="border border-yellow-200 rounded-lg p-4 bg-yellow-50"
              >
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="font-medium text-yellow-800">
                    {warning.fieldName ? `${warning.fieldName}: ` : ''}
                    {warning.message}
                  </span>
                </div>
              </div>
            ))}

            {/* Duplicate Resolution */}
            {duplicates.map((duplicate, index) => (
              <div
                key={`duplicate-${index}`}
                className="border border-blue-200 rounded-lg p-4 bg-blue-50"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <Copy className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-800">
                      {duplicate.message}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      Keep Excel
                    </Button>
                    <Button variant="outline" size="sm">
                      Keep Database
                    </Button>
                    <Button variant="outline" size="sm">
                      Merge
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default UnifiedReviewStep;
