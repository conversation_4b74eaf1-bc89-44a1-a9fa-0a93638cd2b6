'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2 } from 'lucide-react';
import Link from 'next/link';
import { FaPlus } from 'react-icons/fa6';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns } from '@/lib/tableUtils';
import { BriefData } from '@/models/BriefData';
import RoleQuery from '@/services/queries/RoleQuery';

export default function RoleTable() {
  const { data, isLoading } = useQuery({
    queryKey: RoleQuery.tags,
    queryFn: () => RoleQuery.getAll(),
  });
  const newColumns = generateTableColumns<BriefData>(
    {
      id: { name: 'id', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
    },
    {
      'edit-action': {
        name: 'Edit',
        value: <Edit2 className=" size-4" />,
        type: {
          type: 'node',
          render: ({ row, cell }) => (
            <Link
              href={`/dashboard/setup/users-roles/role-management/${row.id ?? 'add'} `}
            >
              <Button variant="secondary" size="icon">
                {cell}
              </Button>
            </Link>
          ),
        },
      },
    },
    false,
  );
  return (
    <DataTable
      filterFields={[{ id: 'name', name: 'Name', type: 'text' }]}
      columns={newColumns}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row  gap-2 justify-end ">
          <Link href="/dashboard/setup/users-roles/role-management/add">
            <Button variant="default">
              <FaPlus />
              Add Role
            </Button>
          </Link>
        </div>
      }
    />
  );
}
