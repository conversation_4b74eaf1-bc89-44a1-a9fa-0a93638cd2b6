'use client';
import { useRouter } from '@/utils/navigation';
import { Button } from './button';

interface HeadingProps {
  title: string;
  description?: string;
}

export const Heading: React.FC<HeadingProps> = ({ title, description }) => {
  const router = useRouter();
  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className="flex flex-row gap-2 border-b border-foreground items-center mb-2 w-[50%] pb-2">
      <Button
        variant="outline"
        // size="sm"
        iconName="BackIcon"
        iconProps={{ className: 'text-main', size: 20 }}
        onClick={handleBackClick}
      >
        Back
      </Button>
      <div className="flex flex-col justify-center w-full">
        <h2 className="text-main text-2xl font-bold tracking-tight">{title}</h2>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
    </div>
  );
};
