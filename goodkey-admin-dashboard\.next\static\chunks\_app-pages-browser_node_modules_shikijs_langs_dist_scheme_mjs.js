"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_scheme_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/scheme.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/scheme.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Scheme\\\",\\\"fileTypes\\\":[\\\"scm\\\",\\\"ss\\\",\\\"sch\\\",\\\"rkt\\\"],\\\"name\\\":\\\"scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#language-functions\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#illegal\\\"}],\\\"repository\\\":{\\\"block-comment\\\":{\\\"begin\\\":\\\"\\\\\\\\#\\\\\\\\|\\\",\\\"contentName\\\":\\\"comment\\\",\\\"end\\\":\\\"\\\\\\\\|\\\\\\\\#\\\",\\\"name\\\":\\\"comment\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-comment\\\",\\\"name\\\":\\\"comment\\\"}]},\\\"comment\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=;)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.scheme\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\";\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scheme\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.semicolon.scheme\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"#[t|f]\\\",\\\"name\\\":\\\"constant.language.boolean.scheme\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\(\\\\\\\\s])((#e|#i)?[0-9]+(\\\\\\\\.[0-9]+)?|(#x)[0-9a-fA-F]+|(#o)[0-7]+|(#b)[01]+)(?=[\\\\\\\\s;()'\\\\\\\",\\\\\\\\[\\\\\\\\]])\\\",\\\"name\\\":\\\"constant.numeric.scheme\\\"}]},\\\"illegal\\\":{\\\"match\\\":\\\"[()\\\\\\\\[\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.parenthesis.scheme\\\"},\\\"language-functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\[))(do|or|and|else|quasiquote|begin|if|case|set!|cond|let|unquote|define|let\\\\\\\\*|unquote-splicing|delay|letrec)(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"keyword.control.scheme\\\"},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tThese functions run a test, and return a boolean\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tanswer.\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\())(char-alphabetic|char-lower-case|char-numeric|char-ready|char-upper-case|char-whitespace|(?:char|string)(?:-ci)?(?:=|<=?|>=?)|atom|boolean|bound-identifier=|char|complex|identifier|integer|symbol|free-identifier=|inexact|eof-object|exact|list|(?:input|output)-port|pair|real|rational|zero|vector|negative|odd|null|string|eq|equal|eqv|even|number|positive|procedure)(\\\\\\\\?)(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.boolean-test.scheme\\\"},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tThese functions change one type into another.\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\())(char->integer|exact->inexact|inexact->exact|integer->char|symbol->string|list->vector|list->string|identifier->symbol|vector->list|string->list|string->number|string->symbol|number->string)(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.convert-type.scheme\\\"},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tThese functions are potentially dangerous because\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthey have side-effects which could affect other\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tparts of the program.\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\())(set-(?:car|cdr)|(?:vector|string)-(?:fill|set))(!)(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.with-side-effects.scheme\\\"},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+, -, *, /, =, >, etc. \\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\())(>=?|<=?|=|[*/+-])(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.scheme\\\"},{\\\"match\\\":\\\"(?<=(\\\\\\\\s|\\\\\\\\())(append|apply|approximate|call-with-current-continuation|call/cc|catch|construct-identifier|define-syntax|display|foo|for-each|force|format|cd|gen-counter|gen-loser|generate-identifier|last-pair|length|let-syntax|letrec-syntax|list|list-ref|list-tail|load|log|macro|magnitude|map|map-streams|max|member|memq|memv|min|newline|nil|not|peek-char|rationalize|read|read-char|return|reverse|sequence|substring|syntax|syntax-rules|transcript-off|transcript-on|truncate|unwrap-syntax|values-list|write|write-char|cons|c(a|d){1,4}r|abs|acos|angle|asin|assoc|assq|assv|atan|ceiling|cos|floor|round|sin|sqrt|tan|(?:real|imag)-part|numerator|denominatormodulo|exp|expt|remainder|quotient|lcm|call-with-(?:input|output)-file|(?:close|current)-(?:input|output)-port|with-(?:input|output)-from-file|open-(?:input|output)-file|char-(?:downcase|upcase|ready)|make-(?:polar|promise|rectangular|string|vector)string(?:-(?:append|copy|length|ref))?|vector(?:-length|-ref))(?=(\\\\\\\\s|\\\\\\\\())\\\",\\\"name\\\":\\\"support.function.general.scheme\\\"}]},\\\"quote\\\":{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\tWe need to be able to quote any kind of item, which creates\\\\n\\\\t\\\\t\\\\t\\\\ta tiny bit of complexity in our grammar.  It is hopefully\\\\n\\\\t\\\\t\\\\t\\\\tnot overwhelming complexity.\\\\n\\\\t\\\\t\\\\t\\\\t\\\\n\\\\t\\\\t\\\\t\\\\tNote: the first two matches are special cases.  quoted\\\\n\\\\t\\\\t\\\\t\\\\tsymbols, and quoted empty lists are considered constant.other\\\\n\\\\t\\\\t\\\\t\\\\t\\\\n\\\\t\\\\t\\\\t\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.symbol.scheme\\\"}},\\\"match\\\":\\\"(')\\\\\\\\s*([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)\\\",\\\"name\\\":\\\"constant.other.symbol.scheme\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.empty-list.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.expression.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"}},\\\"match\\\":\\\"(')\\\\\\\\s*((\\\\\\\\()\\\\\\\\s*(\\\\\\\\)))\\\",\\\"name\\\":\\\"constant.other.empty-list.schem\\\"},{\\\"begin\\\":\\\"(')\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.quoted.scheme\\\"}},\\\"comment\\\":\\\"quoted double-quoted string or s-expression\\\",\\\"end\\\":\\\"(?=[\\\\\\\\s()])|(?<=\\\\\\\\n)\\\",\\\"name\\\":\\\"string.other.quoted-object.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]}]},\\\"quote-sexp\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\()\\\\\\\\s*(quote)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.quote.scheme\\\"}},\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\tSomething quoted with (quote «thing»).  In this case «thing»\\\\n\\\\t\\\\t\\\\t\\\\twill not be evaluated, so we are considering it a string.\\\\n\\\\t\\\\t\\\\t\\\",\\\"contentName\\\":\\\"string.other.quote.scheme\\\",\\\"end\\\":\\\"(?=[\\\\\\\\s)])|(?<=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]},\\\"quoted\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"}},\\\"name\\\":\\\"meta.expression.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#quoted\\\"}]},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},\\\"sexp\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\\))(\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.expression.end.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.after-expression.scheme\\\"}},\\\"name\\\":\\\"meta.expression.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(define)\\\\\\\\s+(\\\\\\\\()([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)((\\\\\\\\s+([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._]))*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.function.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scheme\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.parameter.function.scheme\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.function.scheme\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.procedure.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(lambda)\\\\\\\\s+(\\\\\\\\()((?:([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._])\\\\\\\\s+)*(?:([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._]))?)(\\\\\\\\))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.variable.scheme\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.scheme\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.variable.scheme\\\"}},\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tNot sure this one is quite correct.  That \\\\\\\\s* is\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tparticularly troubling\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.procedure.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\()(define)\\\\\\\\s([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)\\\\\\\\s*.*?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.scheme\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.scheme\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.declaration.variable.scheme\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},{\\\"include\\\":\\\"#quote-sexp\\\"},{\\\"include\\\":\\\"#quote\\\"},{\\\"include\\\":\\\"#language-functions\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\(\\\\\\\\s])(#\\\\\\\\\\\\\\\\)(space|newline|tab)(?=[\\\\\\\\s\\\\\\\\)])\\\",\\\"name\\\":\\\"constant.character.named.scheme\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\(\\\\\\\\s])(#\\\\\\\\\\\\\\\\)x[0-9A-F]{2,4}(?=[\\\\\\\\s\\\\\\\\)])\\\",\\\"name\\\":\\\"constant.character.hex-literal.scheme\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\(\\\\\\\\s])(#\\\\\\\\\\\\\\\\).(?=[\\\\\\\\s\\\\\\\\)])\\\",\\\"name\\\":\\\"constant.character.escape.scheme\\\"},{\\\"comment\\\":\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthe . in (a . b) which conses together two elements\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ta and b. (a b c) == (a . (b . (c . nil)))\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\",\\\"match\\\":\\\"(?<=[ ()])\\\\\\\\.(?=[ ()])\\\",\\\"name\\\":\\\"punctuation.separator.cons.scheme\\\"},{\\\"include\\\":\\\"#sexp\\\"},{\\\"include\\\":\\\"#illegal\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scheme\\\"}},\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scheme\\\"}},\\\"name\\\":\\\"string.quoted.double.scheme\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.scheme\\\"}]}},\\\"scopeName\\\":\\\"source.scheme\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/scheme.mjs\n"));

/***/ })

}]);