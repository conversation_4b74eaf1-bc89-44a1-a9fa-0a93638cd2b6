"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-docx";
exports.ids = ["vendor-chunks/prosemirror-docx"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-docx/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-docx/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocxSerializer: () => (/* reexport safe */ _serializer__WEBPACK_IMPORTED_MODULE_0__.DocxSerializer),\n/* harmony export */   DocxSerializerState: () => (/* reexport safe */ _serializer__WEBPACK_IMPORTED_MODULE_0__.DocxSerializerState),\n/* harmony export */   createDocFromState: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_2__.createDocFromState),\n/* harmony export */   defaultDocxSerializer: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.defaultDocxSerializer),\n/* harmony export */   defaultMarks: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.defaultMarks),\n/* harmony export */   defaultNodes: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.defaultNodes),\n/* harmony export */   writeDocx: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_2__.writeDocx)\n/* harmony export */ });\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./serializer */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/serializer.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/schema.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/utils.js\");\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvc2VtaXJyb3ItZG9jeC9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQW1FO0FBQ1U7QUFDckI7QUFDeEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccHJvc2VtaXJyb3ItZG9jeFxcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBEb2N4U2VyaWFsaXplclN0YXRlLCBEb2N4U2VyaWFsaXplciB9IGZyb20gJy4vc2VyaWFsaXplcic7XG5leHBvcnQgeyBkZWZhdWx0RG9jeFNlcmlhbGl6ZXIsIGRlZmF1bHROb2RlcywgZGVmYXVsdE1hcmtzIH0gZnJvbSAnLi9zY2hlbWEnO1xuZXhwb3J0IHsgd3JpdGVEb2N4LCBjcmVhdGVEb2NGcm9tU3RhdGUgfSBmcm9tICcuL3V0aWxzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-docx/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prosemirror-docx/dist/esm/numbering.js":
/*!*************************************************************!*\
  !*** ./node_modules/prosemirror-docx/dist/esm/numbering.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNumbering: () => (/* binding */ createNumbering)\n/* harmony export */ });\n/* harmony import */ var docx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! docx */ \"(ssr)/./node_modules/docx/build/index.mjs\");\n\nfunction basicIndentStyle(indent) {\n    return {\n        alignment: docx__WEBPACK_IMPORTED_MODULE_0__.AlignmentType.START,\n        style: {\n            paragraph: {\n                indent: { left: (0,docx__WEBPACK_IMPORTED_MODULE_0__.convertInchesToTwip)(indent), hanging: (0,docx__WEBPACK_IMPORTED_MODULE_0__.convertInchesToTwip)(0.18) },\n            },\n        },\n    };\n}\nconst numbered = Array(3)\n    .fill([docx__WEBPACK_IMPORTED_MODULE_0__.LevelFormat.DECIMAL, docx__WEBPACK_IMPORTED_MODULE_0__.LevelFormat.LOWER_LETTER, docx__WEBPACK_IMPORTED_MODULE_0__.LevelFormat.LOWER_ROMAN])\n    .flat()\n    .map((format, level) => (Object.assign({ level,\n    format, text: `%${level + 1}.` }, basicIndentStyle((level + 1) / 2))));\nconst bullets = Array(3)\n    .fill(['●', '○', '■'])\n    .flat()\n    .map((text, level) => (Object.assign({ level, format: docx__WEBPACK_IMPORTED_MODULE_0__.LevelFormat.BULLET, text }, basicIndentStyle((level + 1) / 2))));\nconst styles = {\n    numbered,\n    bullets,\n};\nfunction createNumbering(reference, style) {\n    return {\n        reference,\n        levels: styles[style],\n    };\n}\n//# sourceMappingURL=numbering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-docx/dist/esm/numbering.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prosemirror-docx/dist/esm/schema.js":
/*!**********************************************************!*\
  !*** ./node_modules/prosemirror-docx/dist/esm/schema.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultDocxSerializer: () => (/* binding */ defaultDocxSerializer),\n/* harmony export */   defaultMarks: () => (/* binding */ defaultMarks),\n/* harmony export */   defaultNodes: () => (/* binding */ defaultNodes)\n/* harmony export */ });\n/* harmony import */ var docx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! docx */ \"(ssr)/./node_modules/docx/build/index.mjs\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serializer */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/serializer.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/utils.js\");\n\n\n\nconst defaultNodes = {\n    text(state, node) {\n        var _a;\n        state.text((_a = node.text) !== null && _a !== void 0 ? _a : '');\n    },\n    paragraph(state, node) {\n        state.renderInline(node);\n        state.closeBlock(node);\n    },\n    heading(state, node) {\n        state.renderInline(node);\n        const heading = [\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_1,\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_2,\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_3,\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_4,\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_5,\n            docx__WEBPACK_IMPORTED_MODULE_0__.HeadingLevel.HEADING_6,\n        ][node.attrs.level - 1];\n        state.closeBlock(node, { heading });\n    },\n    blockquote(state, node) {\n        state.renderContent(node, { style: 'IntenseQuote' });\n    },\n    code_block(state, node) {\n        // TODO: something for code\n        state.renderContent(node);\n        state.closeBlock(node);\n    },\n    horizontal_rule(state, node) {\n        // Kinda hacky, but this works to insert two paragraphs, the first with a break\n        state.closeBlock(node, { thematicBreak: true });\n        state.closeBlock(node);\n    },\n    hard_break(state) {\n        state.addRunOptions({ break: 1 });\n    },\n    ordered_list(state, node) {\n        state.renderList(node, 'numbered');\n    },\n    bullet_list(state, node) {\n        state.renderList(node, 'bullets');\n    },\n    list_item(state, node) {\n        state.renderListItem(node);\n    },\n    // Presentational\n    image(state, node) {\n        const { src } = node.attrs;\n        state.image(src);\n        state.closeBlock(node);\n    },\n    // Technical\n    math(state, node) {\n        state.math((0,_utils__WEBPACK_IMPORTED_MODULE_2__.getLatexFromNode)(node), { inline: true });\n    },\n    equation(state, node) {\n        const { id, numbered } = node.attrs;\n        state.math((0,_utils__WEBPACK_IMPORTED_MODULE_2__.getLatexFromNode)(node), { inline: false, numbered, id });\n        state.closeBlock(node);\n    },\n    table(state, node) {\n        state.table(node);\n    },\n};\nconst defaultMarks = {\n    em() {\n        return { italics: true };\n    },\n    strong() {\n        return { bold: true };\n    },\n    italic() {\n        return { italics: true };\n    },\n    bold() {\n        return { bold: true };\n    },\n    link() {\n        // Note, this is handled specifically in the serializer\n        // Word treats links more like a Node rather than a mark\n        return {};\n    },\n    code() {\n        return {\n            font: {\n                name: 'Monospace',\n            },\n            color: '000000',\n            shading: {\n                type: docx__WEBPACK_IMPORTED_MODULE_0__.ShadingType.SOLID,\n                color: 'D2D3D2',\n                fill: 'D2D3D2',\n            },\n        };\n    },\n    abbr() {\n        // TODO: abbreviation\n        return {};\n    },\n    subscript() {\n        return { subScript: true };\n    },\n    superscript() {\n        return { superScript: true };\n    },\n    strikethrough() {\n        // doubleStrike!\n        return { strike: true };\n    },\n    underline() {\n        return {\n            underline: {},\n        };\n    },\n    smallcaps() {\n        return { smallCaps: true };\n    },\n    allcaps() {\n        return { allCaps: true };\n    },\n};\nconst defaultDocxSerializer = new _serializer__WEBPACK_IMPORTED_MODULE_1__.DocxSerializer(defaultNodes, defaultMarks);\n//# sourceMappingURL=schema.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-docx/dist/esm/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prosemirror-docx/dist/esm/serializer.js":
/*!**************************************************************!*\
  !*** ./node_modules/prosemirror-docx/dist/esm/serializer.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocxSerializer: () => (/* binding */ DocxSerializer),\n/* harmony export */   DocxSerializerState: () => (/* binding */ DocxSerializerState)\n/* harmony export */ });\n/* harmony import */ var docx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! docx */ \"(ssr)/./node_modules/docx/build/index.mjs\");\n/* harmony import */ var buffer_image_size__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! buffer-image-size */ \"(ssr)/./node_modules/buffer-image-size/lib/index.js\");\n/* harmony import */ var buffer_image_size__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(buffer_image_size__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _numbering__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./numbering */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/numbering.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/prosemirror-docx/dist/esm/utils.js\");\n\n\n\n\nconst MAX_IMAGE_WIDTH = 600;\nfunction createReferenceBookmark(id, kind, before, after) {\n    const textBefore = before ? [new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(before)] : [];\n    const textAfter = after ? [new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(after)] : [];\n    return new docx__WEBPACK_IMPORTED_MODULE_0__.Bookmark({\n        id,\n        children: [...textBefore, new docx__WEBPACK_IMPORTED_MODULE_0__.SequentialIdentifier(kind), ...textAfter],\n    });\n}\nclass DocxSerializerState {\n    constructor(nodes, marks, options) {\n        this.footnotes = {};\n        this.current = [];\n        // not sure what this actually is, seems to be close for 8.5x11\n        this.maxImageWidth = MAX_IMAGE_WIDTH;\n        this.$footnoteCounter = 0;\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options !== null && options !== void 0 ? options : {};\n        this.children = [];\n        this.numbering = [];\n    }\n    renderContent(parent, opts) {\n        parent.forEach((node, _, i) => {\n            if (opts)\n                this.addParagraphOptions(opts);\n            this.render(node, parent, i);\n        });\n    }\n    render(node, parent, index) {\n        if (typeof parent === 'number')\n            throw new Error('!');\n        if (!this.nodes[node.type.name])\n            throw new Error(`Token type \\`${node.type.name}\\` not supported by Word renderer`);\n        this.nodes[node.type.name](this, node, parent, index);\n    }\n    renderMarks(node, marks) {\n        return marks\n            .map((mark) => {\n            var _a, _b;\n            return (_b = (_a = this.marks)[mark.type.name]) === null || _b === void 0 ? void 0 : _b.call(_a, this, node, mark);\n        })\n            .reduce((a, b) => (Object.assign(Object.assign({}, a), b)), {});\n    }\n    renderInline(parent) {\n        // Pop the stack over to this object when we encounter a link, and closeLink restores it\n        let currentLink;\n        const closeLink = () => {\n            if (!currentLink)\n                return;\n            const hyperlink = new docx__WEBPACK_IMPORTED_MODULE_0__.ExternalHyperlink({\n                link: currentLink.link,\n                // child: this.current[0],\n                children: this.current,\n            });\n            this.current = [...currentLink.stack, hyperlink];\n            currentLink = undefined;\n        };\n        const openLink = (href) => {\n            const sameLink = href === (currentLink === null || currentLink === void 0 ? void 0 : currentLink.link);\n            this.addRunOptions({ style: 'Hyperlink' });\n            // TODO: https://github.com/dolanmiu/docx/issues/1119\n            // Remove the if statement here and oneLink!\n            const oneLink = true;\n            if (!oneLink) {\n                closeLink();\n            }\n            else {\n                if (currentLink && sameLink)\n                    return;\n                if (currentLink && !sameLink) {\n                    // Close previous, and open a new one\n                    closeLink();\n                }\n            }\n            currentLink = {\n                link: href,\n                stack: this.current,\n            };\n            this.current = [];\n        };\n        const progress = (node, offset, index) => {\n            const links = node.marks.filter((m) => m.type.name === 'link');\n            const hasLink = links.length > 0;\n            if (hasLink) {\n                openLink(links[0].attrs.href);\n            }\n            else if (!hasLink && currentLink) {\n                closeLink();\n            }\n            if (node.isText) {\n                this.text(node.text, this.renderMarks(node, [...node.marks]));\n            }\n            else {\n                this.render(node, parent, index);\n            }\n        };\n        parent.forEach(progress);\n        // Must call close at the end of everything, just in case\n        closeLink();\n    }\n    renderList(node, style) {\n        if (!this.currentNumbering) {\n            const nextId = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.createShortId)();\n            this.numbering.push((0,_numbering__WEBPACK_IMPORTED_MODULE_2__.createNumbering)(nextId, style));\n            this.currentNumbering = { reference: nextId, level: 0 };\n        }\n        else {\n            const { reference, level } = this.currentNumbering;\n            this.currentNumbering = { reference, level: level + 1 };\n        }\n        this.renderContent(node);\n        if (this.currentNumbering.level === 0) {\n            delete this.currentNumbering;\n        }\n        else {\n            const { reference, level } = this.currentNumbering;\n            this.currentNumbering = { reference, level: level - 1 };\n        }\n    }\n    // This is a pass through to the paragraphs, etc. underneath they will close the block\n    renderListItem(node) {\n        if (!this.currentNumbering)\n            throw new Error('Trying to create a list item without a list?');\n        this.addParagraphOptions({ numbering: this.currentNumbering });\n        this.renderContent(node);\n    }\n    addParagraphOptions(opts) {\n        this.nextParentParagraphOpts = Object.assign(Object.assign({}, this.nextParentParagraphOpts), opts);\n    }\n    addRunOptions(opts) {\n        this.nextRunOpts = Object.assign(Object.assign({}, this.nextRunOpts), opts);\n    }\n    text(text, opts) {\n        if (!text)\n            return;\n        this.current.push(new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(Object.assign(Object.assign({ text }, this.nextRunOpts), opts)));\n        delete this.nextRunOpts;\n    }\n    math(latex, opts = { inline: true }) {\n        var _a;\n        if (opts.inline || !opts.numbered) {\n            this.current.push(new docx__WEBPACK_IMPORTED_MODULE_0__.Math({ children: [new docx__WEBPACK_IMPORTED_MODULE_0__.MathRun(latex)] }));\n            return;\n        }\n        const id = (_a = opts.id) !== null && _a !== void 0 ? _a : (0,_utils__WEBPACK_IMPORTED_MODULE_3__.createShortId)();\n        this.current = [\n            new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun('\\t'),\n            new docx__WEBPACK_IMPORTED_MODULE_0__.Math({\n                children: [new docx__WEBPACK_IMPORTED_MODULE_0__.MathRun(latex)],\n            }),\n            new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun('\\t('),\n            createReferenceBookmark(id, 'Equation'),\n            new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(')'),\n        ];\n        this.addParagraphOptions({\n            tabStops: [\n                {\n                    type: docx__WEBPACK_IMPORTED_MODULE_0__.TabStopType.CENTER,\n                    position: docx__WEBPACK_IMPORTED_MODULE_0__.TabStopPosition.MAX / 2,\n                },\n                {\n                    type: docx__WEBPACK_IMPORTED_MODULE_0__.TabStopType.RIGHT,\n                    position: docx__WEBPACK_IMPORTED_MODULE_0__.TabStopPosition.MAX,\n                },\n            ],\n        });\n    }\n    image(src, widthPercent = 70, align = 'center', imageRunOpts) {\n        const buffer = this.options.getImageBuffer(src);\n        const dimensions = buffer_image_size__WEBPACK_IMPORTED_MODULE_1___default()(buffer);\n        const aspect = dimensions.height / dimensions.width;\n        const width = this.maxImageWidth * (widthPercent / 100);\n        this.current.push(new docx__WEBPACK_IMPORTED_MODULE_0__.ImageRun(Object.assign(Object.assign({}, imageRunOpts), { data: buffer, \n            // Assume that the file extension is a valid docx image type.\n            type: src.replace(/.*\\./, '').toLowerCase(), transformation: Object.assign(Object.assign({}, ((imageRunOpts === null || imageRunOpts === void 0 ? void 0 : imageRunOpts.transformation) || {})), { width, height: width * aspect }) })));\n        let alignment;\n        switch (align) {\n            case 'right':\n                alignment = docx__WEBPACK_IMPORTED_MODULE_0__.AlignmentType.RIGHT;\n                break;\n            case 'left':\n                alignment = docx__WEBPACK_IMPORTED_MODULE_0__.AlignmentType.LEFT;\n                break;\n            default:\n                alignment = docx__WEBPACK_IMPORTED_MODULE_0__.AlignmentType.CENTER;\n        }\n        this.addParagraphOptions({\n            // TODO: fix\n            alignment: alignment,\n        });\n    }\n    table(node, opts = {}) {\n        const { getCellOptions, getRowOptions, tableOptions } = opts;\n        const actualChildren = this.children;\n        const rows = [];\n        node.content.forEach((row) => {\n            const cells = [];\n            // Check if all cells are headers in this row\n            let tableHeader = true;\n            row.content.forEach((cell) => {\n                if (cell.type.name !== 'table_header') {\n                    tableHeader = false;\n                }\n            });\n            // This scales images inside of tables\n            this.maxImageWidth = MAX_IMAGE_WIDTH / row.content.childCount;\n            row.content.forEach((cell) => {\n                var _a, _b;\n                this.children = [];\n                this.renderContent(cell);\n                const tableCellOpts = { children: this.children };\n                const colspan = (_a = cell.attrs.colspan) !== null && _a !== void 0 ? _a : 1;\n                const rowspan = (_b = cell.attrs.rowspan) !== null && _b !== void 0 ? _b : 1;\n                if (colspan > 1)\n                    tableCellOpts.columnSpan = colspan;\n                if (rowspan > 1)\n                    tableCellOpts.rowSpan = rowspan;\n                cells.push(new docx__WEBPACK_IMPORTED_MODULE_0__.TableCell(Object.assign(Object.assign({}, tableCellOpts), ((getCellOptions === null || getCellOptions === void 0 ? void 0 : getCellOptions(cell)) || {}))));\n            });\n            rows.push(new docx__WEBPACK_IMPORTED_MODULE_0__.TableRow(Object.assign(Object.assign({}, ((getRowOptions === null || getRowOptions === void 0 ? void 0 : getRowOptions(row)) || {})), { children: cells, tableHeader })));\n        });\n        this.maxImageWidth = MAX_IMAGE_WIDTH;\n        const table = new docx__WEBPACK_IMPORTED_MODULE_0__.Table(Object.assign(Object.assign({}, tableOptions), { rows }));\n        actualChildren.push(table);\n        // If there are multiple tables, this seperates them\n        actualChildren.push(new docx__WEBPACK_IMPORTED_MODULE_0__.Paragraph(''));\n        this.children = actualChildren;\n    }\n    captionLabel(id, kind, { suffix } = { suffix: ': ' }) {\n        this.current.push(...[createReferenceBookmark(id, kind, `${kind} `), new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(suffix)]);\n    }\n    footnote(node) {\n        const { current, nextRunOpts } = this;\n        // Delete everything and work with the footnote inline on the current\n        this.current = [];\n        delete this.nextRunOpts;\n        this.$footnoteCounter += 1;\n        this.renderInline(node);\n        this.footnotes[this.$footnoteCounter] = {\n            children: [new docx__WEBPACK_IMPORTED_MODULE_0__.Paragraph({ children: this.current })],\n        };\n        this.current = current;\n        this.nextRunOpts = nextRunOpts;\n        this.current.push(new docx__WEBPACK_IMPORTED_MODULE_0__.FootnoteReferenceRun(this.$footnoteCounter));\n    }\n    closeBlock(node, props) {\n        const paragraph = new docx__WEBPACK_IMPORTED_MODULE_0__.Paragraph(Object.assign(Object.assign({ children: this.current }, this.nextParentParagraphOpts), props));\n        this.current = [];\n        delete this.nextParentParagraphOpts;\n        this.children.push(paragraph);\n    }\n    createReference(id, before, after) {\n        const children = [];\n        if (before)\n            children.push(new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(before));\n        children.push(new docx__WEBPACK_IMPORTED_MODULE_0__.SimpleField(`REF ${id} \\\\h`));\n        if (after)\n            children.push(new docx__WEBPACK_IMPORTED_MODULE_0__.TextRun(after));\n        const ref = new docx__WEBPACK_IMPORTED_MODULE_0__.InternalHyperlink({ anchor: id, children });\n        this.current.push(ref);\n    }\n}\nclass DocxSerializer {\n    constructor(nodes, marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    serialize(content, options) {\n        const state = new DocxSerializerState(this.nodes, this.marks, options);\n        state.renderContent(content);\n        const doc = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.createDocFromState)(state);\n        return doc;\n    }\n}\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-docx/dist/esm/serializer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/prosemirror-docx/dist/esm/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-docx/dist/esm/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDocFromState: () => (/* binding */ createDocFromState),\n/* harmony export */   createShortId: () => (/* binding */ createShortId),\n/* harmony export */   getLatexFromNode: () => (/* binding */ getLatexFromNode),\n/* harmony export */   writeDocx: () => (/* binding */ writeDocx)\n/* harmony export */ });\n/* harmony import */ var docx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! docx */ \"(ssr)/./node_modules/docx/build/index.mjs\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nfunction createShortId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction createDocFromState(state) {\n    const doc = new docx__WEBPACK_IMPORTED_MODULE_0__.Document({\n        footnotes: state.footnotes,\n        numbering: {\n            config: state.numbering,\n        },\n        sections: [\n            {\n                properties: {\n                    type: docx__WEBPACK_IMPORTED_MODULE_0__.SectionType.CONTINUOUS,\n                },\n                children: state.children,\n            },\n        ],\n    });\n    return doc;\n}\nfunction writeDocx(doc, write) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const buffer = yield docx__WEBPACK_IMPORTED_MODULE_0__.Packer.toBuffer(doc);\n        return write(buffer);\n    });\n}\nfunction getLatexFromNode(node) {\n    let math = '';\n    node.forEach((child) => {\n        if (child.isText)\n            math += child.text;\n        // TODO: improve this as we may have other things in the future\n    });\n    return math;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-docx/dist/esm/utils.js\n");

/***/ })

};
;