"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_typespec_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/typespec.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/typespec.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"TypeSpec\\\",\\\"fileTypes\\\":[\\\"tsp\\\"],\\\"name\\\":\\\"typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"}],\\\"repository\\\":{\\\"alias-id\\\":{\\\"begin\\\":\\\"(=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.alias-id.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"alias-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(alias)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.alias-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#alias-id\\\"},{\\\"include\\\":\\\"#type-parameters\\\"}]},\\\"augment-decorator-statement\\\":{\\\"begin\\\":\\\"((@@)\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"end\\\":\\\"(?=[_$[:alpha:]])|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.augment-decorator-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"}]},\\\"block-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"boolean-literal\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.tsp\\\"},\\\"callExpression\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.callExpression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"const-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(const)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.const-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#operator-assignment\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"((@)\\\\\\\\b[_$[:alpha:]](?:[_$[:alnum:]]|\\\\\\\\.[_$[:alpha:]])*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"end\\\":\\\"(?=[_$[:alpha:]])|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.decorator.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"}]},\\\"decorator-declaration-statement\\\":{\\\"begin\\\":\\\"(?:(extern)\\\\\\\\s+)?\\\\\\\\b(dec)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.decorator-declaration-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"}]},\\\"directive\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(#\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.directive.name.tsp\\\"}},\\\"end\\\":\\\"$|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.directive.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"}]},\\\"doc-comment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"comment.block.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"comment.block.tsp\\\"}},\\\"name\\\":\\\"comment.block.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment-block\\\"}]},\\\"doc-comment-block\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment-param\\\"},{\\\"include\\\":\\\"#doc-comment-return-tag\\\"},{\\\"include\\\":\\\"#doc-comment-unknown-tag\\\"}]},\\\"doc-comment-param\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"match\\\":\\\"((@)(?:param|template|prop))\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"doc-comment-return-tag\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.tag.tspdoc\\\"}},\\\"match\\\":\\\"((@)(?:returns))\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"doc-comment-unknown-tag\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.tsp\\\"}},\\\"match\\\":\\\"((@)(?:\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`))\\\\\\\\b\\\",\\\"name\\\":\\\"comment.block.tsp\\\"},\\\"else-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\b(else)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.else-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-expression\\\"},{\\\"include\\\":\\\"#projection-body\\\"}]},\\\"else-if-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\b(else)\\\\\\\\s+(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.else-if-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-expression\\\"},{\\\"include\\\":\\\"#projection-body\\\"}]},\\\"enum-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.enum-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#enum-member\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"enum-member\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:?))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.enum-member.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"enum-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(enum)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.enum-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#enum-body\\\"}]},\\\"escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.tsp\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#parenthesized-expression\\\"},{\\\"include\\\":\\\"#valueof\\\"},{\\\"include\\\":\\\"#typeof\\\"},{\\\"include\\\":\\\"#type-arguments\\\"},{\\\"include\\\":\\\"#object-literal\\\"},{\\\"include\\\":\\\"#tuple-literal\\\"},{\\\"include\\\":\\\"#tuple-expression\\\"},{\\\"include\\\":\\\"#model-expression\\\"},{\\\"include\\\":\\\"#callExpression\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"}]},\\\"function-call\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.function-call.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"function-declaration-statement\\\":{\\\"begin\\\":\\\"(?:(extern)\\\\\\\\s+)?\\\\\\\\b(fn)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.function-declaration-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"identifier-expression\\\":{\\\"match\\\":\\\"\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`\\\",\\\"name\\\":\\\"entity.name.type.tsp\\\"},\\\"if-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\b(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.if-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-expression\\\"},{\\\"include\\\":\\\"#projection-body\\\"}]},\\\"import-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(import)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.import-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"}]},\\\"interface-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.interface-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#interface-member\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"interface-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.interface-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"interface-member\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\b(op)\\\\\\\\b\\\\\\\\s+)?(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.interface-member.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-signature\\\"}]},\\\"interface-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.interface-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#interface-heritage\\\"},{\\\"include\\\":\\\"#interface-body\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"line-comment\\\":{\\\"match\\\":\\\"//.*$\\\",\\\"name\\\":\\\"comment.line.double-slash.tsp\\\"},\\\"model-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.model-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#model-property\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"model-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends|is)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.model-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"model-property\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)|(\\\\\\\\\\\\\\\"(?:[^\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*\\\\\\\\\\\\\\\"))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.quoted.double.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.model-property.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-annotation\\\"},{\\\"include\\\":\\\"#operator-assignment\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"model-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(model)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.model-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#model-heritage\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"namespace-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.namespace-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"}]},\\\"namespace-name\\\":{\\\"begin\\\":\\\"(?=[_$[:alpha:]])\\\",\\\"end\\\":\\\"((?=\\\\\\\\{)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.namespace-name.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#identifier-expression\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"}]},\\\"namespace-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(namespace)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.namespace-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#namespace-name\\\"},{\\\"include\\\":\\\"#namespace-body\\\"}]},\\\"numeric-literal\\\":{\\\"match\\\":\\\"(?:\\\\\\\\b(?<!\\\\\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\\\\\b(?!\\\\\\\\$)|\\\\\\\\b(?<!\\\\\\\\$)0(?:b|B)[01][01_]*(n)?\\\\\\\\b(?!\\\\\\\\$)|(?<!\\\\\\\\$)(?:(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.)(n)?\\\\\\\\B)|(?:\\\\\\\\B(\\\\\\\\.)[0-9][0-9_]*(n)?\\\\\\\\b)|(?:\\\\\\\\b[0-9][0-9_]*(n)?\\\\\\\\b(?!\\\\\\\\.)))(?!\\\\\\\\$))\\\",\\\"name\\\":\\\"constant.numeric.tsp\\\"},\\\"object-literal\\\":{\\\"begin\\\":\\\"#\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.hashcurlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.object-literal.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#object-literal-property\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"object-literal-property\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.object-literal-property.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"operation-heritage\\\":{\\\"begin\\\":\\\"\\\\\\\\b(is)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.operation-heritage.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"operation-parameters\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.operation-parameters.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#model-property\\\"},{\\\"include\\\":\\\"#spread-operator\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"operation-signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#operation-heritage\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"},{\\\"include\\\":\\\"#type-annotation\\\"}]},\\\"operation-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(op)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.operation-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-signature\\\"}]},\\\"operator-assignment\\\":{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"},\\\"parenthesized-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.parenthesized-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"projection\\\":{\\\"begin\\\":\\\"(from|to)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.projection.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-parameters\\\"},{\\\"include\\\":\\\"#projection-body\\\"}]},\\\"projection-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.projection-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-expression\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"projection-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#else-if-expression\\\"},{\\\"include\\\":\\\"#if-expression\\\"},{\\\"include\\\":\\\"#else-expression\\\"},{\\\"include\\\":\\\"#function-call\\\"}]},\\\"projection-parameter\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.projection-parameter.typespec\\\",\\\"patterns\\\":[]},\\\"projection-parameters\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.close.tsp\\\"}},\\\"name\\\":\\\"meta.projection-parameters.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#projection-parameter\\\"}]},\\\"projection-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(projection)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)(#)(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.selector.tsp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"}},\\\"end\\\":\\\"((?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.projection-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection-statement-body\\\"}]},\\\"projection-statement-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.projection-statement-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#projection\\\"}]},\\\"punctuation-accessor\\\":{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.accessor.tsp\\\"},\\\"punctuation-comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.tsp\\\"},\\\"punctuation-semicolon\\\":{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.tsp\\\"},\\\"scalar-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.scalar-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#scalar-constructor\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"scalar-constructor\\\":{\\\"begin\\\":\\\"\\\\\\\\b(init)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-constructor.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#operation-parameters\\\"}]},\\\"scalar-extends\\\":{\\\"begin\\\":\\\"\\\\\\\\b(extends)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-extends.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"scalar-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(scalar)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.scalar-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameters\\\"},{\\\"include\\\":\\\"#scalar-extends\\\"},{\\\"include\\\":\\\"#scalar-body\\\"}]},\\\"spread-operator\\\":{\\\"begin\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.spread.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.spread-operator.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#augment-decorator-statement\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#model-statement\\\"},{\\\"include\\\":\\\"#scalar-statement\\\"},{\\\"include\\\":\\\"#union-statement\\\"},{\\\"include\\\":\\\"#interface-statement\\\"},{\\\"include\\\":\\\"#enum-statement\\\"},{\\\"include\\\":\\\"#alias-statement\\\"},{\\\"include\\\":\\\"#const-statement\\\"},{\\\"include\\\":\\\"#namespace-statement\\\"},{\\\"include\\\":\\\"#operation-statement\\\"},{\\\"include\\\":\\\"#import-statement\\\"},{\\\"include\\\":\\\"#using-statement\\\"},{\\\"include\\\":\\\"#decorator-declaration-statement\\\"},{\\\"include\\\":\\\"#function-declaration-statement\\\"},{\\\"include\\\":\\\"#projection-statement\\\"},{\\\"include\\\":\\\"#punctuation-semicolon\\\"}]},\\\"string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"|$\\\",\\\"name\\\":\\\"string.quoted.double.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#template-expression\\\"},{\\\"include\\\":\\\"#escape-character\\\"}]},\\\"template-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\$\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.begin.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.template-expression.end.tsp\\\"}},\\\"name\\\":\\\"meta.template-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"token\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comment\\\"},{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"},{\\\"include\\\":\\\"#triple-quoted-string-literal\\\"},{\\\"include\\\":\\\"#string-literal\\\"},{\\\"include\\\":\\\"#boolean-literal\\\"},{\\\"include\\\":\\\"#numeric-literal\\\"}]},\\\"triple-quoted-string-literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.triple.tsp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#template-expression\\\"},{\\\"include\\\":\\\"#escape-character\\\"}]},\\\"tuple-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.close.tsp\\\"}},\\\"name\\\":\\\"meta.tuple-expression.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"tuple-literal\\\":{\\\"begin\\\":\\\"#\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.hashsquarebracket.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.squarebracket.close.tsp\\\"}},\\\"name\\\":\\\"meta.tuple-literal.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-annotation\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\??)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.optional.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|=|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-annotation.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-argument\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(=))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"name\\\":\\\"meta.type-argument.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-arguments\\\":{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.tsp\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.tsp\\\"}},\\\"name\\\":\\\"meta.type-arguments.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-argument\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"type-parameter\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#type-parameter-constraint\\\"},{\\\"include\\\":\\\"#type-parameter-default\\\"}]},\\\"type-parameter-constraint\\\":{\\\"begin\\\":\\\"extends\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter-constraint.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-parameter-default\\\":{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.assignment.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.type-parameter-default.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"type-parameters\\\":{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.begin.tsp\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.typeparameters.end.tsp\\\"}},\\\"name\\\":\\\"meta.type-parameters.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type-parameter\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"typeof\\\":{\\\"begin\\\":\\\"\\\\\\\\b(typeof)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.typeof.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"union-body\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.open.tsp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.curlybrace.close.tsp\\\"}},\\\"name\\\":\\\"meta.union-body.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#union-variant\\\"},{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#directive\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#punctuation-comma\\\"}]},\\\"union-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(union)\\\\\\\\b\\\\\\\\s+(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.tsp\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.union-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#union-body\\\"}]},\\\"union-variant\\\":{\\\"begin\\\":\\\"(?:(\\\\\\\\b[_$[:alpha:]][_$[:alnum:]]*\\\\\\\\b|`(?:[^`\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\.)*`)\\\\\\\\s*(:))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.name.tsp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.type.annotation.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.union-variant.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"using-statement\\\":{\\\"begin\\\":\\\"\\\\\\\\b(using)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.using-statement.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#token\\\"},{\\\"include\\\":\\\"#identifier-expression\\\"},{\\\"include\\\":\\\"#punctuation-accessor\\\"}]},\\\"valueof\\\":{\\\"begin\\\":\\\"\\\\\\\\b(valueof)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tsp\\\"}},\\\"end\\\":\\\"(?=>)|(?=,|;|@|\\\\\\\\)|\\\\\\\\}|\\\\\\\\b(?:extern)\\\\\\\\b|\\\\\\\\b(?:namespace|model|op|using|import|enum|alias|union|interface|dec|fn)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.valueof.typespec\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]}},\\\"scopeName\\\":\\\"source.tsp\\\",\\\"aliases\\\":[\\\"tsp\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L3R5cGVzcGVjLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsd0NBQXdDLDBGQUEwRiwyQkFBMkIsa0JBQWtCLGNBQWMsMkNBQTJDLE9BQU8sOENBQThDLGlCQUFpQixjQUFjLDRKQUE0Siw0QkFBNEIsRUFBRSxzQkFBc0Isb0lBQW9JLE9BQU8sK0JBQStCLFFBQVEsbUNBQW1DLGlCQUFpQixjQUFjLG1LQUFtSywwQkFBMEIsRUFBRSxpQ0FBaUMsRUFBRSxrQ0FBa0Msb0dBQW9HLE9BQU8saUNBQWlDLFFBQVEsa0NBQWtDLG1DQUFtQyxjQUFjLCtLQUErSyx1QkFBdUIsRUFBRSwwQ0FBMEMsRUFBRSxvQkFBb0IsdUVBQXVFLHNCQUFzQix3RUFBd0UscUJBQXFCLDZHQUE2RyxPQUFPLHNDQUFzQyxRQUFRLCtDQUErQyxvQ0FBb0MsT0FBTyxnREFBZ0QsMERBQTBELHVCQUF1QixFQUFFLDRCQUE0QixFQUFFLG1DQUFtQyxFQUFFLHNCQUFzQiw4SEFBOEgsT0FBTywrQkFBK0IsUUFBUSxnQ0FBZ0MsaUJBQWlCLGNBQWMsbUtBQW1LLGlDQUFpQyxFQUFFLHFDQUFxQyxFQUFFLDRCQUE0QixFQUFFLGdCQUFnQixtR0FBbUcsT0FBTyxpQ0FBaUMsUUFBUSxrQ0FBa0MsbUNBQW1DLGNBQWMsNkpBQTZKLHVCQUF1QixFQUFFLDBDQUEwQyxFQUFFLHNDQUFzQywrSUFBK0ksT0FBTywrQkFBK0IsUUFBUSwrQkFBK0IsUUFBUSx1Q0FBdUMsaUJBQWlCLGNBQWMsbUxBQW1MLHVCQUF1QixFQUFFLHNDQUFzQyxFQUFFLGdCQUFnQixnRkFBZ0YsT0FBTyx5Q0FBeUMsbUJBQW1CLGNBQWMsNkpBQTZKLGdDQUFnQyxFQUFFLHVDQUF1QyxFQUFFLGtCQUFrQiw2Q0FBNkMsT0FBTyxnQ0FBZ0MscUNBQXFDLE9BQU8sZ0NBQWdDLCtDQUErQyxtQ0FBbUMsRUFBRSx3QkFBd0IsZUFBZSxtQ0FBbUMsRUFBRSx3Q0FBd0MsRUFBRSx5Q0FBeUMsRUFBRSx3QkFBd0IsY0FBYyxPQUFPLGdDQUFnQyxRQUFRLGdDQUFnQyxRQUFRLGdDQUFnQywySkFBMkosNkJBQTZCLGNBQWMsT0FBTyxnQ0FBZ0MsUUFBUSxpQ0FBaUMsb0VBQW9FLDhCQUE4QixjQUFjLE9BQU8saUNBQWlDLFFBQVEsa0NBQWtDLGdJQUFnSSxzQkFBc0Isa0RBQWtELE9BQU8sZ0NBQWdDLHFCQUFxQixRQUFRLGNBQWMsb0tBQW9LLHVDQUF1QyxFQUFFLGlDQUFpQyxFQUFFLHlCQUF5Qiw0REFBNEQsT0FBTywrQkFBK0IsUUFBUSxnQ0FBZ0MscUJBQXFCLFFBQVEsY0FBYyx1S0FBdUssdUNBQXVDLEVBQUUsaUNBQWlDLEVBQUUsZ0JBQWdCLGlCQUFpQixzQkFBc0IsT0FBTyw4Q0FBOEMsZ0JBQWdCLG9CQUFvQixPQUFPLCtDQUErQyxxREFBcUQsNkJBQTZCLEVBQUUsdUJBQXVCLEVBQUUsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsbUNBQW1DLEVBQUUsa0JBQWtCLHFIQUFxSCxPQUFPLCtCQUErQixRQUFRLG1EQUFtRCxpQkFBaUIsY0FBYywrSkFBK0osdUJBQXVCLEVBQUUsaUNBQWlDLEVBQUUscUJBQXFCLDZIQUE2SCxPQUFPLCtCQUErQixRQUFRLG1DQUFtQyxvQkFBb0IsUUFBUSxjQUFjLGtLQUFrSyx1QkFBdUIsRUFBRSwyQkFBMkIsRUFBRSx1QkFBdUIsbUVBQW1FLGlCQUFpQixlQUFlLHVCQUF1QixFQUFFLDJCQUEyQixFQUFFLDBDQUEwQyxFQUFFLHlCQUF5QixFQUFFLHdCQUF3QixFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLCtCQUErQixFQUFFLGtDQUFrQyxFQUFFLGtDQUFrQyxFQUFFLGdDQUFnQyxFQUFFLHVDQUF1QyxFQUFFLG9CQUFvQixvSEFBb0gsT0FBTyxzQ0FBc0MsUUFBUSwrQ0FBK0Msb0NBQW9DLE9BQU8sZ0RBQWdELHlEQUF5RCw0QkFBNEIsRUFBRSxxQ0FBcUMsOElBQThJLE9BQU8sK0JBQStCLFFBQVEsK0JBQStCLFFBQVEsdUNBQXVDLGlCQUFpQixjQUFjLGtMQUFrTCx1QkFBdUIsRUFBRSxzQ0FBc0MsRUFBRSxpQ0FBaUMsRUFBRSw0QkFBNEIsb0hBQW9ILG9CQUFvQixnREFBZ0QsT0FBTyxnQ0FBZ0MscUJBQXFCLFFBQVEsY0FBYyxrS0FBa0ssdUNBQXVDLEVBQUUsaUNBQWlDLEVBQUUsdUJBQXVCLG9EQUFvRCxPQUFPLGdDQUFnQyxpQkFBaUIsY0FBYyxvS0FBb0ssdUJBQXVCLEVBQUUscUJBQXFCLGlCQUFpQixzQkFBc0IsT0FBTyw4Q0FBOEMsZ0JBQWdCLG9CQUFvQixPQUFPLCtDQUErQywwREFBMEQsdUJBQXVCLEVBQUUsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsa0NBQWtDLEVBQUUsdUNBQXVDLEVBQUUseUJBQXlCLHFEQUFxRCxPQUFPLGdDQUFnQyxvQkFBb0IsTUFBTSxjQUFjLHVLQUF1Syw0QkFBNEIsRUFBRSxtQ0FBbUMsRUFBRSx1QkFBdUIsZ0lBQWdJLE9BQU8sK0JBQStCLFFBQVEsdUNBQXVDLGlCQUFpQixjQUFjLG9LQUFvSyx1QkFBdUIsRUFBRSxxQ0FBcUMsRUFBRSwwQkFBMEIsdURBQXVELE9BQU8sZ0NBQWdDLG9CQUFvQixRQUFRLGNBQWMsdUtBQXVLLHVCQUF1QixFQUFFLGlDQUFpQyxFQUFFLG9DQUFvQyxFQUFFLGdDQUFnQyxFQUFFLDRCQUE0QixFQUFFLG1CQUFtQiwrREFBK0QsdUJBQXVCLGlCQUFpQixzQkFBc0IsT0FBTyw4Q0FBOEMsZ0JBQWdCLG9CQUFvQixPQUFPLCtDQUErQyw0REFBNEQsZ0NBQWdDLEVBQUUsdUJBQXVCLEVBQUUsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsaUNBQWlDLEVBQUUsdUNBQXVDLEVBQUUscUJBQXFCLHdEQUF3RCxPQUFPLGdDQUFnQyxvQkFBb0IsTUFBTSxjQUFjLG1LQUFtSyw0QkFBNEIsRUFBRSxtQ0FBbUMsRUFBRSxxQkFBcUIsZ0tBQWdLLE9BQU8sK0JBQStCLFFBQVEsdUNBQXVDLGlCQUFpQixjQUFjLGtLQUFrSyx1QkFBdUIsRUFBRSxpQ0FBaUMsRUFBRSxxQ0FBcUMsRUFBRSw0QkFBNEIsRUFBRSxzQkFBc0IsbURBQW1ELE9BQU8sZ0NBQWdDLG9CQUFvQixRQUFRLGNBQWMsbUtBQW1LLHVCQUF1QixFQUFFLGlDQUFpQyxFQUFFLGdDQUFnQyxFQUFFLDRCQUE0QixFQUFFLHFCQUFxQixpQkFBaUIsc0JBQXNCLE9BQU8sOENBQThDLGdCQUFnQixvQkFBb0IsT0FBTywrQ0FBK0MsMERBQTBELDJCQUEyQixFQUFFLHFCQUFxQixtREFBbUQsUUFBUSxjQUFjLG1LQUFtSyx1Q0FBdUMsRUFBRSxzQ0FBc0MsRUFBRSwwQkFBMEIsdURBQXVELE9BQU8sZ0NBQWdDLHFCQUFxQixRQUFRLGNBQWMsd0tBQXdLLHVCQUF1QixFQUFFLGdDQUFnQyxFQUFFLGdDQUFnQyxFQUFFLHNCQUFzQix3bEJBQXdsQixxQkFBcUIsa0JBQWtCLHNCQUFzQixPQUFPLGtEQUFrRCxnQkFBZ0Isb0JBQW9CLE9BQU8sK0NBQStDLDBEQUEwRCx1QkFBdUIsRUFBRSx5Q0FBeUMsRUFBRSwyQkFBMkIsRUFBRSxpQ0FBaUMsRUFBRSxtQ0FBbUMsRUFBRSw4QkFBOEIsb0hBQW9ILE9BQU8sK0JBQStCLFFBQVEsbURBQW1ELGlCQUFpQixjQUFjLDJLQUEySyx1QkFBdUIsRUFBRSw0QkFBNEIsRUFBRSx5QkFBeUIsZ0RBQWdELE9BQU8sZ0NBQWdDLGlCQUFpQixjQUFjLHNLQUFzSyw0QkFBNEIsRUFBRSwyQkFBMkIsdUNBQXVDLE9BQU8sK0NBQStDLG9DQUFvQyxPQUFPLGdEQUFnRCxnRUFBZ0UsdUJBQXVCLEVBQUUsMkJBQTJCLEVBQUUsZ0NBQWdDLEVBQUUsaUNBQWlDLEVBQUUsbUNBQW1DLEVBQUUsMEJBQTBCLGVBQWUsaUNBQWlDLEVBQUUsb0NBQW9DLEVBQUUsc0NBQXNDLEVBQUUsaUNBQWlDLEVBQUUsMEJBQTBCLDJIQUEySCxPQUFPLCtCQUErQixRQUFRLHVDQUF1QyxpQkFBaUIsY0FBYyx1S0FBdUssdUJBQXVCLEVBQUUscUNBQXFDLEVBQUUsMEJBQTBCLDZEQUE2RCwrQkFBK0IsdUNBQXVDLE9BQU8sK0NBQStDLG9DQUFvQyxPQUFPLGdEQUFnRCxvRUFBb0UsNEJBQTRCLEVBQUUsbUNBQW1DLEVBQUUsaUJBQWlCLDJDQUEyQyxPQUFPLGdDQUFnQyxxQkFBcUIsUUFBUSxjQUFjLCtKQUErSix1Q0FBdUMsRUFBRSxpQ0FBaUMsRUFBRSxzQkFBc0IsaUJBQWlCLHNCQUFzQixPQUFPLDhDQUE4QyxnQkFBZ0Isb0JBQW9CLE9BQU8sK0NBQStDLDJEQUEyRCx1Q0FBdUMsRUFBRSx1Q0FBdUMsRUFBRSw0QkFBNEIsZUFBZSxvQ0FBb0MsRUFBRSwrQkFBK0IsRUFBRSxpQ0FBaUMsRUFBRSwrQkFBK0IsRUFBRSwyQkFBMkIsdUdBQXVHLE9BQU8sZ0NBQWdDLDJCQUEyQixjQUFjLHlLQUF5Syw0QkFBNEIsdUNBQXVDLE9BQU8sK0NBQStDLG9DQUFvQyxPQUFPLGdEQUFnRCxpRUFBaUUsdUJBQXVCLEVBQUUsc0NBQXNDLEVBQUUsMkJBQTJCLDJNQUEyTSxPQUFPLCtCQUErQixRQUFRLCtCQUErQixRQUFRLDJDQUEyQyxRQUFRLGdDQUFnQyxxQkFBcUIsUUFBUSxjQUFjLHlLQUF5SywyQ0FBMkMsRUFBRSxnQ0FBZ0MsaUJBQWlCLHNCQUFzQixPQUFPLDhDQUE4QyxnQkFBZ0Isb0JBQW9CLE9BQU8sK0NBQStDLHFFQUFxRSw0QkFBNEIsRUFBRSwyQkFBMkIsMERBQTBELHdCQUF3QixtREFBbUQsNEJBQTRCLGFBQWEscURBQXFELGtCQUFrQixpQkFBaUIsc0JBQXNCLE9BQU8sOENBQThDLGdCQUFnQixvQkFBb0IsT0FBTywrQ0FBK0MsdURBQXVELHVCQUF1QixFQUFFLDJCQUEyQixFQUFFLG9DQUFvQyxFQUFFLHVDQUF1QyxFQUFFLHlCQUF5Qiw2SEFBNkgsT0FBTywrQkFBK0IsUUFBUSx1Q0FBdUMsaUJBQWlCLGNBQWMsc0tBQXNLLHVCQUF1QixFQUFFLHNDQUFzQyxFQUFFLHFCQUFxQixxREFBcUQsT0FBTyxnQ0FBZ0MsZUFBZSxjQUFjLGtLQUFrSyw0QkFBNEIsRUFBRSxtQ0FBbUMsRUFBRSx1QkFBdUIsK0hBQStILE9BQU8sK0JBQStCLFFBQVEsbUNBQW1DLG9CQUFvQixRQUFRLGNBQWMsb0tBQW9LLHVCQUF1QixFQUFFLGlDQUFpQyxFQUFFLGdDQUFnQyxFQUFFLDZCQUE2QixFQUFFLHNCQUFzQixpREFBaUQsT0FBTywwQ0FBMEMsaUJBQWlCLGNBQWMsbUtBQW1LLDRCQUE0QixFQUFFLGdCQUFnQixlQUFlLHVCQUF1QixFQUFFLDJCQUEyQixFQUFFLDZDQUE2QyxFQUFFLDJCQUEyQixFQUFFLGlDQUFpQyxFQUFFLGtDQUFrQyxFQUFFLGlDQUFpQyxFQUFFLHFDQUFxQyxFQUFFLGdDQUFnQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxFQUFFLHFDQUFxQyxFQUFFLHFDQUFxQyxFQUFFLGtDQUFrQyxFQUFFLGlDQUFpQyxFQUFFLGlEQUFpRCxFQUFFLGdEQUFnRCxFQUFFLHNDQUFzQyxFQUFFLHVDQUF1QyxFQUFFLHFCQUFxQiwyRkFBMkYscUNBQXFDLEVBQUUsa0NBQWtDLEVBQUUsMEJBQTBCLHNCQUFzQixzQkFBc0IsT0FBTyxtRUFBbUUsZ0JBQWdCLG9CQUFvQixPQUFPLGlFQUFpRSwrREFBK0QsNEJBQTRCLEVBQUUsWUFBWSxlQUFlLDZCQUE2QixFQUFFLDhCQUE4QixFQUFFLCtCQUErQixFQUFFLDhDQUE4QyxFQUFFLGdDQUFnQyxFQUFFLGlDQUFpQyxFQUFFLGlDQUFpQyxFQUFFLG1DQUFtQyx5R0FBeUcscUNBQXFDLEVBQUUsa0NBQWtDLEVBQUUsdUJBQXVCLHVDQUF1QyxPQUFPLGlEQUFpRCxvQ0FBb0MsT0FBTyxrREFBa0QsNERBQTRELDRCQUE0QixFQUFFLG9CQUFvQix3Q0FBd0MsT0FBTyxxREFBcUQsb0NBQW9DLE9BQU8sa0RBQWtELHlEQUF5RCw0QkFBNEIsRUFBRSxtQ0FBbUMsRUFBRSxzQkFBc0IseURBQXlELE9BQU8sMkNBQTJDLFFBQVEsbURBQW1ELGlCQUFpQixjQUFjLGdKQUFnSiw0QkFBNEIsRUFBRSxvQkFBb0Isb0hBQW9ILE9BQU8sa0NBQWtDLFFBQVEsOENBQThDLHVCQUF1QixjQUFjLDBIQUEwSCxPQUFPLDhDQUE4Qyx5REFBeUQsdUJBQXVCLEVBQUUsNEJBQTRCLEVBQUUsbUNBQW1DLEVBQUUscUJBQXFCLG1DQUFtQyxPQUFPLDhEQUE4RCxnQ0FBZ0MsT0FBTyw0REFBNEQsMERBQTBELCtCQUErQixFQUFFLDRCQUE0QixFQUFFLG1DQUFtQyxFQUFFLHFCQUFxQix1R0FBdUcsT0FBTyxtQ0FBbUMsdUJBQXVCLGNBQWMsa0tBQWtLLHVCQUF1QixFQUFFLDJDQUEyQyxFQUFFLHdDQUF3QyxFQUFFLGdDQUFnQyx5Q0FBeUMsT0FBTyxnQ0FBZ0MsdUJBQXVCLGNBQWMsNktBQTZLLDRCQUE0QixFQUFFLDZCQUE2QixtQ0FBbUMsT0FBTyw4Q0FBOEMsdUJBQXVCLGNBQWMsMEtBQTBLLDRCQUE0QixFQUFFLHNCQUFzQixtQ0FBbUMsT0FBTyw4REFBOEQsZ0NBQWdDLE9BQU8sNERBQTRELDJEQUEyRCxnQ0FBZ0MsRUFBRSxtQ0FBbUMsRUFBRSxhQUFhLCtDQUErQyxPQUFPLGdDQUFnQyx1QkFBdUIsY0FBYywwSkFBMEosNEJBQTRCLEVBQUUsaUJBQWlCLGlCQUFpQixzQkFBc0IsT0FBTyw4Q0FBOEMsZ0JBQWdCLG9CQUFvQixPQUFPLCtDQUErQyxzREFBc0QsK0JBQStCLEVBQUUsdUJBQXVCLEVBQUUsMkJBQTJCLEVBQUUsMkJBQTJCLEVBQUUsNEJBQTRCLEVBQUUsbUNBQW1DLEVBQUUsc0JBQXNCLDhIQUE4SCxPQUFPLCtCQUErQixRQUFRLG1DQUFtQyxvQkFBb0IsUUFBUSxjQUFjLG1LQUFtSyx1QkFBdUIsRUFBRSw0QkFBNEIsRUFBRSxvQkFBb0Isb0hBQW9ILE9BQU8sK0JBQStCLFFBQVEsbURBQW1ELGlCQUFpQixjQUFjLGlLQUFpSyx1QkFBdUIsRUFBRSw0QkFBNEIsRUFBRSxzQkFBc0IsbURBQW1ELE9BQU8sZ0NBQWdDLGlCQUFpQixjQUFjLG1LQUFtSyx1QkFBdUIsRUFBRSx1Q0FBdUMsRUFBRSxzQ0FBc0MsRUFBRSxjQUFjLGdEQUFnRCxPQUFPLGdDQUFnQyx1QkFBdUIsY0FBYywySkFBMkosNEJBQTRCLEdBQUcsb0RBQW9EOztBQUUzejhCLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxcdHlwZXNwZWMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiVHlwZVNwZWNcXFwiLFxcXCJmaWxlVHlwZXNcXFwiOltcXFwidHNwXFxcIl0sXFxcIm5hbWVcXFwiOlxcXCJ0eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0YXRlbWVudFxcXCJ9XSxcXFwicmVwb3NpdG9yeVxcXCI6e1xcXCJhbGlhcy1pZFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig9KVxcXFxcXFxccypcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuYXNzaWdubWVudC50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuYWxpYXMtaWQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwiYWxpYXMtc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGFsaWFzKVxcXFxcXFxcYlxcXFxcXFxccysoXFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYClcXFxcXFxcXHMqXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50eXBlLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5hbGlhcy1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbGlhcy1pZFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLXBhcmFtZXRlcnNcXFwifV19LFxcXCJhdWdtZW50LWRlY29yYXRvci1zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoKEBAKVxcXFxcXFxcYltfJFs6YWxwaGE6XV0oPzpbXyRbOmFsbnVtOl1dfFxcXFxcXFxcLltfJFs6YWxwaGE6XV0pKlxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLnRhZy50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudGFnLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89W18kWzphbHBoYTpdXSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5hdWdtZW50LWRlY29yYXRvci1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXJlbnRoZXNpemVkLWV4cHJlc3Npb25cXFwifV19LFxcXCJibG9jay1jb21tZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiL1xcXFxcXFxcKlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKi9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay50c3BcXFwifSxcXFwiYm9vbGVhbi1saXRlcmFsXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKHRydWV8ZmFsc2UpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlLnRzcFxcXCJ9LFxcXCJjYWxsRXhwcmVzc2lvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXGJbXyRbOmFscGhhOl1dKD86W18kWzphbG51bTpdXXxcXFxcXFxcXC5bXyRbOmFscGhhOl1dKSpcXFxcXFxcXGIpXFxcXFxcXFxzKihcXFxcXFxcXCgpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbi50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ucGFyZW50aGVzaXMub3Blbi50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnBhcmVudGhlc2lzLmNsb3NlLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuY2FsbEV4cHJlc3Npb24udHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3B1bmN0dWF0aW9uLWNvbW1hXFxcIn1dfSxcXFwiY29uc3Qtc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGNvbnN0KVxcXFxcXFxcYlxcXFxcXFxccysoXFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm5hbWUudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmNvbnN0LXN0YXRlbWVudC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGUtYW5ub3RhdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRvci1hc3NpZ25tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJkZWNvcmF0b3JcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoKEApXFxcXFxcXFxiW18kWzphbHBoYTpdXSg/OltfJFs6YWxudW06XV18XFxcXFxcXFwuW18kWzphbHBoYTpdXSkqXFxcXFxcXFxiKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudGFnLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50YWcudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz1bXyRbOmFscGhhOl1dKXwoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmRlY29yYXRvci50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3BhcmVudGhlc2l6ZWQtZXhwcmVzc2lvblxcXCJ9XX0sXFxcImRlY29yYXRvci1kZWNsYXJhdGlvbi1zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIoPzooZXh0ZXJuKVxcXFxcXFxccyspP1xcXFxcXFxcYihkZWMpXFxcXFxcXFxiXFxcXFxcXFxzKyhcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmRlY29yYXRvci1kZWNsYXJhdGlvbi1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRpb24tcGFyYW1ldGVyc1xcXCJ9XX0sXFxcImRpcmVjdGl2ZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxccyooI1xcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuZGlyZWN0aXZlLm5hbWUudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIkfCg/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuZGlyZWN0aXZlLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjc3RyaW5nLWxpdGVyYWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWRlbnRpZmllci1leHByZXNzaW9uXFxcIn1dfSxcXFwiZG9jLWNvbW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIvXFxcXFxcXFwqXFxcXFxcXFwqXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwqL1xcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sudHNwXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay50c3BcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkb2MtY29tbWVudC1ibG9ja1xcXCJ9XX0sXFxcImRvYy1jb21tZW50LWJsb2NrXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RvYy1jb21tZW50LXBhcmFtXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RvYy1jb21tZW50LXJldHVybi10YWdcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZG9jLWNvbW1lbnQtdW5rbm93bi10YWdcXFwifV19LFxcXCJkb2MtY29tbWVudC1wYXJhbVxcXCI6e1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLnRhZy50c3Bkb2NcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC50YWcudHNwZG9jXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm5hbWUudHNwXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIigoQCkoPzpwYXJhbXx0ZW1wbGF0ZXxwcm9wKSlcXFxcXFxcXHMrKFxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmApXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sudHNwXFxcIn0sXFxcImRvYy1jb21tZW50LXJldHVybi10YWdcXFwiOntcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC50YWcudHNwZG9jXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQudGFnLnRzcGRvY1xcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKEApKD86cmV0dXJucykpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sudHNwXFxcIn0sXFxcImRvYy1jb21tZW50LXVua25vd24tdGFnXFxcIjp7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLnRhZy50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudGFnLnRzcFxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoKEApKD86XFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYCkpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2sudHNwXFxcIn0sXFxcImVsc2UtZXhwcmVzc2lvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYihlbHNlKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIigoPzw9XFxcXFxcXFx9KXwoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5lbHNlLWV4cHJlc3Npb24udHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9qZWN0aW9uLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJvamVjdGlvbi1ib2R5XFxcIn1dfSxcXFwiZWxzZS1pZi1leHByZXNzaW9uXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGVsc2UpXFxcXFxcXFxzKyhpZilcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYikpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuZWxzZS1pZi1leHByZXNzaW9uLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJvamVjdGlvbi1leHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Byb2plY3Rpb24tYm9keVxcXCJ9XX0sXFxcImVudW0tYm9keVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmVudW0tYm9keS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VudW0tbWVtYmVyXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RpcmVjdGl2ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNvcmF0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tY29tbWFcXFwifV19LFxcXCJlbnVtLW1lbWJlclxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/OihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXFxcXFxccyooOj8pKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUubmFtZS50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci50eXBlLmFubm90YXRpb24udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmVudW0tbWVtYmVyLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZS1hbm5vdGF0aW9uXFxcIn1dfSxcXFwiZW51bS1zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoZW51bSlcXFxcXFxcXGJcXFxcXFxcXHMrKFxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmApXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50eXBlLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5lbnVtLXN0YXRlbWVudC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VudW0tYm9keVxcXCJ9XX0sXFxcImVzY2FwZS1jaGFyYWN0ZXJcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXFxcXFxcXFxcLlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLnRzcFxcXCJ9LFxcXCJleHByZXNzaW9uXFxcIjp7XFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RpcmVjdGl2ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwYXJlbnRoZXNpemVkLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdmFsdWVvZlxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlb2ZcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZS1hcmd1bWVudHNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb2JqZWN0LWxpdGVyYWxcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHVwbGUtbGl0ZXJhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0dXBsZS1leHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21vZGVsLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY2FsbEV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaWRlbnRpZmllci1leHByZXNzaW9uXFxcIn1dfSxcXFwiZnVuY3Rpb24tY2FsbFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXFxcXFxccyooXFxcXFxcXFwoKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24udHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnBhcmVudGhlc2lzLm9wZW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5wYXJlbnRoZXNpcy5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLWNhbGwudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwiZnVuY3Rpb24tZGVjbGFyYXRpb24tc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD86KGV4dGVybilcXFxcXFxcXHMrKT9cXFxcXFxcXGIoZm4pXFxcXFxcXFxiXFxcXFxcXFxzKyhcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLWRlY2xhcmF0aW9uLXN0YXRlbWVudC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdGlvbi1wYXJhbWV0ZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGUtYW5ub3RhdGlvblxcXCJ9XX0sXFxcImlkZW50aWZpZXItZXhwcmVzc2lvblxcXCI6e1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmBcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudHlwZS50c3BcXFwifSxcXFwiaWYtZXhwcmVzc2lvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYihpZilcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYikpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuaWYtZXhwcmVzc2lvbi50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Byb2plY3Rpb24tZXhwcmVzc2lvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9qZWN0aW9uLWJvZHlcXFwifV19LFxcXCJpbXBvcnQtc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGltcG9ydClcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmltcG9ydC1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9XX0sXFxcImludGVyZmFjZS1ib2R5XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5jdXJseWJyYWNlLm9wZW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXH1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5jdXJseWJyYWNlLmNsb3NlLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEuaW50ZXJmYWNlLWJvZHkudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXJlY3RpdmVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjb3JhdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2ludGVyZmFjZS1tZW1iZXJcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tc2VtaWNvbG9uXFxcIn1dfSxcXFwiaW50ZXJmYWNlLWhlcml0YWdlXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGV4dGVuZHMpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKCg/PVxcXFxcXFxceyl8KD89O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmludGVyZmFjZS1oZXJpdGFnZS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tY29tbWFcXFwifV19LFxcXCJpbnRlcmZhY2UtbWVtYmVyXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD86XFxcXFxcXFxiKG9wKVxcXFxcXFxcYlxcXFxcXFxccyspPyhcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmludGVyZmFjZS1tZW1iZXIudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRpb24tc2lnbmF0dXJlXFxcIn1dfSxcXFwiaW50ZXJmYWNlLXN0YXRlbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYihpbnRlcmZhY2UpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5pbnRlcmZhY2Utc3RhdGVtZW50LnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZS1wYXJhbWV0ZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2ludGVyZmFjZS1oZXJpdGFnZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbnRlcmZhY2UtYm9keVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwibGluZS1jb21tZW50XFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiLy8uKiRcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLmRvdWJsZS1zbGFzaC50c3BcXFwifSxcXFwibW9kZWwtZXhwcmVzc2lvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLm1vZGVsLWV4cHJlc3Npb24udHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNtb2RlbC1wcm9wZXJ0eVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXJlY3RpdmVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjb3JhdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NwcmVhZC1vcGVyYXRvclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1zZW1pY29sb25cXFwifV19LFxcXCJtb2RlbC1oZXJpdGFnZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYihleHRlbmRzfGlzKVxcXFxcXFxcYlxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIigoPz1cXFxcXFxcXHspfCg/PTt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5tb2RlbC1oZXJpdGFnZS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tY29tbWFcXFwifV19LFxcXCJtb2RlbC1wcm9wZXJ0eVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/OihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKXwoXFxcXFxcXFxcXFxcXFxcIig/OlteXFxcXFxcXFxcXFxcXFxcIlxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpcXFxcXFxcXFxcXFxcXFwiKSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm5hbWUudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuZG91YmxlLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5tb2RlbC1wcm9wZXJ0eS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGUtYW5ub3RhdGlvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRvci1hc3NpZ25tZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJtb2RlbC1zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIobW9kZWwpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5tb2RlbC1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLXBhcmFtZXRlcnNcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjbW9kZWwtaGVyaXRhZ2VcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcIm5hbWVzcGFjZS1ib2R5XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5jdXJseWJyYWNlLm9wZW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXH1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5jdXJseWJyYWNlLmNsb3NlLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEubmFtZXNwYWNlLWJvZHkudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNzdGF0ZW1lbnRcXFwifV19LFxcXCJuYW1lc3BhY2UtbmFtZVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/PVtfJFs6YWxwaGE6XV0pXFxcIixcXFwiZW5kXFxcIjpcXFwiKCg/PVxcXFxcXFxceyl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYikpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEubmFtZXNwYWNlLW5hbWUudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNpZGVudGlmaWVyLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tYWNjZXNzb3JcXFwifV19LFxcXCJuYW1lc3BhY2Utc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKG5hbWVzcGFjZSlcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYikpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEubmFtZXNwYWNlLXN0YXRlbWVudC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI25hbWVzcGFjZS1uYW1lXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI25hbWVzcGFjZS1ib2R5XFxcIn1dfSxcXFwibnVtZXJpYy1saXRlcmFsXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiKD86XFxcXFxcXFxiKD88IVxcXFxcXFxcJCkwKD86eHxYKVswLTlhLWZBLUZdWzAtOWEtZkEtRl9dKihuKT9cXFxcXFxcXGIoPyFcXFxcXFxcXCQpfFxcXFxcXFxcYig/PCFcXFxcXFxcXCQpMCg/OmJ8QilbMDFdWzAxX10qKG4pP1xcXFxcXFxcYig/IVxcXFxcXFxcJCl8KD88IVxcXFxcXFxcJCkoPzooPzpcXFxcXFxcXGJbMC05XVswLTlfXSooXFxcXFxcXFwuKVswLTldWzAtOV9dKltlRV1bKy1dP1swLTldWzAtOV9dKihuKT9cXFxcXFxcXGIpfCg/OlxcXFxcXFxcYlswLTldWzAtOV9dKihcXFxcXFxcXC4pW2VFXVsrLV0/WzAtOV1bMC05X10qKG4pP1xcXFxcXFxcYil8KD86XFxcXFxcXFxCKFxcXFxcXFxcLilbMC05XVswLTlfXSpbZUVdWystXT9bMC05XVswLTlfXSoobik/XFxcXFxcXFxiKXwoPzpcXFxcXFxcXGJbMC05XVswLTlfXSpbZUVdWystXT9bMC05XVswLTlfXSoobik/XFxcXFxcXFxiKXwoPzpcXFxcXFxcXGJbMC05XVswLTlfXSooXFxcXFxcXFwuKVswLTldWzAtOV9dKihuKT9cXFxcXFxcXGIpfCg/OlxcXFxcXFxcYlswLTldWzAtOV9dKihcXFxcXFxcXC4pKG4pP1xcXFxcXFxcQil8KD86XFxcXFxcXFxCKFxcXFxcXFxcLilbMC05XVswLTlfXSoobik/XFxcXFxcXFxiKXwoPzpcXFxcXFxcXGJbMC05XVswLTlfXSoobik/XFxcXFxcXFxiKD8hXFxcXFxcXFwuKSkpKD8hXFxcXFxcXFwkKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubnVtZXJpYy50c3BcXFwifSxcXFwib2JqZWN0LWxpdGVyYWxcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIjXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5oYXNoY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLm9iamVjdC1saXRlcmFsLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb2JqZWN0LWxpdGVyYWwtcHJvcGVydHlcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGlyZWN0aXZlXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NwcmVhZC1vcGVyYXRvclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1jb21tYVxcXCJ9XX0sXFxcIm9iamVjdC1saXRlcmFsLXByb3BlcnR5XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiKD86KFxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmApXFxcXFxcXFxzKig6KSlcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm5hbWUudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IudHlwZS5hbm5vdGF0aW9uLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5vYmplY3QtbGl0ZXJhbC1wcm9wZXJ0eS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJvcGVyYXRpb24taGVyaXRhZ2VcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoaXMpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5vcGVyYXRpb24taGVyaXRhZ2UudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwib3BlcmF0aW9uLXBhcmFtZXRlcnNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXChcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnBhcmVudGhlc2lzLm9wZW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClcXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5wYXJlbnRoZXNpcy5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLm9wZXJhdGlvbi1wYXJhbWV0ZXJzLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZGVjb3JhdG9yXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI21vZGVsLXByb3BlcnR5XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NwcmVhZC1vcGVyYXRvclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1jb21tYVxcXCJ9XX0sXFxcIm9wZXJhdGlvbi1zaWduYXR1cmVcXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZS1wYXJhbWV0ZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdGlvbi1oZXJpdGFnZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNvcGVyYXRpb24tcGFyYW1ldGVyc1xcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLWFubm90YXRpb25cXFwifV19LFxcXCJvcGVyYXRpb24tc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKG9wKVxcXFxcXFxcYlxcXFxcXFxccysoXFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5vcGVyYXRpb24tc3RhdGVtZW50LnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3BlcmF0aW9uLXNpZ25hdHVyZVxcXCJ9XX0sXFxcIm9wZXJhdG9yLWFzc2lnbm1lbnRcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCI9XFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuYXNzaWdubWVudC50c3BcXFwifSxcXFwicGFyZW50aGVzaXplZC1leHByZXNzaW9uXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwoXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5wYXJlbnRoZXNpcy5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFwpXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ucGFyZW50aGVzaXMuY2xvc2UudHNwXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wYXJlbnRoZXNpemVkLWV4cHJlc3Npb24udHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3B1bmN0dWF0aW9uLWNvbW1hXFxcIn1dfSxcXFwicHJvamVjdGlvblxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihmcm9tfHRvKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIigoPzw9XFxcXFxcXFx9KXwoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcm9qZWN0aW9uLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJvamVjdGlvbi1wYXJhbWV0ZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Byb2plY3Rpb24tYm9keVxcXCJ9XX0sXFxcInByb2plY3Rpb24tYm9keVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByb2plY3Rpb24tYm9keS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Byb2plY3Rpb24tZXhwcmVzc2lvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1zZW1pY29sb25cXFwifV19LFxcXCJwcm9qZWN0aW9uLWV4cHJlc3Npb25cXFwiOntcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZWxzZS1pZi1leHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2lmLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZWxzZS1leHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWNhbGxcXFwifV19LFxcXCJwcm9qZWN0aW9uLXBhcmFtZXRlclxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUubmFtZS50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PVxcXFxcXFxcKSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcm9qZWN0aW9uLXBhcmFtZXRlci50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbXX0sXFxcInByb2plY3Rpb24tcGFyYW1ldGVyc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcKFxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ucGFyZW50aGVzaXMub3Blbi50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnBhcmVudGhlc2lzLmNsb3NlLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJvamVjdGlvbi1wYXJhbWV0ZXJzLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHJvamVjdGlvbi1wYXJhbWV0ZXJcXFwifV19LFxcXCJwcm9qZWN0aW9uLXN0YXRlbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYihwcm9qZWN0aW9uKVxcXFxcXFxcYlxcXFxcXFxccysoXFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYCkoIykoXFxcXFxcXFxiW18kWzphbHBoYTpdXVtfJFs6YWxudW06XV0qXFxcXFxcXFxifGAoPzpbXmBcXFxcXFxcXFxcXFxcXFxcXXxcXFxcXFxcXFxcXFxcXFxcLikqYClcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iuc2VsZWN0b3IudHNwXFxcIn0sXFxcIjRcXFwiOntcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLm5hbWUudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYikpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEucHJvamVjdGlvbi1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9qZWN0aW9uLXN0YXRlbWVudC1ib2R5XFxcIn1dfSxcXFwicHJvamVjdGlvbi1zdGF0ZW1lbnQtYm9keVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnByb2plY3Rpb24tc3RhdGVtZW50LWJvZHkudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9qZWN0aW9uXFxcIn1dfSxcXFwicHVuY3R1YXRpb24tYWNjZXNzb3JcXFwiOntcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXC5cXFwiLFxcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uYWNjZXNzb3IudHNwXFxcIn0sXFxcInB1bmN0dWF0aW9uLWNvbW1hXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiLFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5jb21tYS50c3BcXFwifSxcXFwicHVuY3R1YXRpb24tc2VtaWNvbG9uXFxcIjp7XFxcIm1hdGNoXFxcIjpcXFwiO1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi50ZXJtaW5hdG9yLnN0YXRlbWVudC50c3BcXFwifSxcXFwic2NhbGFyLWJvZHlcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXHtcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmN1cmx5YnJhY2Uub3Blbi50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcfVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmN1cmx5YnJhY2UuY2xvc2UudHNwXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS5zY2FsYXItYm9keS50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RpcmVjdGl2ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNzY2FsYXItY29uc3RydWN0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tc2VtaWNvbG9uXFxcIn1dfSxcXFwic2NhbGFyLWNvbnN0cnVjdG9yXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKGluaXQpXFxcXFxcXFxiXFxcXFxcXFxzKyhcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnNjYWxhci1jb25zdHJ1Y3Rvci50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI29wZXJhdGlvbi1wYXJhbWV0ZXJzXFxcIn1dfSxcXFwic2NhbGFyLWV4dGVuZHNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIoZXh0ZW5kcylcXFxcXFxcXGJcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz07fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5zY2FsYXItZXh0ZW5kcy50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tY29tbWFcXFwifV19LFxcXCJzY2FsYXItc3RhdGVtZW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKHNjYWxhcilcXFxcXFxcXGJcXFxcXFxcXHMrKFxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmApXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50eXBlLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5zY2FsYXItc3RhdGVtZW50LnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjdG9rZW5cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjdHlwZS1wYXJhbWV0ZXJzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NjYWxhci1leHRlbmRzXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3NjYWxhci1ib2R5XFxcIn1dfSxcXFwic3ByZWFkLW9wZXJhdG9yXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwuXFxcXFxcXFwuXFxcXFxcXFwuXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnNwcmVhZC50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuc3ByZWFkLW9wZXJhdG9yLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcInN0YXRlbWVudFxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkaXJlY3RpdmVcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYXVnbWVudC1kZWNvcmF0b3Itc3RhdGVtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RlY29yYXRvclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNtb2RlbC1zdGF0ZW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjc2NhbGFyLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN1bmlvbi1zdGF0ZW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjaW50ZXJmYWNlLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNlbnVtLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNhbGlhcy1zdGF0ZW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjY29uc3Qtc3RhdGVtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI25hbWVzcGFjZS1zdGF0ZW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjb3BlcmF0aW9uLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpbXBvcnQtc3RhdGVtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3VzaW5nLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNvcmF0b3ItZGVjbGFyYXRpb24tc3RhdGVtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Z1bmN0aW9uLWRlY2xhcmF0aW9uLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwcm9qZWN0aW9uLXN0YXRlbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1zZW1pY29sb25cXFwifV19LFxcXCJzdHJpbmctbGl0ZXJhbFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJ8JFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS50c3BcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0ZW1wbGF0ZS1leHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2VzY2FwZS1jaGFyYWN0ZXJcXFwifV19LFxcXCJ0ZW1wbGF0ZS1leHByZXNzaW9uXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFwkXFxcXFxcXFx7XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRlbXBsYXRlLWV4cHJlc3Npb24uYmVnaW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXH1cXFwiLFxcXCJlbmRDYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnRlbXBsYXRlLWV4cHJlc3Npb24uZW5kLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEudGVtcGxhdGUtZXhwcmVzc2lvbi50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJ0b2tlblxcXCI6e1xcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNkb2MtY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNsaW5lLWNvbW1lbnRcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjYmxvY2stY29tbWVudFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0cmlwbGUtcXVvdGVkLXN0cmluZy1saXRlcmFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3N0cmluZy1saXRlcmFsXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2Jvb2xlYW4tbGl0ZXJhbFxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNudW1lcmljLWxpdGVyYWxcXFwifV19LFxcXCJ0cmlwbGUtcXVvdGVkLXN0cmluZy1saXRlcmFsXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXCJcXFxcXFxcIlxcXFxcXFwiXFxcIixcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXCJcXFxcXFxcIlxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQudHJpcGxlLnRzcFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3RlbXBsYXRlLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXNjYXBlLWNoYXJhY3RlclxcXCJ9XX0sXFxcInR1cGxlLWV4cHJlc3Npb25cXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXFtcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNxdWFyZWJyYWNrZXQub3Blbi50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcXVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLnNxdWFyZWJyYWNrZXQuY2xvc2UudHNwXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS50dXBsZS1leHByZXNzaW9uLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcInR1cGxlLWxpdGVyYWxcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCIjXFxcXFxcXFxbXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5oYXNoc3F1YXJlYnJhY2tldC5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFxdXFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uc3F1YXJlYnJhY2tldC5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnR1cGxlLWxpdGVyYWwudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3B1bmN0dWF0aW9uLWNvbW1hXFxcIn1dfSxcXFwidHlwZS1hbm5vdGF0aW9uXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxzKihcXFxcXFxcXD8/KVxcXFxcXFxccyooOilcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iub3B0aW9uYWwudHNwXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IudHlwZS5hbm5vdGF0aW9uLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXw9fFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS50eXBlLWFubm90YXRpb24udHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwidHlwZS1hcmd1bWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/OihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXFxcXFxccyooPSkpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50eXBlLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLmFzc2lnbm1lbnQudHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPz0+KXwoPz0sfDt8QHxcXFxcXFxcXCl8XFxcXFxcXFx9fFxcXFxcXFxcYig/OmV4dGVybilcXFxcXFxcXGJ8XFxcXFxcXFxiKD86bmFtZXNwYWNlfG1vZGVsfG9wfHVzaW5nfGltcG9ydHxlbnVtfGFsaWFzfHVuaW9ufGludGVyZmFjZXxkZWN8Zm4pXFxcXFxcXFxiKVxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3IuYXNzaWdubWVudC50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnR5cGUtYXJndW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3B1bmN0dWF0aW9uLWNvbW1hXFxcIn1dfSxcXFwidHlwZS1hcmd1bWVudHNcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCI8XFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLnR5cGVwYXJhbWV0ZXJzLmJlZ2luLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiPlxcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udHlwZXBhcmFtZXRlcnMuZW5kLnRzcFxcXCJ9fSxcXFwibmFtZVxcXCI6XFxcIm1ldGEudHlwZS1hcmd1bWVudHMudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLWFyZ3VtZW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tY29tbWFcXFwifV19LFxcXCJ0eXBlLXBhcmFtZXRlclxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudHlwZS50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PT4pfCg/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEudHlwZS1wYXJhbWV0ZXIudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLXBhcmFtZXRlci1jb25zdHJhaW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3R5cGUtcGFyYW1ldGVyLWRlZmF1bHRcXFwifV19LFxcXCJ0eXBlLXBhcmFtZXRlci1jb25zdHJhaW50XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiZXh0ZW5kc1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PT4pfCg/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEudHlwZS1wYXJhbWV0ZXItY29uc3RyYWludC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJ0eXBlLXBhcmFtZXRlci1kZWZhdWx0XFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiPVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vcGVyYXRvci5hc3NpZ25tZW50LnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89Pil8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS50eXBlLXBhcmFtZXRlci1kZWZhdWx0LnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX0sXFxcInR5cGUtcGFyYW1ldGVyc1xcXCI6e1xcXCJiZWdpblxcXCI6XFxcIjxcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24udHlwZXBhcmFtZXRlcnMuYmVnaW4udHNwXFxcIn19LFxcXCJlbmRcXFwiOlxcXCI+XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uZGVmaW5pdGlvbi50eXBlcGFyYW1ldGVycy5lbmQudHNwXFxcIn19LFxcXCJuYW1lXFxcIjpcXFwibWV0YS50eXBlLXBhcmFtZXRlcnMudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0eXBlLXBhcmFtZXRlclxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1jb21tYVxcXCJ9XX0sXFxcInR5cGVvZlxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYih0eXBlb2YpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89Pil8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS50eXBlb2YudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiNleHByZXNzaW9uXFxcIn1dfSxcXFwidW5pb24tYm9keVxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5vcGVuLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiXFxcXFxcXFx9XFxcIixcXFwiZW5kQ2FwdHVyZXNcXFwiOntcXFwiMFxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24uY3VybHlicmFjZS5jbG9zZS50c3BcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJtZXRhLnVuaW9uLWJvZHkudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN1bmlvbi12YXJpYW50XFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2RpcmVjdGl2ZVxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNkZWNvcmF0b3JcXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNwdW5jdHVhdGlvbi1jb21tYVxcXCJ9XX0sXFxcInVuaW9uLXN0YXRlbWVudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxcYih1bmlvbilcXFxcXFxcXGJcXFxcXFxcXHMrKFxcXFxcXFxcYltfJFs6YWxwaGE6XV1bXyRbOmFsbnVtOl1dKlxcXFxcXFxcYnxgKD86W15gXFxcXFxcXFxcXFxcXFxcXF18XFxcXFxcXFxcXFxcXFxcXC4pKmApXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS50eXBlLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD88PVxcXFxcXFxcfSl8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS51bmlvbi1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiN1bmlvbi1ib2R5XFxcIn1dfSxcXFwidW5pb24tdmFyaWFudFxcXCI6e1xcXCJiZWdpblxcXCI6XFxcIig/OihcXFxcXFxcXGJbXyRbOmFscGhhOl1dW18kWzphbG51bTpdXSpcXFxcXFxcXGJ8YCg/OlteYFxcXFxcXFxcXFxcXFxcXFxdfFxcXFxcXFxcXFxcXFxcXFwuKSpgKVxcXFxcXFxccyooOikpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5uYW1lLnRzcFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnR5cGUuYW5ub3RhdGlvbi50c3BcXFwifX0sXFxcImVuZFxcXCI6XFxcIig/PSx8O3xAfFxcXFxcXFxcKXxcXFxcXFxcXH18XFxcXFxcXFxiKD86ZXh0ZXJuKVxcXFxcXFxcYnxcXFxcXFxcXGIoPzpuYW1lc3BhY2V8bW9kZWx8b3B8dXNpbmd8aW1wb3J0fGVudW18YWxpYXN8dW5pb258aW50ZXJmYWNlfGRlY3xmbilcXFxcXFxcXGIpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEudW5pb24tdmFyaWFudC50eXBlc3BlY1xcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJpbmNsdWRlXFxcIjpcXFwiI3Rva2VuXFxcIn0se1xcXCJpbmNsdWRlXFxcIjpcXFwiI2V4cHJlc3Npb25cXFwifV19LFxcXCJ1c2luZy1zdGF0ZW1lbnRcXFwiOntcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXGIodXNpbmcpXFxcXFxcXFxiXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS51c2luZy1zdGF0ZW1lbnQudHlwZXNwZWNcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiaW5jbHVkZVxcXCI6XFxcIiN0b2tlblxcXCJ9LHtcXFwiaW5jbHVkZVxcXCI6XFxcIiNpZGVudGlmaWVyLWV4cHJlc3Npb25cXFwifSx7XFxcImluY2x1ZGVcXFwiOlxcXCIjcHVuY3R1YXRpb24tYWNjZXNzb3JcXFwifV19LFxcXCJ2YWx1ZW9mXFxcIjp7XFxcImJlZ2luXFxcIjpcXFwiXFxcXFxcXFxiKHZhbHVlb2YpXFxcIixcXFwiYmVnaW5DYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnRzcFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiKD89Pil8KD89LHw7fEB8XFxcXFxcXFwpfFxcXFxcXFxcfXxcXFxcXFxcXGIoPzpleHRlcm4pXFxcXFxcXFxifFxcXFxcXFxcYig/Om5hbWVzcGFjZXxtb2RlbHxvcHx1c2luZ3xpbXBvcnR8ZW51bXxhbGlhc3x1bmlvbnxpbnRlcmZhY2V8ZGVjfGZuKVxcXFxcXFxcYilcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS52YWx1ZW9mLnR5cGVzcGVjXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImluY2x1ZGVcXFwiOlxcXCIjZXhwcmVzc2lvblxcXCJ9XX19LFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2UudHNwXFxcIixcXFwiYWxpYXNlc1xcXCI6W1xcXCJ0c3BcXFwiXX1cIikpXG5cbmV4cG9ydCBkZWZhdWx0IFtcbmxhbmdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/typespec.mjs\n"));

/***/ })

}]);