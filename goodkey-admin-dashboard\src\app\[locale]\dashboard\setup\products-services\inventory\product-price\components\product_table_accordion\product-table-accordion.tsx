'use client';

import { useQuery } from '@tanstack/react-query';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Spinner } from '@/components/ui/spinner';
import CategoryDisplay from '../category_display';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';

interface Props {
  selectedWarehouseId: number | null;
}

export const ProductTableAccordion = ({ selectedWarehouseId }: Props) => {
  const groupId = 1;
  const { data, isLoading } = useQuery({
    queryKey: ['products', Number(selectedWarehouseId)],
    queryFn: () =>
      OfferingRateQuery.getAllByWarehouse(Number(selectedWarehouseId)),
    // selectedWarehouseId !== null
    //   ? OfferingRateQuery.getAllByWarehouse(selectedWarehouseId)
    //   : OfferingRateQuery.getAll(), // fallback for "view-all"
    enabled: selectedWarehouseId !== undefined,
  });

  if (isLoading)
    return (
      <div>
        <Spinner />
      </div>
    );

  return (
    <div className="overflow-x-auto w-full">
      <div className="w-full border-collapse mx-auto">
        <div className="w-full">
          {/* Header for columns */}
          <div className="py-4 bg-slate-100 grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 border-b border-gray-200">
            <div className="text-slate-600 font-medium pl-3">Product Name</div>
            <div className="text-slate-600 font-medium text-left -ml-[260px]">
              Quantity
            </div>
            <div className="text-slate-600 font-medium text-left -ml-[190px]">
              Price / Unit
            </div>
            <div className="text-slate-600 font-medium -ml-[120px]">
              Discontinued
            </div>
            <div className="text-slate-600 font-medium text-left -ml-[20px]">
              Actions
            </div>
          </div>
          {data &&
            data.group.length > 0 &&
            data.group.map((g) => (
              <Accordion
                key={g.groupId}
                type="single"
                collapsible
                className="space-y-3"
              >
                <AccordionItem value={g.groupId.toString()}>
                  <div
                    className={`pl-3 pr-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
                  >
                    <div className="grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
                      <div>
                        <AccordionTrigger className="cursor-pointer hover:text-main hover:underline flex items-center gap-1">
                          <span
                            className={`text-md font-medium ${g.categories.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 hover:text-main`}
                          >
                            {g.groupName}
                          </span>
                          <span
                            className="font-mono font-normal text-sm"
                            style={{ color: '#B10055' }}
                          >
                            ({g.code})
                          </span>
                          {/* {g.categories.length === 0 && (
                      <span className="text-xs text-gray-600 line-through">
                        No category available
                      </span>
                        )} */}
                        </AccordionTrigger>
                      </div>
                      <div className="text-left -ml-[260px]">
                        {/* <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          className={`w-20 bg-gray-100 border-none selection:bg-primary selection:text-primary-foreground flex h-8 min-w-0 rounded-md border text-base transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:ring-ring/30 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`}
                          value={warehouseData.qty}
                          onChange={(e) =>
                            handleQuantityChange(
                              product.id,
                              warehouse,
                              e.target.value,
                            )
                          }
                          className={`w-20 ${warehouseStockStatus.isLowStock ? 'border-orange-300 bg-orange-50' : ''} ${isViewAllMode ? 'bg-gray-50 cursor-not-allowed text-gray-900 !text-gray-900' : ''}`}
                          readOnly={isViewAllMode}
                          disabled={isViewAllMode}
                        /> */}
                      </div>
                      <div className="text-left -ml-[190px]">
                        {/* <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                            $
                          </span>
                          <Input
                            type="number"
                            placeholder="0.00"
                            min="0.00"
                            step="0.01"
                            value={warehouseData.price.toFixed(2)}
                            onChange={(e) =>
                              handlePriceChange(
                                product.id,
                                warehouse,
                                e.target.value,
                              )
                            }
                            className={`w-28 pl-8 bg-gray-100 border-none selection:bg-primary selection:text-primary-foreground flex h-8 min-w-0 rounded-md border text-base transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:ring-ring/30 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`}
                            readOnly={isViewAllMode}
                            disabled={isViewAllMode}
                          />
                        </div> */}
                      </div>
                      <div className="-ml-[80px]">{/* <Checkbox /> */}</div>
                      {/* Empty status column for groups */}
                      {/* <Link
                    href={`/dashboard/setup/product/${groupId}/category/${category.categoryId}/add`}
                  >
                    <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                      <FaPlus className="h-3 w-3" />
                    </Button>
                  </Link> */}
                      <div className="-ml-[20px]">
                        {/* <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                          title="Update quantity, unit price, and status for all products in this group"
                        >
                          <span className="text-sm">Save</span>
                        </Button> */}
                      </div>
                    </div>
                  </div>

                  <AccordionContent className="pl-4 pb-3 w-full">
                    {g.categories && g.categories.length > 0 ? (
                      g.categories.map((c) => (
                        <CategoryDisplay
                          key={c.categoryId}
                          data={c}
                          groupId={groupId}
                          categoryId={c.categoryId}
                          warehouseId={selectedWarehouseId}
                        />
                      ))
                    ) : (
                      <div className="text-sm text-gray-500 italic">
                        No category in this group.
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ProductTableAccordion;
