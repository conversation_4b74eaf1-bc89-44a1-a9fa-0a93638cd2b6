"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_gleam_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/gleam.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/gleam.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Gleam\\\",\\\"name\\\":\\\"gleam\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#entity\\\"},{\\\"include\\\":\\\"#discards\\\"}],\\\"repository\\\":{\\\"binary_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[bB]0*1[01_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.binary.gleam\\\",\\\"patterns\\\":[]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"//.*\\\",\\\"name\\\":\\\"comment.line.gleam\\\"}]},\\\"constant\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#binary_number\\\"},{\\\"include\\\":\\\"#octal_number\\\"},{\\\"include\\\":\\\"#hexadecimal_number\\\"},{\\\"include\\\":\\\"#decimal_number\\\"},{\\\"include\\\":\\\"#boolean\\\"},{\\\"match\\\":\\\"[[:upper:]][[:alnum:]]*\\\",\\\"name\\\":\\\"entity.name.type.gleam\\\"}]},\\\"decimal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b(0*[1-9][0-9_]*|0)(\\\\\\\\.(0*[1-9][0-9_]*|0)?(e-?0*[1-9][0-9]*)?)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.gleam\\\",\\\"patterns\\\":[]},\\\"discards\\\":{\\\"match\\\":\\\"\\\\\\\\b_(?:[[:word:]]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"comment.unused.gleam\\\"},\\\"entity\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b([[:lower:]][[:word:]]*)\\\\\\\\b[[:space:]]*\\\\\\\\(\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.gleam\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b([[:lower:]][[:word:]]*):\\\\\\\\s\\\",\\\"name\\\":\\\"variable.parameter.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:lower:]][[:word:]]*):\\\",\\\"name\\\":\\\"entity.name.namespace.gleam\\\"}]},\\\"hexadecimal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[xX]0*[1-9a-zA-Z][0-9a-zA-Z]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexadecimal.gleam\\\",\\\"patterns\\\":[]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(as|use|case|if|fn|import|let|assert|pub|type|opaque|const|todo|panic|else|try)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.gleam\\\"},{\\\"match\\\":\\\"(<\\\\\\\\-|\\\\\\\\->)\\\",\\\"name\\\":\\\"keyword.operator.arrow.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\|>\\\",\\\"name\\\":\\\"keyword.operator.pipe.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"keyword.operator.splat.gleam\\\"},{\\\"match\\\":\\\"(==|!=)\\\",\\\"name\\\":\\\"keyword.operator.comparison.gleam\\\"},{\\\"match\\\":\\\"(<=\\\\\\\\.|>=\\\\\\\\.|<\\\\\\\\.|>\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.operator.comparison.float.gleam\\\"},{\\\"match\\\":\\\"(<=|>=|<|>)\\\",\\\"name\\\":\\\"keyword.operator.comparison.int.gleam\\\"},{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.logical.gleam\\\"},{\\\"match\\\":\\\"<>\\\",\\\"name\\\":\\\"keyword.operator.string.gleam\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.other.gleam\\\"},{\\\"match\\\":\\\"(\\\\\\\\+\\\\\\\\.|\\\\\\\\-\\\\\\\\.|/\\\\\\\\.|\\\\\\\\*\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.float.gleam\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|\\\\\\\\-|/|\\\\\\\\*|%)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.int.gleam\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.gleam\\\"}]},\\\"octal_number\\\":{\\\"match\\\":\\\"\\\\\\\\b0[oO]0*[1-7][0-7]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.octal.gleam\\\",\\\"patterns\\\":[]},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.gleam\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.gleam\\\"}]}},\\\"scopeName\\\":\\\"source.gleam\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/gleam.mjs\n"));

/***/ })

}]);