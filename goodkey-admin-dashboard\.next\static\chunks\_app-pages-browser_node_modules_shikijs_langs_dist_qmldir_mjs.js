"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_qmldir_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/qmldir.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/qmldir.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"QML Directory\\\",\\\"name\\\":\\\"qmldir\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#version\\\"},{\\\"include\\\":\\\"#names\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.number-sign.qmldir\\\"}]},\\\"file-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\.(qmltypes|qml|js)\\\\\\\\b\\\",\\\"name\\\":\\\"string.unquoted.qmldir\\\"}]},\\\"identifier\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.qmldir\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.qmldir\\\"}]},\\\"module-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.qmldir\\\"}]},\\\"names\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#file-name\\\"},{\\\"include\\\":\\\"#module-name\\\"},{\\\"include\\\":\\\"#identifier\\\"}]},\\\"version\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\.\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.qml\\\"}]}},\\\"scopeName\\\":\\\"source.qmldir\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/qmldir.mjs\n"));

/***/ })

}]);