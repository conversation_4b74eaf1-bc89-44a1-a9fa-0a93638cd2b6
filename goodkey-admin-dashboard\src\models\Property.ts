import { PropertyOption } from './PropertyOption';

export interface Property {
  id: number;
  name: string;
  code?: string | null;
  description?: string | null;
  createdAt?: string; // ISO date string
  createdById?: number | null;
  updatedAt?: string; // ISO date string
  updatedById?: number | null;
}

export interface PropertyDetail {
  id: number;
  code?: string;
  name?: string;
  description?: string;
  option?: PropertyOption[];
}
