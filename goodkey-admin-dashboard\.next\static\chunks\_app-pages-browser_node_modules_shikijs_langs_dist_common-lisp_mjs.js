"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_common-lisp_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/common-lisp.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/common-lisp.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Common Lisp\\\",\\\"fileTypes\\\":[\\\"lisp\\\",\\\"lsp\\\",\\\"l\\\",\\\"cl\\\",\\\"asd\\\",\\\"asdf\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\(\\\",\\\"foldingStopMarker\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"common-lisp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#block-comment\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#lambda-list\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#style-guide\\\"},{\\\"include\\\":\\\"#def-name\\\"},{\\\"include\\\":\\\"#macro\\\"},{\\\"include\\\":\\\"#symbol\\\"},{\\\"include\\\":\\\"#special-operator\\\"},{\\\"include\\\":\\\"#declaration\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#class\\\"},{\\\"include\\\":\\\"#condition-type\\\"},{\\\"include\\\":\\\"#package\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#punctuation\\\"}],\\\"repository\\\":{\\\"block-comment\\\":{\\\"begin\\\":\\\"\\\\\\\\#\\\\\\\\|\\\",\\\"contentName\\\":\\\"comment.block.commonlisp\\\",\\\"end\\\":\\\"\\\\\\\\|\\\\\\\\#\\\",\\\"name\\\":\\\"comment\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block-comment\\\",\\\"name\\\":\\\"comment\\\"}]},\\\"class\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:two-way-stream|synonym-stream|symbol|structure-object|structure-class|string-stream|stream|standard-object|standard-method|\\\\nstandard-generic-function|standard-class|sequence|restart|real|readtable|ratio|random-state|package|number|method|integer|hash-table|\\\\ngeneric-function|file-stream|echo-stream|concatenated-stream|class|built-in-class|broadcast-stream|bit-vector|array)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.class.commonlisp\\\"},\\\"comment\\\":{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=;)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.commonlisp\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\";\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.commonlisp\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.semicolon.commonlisp\\\"}]},\\\"condition-type\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:warning|undefined-function|unbound-variable|unbound-slot|type-error|style-warning|stream-error|storage-condition|simple-warning|\\\\nsimple-type-error|simple-error|simple-condition|serious-condition|reader-error|program-error|print-not-readable|parse-error|package-error|\\\\nfloating-point-underflow|floating-point-overflow|floating-point-invalid-operation|floating-point-inexact|file-error|error|end-of-file|\\\\ndivision-by-zero|control-error|condition|cell-error|arithmetic-error)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.type.exception.commonlisp\\\"},\\\"constant\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(?:t|single-float-negative-epsilon|single-float-epsilon|short-float-negative-epsilon|short-float-epsilon|pi|\\\\nnil|multiple-values-limit|most-positive-single-float|most-positive-short-float|most-positive-long-float|\\\\nmost-positive-fixnum|most-positive-double-float|most-negative-single-float|most-negative-short-float|\\\\nmost-negative-long-float|most-negative-fixnum|most-negative-double-float|long-float-negative-epsilon|\\\\nlong-float-epsilon|least-positive-single-float|least-positive-short-float|least-positive-normalized-single-float|\\\\nleast-positive-normalized-short-float|least-positive-normalized-long-float|least-positive-normalized-double-float|\\\\nleast-positive-long-float|least-positive-double-float|least-negative-single-float|least-negative-short-float|\\\\nleast-negative-normalized-single-float|least-negative-normalized-short-float|least-negative-normalized-long-float|\\\\nleast-negative-normalized-double-float|least-negative-long-float|least-negative-double-float|lambda-parameters-limit|\\\\nlambda-list-keywords|internal-time-units-per-second|double-float-negative-epsilon|double-float-epsilon|char-code-limit|\\\\ncall-arguments-limit|boole-xor|boole-set|boole-orc2|boole-orc1|boole-nor|boole-nand|boole-ior|boole-eqv|boole-clr|\\\\nboole-c2|boole-c1|boole-andc2|boole-andc1|boole-and|boole-2|boole-1|array-total-size-limit|array-rank-limit|array-dimension-limit)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\))) # followed by space, ( or )\\\",\\\"name\\\":\\\"constant.language.commonlisp\\\"},{\\\"match\\\":\\\"(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,)([+-]?[0-9]+(?:\\\\\\\\/[0-9]+)*|[-+]?[0-9]*\\\\\\\\.?[0-9]+([eE][-+]?[0-9]+)?|(\\\\\\\\#b|\\\\\\\\#B)[01\\\\\\\\/+-]+|(\\\\\\\\#o|\\\\\\\\#O)[0-7\\\\\\\\/+-]+|(\\\\\\\\#x|\\\\\\\\#X)[0-9a-fA-F\\\\\\\\/+-]+|(\\\\\\\\#[0-9]+[rR]?)[0-9a-zA-Z\\\\\\\\/+-]+)(?=(\\\\\\\\s|\\\\\\\\)))\\\",\\\"name\\\":\\\"constant.numeric.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=\\\\\\\\s) # preceded by space\\\\n(\\\\\\\\.)\\\\n(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"variable.other.constant.dot.commonlisp\\\"},{\\\"match\\\":\\\"(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,)([+-]?[0-9]*\\\\\\\\.[0-9]*((e|s|f|d|l|E|S|F|D|L)[+-]?[0-9]+)?|[+-]?[0-9]+(\\\\\\\\.[0-9]*)?(e|s|f|d|l|E|S|F|D|L)[+-]?[0-9]+)(?=(\\\\\\\\s|\\\\\\\\)))\\\",\\\"name\\\":\\\"constant.numeric.commonlisp\\\"}]},\\\"declaration\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:type|speed|special|space|safety|optimize|notinline|inline|ignore|ignorable|ftype|dynamic-extent|declaration|debug|compilation-speed)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"storage.type.function.declaration.commonlisp\\\"},\\\"def-name\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.constant.defname.commonlisp\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#package\\\"},{\\\"match\\\":\\\"\\\\\\\\S+?\\\",\\\"name\\\":\\\"entity.name.function.commonlisp\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"variable.other.constant.defname.commonlisp\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#package\\\"},{\\\"match\\\":\\\"\\\\\\\\S+?\\\",\\\"name\\\":\\\"entity.name.function.commonlisp\\\"}]}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()                     # preceded by (\\\\n(defun|defsetf|defmethod|defmacro|define-symbol-macro|define-setf-expander|\\\\ndefine-modify-macro|define-method-combination|define-compiler-macro|defgeneric)  #1 keywords\\\\n\\\\\\\\s+\\\\n(   \\\\\\\\(\\\\\\\\s*\\\\n        ([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+) #3\\\\n    \\\\\\\\s*\\\\n        ((,@|,\\\\\\\\.|,)?) #4\\\\n        ([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)      #6 (<3>something+ <6>name)\\\\n    |\\\\n        ((,@|,\\\\\\\\.|,)?) #7\\\\n        ([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)      #9 name\\\\n) #2\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()\\\\n(deftype|defpackage|define-condition|defclass)              # keywords\\\\n\\\\\\\\s+\\\\n([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)                   # name\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#package\\\"},{\\\"match\\\":\\\"\\\\\\\\S+?\\\",\\\"name\\\":\\\"variable.other.constant.defname.commonlisp\\\"}]}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()\\\\n(defconstant)         # keywords\\\\n\\\\\\\\s+\\\\n([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)                # name\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()\\\\n(defvar|defparameter) # keywords\\\\n\\\\\\\\s+\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.defname.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()\\\\n(defstruct)         # keywords\\\\n\\\\\\\\s+\\\\\\\\(?\\\\\\\\s*\\\\n([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)              # name\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.commonlisp\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#package\\\"},{\\\"match\\\":\\\"\\\\\\\\S+?\\\",\\\"name\\\":\\\"entity.name.function.commonlisp\\\"}]}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\()\\\\n(macrolet|labels|flet)        # keywords\\\\n\\\\\\\\s+\\\\\\\\(\\\\\\\\s*\\\\\\\\(\\\\\\\\s*\\\\n([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)                        # name\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"}]},\\\"escape\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:\\\\\\\\#\\\\\\\\\\\\\\\\\\\\\\\\S+?)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"constant.character.escape.commonlisp\\\"},\\\"function\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:values|third|tenth|symbol-value|symbol-plist|symbol-function|svref|subseq|sixth|seventh|second|schar|sbit|row-major-aref|\\\\n   rest|readtable-case|nth|ninth|mask-field|macro-function|logical-pathname-translations|ldb|gethash|getf|get|fourth|first|\\\\n   find-class|fill-pointer|fifth|fdefinition|elt|eighth|compiler-macro-function|char|cdr|cddr|cdddr|cddddr|cdddar|cddar|cddadr|\\\\n   cddaar|cdar|cdadr|cdaddr|cdadar|cdaar|cdaadr|cdaaar|car|cadr|caddr|cadddr|caddar|cadar|cadadr|cadaar|caar|caadr|caaddr|caadar|\\\\n   caaar|caaadr|caaaar|bit|aref)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.accessor.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:yes-or-no-p|y-or-n-p|write-sequence|write-char|write-byte|warn|vector-pop|use-value|use-package|unuse-package|union|unintern|\\\\nunexport|terpri|tailp|substitute-if-not|substitute-if|substitute|subst-if-not|subst-if|subst|sublis|string-upcase|string-downcase|\\\\nstring-capitalize|store-value|sleep|signal|shadowing-import|shadow|set-syntax-from-char|set-macro-character|set-exclusive-or|\\\\nset-dispatch-macro-character|set-difference|set|rplacd|rplaca|room|reverse|revappend|require|replace|remprop|remove-if-not|remove-if|\\\\nremove-duplicates|remove|remhash|read-sequence|read-byte|random|provide|pprint-tabular|pprint-newline|pprint-linear|pprint-fill|\\\\nnunion|nsubstitute-if-not|nsubstitute-if|nsubstitute|nsubst-if-not|nsubst-if|nsubst|nsublis|nstring-upcase|nstring-downcase|nstring-capitalize|\\\\nnset-exclusive-or|nset-difference|nreverse|nreconc|nintersection|nconc|muffle-warning|method-combination-error|maphash|makunbound|ldiff|\\\\ninvoke-restart-interactively|invoke-restart|invoke-debugger|invalid-method-error|intersection|inspect|import|get-output-stream-string|\\\\nget-macro-character|get-dispatch-macro-character|gentemp|gensym|fresh-line|fill|file-position|export|describe|delete-if-not|delete-if|\\\\ndelete-duplicates|delete|continue|clrhash|close|clear-input|break|abort)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\))) # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.f.sideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:zerop|write-to-string|write-string|write-line|write|wild-pathname-p|vectorp|vector-push-extend|vector-push|vector|values-list|\\\\nuser-homedir-pathname|upper-case-p|upgraded-complex-part-type|upgraded-array-element-type|unread-char|unbound-slot-instance|typep|type-of|\\\\ntype-error-expected-type|type-error-datum|two-way-stream-output-stream|two-way-stream-input-stream|truncate|truename|tree-equal|translate-pathname|\\\\ntranslate-logical-pathname|tanh|tan|synonym-stream-symbol|symbolp|symbol-package|symbol-name|sxhash|subtypep|subsetp|stringp|string>=|string>|\\\\nstring=|string<=|string<|string\\\\\\\\/=|string-trim|string-right-trim|string-not-lessp|string-not-greaterp|string-not-equal|string-lessp|\\\\nstring-left-trim|string-greaterp|string-equal|string|streamp|stream-external-format|stream-error-stream|stream-element-type|standard-char-p|\\\\nstable-sort|sqrt|special-operator-p|sort|some|software-version|software-type|slot-value|slot-makunbound|slot-exists-p|slot-boundp|sinh|sin|\\\\nsimple-vector-p|simple-string-p|simple-condition-format-control|simple-condition-format-arguments|simple-bit-vector-p|signum|short-site-name|\\\\nset-pprint-dispatch|search|scale-float|round|restart-name|rename-package|rename-file|rem|reduce|realpart|realp|readtablep|\\\\nread-preserving-whitespace|read-line|read-from-string|read-delimited-list|read-char-no-hang|read-char|read|rationalp|rationalize|\\\\nrational|rassoc-if-not|rassoc-if|rassoc|random-state-p|proclaim|probe-file|print-not-readable-object|print|princ-to-string|princ|\\\\nprin1-to-string|prin1|pprint-tab|pprint-indent|pprint-dispatch|pprint|position-if-not|position-if|position|plusp|phase|peek-char|pathnamep|\\\\npathname-version|pathname-type|pathname-name|pathname-match-p|pathname-host|pathname-directory|pathname-device|pathname|parse-namestring|\\\\nparse-integer|pairlis|packagep|package-used-by-list|package-use-list|package-shadowing-symbols|package-nicknames|package-name|package-error-package|\\\\noutput-stream-p|open-stream-p|open|oddp|numerator|numberp|null|nthcdr|notevery|notany|not|next-method-p|nbutlast|namestring|name-char|mod|mismatch|\\\\nminusp|min|merge-pathnames|merge|member-if-not|member-if|member|max|maplist|mapl|mapcon|mapcar|mapcan|mapc|map-into|map|make-two-way-stream|\\\\nmake-synonym-stream|make-symbol|make-string-output-stream|make-string-input-stream|make-string|make-sequence|make-random-state|make-pathname|\\\\nmake-package|make-load-form-saving-slots|make-list|make-hash-table|make-echo-stream|make-dispatch-macro-character|make-condition|\\\\nmake-concatenated-stream|make-broadcast-stream|make-array|macroexpand-1|macroexpand|machine-version|machine-type|machine-instance|lower-case-p|\\\\nlong-site-name|logxor|logtest|logorc2|logorc1|lognot|lognor|lognand|logior|logical-pathname|logeqv|logcount|logbitp|logandc2|logandc1|logand|\\\\nlog|load-logical-pathname-translations|load|listp|listen|list-length|list-all-packages|list\\\\\\\\*|list|lisp-implementation-version|\\\\nlisp-implementation-type|length|ldb-test|lcm|last|keywordp|isqrt|intern|interactive-stream-p|integerp|integer-length|integer-decode-float|\\\\ninput-stream-p|imagpart|identity|host-namestring|hash-table-test|hash-table-size|hash-table-rehash-threshold|hash-table-rehash-size|hash-table-p|\\\\nhash-table-count|graphic-char-p|get-universal-time|get-setf-expansion|get-properties|get-internal-run-time|get-internal-real-time|\\\\nget-decoded-time|gcd|functionp|function-lambda-expression|funcall|ftruncate|fround|format|force-output|fmakunbound|floor|floatp|float-sign|\\\\nfloat-radix|float-precision|float-digits|float|finish-output|find-symbol|find-restart|find-package|find-if-not|find-if|find-all-symbols|find|\\\\nfile-write-date|file-string-length|file-namestring|file-length|file-error-pathname|file-author|ffloor|fceiling|fboundp|expt|exp|every|evenp|\\\\neval|equalp|equal|eql|eq|ensure-generic-function|ensure-directories-exist|enough-namestring|endp|encode-universal-time|ed|echo-stream-output-stream|\\\\necho-stream-input-stream|dribble|dpb|disassemble|directory-namestring|directory|digit-char-p|digit-char|deposit-field|denominator|delete-package|\\\\ndelete-file|decode-universal-time|decode-float|count-if-not|count-if|count|cosh|cos|copy-tree|copy-symbol|copy-structure|copy-seq|copy-readtable|\\\\ncopy-pprint-dispatch|copy-list|copy-alist|constantp|constantly|consp|cons|conjugate|concatenated-stream-streams|concatenate|compute-restarts|\\\\ncomplexp|complex|complement|compiled-function-p|compile-file-pathname|compile-file|compile|coerce|code-char|clear-output|class-of|cis|characterp|\\\\ncharacter|char>=|char>|char=|char<=|char<|char\\\\\\\\/=|char-upcase|char-not-lessp|char-not-greaterp|char-not-equal|char-name|char-lessp|char-int|\\\\nchar-greaterp|char-equal|char-downcase|char-code|cerror|cell-error-name|ceiling|call-next-method|byte-size|byte-position|byte|butlast|\\\\nbroadcast-stream-streams|boundp|both-case-p|boole|bit-xor|bit-vector-p|bit-orc2|bit-orc1|bit-not|bit-nor|bit-nand|bit-ior|bit-eqv|bit-andc2|\\\\nbit-andc1|bit-and|atom|atanh|atan|assoc-if-not|assoc-if|assoc|asinh|asin|ash|arrayp|array-total-size|array-row-major-index|array-rank|\\\\narray-in-bounds-p|array-has-fill-pointer-p|array-element-type|array-displacement|array-dimensions|array-dimension|arithmetic-error-operation|\\\\narithmetic-error-operands|apropos-list|apropos|apply|append|alphanumericp|alpha-char-p|adjustable-array-p|adjust-array|adjoin|acosh|acos|acons|\\\\nabs|>=|>|=|<=|<|1-|1\\\\\\\\+|\\\\\\\\/=|\\\\\\\\/|-|\\\\\\\\+|\\\\\\\\*)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\))) # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.f.sideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:variable|update-instance-for-redefined-class|update-instance-for-different-class|structure|slot-unbound|slot-missing|shared-initialize|\\\\nremove-method|print-object|no-next-method|no-applicable-method|method-qualifiers|make-load-form|make-instances-obsolete|make-instance|\\\\ninitialize-instance|function-keywords|find-method|documentation|describe-object|compute-applicable-methods|compiler-macro|class-name|\\\\nchange-class|allocate-instance|add-method)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.sgf.nosideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:reinitialize-instance)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.sgf.sideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\#') # preceded by space or (\\\\n(?:satisfies)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.function.typespecifier.commonlisp\\\"}]},\\\"lambda-list\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:&[#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?|&whole|&rest|&optional|&key|&environment|&body|&aux|&allow-other-keys)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\))) # followed by space, ( or )\\\",\\\"name\\\":\\\"keyword.other.lambdalist.commonlisp\\\"},\\\"macro\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:with-standard-io-syntax|with-slots|with-simple-restart|with-package-iterator|with-hash-table-iterator|with-condition-restarts|\\\\nwith-compilation-unit|with-accessors|when|unless|typecase|time|step|shiftf|setf|rotatef|return|restart-case|restart-bind|psetf|prog2|prog1|\\\\nprog\\\\\\\\*|prog|print-unreadable-object|pprint-logical-block|pprint-exit-if-list-exhausted|or|nth-value|multiple-value-setq|multiple-value-list|\\\\nmultiple-value-bind|make-method|loop|lambda|ignore-errors|handler-case|handler-bind|formatter|etypecase|dotimes|dolist|do-symbols|do-external-symbols|\\\\ndo-all-symbols|do\\\\\\\\*|do|destructuring-bind|defun|deftype|defstruct|defsetf|defpackage|defmethod|defmacro|define-symbol-macro|define-setf-expander|\\\\ndefine-condition|define-compiler-macro|defgeneric|defconstant|defclass|declaim|ctypecase|cond|call-method|assert|and)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"storage.type.function.m.nosideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:with-output-to-string|with-open-stream|with-open-file|with-input-from-string|untrace|trace|remf|pushnew|push|psetq|pprint-pop|pop|\\\\notherwise|loop-finish|incf|in-package|ecase|defvar|defparameter|define-modify-macro|define-method-combination|decf|check-type|ccase|case)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"storage.type.function.m.sideeffects.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:setq)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"storage.type.function.specialform.commonlisp\\\"}]},\\\"package\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.type.package.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.package.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\n  ([A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)  #2\\\\n  | \\\\n  (\\\\\\\\#) #3\\\\n)\\\\n(?=\\\\\\\\:\\\\\\\\:|\\\\\\\\:)\\\"}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n('|`)\\\\n(?=\\\\\\\\S)\\\",\\\"name\\\":\\\"variable.other.constant.singlequote.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(?:\\\\\\\\:[#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))       # followed by space, ( or )\\\",\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)([0-9]*)\\\\n(?=\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)\\\\n([0-9]*)\\\\n(\\\\\\\\*)\\\\n(?=0|1)\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#\\\\\\\\*|\\\\\\\\#0\\\\\\\\*)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))       # followed by space, ( or )\\\",\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)\\\\n([0-9]+)\\\\n(a|A)\\\\n(?=.)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)\\\\n([0-9]+)\\\\n(=)\\\\n(?=.)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)\\\\n([0-9]+)\\\\n(\\\\\\\\#)\\\\n(?=.)\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#(\\\\\\\\+|-))\\\\n(?=\\\\\\\\S)\\\",\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#('|,|\\\\\\\\.|c|C|s|S|p|P))\\\\n(?=\\\\\\\\S)\\\",\\\"name\\\":\\\"variable.other.constant.sharpsign.commonlisp\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.package.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\#)\\\\n(:)\\\\n(?=\\\\\\\\S)\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"variable.other.constant.backquote.commonlisp\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.constant.backquote.commonlisp\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.constant.backquote.commonlisp\\\"},\\\"5\\\":{\\\"name\\\":\\\"variable.other.constant.backquote.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(\\\\n    (`\\\\\\\\#) #2\\\\n    |\\\\n    (`)(,@|,\\\\\\\\.|,)? #3, #4\\\\n    |\\\\n    (,@|,\\\\\\\\.|,) #5\\\\n)\\\\n(?=\\\\\\\\S)\\\"}]},\\\"special-operator\\\":{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(\\\\\\\\(\\\\\\\\s*) # preceded by (\\\\n(unwind-protect|throw|the|tagbody|symbol-macrolet|return-from|quote|progv|progn|multiple-value-prog1|multiple-value-call|\\\\nmacrolet|locally|load-time-value|let\\\\\\\\*|let|labels|if|go|function|flet|eval-when|catch|block)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\"},\\\"string\\\":{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.commonlisp\\\"}},\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.commonlisp\\\"}},\\\"name\\\":\\\"string.quoted.double.commonlisp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.commonlisp\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.formattedstring.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.constant.formattedstring.commonlisp\\\"},\\\"8\\\":{\\\"name\\\":\\\"storage.type.function.formattedstring.commonlisp\\\"},\\\"10\\\":{\\\"name\\\":\\\"storage.type.function.formattedstring.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n(~) #1 tilde\\\\n(\\\\n    (\\\\n        (([+-]?[0-9]+)|('.)|V|\\\\\\\\#)*?\\\\n        (,)?\\\\n    )\\\\n*?) #2 prefix parameters, signed decimal numbers|single char, separated by commas\\\\n(\\\\n    (:@|@:|:|@)\\\\n?) #8 modifiers\\\\n(\\\\\\\\(|\\\\\\\\)|\\\\\\\\[|\\\\\\\\]|;|{|}|<|>|\\\\\\\\^) #10 control structures\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.constant.formattedstring.commonlisp\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"10\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n(~) #1 tilde\\\\n(\\\\n    (\\\\n        (([+-]?[0-9]+)|('.)|V|\\\\\\\\#)*?\\\\n        (,)?\\\\n    )\\\\n*?) #2 prefix parameters, signed decimal numbers|single char, separated by commas\\\\n(\\\\n    (:@|@:|:|@)\\\\n?) #8 modifiers\\\\n(A|S|D|B|O|X|R|P|C|F|E|G|\\\\\\\\$|%|\\\\\\\\&|\\\\\\\\||~|T|\\\\\\\\*|\\\\\\\\?|_|W|I) #10 directives\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.constant.formattedstring.commonlisp\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"10\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"11\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"},\\\"12\\\":{\\\"name\\\":\\\"entity.name.variable.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n(~) #1 tilde\\\\n(\\\\n    (\\\\n        (([+-]?[0-9]+)|('.)|V|\\\\\\\\#)*?\\\\n        (,)?\\\\n    )\\\\n*?) #2 prefix parameters, signed decimal numbers|single char, separated by commas\\\\n(\\\\n    (:@|@:|:|@)\\\\n?) #8 modifiers\\\\n(\\\\\\\\/) #10\\\\n([#:A-Za-z0-9\\\\\\\\+\\\\\\\\-\\\\\\\\*\\\\\\\\/\\\\\\\\@\\\\\\\\$\\\\\\\\%\\\\\\\\^\\\\\\\\&\\\\\\\\_\\\\\\\\=\\\\\\\\<\\\\\\\\>\\\\\\\\~\\\\\\\\!\\\\\\\\?\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}\\\\\\\\.]+?) #11 call function\\\\n(\\\\\\\\/) #12\\\"},{\\\"match\\\":\\\"(~\\\\\\\\n)\\\",\\\"name\\\":\\\"variable.other.constant.formattedstring.commonlisp\\\"}]},\\\"style-guide\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"source.commonlisp\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^'|\\\\\\\\s'|\\\\\\\\('|,@'|,\\\\\\\\.'|,')\\\\n(\\\\\\\\S+?)\\\\n(\\\\\\\\:\\\\\\\\:|\\\\\\\\:)\\\\n((\\\\\\\\+[^\\\\\\\\s\\\\\\\\+]+\\\\\\\\+)|(\\\\\\\\*[^\\\\\\\\s\\\\\\\\*]+\\\\\\\\*))\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=\\\\\\\\S:|^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\+[^\\\\\\\\s\\\\\\\\+]+\\\\\\\\+)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"variable.other.constant.earmuffsplus.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=\\\\\\\\S:|^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(\\\\\\\\*[^\\\\\\\\s\\\\\\\\*]+\\\\\\\\*)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"string.regexp.earmuffsasterisk.commonlisp\\\"}]},\\\"symbol\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:method-combination|declare)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"storage.type.function.symbol.commonlisp\\\"},\\\"type\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\() # preceded by space or (\\\\n(?:unsigned-byte|standard-char|standard|single-float|simple-vector|simple-string|simple-bit-vector|simple-base-string|simple-array|\\\\nsigned-byte|short-float|long-float|keyword|fixnum|extended-char|double-float|compiled-function|boolean|bignum|base-string|base-char)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"support.type.t.commonlisp\\\"},\\\"variable\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(?:\\\\\\\\*trace-output\\\\\\\\*|\\\\\\\\*terminal-io\\\\\\\\*|\\\\\\\\*standard-output\\\\\\\\*|\\\\\\\\*standard-input\\\\\\\\*|\\\\\\\\*readtable\\\\\\\\*|\\\\\\\\*read-suppress\\\\\\\\*|\\\\\\\\*read-eval\\\\\\\\*|\\\\n\\\\\\\\*read-default-float-format\\\\\\\\*|\\\\\\\\*read-base\\\\\\\\*|\\\\\\\\*random-state\\\\\\\\*|\\\\\\\\*query-io\\\\\\\\*|\\\\\\\\*print-right-margin\\\\\\\\*|\\\\\\\\*print-readably\\\\\\\\*|\\\\\\\\*print-radix\\\\\\\\*|\\\\\\\\*print-pretty\\\\\\\\*|\\\\n\\\\\\\\*print-pprint-dispatch\\\\\\\\*|\\\\\\\\*print-miser-width\\\\\\\\*|\\\\\\\\*print-lines\\\\\\\\*|\\\\\\\\*print-level\\\\\\\\*|\\\\\\\\*print-length\\\\\\\\*|\\\\\\\\*print-gensym\\\\\\\\*|\\\\\\\\*print-escape\\\\\\\\*|\\\\\\\\*print-circle\\\\\\\\*|\\\\n\\\\\\\\*print-case\\\\\\\\*|\\\\\\\\*print-base\\\\\\\\*|\\\\\\\\*print-array\\\\\\\\*|\\\\\\\\*package\\\\\\\\*|\\\\\\\\*modules\\\\\\\\*|\\\\\\\\*macroexpand-hook\\\\\\\\*|\\\\\\\\*load-verbose\\\\\\\\*|\\\\\\\\*load-truename\\\\\\\\*|\\\\\\\\*load-print\\\\\\\\*|\\\\n\\\\\\\\*load-pathname\\\\\\\\*|\\\\\\\\*gensym-counter\\\\\\\\*|\\\\\\\\*features\\\\\\\\*|\\\\\\\\*error-output\\\\\\\\*|\\\\\\\\*default-pathname-defaults\\\\\\\\*|\\\\\\\\*debugger-hook\\\\\\\\*|\\\\\\\\*debug-io\\\\\\\\*|\\\\\\\\*compile-verbose\\\\\\\\*|\\\\n\\\\\\\\*compile-print\\\\\\\\*|\\\\\\\\*compile-file-truename\\\\\\\\*|\\\\\\\\*compile-file-pathname\\\\\\\\*|\\\\\\\\*break-on-signals\\\\\\\\*)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"string.regexp.earmuffsasterisk.commonlisp\\\"},{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|,@|,\\\\\\\\.|,) # preceded by space , ( or `,`|`,@`|`,.`\\\\n(?:\\\\\\\\*\\\\\\\\*\\\\\\\\*|\\\\\\\\*\\\\\\\\*|\\\\\\\\+\\\\\\\\+\\\\\\\\+|\\\\\\\\+\\\\\\\\+|\\\\\\\\/\\\\\\\\/\\\\\\\\/|\\\\\\\\/\\\\\\\\/)\\\\n(?=(\\\\\\\\s|\\\\\\\\(|\\\\\\\\)))  # followed by space, ( or )\\\",\\\"name\\\":\\\"variable.other.repl.commonlisp\\\"}]}},\\\"scopeName\\\":\\\"source.commonlisp\\\",\\\"aliases\\\":[\\\"lisp\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/common-lisp.mjs\n"));

/***/ })

}]);