'use client';

import { useMemo, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronRight, Pin, PinOff } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import type { MenuItem } from '@/models/Menu';
import AuthQuery from '@/services/queries/AuthQuery';
import MenuQuery from '@/services/queries/MenuQuery';
import { useSetupSidebarStore } from '@/stores/setup-sidebar-store';
import { icons } from '@/common/menu-icons';

function isPathActive(currentPath: string, itemPath: string): boolean {
  if (currentPath === itemPath) return true;
  if (
    itemPath !== '/' &&
    currentPath.startsWith(itemPath + '/') &&
    (currentPath.length === itemPath.length + 1 ||
      currentPath[itemPath.length + 1] === '/')
  ) {
    return true;
  }
  return false;
}

function hasActiveChild(item: MenuItem, currentPath: string): boolean {
  if (item.submenu) {
    return item.submenu.some(
      (subItem) =>
        isPathActive(currentPath, subItem.path) ||
        hasActiveChild(subItem, currentPath),
    );
  }
  return false;
}

function processMenuItems(items: MenuItem[]): MenuItem[] {
  return items.map((item) => ({
    ...item,

    submenu: item.submenu?.length ? processMenuItems(item.submenu) : undefined,
  }));
}

function filterMenuItemsByUserMenuItems(
  items: MenuItem[],
  userMenuItems: number[],
): MenuItem[] {
  return items
    .map((item) => ({
      ...item,
      submenu: item.submenu
        ? filterMenuItemsByUserMenuItems(item.submenu, userMenuItems)
        : undefined,
    }))
    .filter((item) => {
      if (!item.menuItemId) return true;
      const isAuthorized = userMenuItems.includes(item.menuItemId);
      return isAuthorized || (item.submenu && item.submenu.length > 0);
    });
}

function filterMenuItemsBySection(
  items: MenuItem[],
  section: string,
): MenuItem[] {
  return items
    .map((item) => ({
      ...item,
      submenu: item.submenu
        ? filterMenuItemsBySection(item.submenu, section)
        : undefined,
    }))
    .filter((item) => {
      return (
        item.section === section || (item.submenu && item.submenu.length > 0)
      );
    });
}

export default function SetupSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {},
  );
  const {
    collapsed,
    hovered,
    userToggled,
    setHovered,
    toggleCollapsed,
    setCollapsed,
  } = useSetupSidebarStore();

  useEffect(() => {
    if (!userToggled) {
      setCollapsed(true);
    }
  }, [pathname, setCollapsed, userToggled]);

  const {
    data: userData,
    isLoading: isLoadingUserData,
    isError: isUserDataError,
  } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  const {
    data: rawItems,
    isLoading: isLoadingItems,
    isError: isItemsError,
  } = useQuery({
    queryKey: [MenuQuery.tags, 'brief'],
    queryFn: MenuQuery.getBrief,
  });

  const processedItems = useMemo(() => {
    if (!rawItems) return [];
    return processMenuItems(rawItems);
  }, [rawItems]);

  const menuItems = useMemo(() => {
    if (!processedItems || !userData) return [];

    const userFilteredItems = filterMenuItemsByUserMenuItems(
      processedItems,
      userData.menuItems,
    );
    return filterMenuItemsBySection(userFilteredItems, 'setup');
  }, [processedItems, userData]);

  useMemo(() => {
    if (menuItems && menuItems.length > 0) {
      const newExpandedItems: Record<string, boolean> = {};

      const expandActiveParents = (
        items: MenuItem[],
        parentPath: string[] = [],
      ) => {
        items.forEach((item) => {
          const isActive = isPathActive(pathname, item.path);
          const hasActive = hasActiveChild(item, pathname);

          if (isActive || hasActive) {
            parentPath.forEach((parent) => {
              newExpandedItems[parent] = true;
            });

            if (item.submenu && item.submenu.length > 0) {
              newExpandedItems[item.title] = true;
            }
          }

          if (item.submenu && item.submenu.length > 0) {
            expandActiveParents(item.submenu, [...parentPath, item.title]);
          }
        });
      };

      expandActiveParents(menuItems);

      // Preserve existing expanded state for non-active items
      setExpandedItems((prev) => {
        // Create a merged state that preserves user expanded items
        // but ensures active paths are always expanded
        return { ...prev, ...newExpandedItems };
      });
    }
  }, [menuItems, pathname]);

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.submenu && item.submenu.length > 0) {
      toggleExpand(item.title);
    } else if (item.path) {
      router.push(item.path);
    }
  };

  // Determine if sidebar should be in collapsed state
  const isSidebarCollapsed = collapsed && !hovered;

  if (isLoadingUserData || isLoadingItems) {
    return (
      <div className="w-full md:w-64 md:min-w-64 bg-white rounded-md border border-slate-200 p-4">
        <div className="space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className="h-8 bg-slate-100 rounded animate-pulse"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  if (isUserDataError || isItemsError) {
    return (
      <div className="w-full md:w-64 md:min-w-64 bg-white rounded-md border border-slate-200 p-4">
        <div className="text-red-500">
          Error loading menu items. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'transition-all duration-300 bg-white rounded-md border border-slate-200 h-fit',
        isSidebarCollapsed ? 'w-16 min-w-16' : 'w-full md:w-64 md:min-w-64',
      )}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div
        className={collapsed ? 'flex justify-center my-2' : 'flex justify-end'}
      >
        <button
          onClick={toggleCollapsed}
          className="mt-1 mr-1 rounded-md hover:bg-slate-100"
          aria-label={collapsed ? 'Pin sidebar' : 'Unpin sidebar'}
        >
          {collapsed ? (
            <PinOff className="h-4 w-4 text-slate-500" />
          ) : (
            <Pin className="h-4 w-4 text-slate-500" />
          )}
        </button>
      </div>
      <ScrollArea className="max-h-[calc(100vh-10rem)] mb-2">
        <div className="px-2">
          {menuItems.map((item) => {
            const IconComponent = item.icon
              ? (icons as any)[item.icon as any]
              : undefined;
            const isItemActive = isPathActive(pathname, item.path);
            const hasChildren = item.submenu && item.submenu.length > 0;
            const isExpanded =
              expandedItems[item.title] || hasActiveChild(item, pathname);

            return (
              <div key={item.title} className="mb-1">
                {hasChildren ? (
                  <button
                    className={cn(
                      'w-full flex items-center justify-between p-2 rounded-md text-left',
                      isItemActive || hasActiveChild(item, pathname)
                        ? 'bg-slate-50 text-[#00646C]'
                        : 'font-medium text-slate-700 hover:bg-slate-50 hover:text-[#00646C]',
                      isSidebarCollapsed && 'justify-center',
                    )}
                    onClick={() => handleItemClick(item)}
                  >
                    <div
                      className={cn(
                        'flex items-start',
                        isSidebarCollapsed && 'justify-center',
                      )}
                    >
                      {IconComponent && (
                        <IconComponent
                          className={cn(
                            isSidebarCollapsed ? 'h-6 w-6' : 'h-5 w-5',
                            !isSidebarCollapsed && 'mr-2 flex-shrink-0 mt-0.5',
                          )}
                        />
                      )}
                      {!isSidebarCollapsed && (
                        <span className="leading-tight">{item.title}</span>
                      )}
                    </div>
                    {!isSidebarCollapsed && hasChildren && (
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </motion.div>
                    )}
                  </button>
                ) : (
                  <Link
                    href={item.path}
                    className={cn(
                      'w-full flex items-center justify-between p-2 rounded-md text-left',
                      isItemActive
                        ? 'bg-slate-50 text-[#00646C]'
                        : 'text-slate-700 hover:bg-slate-50',
                      isSidebarCollapsed && 'justify-center',
                    )}
                  >
                    <div
                      className={cn(
                        'flex items-start',
                        isSidebarCollapsed && 'justify-center',
                      )}
                    >
                      {IconComponent && (
                        <IconComponent
                          className={cn(
                            isSidebarCollapsed ? 'h-6 w-6' : 'h-5 w-5',
                            !isSidebarCollapsed && 'mr-2 flex-shrink-0 mt-0.5',
                          )}
                        />
                      )}
                      {!isSidebarCollapsed && (
                        <span className="leading-tight">{item.title}</span>
                      )}
                    </div>
                  </Link>
                )}

                {!isSidebarCollapsed && hasChildren && isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="ml-6 mt-1 border-l border-slate-200 pl-2">
                      {item.submenu?.map((child) => {
                        const ChildIconComponent = child.icon
                          ? (icons as any)[child.icon as any]
                          : undefined;
                        const isChildActive = isPathActive(
                          pathname,
                          child.path,
                        );
                        const hasGrandchildren =
                          child.submenu && child.submenu.length > 0;
                        const isChildExpanded =
                          expandedItems[child.title] ||
                          hasActiveChild(child, pathname);

                        return (
                          <div key={child.title}>
                            {hasGrandchildren ? (
                              <button
                                className={cn(
                                  'w-full flex items-center justify-between p-2 rounded-md text-left',
                                  isChildActive ||
                                    hasActiveChild(child, pathname)
                                    ? 'bg-slate-50 text-[#00646C]'
                                    : 'font-medium text-slate-600 hover:bg-slate-50 hover:text-[#00646C]',
                                )}
                                onClick={() => handleItemClick(child)}
                              >
                                <div className="flex items-start">
                                  {ChildIconComponent && (
                                    <ChildIconComponent
                                      className={cn(
                                        isSidebarCollapsed
                                          ? 'h-5 w-5'
                                          : 'h-4 w-4',
                                        'mr-2 flex-shrink-0 mt-0.5',
                                      )}
                                    />
                                  )}
                                  <span className="leading-tight">
                                    {child.title}
                                  </span>
                                </div>
                                {hasGrandchildren && (
                                  <motion.div
                                    animate={{
                                      rotate: isChildExpanded ? 180 : 0,
                                    }}
                                    transition={{ duration: 0.2 }}
                                  >
                                    {isChildExpanded ? (
                                      <ChevronDown className="h-3 w-3" />
                                    ) : (
                                      <ChevronRight className="h-3 w-3" />
                                    )}
                                  </motion.div>
                                )}
                              </button>
                            ) : (
                              <Link
                                href={child.path}
                                className={cn(
                                  'w-full flex items-center justify-between p-2 rounded-md text-left',
                                  isChildActive
                                    ? 'bg-slate-50 text-[#00646C]'
                                    : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]',
                                )}
                              >
                                <div className="flex items-start">
                                  {ChildIconComponent && (
                                    <ChildIconComponent
                                      className={cn(
                                        isSidebarCollapsed
                                          ? 'h-5 w-5'
                                          : 'h-4 w-4',
                                        'mr-2 flex-shrink-0 mt-0.5',
                                      )}
                                    />
                                  )}
                                  <span className="leading-tight">
                                    {child.title}
                                  </span>
                                </div>
                              </Link>
                            )}

                            {hasGrandchildren && isChildExpanded && (
                              <motion.div
                                initial={{ opacity: 0, y: -5 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.2 }}
                              >
                                <div className="ml-4 mt-1 border-l border-slate-200 pl-2">
                                  {child.submenu?.map((grandchild) => {
                                    const GrandchildIconComponent =
                                      grandchild.icon
                                        ? (icons as any)[grandchild.icon as any]
                                        : undefined;
                                    const isGrandchildActive = isPathActive(
                                      pathname,
                                      grandchild.path,
                                    );

                                    return (
                                      <Link
                                        key={grandchild.title}
                                        href={grandchild.path}
                                        className={cn(
                                          'w-full flex items-center p-2 rounded-md text-left',
                                          isGrandchildActive
                                            ? 'bg-slate-50 text-[#00646C]'
                                            : 'text-slate-500 hover:bg-slate-50 hover:text-[#00646C]',
                                        )}
                                      >
                                        <div className="flex items-start">
                                          {GrandchildIconComponent && (
                                            <GrandchildIconComponent
                                              className={cn(
                                                isSidebarCollapsed
                                                  ? 'h-4 w-4'
                                                  : 'h-3 w-3',
                                                'mr-2 flex-shrink-0 mt-0.5',
                                              )}
                                            />
                                          )}
                                          <span className="leading-tight">
                                            {grandchild.title}
                                          </span>
                                        </div>
                                      </Link>
                                    );
                                  })}
                                </div>
                              </motion.div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </motion.div>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
}
