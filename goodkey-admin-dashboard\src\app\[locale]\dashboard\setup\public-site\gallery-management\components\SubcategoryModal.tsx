'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { GalleryCategory } from '@/models/Gallery';
import Field from '@/components/ui/inputs/field';
import { Form } from '@/components/ui/form';

const schema = z.object({
  categoryId: z
    .string({ required_error: 'Category is required' })
    .min(1, 'Category is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  displayOrder: z.coerce.number().min(1, 'Display order is required'),
});

type SubcategoryFormValues = z.infer<typeof schema>;

interface SubcategoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: SubcategoryFormValues) => void;
  initialData?: Partial<SubcategoryFormValues>;
  categories: GalleryCategory[];
  loading?: boolean;
}

export default function SubcategoryModal({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  categories,
  loading,
}: SubcategoryModalProps) {
  const form = useForm<SubcategoryFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      categoryId: categories[0] ? String(categories[0].id) : '',
      name: '',
      description: '',
      displayOrder: 1,
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        ...initialData,
        categoryId: initialData.categoryId
          ? String(initialData.categoryId)
          : categories[0]
            ? String(categories[0].id)
            : '',
      });
    }
  }, [initialData, form, categories]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {initialData ? 'Edit Subcategory' : 'Add Subcategory'}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <Field
              type={{
                type: 'select',
                props: {
                  options: categories.map((cat) => ({
                    label: cat.name,
                    value: cat.id.toString(),
                  })),
                  placeholder: 'Select category',
                },
              }}
              control={form.control}
              name="categoryId"
              label="Category"
              required
              disabled={loading}
            />

            <Field
              type="text"
              control={form.control}
              name="name"
              label="Name"
              required
              disabled={loading}
            />

            <Field
              type="textarea"
              control={form.control}
              name="description"
              label="Description"
              disabled={loading}
            />
            <Field
              type="number"
              control={form.control}
              name="displayOrder"
              label="Display Order"
              required
              disabled={loading}
            />
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
                iconName="CancelIcon"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="main"
                disabled={loading}
                iconName={'SaveIcon'}
                iconProps={{
                  className: 'text-white',
                }}
              >
                {initialData ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
