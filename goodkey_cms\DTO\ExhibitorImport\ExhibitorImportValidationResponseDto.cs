namespace goodkey_cms.DTO.ExhibitorImport
{
    public class ExhibitorImportValidationResponseDto
    {
        public string SessionId { get; set; }
        public int ShowId { get; set; }
        public string FileName { get; set; }
        public ExhibitorImportSummaryDto Summary { get; set; }
        public List<ExhibitorImportRowDto> Rows { get; set; }
        public List<ExhibitorImportValidationMessageDto> ValidationMessages { get; set; }
        public List<ExhibitorImportDuplicateDto> Duplicates { get; set; }
        public bool CanProceed { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class ExhibitorImportSummaryDto
    {
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public int WarningRows { get; set; }
        public int UnresolvedDuplicates { get; set; }
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public bool HasDuplicates { get; set; }
    }

    public class ExhibitorImportRowDto
    {
        public int RowNumber { get; set; }
        public string Status { get; set; }
        
        // Company Data
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress1 { get; set; }
        public string CompanyAddress2 { get; set; }
        public string CompanyCity { get; set; }
        public string CompanyProvince { get; set; }
        public string CompanyPostalCode { get; set; }
        public string CompanyCountry { get; set; }
        public string CompanyWebsite { get; set; }
        
        // Contact Data
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string ContactType { get; set; }
        
        // Booth Data
        public string BoothNumbers { get; set; }
        
        // Validation Status
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
    }

    public class ExhibitorImportValidationMessageDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
        public string MessageType { get; set; } // Error, Warning, Info
        public string ValidationRule { get; set; }
        public string MessageCode { get; set; }
        public string Message { get; set; }
    }

    public class ExhibitorImportDuplicateDto
    {
        public int DuplicateId { get; set; }
        public string DuplicateType { get; set; } // EmailDuplicate, DatabaseConflict
        public string DuplicateValue { get; set; }
        public List<int> RowNumbers { get; set; }
        public string ConflictDescription { get; set; }
        public bool RequiresUserDecision { get; set; }
        public int? ExistingRecordId { get; set; }
        public string ExistingRecordType { get; set; } // Contact, Company
        public List<ExhibitorImportFieldConflictDto> FieldConflicts { get; set; }
    }

    public class ExhibitorImportFieldConflictDto
    {
        public string FieldName { get; set; }
        public string ExcelValue { get; set; }
        public string DatabaseValue { get; set; }
        public bool HasConflict { get; set; }
    }
}
