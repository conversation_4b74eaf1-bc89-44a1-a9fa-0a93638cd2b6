"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/dashboard/layout",{

/***/ "(app-pages-browser)/./src/services/queries/MenuQuery.ts":
/*!*******************************************!*\
  !*** ./src/services/queries/MenuQuery.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _fetcher__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fetcher */ \"(app-pages-browser)/./src/services/queries/fetcher.ts\");\n/* harmony import */ var _utils_file_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/file-helper */ \"(app-pages-browser)/./src/utils/file-helper.ts\");\n\n\nconst MenuQuery = {\n    tags: [\n        'menu-item'\n    ],\n    getAll: async ()=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items\"),\n    getBrief: async ()=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('MenuItem'),\n    getSections: async ()=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu\"),\n    getBySection: async (sectionName)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/ItemsBySection/\".concat(sectionName)),\n    getConfig: async (menuId)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/config/\".concat(menuId)),\n    get: async (id)=>{\n        const data = await (0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items/\".concat(id));\n        if (data.imagePath) {\n            const file = await (0,_utils_file_helper__WEBPACK_IMPORTED_MODULE_1__.urlToFile)('/images' + data.imagePath);\n            return {\n                ...data,\n                image: file ? [\n                    file\n                ] : []\n            };\n        } else return data;\n    },\n    update: (id)=>async (data)=>{\n            var _data_image, _data_menuIds;\n            const formData = new FormData();\n            if ((_data_image = data.image) === null || _data_image === void 0 ? void 0 : _data_image.length) formData.append('image', data.image[0]);\n            formData.append('name', data.name);\n            if (data.description && data.description.length > 0) formData.append('description', data.description);\n            if (data.metaDescription && data.metaDescription.length > 0) formData.append('metaDescription', data.metaDescription);\n            if (data.keywords && data.keywords.length > 0) formData.append('keywords', data.keywords);\n            if (data.url) formData.append('url', data.url);\n            formData.append('displayOrder', data.displayOrder.toString());\n            if (data.permissionKey) formData.append('permissionKey', data.permissionKey);\n            if (data.iconName) formData.append('iconName', data.iconName);\n            if (data.target) formData.append('target', data.target);\n            if (data.parentId) formData.append('parentId', data.parentId);\n            if (data.sectionId) formData.append('sectionId', data.sectionId);\n            if (data.roleId) formData.append('roleId', data.roleId);\n            if (data.level) formData.append('level', data.level);\n            if (data.isStatic) formData.append('isStatic', data.isStatic.toString());\n            if (data.isVisible) formData.append('isVisible', data.isVisible.toString());\n            if (data.direction) formData.append('direction', data.direction.toString());\n            if (data.isParent) formData.append('isParent', data.isParent.toString());\n            if (data.isDashboard) formData.append('isDashboard', data.isDashboard.toString());\n            (_data_menuIds = data.menuIds) === null || _data_menuIds === void 0 ? void 0 : _data_menuIds.forEach((i)=>formData.append('menuIds', i));\n            return (0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items/\".concat(id), {\n                method: 'PATCH',\n                body: formData\n            }, true);\n        },\n    create: async (data)=>{\n        var _data_image, _data_menuIds;\n        const formData = new FormData();\n        if ((_data_image = data.image) === null || _data_image === void 0 ? void 0 : _data_image.length) formData.append('image', data.image[0]);\n        formData.append('name', data.name);\n        if (data.description && data.description.length > 0) formData.append('description', data.description);\n        if (data.metaDescription && data.metaDescription.length > 0) formData.append('metaDescription', data.metaDescription);\n        if (data.keywords && data.keywords.length > 0) formData.append('keywords', data.keywords);\n        if (data.url) formData.append('url', data.url);\n        formData.append('displayOrder', data.displayOrder.toString());\n        if (data.permissionKey) formData.append('permissionKey', data.permissionKey);\n        if (data.iconName) formData.append('iconName', data.iconName);\n        if (data.target) formData.append('target', data.target);\n        if (data.parentId) formData.append('parentId', data.parentId);\n        if (data.sectionId) formData.append('sectionId', data.sectionId);\n        if (data.roleId) formData.append('roleId', data.roleId);\n        if (data.level) formData.append('level', data.level);\n        if (data.isStatic) formData.append('isStatic', data.isStatic.toString());\n        if (data.isVisible) formData.append('isVisible', data.isVisible.toString());\n        if (data.direction) formData.append('direction', data.direction.toString());\n        if (data.isParent) formData.append('isParent', data.isParent.toString());\n        if (data.isDashboard) formData.append('isDashboard', data.isDashboard.toString());\n        (_data_menuIds = data.menuIds) === null || _data_menuIds === void 0 ? void 0 : _data_menuIds.forEach((i)=>formData.append('menuIds', i));\n        return (0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items\", {\n            method: 'POST',\n            body: formData\n        }, true);\n    },\n    switchArchive: (id)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items/\".concat(id, \"/SwitchArchive\"), {\n            method: 'PATCH'\n        }),\n    switchPublic: (id)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items/\".concat(id, \"/switchPublic\"), {\n            method: 'PATCH'\n        }),\n    getStatus: (id)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/items/\".concat(id, \"/status\"), {\n            method: 'GET'\n        }),\n    MoveUp: (id)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/MoveUp/\".concat(id), {\n            method: 'PATCH'\n        }),\n    MoveDown: (id)=>(0,_fetcher__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"menu/MoveDown/\".concat(id), {\n            method: 'PATCH'\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MenuQuery);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/queries/MenuQuery.ts\n"));

/***/ })

});