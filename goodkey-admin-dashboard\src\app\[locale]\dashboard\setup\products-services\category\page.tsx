import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import CategoryQuery from '@/services/queries/CategoryQuery';
import CategoryTable from './components/category_table';

export const metadata: Metadata = {
  title: 'Goodkey | Category',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: CategoryQuery.tags,
    queryFn: () => CategoryQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Category',
          link: '/dashboard/setup/products-services/category',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <CategoryTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
