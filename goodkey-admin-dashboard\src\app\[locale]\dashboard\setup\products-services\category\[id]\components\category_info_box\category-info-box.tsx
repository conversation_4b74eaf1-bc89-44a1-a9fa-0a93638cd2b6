'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import CategoryQuery from '@/services/queries/CategoryQuery';
import { CategoryData } from '@/schema/CategorySchema';
import { CheckCircle, XCircle } from 'lucide-react';
interface ICategoryInfoBox {
  id?: number;
}

function CategoryInfoBox({ id }: ICategoryInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Category', { id }],
    queryFn: () => CategoryQuery.getById(id!),
    enabled: !!id,
    select: (res): CategoryData => ({
      name: res.name ?? '',
      code: res.code ?? '',
      groupId: res.groupId?.toString() ?? '',
      displayOrder: res.displayOrder?.toString() ?? '',
      isAvailable: res.isAvailable ?? false,
      isInternalProduct: res.isInternalProduct ?? false,
      isSoldByQ: res.isSoldByQ ?? false,
      imagePath: res.imagePath ?? '',
      image: res.image,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <div className="category-info-box">
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
            {data ? data.name : 'Setup a new category'}
            {data && (
              <span
                className={`inline-flex items-center gap-1 text-xs font-medium px-2 py-0.5 rounded-full`}
              >
                {data.isAvailable ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 text-red-600" />
                  </>
                )}
              </span>
            )}
          </h1>
          <p className="text-slate-600 text-sm">
            {data ? (
              'Edit the details below to update the category.'
            ) : (
              <span>
                Please fill out the form below to add a new category. Required
                fields are marked with a{' '}
                <span className="text-red-500 font-semibold">*</span>.
              </span>
            )}
          </p>
        </div>
      </div>
    </Suspense>
  );
}

export default CategoryInfoBox;
