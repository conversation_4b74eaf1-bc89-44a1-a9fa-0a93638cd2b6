import { ReactNode } from 'react';

import IntlProvider from './providers/intl-provider';
import QueryProvider from './providers/query-provider';
import ThemeProvider from './providers/theme-provider';
import { TooltipProvider } from '../ui/tooltip';

interface ProviderProps {
  children: ReactNode;
}
export function Provider({ children }: ProviderProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem
      disableTransitionOnChange
    >
      <IntlProvider>
        <TooltipProvider>
          <QueryProvider> {children}</QueryProvider>
        </TooltipProvider>
      </IntlProvider>
    </ThemeProvider>
  );
}
