"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_json5_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/json5.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/json5.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON5\\\",\\\"fileTypes\\\":[\\\"json5\\\"],\\\"name\\\":\\\"json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json5\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json5\\\"}},\\\"name\\\":\\\"meta.structure.array.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json5\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json5\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"/{2}.*\\\",\\\"name\\\":\\\"comment.single.json5\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json5\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json5\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json5\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json5\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null|Infinity|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json5\\\"},\\\"infinity\\\":{\\\"match\\\":\\\"(-)*\\\\\\\\b(?:Infinity|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json5\\\"},\\\"key\\\":{\\\"name\\\":\\\"string.key.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringSingle\\\"},{\\\"include\\\":\\\"#stringDouble\\\"},{\\\"match\\\":\\\"[a-zA-Z0-9_-]\\\",\\\"name\\\":\\\"string.key.json5\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"handles hexadecimal numbers\\\",\\\"match\\\":\\\"(0x)[0-9a-fA-f]*\\\",\\\"name\\\":\\\"constant.hex.numeric.json5\\\"},{\\\"comment\\\":\\\"handles integer and decimal numbers\\\",\\\"match\\\":\\\"[+-.]?(?=[1-9]|0(?!\\\\\\\\d))\\\\\\\\d+(\\\\\\\\.\\\\\\\\d+)?([eE][+-]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.dec.numeric.json5\\\"}]},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json5\\\"}},\\\"comment\\\":\\\"a json5 object\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json5\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json5\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"comment\\\":\\\"the json5 object key\\\",\\\"include\\\":\\\"#key\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json5\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json5\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json5\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"the json5 object value\\\",\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json5\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json5\\\"}]},\\\"stringDouble\\\":{\\\"begin\\\":\\\"[\\\\\\\"]\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json5\\\"}},\\\"end\\\":\\\"[\\\\\\\"]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json5\\\"}},\\\"name\\\":\\\"string.quoted.json5\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?:\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4}))\\\",\\\"name\\\":\\\"constant.character.escape.json5\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json5\\\"}]},\\\"stringSingle\\\":{\\\"begin\\\":\\\"[']\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json5\\\"}},\\\"end\\\":\\\"[']\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json5\\\"}},\\\"name\\\":\\\"string.quoted.json5\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?:\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4}))\\\",\\\"name\\\":\\\"constant.character.escape.json5\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json5\\\"}]},\\\"value\\\":{\\\"comment\\\":\\\"the 'value' diagram at http://json.org\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#infinity\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#stringSingle\\\"},{\\\"include\\\":\\\"#stringDouble\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"}]}},\\\"scopeName\\\":\\\"source.json5\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/json5.mjs\n"));

/***/ })

}]);