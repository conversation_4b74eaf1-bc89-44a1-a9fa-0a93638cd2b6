"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_docker_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/docker.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/docker.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Dockerfile\\\",\\\"name\\\":\\\"docker\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*\\\\\\\\b(?i:(FROM))\\\\\\\\b.*?\\\\\\\\b(?i:(AS))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\\\\\\\s\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(CMD|ENTRYPOINT))\\\\\\\\s\\\"},{\\\"include\\\":\\\"#string-character-escape\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.double.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.single.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.number-sign.dockerfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.dockerfile\\\"}},\\\"comment\\\":\\\"comment.line\\\",\\\"match\\\":\\\"^(\\\\\\\\s*)((#).*$\\\\\\\\n?)\\\"}],\\\"repository\\\":{\\\"string-character-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escaped.dockerfile\\\"}},\\\"scopeName\\\":\\\"source.dockerfile\\\",\\\"aliases\\\":[\\\"dockerfile\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/docker.mjs\n"));

/***/ })

}]);