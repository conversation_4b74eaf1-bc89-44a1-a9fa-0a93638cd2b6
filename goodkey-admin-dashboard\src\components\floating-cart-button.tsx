'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ShoppingCart } from 'lucide-react';
import { Badge } from './ui/Badge';

interface FloatingCartButtonProps {
  eventId: string;
  itemCount?: number;
}

export default function FloatingCartButton({
  eventId,
  itemCount = 0,
}: FloatingCartButtonProps) {
  const router = useRouter();

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        className="h-14 w-14 rounded-full bg-[#00646C] hover:bg-[#00646C]/90 shadow-lg"
        onClick={() => router.push(`/event/${eventId}/cart`)}
      >
        <div className="relative">
          <ShoppingCart className="h-6 w-6" />
          {itemCount > 0 && (
            <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-red-500">
              {itemCount}
            </Badge>
          )}
        </div>
      </Button>
    </div>
  );
}
