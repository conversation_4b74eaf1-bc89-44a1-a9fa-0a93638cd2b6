import { redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import Logo from '@/assets/logo';
import AuthQuery from '@/services/queries/AuthQuery';
import { getQueryClient } from '@/utils/query-client';

import PasswordResetForm from './component/Password-Reset-form';

export default async function Page(props: {
  params: Promise<{ locale: string }>;
  searchParams?: Promise<{ [key: string]: string | undefined }>;
}) {
  const { params, searchParams } = props;
  // Await the params and searchParams
  const resolvedParams = await params;
  const resolvedSearchParams = searchParams ? await searchParams : undefined;

  const key = resolvedSearchParams?.key;
  if (!key) {
    redirect('login');
  }

  let user;
  try {
    user = await getQueryClient().fetchQuery({
      queryFn: () => AuthQuery.getUserToVerify(key),
      queryKey: [AuthQuery.tags.me],
    });
  } catch {
    redirect('login');
  }
  const t = await getTranslations('contact');

  return (
    <div className="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        <Logo />
        <h2 className="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-primary-900">
          {t('resetPassword')}
        </h2>
      </div>

      <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <PasswordResetForm user={user} token={key} />
      </div>
    </div>
  );
}
