"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_cadence_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/cadence.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/cadence.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Cadence\\\",\\\"name\\\":\\\"cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expressions\\\"},{\\\"include\\\":\\\"#declarations\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#code-block\\\"},{\\\"include\\\":\\\"#composite\\\"},{\\\"include\\\":\\\"#event\\\"}],\\\"repository\\\":{\\\"code-block\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.cadence\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.cadence\\\"}},\\\"match\\\":\\\"\\\\\\\\A^(#!).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.cadence\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.cadence\\\"}},\\\"name\\\":\\\"comment.block.documentation.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nested\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.cadence\\\"}},\\\"name\\\":\\\"comment.block.documentation.playground.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nested\\\"}]},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.cadence\\\"}},\\\"name\\\":\\\"comment.block.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nested\\\"}]},{\\\"match\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"invalid.illegal.unexpected-end-of-block-comment.cadence\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.cadence\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"///\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.cadence\\\"}},\\\"end\\\":\\\"^\\\",\\\"name\\\":\\\"comment.line.triple-slash.documentation.cadence\\\"},{\\\"begin\\\":\\\"//:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.cadence\\\"}},\\\"end\\\":\\\"^\\\",\\\"name\\\":\\\"comment.line.double-slash.documentation.cadence\\\"},{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.cadence\\\"}},\\\"end\\\":\\\"^\\\",\\\"name\\\":\\\"comment.line.double-slash.cadence\\\"}]}],\\\"repository\\\":{\\\"nested\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#nested\\\"}]}}},\\\"composite\\\":{\\\"begin\\\":\\\"\\\\\\\\b((?:(?:struct|resource|contract)(?:\\\\\\\\s+interface)?)|transaction|enum)\\\\\\\\s+([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.$1.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.$1.cadence\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.definition.type.composite.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#conformance-clause\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.type.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.type.end.cadence\\\"}},\\\"name\\\":\\\"meta.definition.type.body.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"conformance-clause\\\":{\\\"begin\\\":\\\"(:)(?=\\\\\\\\s*\\\\\\\\{)|(:)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.empty-conformance-clause.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.conformance-clause.cadence\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)$|(?=[={}])\\\",\\\"name\\\":\\\"meta.conformance-clause.cadence\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)$|(?=[={}])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#type\\\"}]}]},\\\"declarations\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#var-let-declaration\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#initializer\\\"}]},\\\"event\\\":{\\\"begin\\\":\\\"\\\\\\\\b(event)\\\\\\\\b\\\\\\\\s+([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.event.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.event.cadence\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\))|$\\\",\\\"name\\\":\\\"meta.definition.type.event.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#parameter-clause\\\"}]},\\\"expression-element-list\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.argument-label.cadence\\\"}},\\\"comment\\\":\\\"an element with a label\\\",\\\"end\\\":\\\"(?=[,)\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expressions\\\"}]},{\\\"begin\\\":\\\"(?![,)\\\\\\\\]])(?=\\\\\\\\S)\\\",\\\"comment\\\":\\\"an element without a label (i.e. anything else)\\\",\\\"end\\\":\\\"(?=[,)\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expressions\\\"}]}]},\\\"expressions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#function-call-expression\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#language-variables\\\"}]},\\\"function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(fun)\\\\\\\\b\\\\\\\\s+([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.cadence\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|$\\\",\\\"name\\\":\\\"meta.definition.function.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#parameter-clause\\\"},{\\\"include\\\":\\\"#function-result\\\"},{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.cadence\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.end.cadence\\\"}},\\\"name\\\":\\\"meta.definition.function.body.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"function-call-expression\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!(?:set|init))([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.cadence\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.cadence\\\"}},\\\"comment\\\":\\\"foo(args) -- a call whose callee is a highlightable name\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.cadence\\\"}},\\\"name\\\":\\\"meta.function-call.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-element-list\\\"}]}]},\\\"function-result\\\":{\\\"begin\\\":\\\"(?<![/=\\\\\\\\-+!*%<>&|\\\\\\\\^~.])(:)(?![/=\\\\\\\\-+!*%<>&|\\\\\\\\^~.])\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.function-result.cadence\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)(?=\\\\\\\\{|;)|$\\\",\\\"name\\\":\\\"meta.function-result.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]},\\\"initializer\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(init)\\\\\\\\s*(?=\\\\\\\\(|<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.cadence\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|$\\\",\\\"name\\\":\\\"meta.definition.function.initializer.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#parameter-clause\\\"},{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.cadence\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.end.cadence\\\"}},\\\"name\\\":\\\"meta.definition.function.body.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:if|else|switch|case|default)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.branch.cadence\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:return|continue|break)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.transfer.cadence\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:while|for|in)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.loop.cadence\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:pre|post|prepare|execute|create|destroy|emit)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.cadence\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:private|pub(?:\\\\\\\\(set\\\\\\\\))?|access\\\\\\\\((?:self|contract|account|all)\\\\\\\\))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.declaration-specifier.accessibility.cadence\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:init|destroy)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.function.cadence\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(?:import|from)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.cadence\\\"}]},\\\"language-variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(self)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.cadence\\\"}]},\\\"literals\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#boolean\\\"},{\\\"include\\\":\\\"#numeric\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"match\\\":\\\"\\\\\\\\bnil\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.nil.cadence\\\"}],\\\"repository\\\":{\\\"boolean\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.cadence\\\"},\\\"numeric\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#binary\\\"},{\\\"include\\\":\\\"#octal\\\"},{\\\"include\\\":\\\"#decimal\\\"},{\\\"include\\\":\\\"#hexadecimal\\\"}],\\\"repository\\\":{\\\"binary\\\":{\\\"comment\\\":\\\"\\\",\\\"match\\\":\\\"(\\\\\\\\B\\\\\\\\-|\\\\\\\\b)0b[01]([_01]*[01])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.binary.cadence\\\"},\\\"decimal\\\":{\\\"comment\\\":\\\"\\\",\\\"match\\\":\\\"(\\\\\\\\B\\\\\\\\-|\\\\\\\\b)[0-9]([_0-9]*[0-9])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.decimal.cadence\\\"},\\\"hexadecimal\\\":{\\\"comment\\\":\\\"\\\",\\\"match\\\":\\\"(\\\\\\\\B\\\\\\\\-|\\\\\\\\b)0x[0-9A-Fa-f]([_0-9A-Fa-f]*[0-9A-Fa-f])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.cadence\\\"},\\\"octal\\\":{\\\"comment\\\":\\\"\\\",\\\"match\\\":\\\"(\\\\\\\\B\\\\\\\\-|\\\\\\\\b)0o[0-7]([_0-7]*[0-7])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.octal.cadence\\\"}}},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.cadence\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.cadence\\\"}},\\\"name\\\":\\\"string.quoted.double.single-line.cadence\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\r|\\\\\\\\n\\\",\\\"name\\\":\\\"invalid.illegal.returns-not-allowed.cadence\\\"},{\\\"include\\\":\\\"#string-guts\\\"}]}],\\\"repository\\\":{\\\"string-guts\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[0\\\\\\\\\\\\\\\\tnr\\\\\\\"']\\\",\\\"name\\\":\\\"constant.character.escape.cadence\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u\\\\\\\\{[0-9a-fA-F]{1,8}\\\\\\\\}\\\",\\\"name\\\":\\\"constant.character.escape.unicode.cadence\\\"}]}}}}},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\-\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.unary.cadence\\\"},{\\\"match\\\":\\\"!\\\",\\\"name\\\":\\\"keyword.operator.logical.not.cadence\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.cadence\\\"},{\\\"match\\\":\\\"<-\\\",\\\"name\\\":\\\"keyword.operator.move.cadence\\\"},{\\\"match\\\":\\\"<-!\\\",\\\"name\\\":\\\"keyword.operator.force-move.cadence\\\"},{\\\"match\\\":\\\"\\\\\\\\+|\\\\\\\\-|\\\\\\\\*|/\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.cadence\\\"},{\\\"match\\\":\\\"%\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.remainder.cadence\\\"},{\\\"match\\\":\\\"==|!=|>|<|>=|<=\\\",\\\"name\\\":\\\"keyword.operator.comparison.cadence\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.coalescing.cadence\\\"},{\\\"match\\\":\\\"&&|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.cadence\\\"},{\\\"match\\\":\\\"[?!]\\\",\\\"name\\\":\\\"keyword.operator.type.optional.cadence\\\"}]},\\\"parameter-clause\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.cadence\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.cadence\\\"}},\\\"name\\\":\\\"meta.parameter-clause.cadence\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parameter-list\\\"}]},\\\"parameter-list\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.cadence\\\"}},\\\"comment\\\":\\\"External parameter labels are considered part of the function name\\\",\\\"match\\\":\\\"([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\\\\\\s+([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)(?=\\\\\\\\s*:)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.cadence\\\"}},\\\"comment\\\":\\\"If no external label is given, the name is both the external label and the internal variable name\\\",\\\"match\\\":\\\"(([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*))(?=\\\\\\\\s*:)\\\"},{\\\"begin\\\":\\\":\\\\\\\\s*(?!\\\\\\\\s)\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"invalid.illegal.extra-colon-in-parameter-list.cadence\\\"}]}]},\\\"type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\",\\\"name\\\":\\\"storage.type.cadence\\\"}]},\\\"var-let-declaration\\\":{\\\"begin\\\":\\\"\\\\\\\\b(var|let)\\\\\\\\b\\\\\\\\s+([\\\\\\\\p{L}_][\\\\\\\\p{L}_\\\\\\\\p{N}\\\\\\\\p{M}]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.$1.cadence\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.$1.cadence\\\"}},\\\"end\\\":\\\"=|<-|<-!|$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type\\\"}]}},\\\"scopeName\\\":\\\"source.cadence\\\",\\\"aliases\\\":[\\\"cdc\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/cadence.mjs\n"));

/***/ })

}]);