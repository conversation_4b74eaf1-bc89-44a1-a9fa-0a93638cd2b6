"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/dashboard/event/[id]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/event-sidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/event-sidebar.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSidebar: () => (/* binding */ EventSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square-quote.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ EventSidebar auto */ \nvar _s = $RefreshSig$();\n\n\nfunction EventSidebar(param) {\n    let { eventId, activeItem } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const menuItems = [\n        {\n            name: 'RFQs',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/rfqs\")\n        },\n        {\n            name: 'ORDERS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/orders\")\n        },\n        {\n            name: 'ORDER FORMS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/order-forms\")\n        },\n        {\n            name: 'SHOW PACKAGES',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/show-packages\")\n        },\n        {\n            name: 'EXHIBITORS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/exhibitors\")\n        },\n        {\n            name: 'LISTS & REPORTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/reports\")\n        },\n        {\n            name: 'SHOW MANAGEMENT',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId)\n        },\n        {\n            name: 'GRAPHICS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/graphics\")\n        },\n        {\n            name: 'PAYMENTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/payments\")\n        }\n    ];\n    const handleItemClick = (url)=>{\n        router.push(url);\n    };\n    const isActive = (name)=>{\n        return name === activeItem;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full md:w-64 bg-white rounded-md border border-slate-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full flex items-center p-2 rounded-md text-left \".concat(isActive(item.name) ? 'bg-slate-50 text-[#00646C]' : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'),\n                        onClick: ()=>handleItemClick(item.url),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this)\n                }, item.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(EventSidebar, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = EventSidebar;\nvar _c;\n$RefreshReg$(_c, \"EventSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/event-sidebar.tsx\n"));

/***/ })

});