'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { CategoryWithOfferingsDto, OfferingDto } from '@/models/Offering';
import OfferingDisplay from '../offering_display';
import { Tooltip, TooltipContent } from '@/components/ui/tooltip';
import { TooltipTrigger } from '@radix-ui/react-tooltip';
import { ImageIcon } from 'lucide-react';
interface GroupDisplayProps {
  data: CategoryWithOfferingsDto;
  groupId: number;
  categoryId: number;
}

const GroupDisplay: React.FC<GroupDisplayProps> = ({
  data,
  groupId,
  categoryId,
}) => {
  return (
    <Accordion
      key={data.categoryId}
      type="single"
      collapsible
      className="space-y-3"
    >
      <AccordionItem value={data.categoryName + data.categoryId.toString()}>
        <AccordionTrigger
          className={`px-3 py-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
        >
          <div className="grid grid-cols-[1fr_80px_100px_80px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
            <div className="flex items-center gap-1.5 cursor-pointer hover:text-main hover:underline">
              <span
                className={`text-md hover:text-main hover:underline font-medium ${data.offerings.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 hover:text-main`}
              >
                {data.categoryName}
              </span>
              {/* {data.offerings.length === 0 && (
                <span className="text-xs text-gray-600 line-through">
                  No products available
                </span>
              )} */}
            </div>
            <div className="text-left -ml-[190px]">
              <span
                className="font-mono font-normal text-sm"
                style={{ color: '#B10055' }}
              >
                {data.code}
              </span>
            </div>
            {/* <div className="flex items-center gap-1">
              <Link
                href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/add`}
              >
                <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                  <FaPlus className="h-3 w-3" />
                </Button>
              </Link>
            </div> */}
            {/* Category Actions Column - Three column grid */}
            <div className="grid grid-cols-[20px_30px_30px] gap-2 items-center -ml-[60px]">
              {/* Column 1: Image Icon */}
              <div className="flex justify-center">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="cursor-pointer">
                      <ImageIcon className="w-4 h-4 text-teal-600 hover:text-teal-700" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="p-2">
                    <div className="w-48 h-32">
                      {/* <ImageWithFallback
                        src={carpetSamplesImage}
                        alt={category.name}
                        className="w-full h-full object-cover rounded"
                      /> */}
                    </div>
                    <p className="text-sm mt-1 text-center">
                      {data.categoryName}{' '}
                      <span style={{ color: '#B10055' }}>{data.code}</span>
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>

              {/* Column 2: Edit Button */}
              <div className="flex justify-center">
                <Link
                  href={`/dashboard/setup/products-services/category/${categoryId}`}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                    title="Edit Category"
                  >
                    <span className="text-sm">Edit</span>
                  </Button>
                </Link>
              </div>

              {/* Column 3: Add Button */}
              <div className="flex justify-center">
                <Link
                  href={`/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/add`}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
                    title="Add Product to Category"
                  >
                    <span className="text-sm">Add</span>
                  </Button>
                </Link>
              </div>
            </div>
            <div className="-ml-[20px]">
              {data.isAvailable ? (
                <span className="text-[rgba(0,91,99,1)] text-sm font-normal">
                  Active
                </span>
              ) : (
                <span className="text-red-600 text-sm font-normal">
                  Discontinued
                </span>
              )}
            </div>
            {/* Category Status Column */}
          </div>
        </AccordionTrigger>
        <AccordionContent className="pl-4 pb-3">
          {data && data.offerings.length > 0 ? (
            data.offerings.map((offering: OfferingDto) => (
              <OfferingDisplay
                key={offering.id}
                id={offering.id}
                name={offering.name}
                groupId={groupId}
                categoryId={data.categoryId}
                options={offering.options}
                code={offering.code}
                isActive={offering.isActive}
              />
            ))
          ) : (
            <div className="ml-3 pl-2 text-sm text-gray-500 italic">
              No products in this category.
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default GroupDisplay;
