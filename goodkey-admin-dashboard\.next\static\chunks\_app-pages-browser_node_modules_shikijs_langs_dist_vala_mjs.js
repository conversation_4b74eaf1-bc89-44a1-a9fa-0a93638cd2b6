"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_vala_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/vala.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/vala.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Vala\\\",\\\"fileTypes\\\":[\\\"vala\\\",\\\"vapi\\\",\\\"gs\\\"],\\\"name\\\":\\\"vala\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#code\\\"}],\\\"repository\\\":{\\\"code\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#variables\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"match\\\":\\\"/\\\\\\\\*\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.empty.vala\\\"},{\\\"include\\\":\\\"text.html.javadoc\\\"},{\\\"include\\\":\\\"#comments-inline\\\"}]},\\\"comments-inline\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.vala\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.double-slash.vala\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.vala\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((//).*$\\\\\\\\n?)\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\\\\\.?[0-9]*)|(\\\\\\\\.[0-9]+))((e|E)(\\\\\\\\+|-)?[0-9]+)?)([LlFfUuDd]|UL|ul)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.constant.vala\\\"}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\w+)(?=\\\\\\\\s*(<[\\\\\\\\s\\\\\\\\w.]+>\\\\\\\\s*)?\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.vala\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w\\\\\\\\.])(as|do|if|in|is|not|or|and|for|get|new|out|ref|set|try|var|base|case|else|enum|lock|null|this|true|void|weak|async|break|catch|class|const|false|owned|throw|using|while|with|yield|delete|extern|inline|params|public|return|sealed|signal|sizeof|static|struct|switch|throws|typeof|unlock|default|dynamic|ensures|finally|foreach|private|unowned|virtual|abstract|continue|delegate|internal|override|requires|volatile|construct|interface|namespace|protected|errordomain)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.vala\\\"},{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w\\\\\\\\.])(bool|double|float|unichar|unichar2|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|string16|string32|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64|va_list|time_t)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.vala\\\"},{\\\"match\\\":\\\"(#if|#elif|#else|#endif)\\\",\\\"name\\\":\\\"keyword.vala\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.triple.vala\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.interpolated.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\\\\\\w+\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\\\\\\(([^)(]|\\\\\\\\(([^)(]|\\\\\\\\([^)]*\\\\\\\\))*\\\\\\\\))*\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.vala\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.vala\\\"}]},{\\\"match\\\":\\\"/((\\\\\\\\\\\\\\\\/)|([^/]))*/(?=\\\\\\\\s*[,;)\\\\\\\\.\\\\\\\\n])\\\",\\\"name\\\":\\\"string.regexp.vala\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^|[^@\\\\\\\\w\\\\\\\\.])(bool|double|float|unichar|unichar2|char|uchar|int|uint|long|ulong|short|ushort|size_t|ssize_t|string|string16|string32|void|signal|int8|int16|int32|int64|uint8|uint16|uint32|uint64|va_list|time_t)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.primitive.vala\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z]+\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.vala\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([_a-z]+\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.vala\\\"}]}},\\\"scopeName\\\":\\\"source.vala\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/vala.mjs\n"));

/***/ })

}]);