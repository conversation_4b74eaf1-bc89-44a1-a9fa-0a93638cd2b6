"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_csv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/csv.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/csv.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CSV\\\",\\\"fileTypes\\\":[\\\"csv\\\"],\\\"name\\\":\\\"csv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"rainbow1\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.rainbow2\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.rainbow3\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.rainbow4\\\"},\\\"5\\\":{\\\"name\\\":\\\"string.rainbow5\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.rainbow6\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.rainbow7\\\"},\\\"8\\\":{\\\"name\\\":\\\"entity.name.type.rainbow8\\\"},\\\"9\\\":{\\\"name\\\":\\\"markup.bold.rainbow9\\\"},\\\"10\\\":{\\\"name\\\":\\\"invalid.rainbow10\\\"}},\\\"match\\\":\\\"((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *\\\\\\\"(?:[^\\\\\\\"]*\\\\\\\"\\\\\\\")*[^\\\\\\\"]*\\\\\\\" *(?:,|$))|(?:[^,]*(?:,|$)))?\\\",\\\"name\\\":\\\"rainbowgroup\\\"}],\\\"scopeName\\\":\\\"text.csv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/csv.mjs\n"));

/***/ })

}]);