'use client';

import {
  CreditCard,
  FileText,
  Package,
  Users,
  BarChart,
  Settings,
  ImageIcon,
  ShoppingCart,
  MessageSquareQuote,
  LucideIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import MenuQuery from '@/services/queries/MenuQuery';
import { MenuItemBrief } from '@/models/MenuItem';

interface EventSidebarProps {
  eventId: string;
  activeItem?: string;
}

// Icon mapping for dynamic menu items
const iconMap: Record<string, LucideIcon> = {
  MessageSquareQuote,
  ShoppingCart,
  FileText,
  Package,
  Users,
  BarChart,
  Settings,
  ImageIcon,
  CreditCard,
};

export function EventSidebar({ eventId, activeItem }: EventSidebarProps) {
  const router = useRouter();

  // Fetch dynamic menu items from API
  const {
    data: menuItems,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [...MenuQuery.tags, 'event_sidebar'],
    queryFn: () => MenuQuery.getBySection('event_sidebar'),
  });

  // Transform API data to component format
  const transformedMenuItems =
    menuItems?.map((item) => ({
      name: item.name || 'Menu Item',
      icon: iconMap[item.icon || ''] || Settings, // Default to Settings icon
      url: item.url?.replace('{eventId}', eventId) || '#',
      displayOrder: item.displayOrder,
      isVisible: item.isVisible,
    })) || [];

  // Fallback to static menu items if API fails or is loading
  const fallbackMenuItems = [
    {
      name: 'RFQs',
      icon: MessageSquareQuote,
      url: `/dashboard/event/${eventId}/rfqs`,
      displayOrder: 1,
      isVisible: true,
    },
    {
      name: 'ORDERS',
      icon: ShoppingCart,
      url: `/dashboard/event/${eventId}/orders`,
      displayOrder: 2,
      isVisible: true,
    },
    {
      name: 'ORDER FORMS',
      icon: FileText,
      url: `/dashboard/event/${eventId}/order-forms`,
      displayOrder: 3,
      isVisible: true,
    },
    {
      name: 'SHOW PACKAGES',
      icon: Package,
      url: `/dashboard/event/${eventId}/show-packages`,
      displayOrder: 4,
      isVisible: true,
    },
    {
      name: 'EXHIBITORS',
      icon: Users,
      url: `/dashboard/event/${eventId}/exhibitors`,
      displayOrder: 5,
      isVisible: true,
    },
    {
      name: 'LISTS & REPORTS',
      icon: BarChart,
      url: `/dashboard/event/${eventId}/reports`,
      displayOrder: 6,
      isVisible: true,
    },
    {
      name: 'SHOW MANAGEMENT',
      icon: Settings,
      url: `/dashboard/event/${eventId}`,
      displayOrder: 7,
      isVisible: true,
    },
    {
      name: 'GRAPHICS',
      icon: ImageIcon,
      url: `/dashboard/event/${eventId}/graphics`,
      displayOrder: 8,
      isVisible: true,
    },
    {
      name: 'PAYMENTS',
      icon: CreditCard,
      url: `/dashboard/event/${eventId}/payments`,
      displayOrder: 9,
      isVisible: true,
    },
  ];

  // Use dynamic menu items if available, otherwise fallback
  const finalMenuItems =
    isLoading || isError || !menuItems?.length
      ? fallbackMenuItems
      : transformedMenuItems.filter((item) => item.isVisible);

  const handleItemClick = (url: string) => {
    router.push(url);
  };

  const isActive = (name: string) => {
    return name === activeItem;
  };

  return (
    <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
      <div className="p-2">
        {finalMenuItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <div key={item.name} className="mb-1">
              <button
                className={`w-full flex items-center p-2 rounded-md text-left ${
                  isActive(item.name)
                    ? 'bg-slate-50 text-[#00646C]'
                    : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'
                }`}
                onClick={() => handleItemClick(item.url)}
              >
                <IconComponent className="h-4 w-4 mr-2" />
                <span>{item.name}</span>
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
