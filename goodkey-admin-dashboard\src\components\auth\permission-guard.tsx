'use client';

import { useQuery } from '@tanstack/react-query';
import React from 'react';

import AuthQuery from '@/services/queries/AuthQuery';
import { Spinner } from '../ui/spinner';
import { PermissionKey } from '@/models/Permission';

interface IPermissionGuardProps {
  requiredPermissions: (PermissionKey | string)[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const PermissionGuard = ({
  requiredPermissions,
  children,
  fallback = null,
}: IPermissionGuardProps) => {
  const {
    data: userData,
    isLoading: isLoadingUser,
    isError: isErrorUser,
  } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
    staleTime: 5 * 60 * 1000,
  });

  if (isLoadingUser) {
    return <Spinner />;
  }

  if (isErrorUser || !userData) {
    return <>{fallback}</>;
  }

  if (userData.isSuper) {
    return <>{children}</>;
  }

  const userPermissions = userData.permissions ?? [];

  const hasAllPermissions = requiredPermissions.every((requiredPerm) =>
    userPermissions.includes(requiredPerm as PermissionKey),
  );

  if (hasAllPermissions) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
};

export default PermissionGuard;
