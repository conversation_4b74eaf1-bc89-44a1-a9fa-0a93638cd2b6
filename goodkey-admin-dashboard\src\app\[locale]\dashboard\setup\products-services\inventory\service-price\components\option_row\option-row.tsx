'use client';
import { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { PropertyOptionsDto, OfferingRateUpsertDto } from '@/models/Offering';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';

interface OptionRowProps {
  option: PropertyOptionsDto;
  offeringId: number;
}

const OptionRow: React.FC<OptionRowProps> = ({ option, offeringId }) => {
  console.log('option', option);

  const [unitPrice, setUnitPrice] = useState<number>(
    option.warehousePriceQuantities &&
      option.warehousePriceQuantities.length > 0
      ? (option.warehousePriceQuantities[0]?.unitPrice ?? 0)
      : 0,
  );

  console.log('unitPrice', unitPrice);

  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (payload: OfferingRateUpsertDto) =>
      OfferingRateQuery.saveOfferingRate(payload),
    onSuccess: () => {
      toast({
        title: 'Saved',
        description: `${option.name} item updated successfully`,
        variant: 'success',
      });
      queryClient.invalidateQueries({
        queryKey: ['Services', { groupType: 2 }],
      });
    },
    onError: (err: any) => {
      toast({
        title: 'Error',
        description: err.message || 'Failed to save',
        variant: 'destructive',
      });
    },
  });

  const handleSave = () => {
    const payload: OfferingRateUpsertDto = {
      offeringId,
      offeringPropertyId: option.id,
      warehouseId: undefined,
      quantity: undefined,
      unitPrice,
      isDiscontinued: undefined,
    };
    mutation.mutate(payload);
  };

  useEffect(() => {
    // Update unit price when the option changes.
    if (
      option.warehousePriceQuantities &&
      option.warehousePriceQuantities.length > 0
    ) {
      setUnitPrice(option.warehousePriceQuantities[0]?.unitPrice ?? 0);
    }
  }, [option.warehousePriceQuantities]); 

  return (
    <div className="py-2 grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 items-center hover:bg-gray-50 w-full">
      <div className="cursor-pointer flex items-center gap-1">
        <span className="text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline pl-2">
          {option.name}
        </span>
      </div>
      <div className="text-left -ml-[260px]"></div>
      <div className="text-left -ml-[190px]">
        <div className="relative w-fit">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
            $
          </span>
          <Input
            type="number"
            placeholder="0.00"
            min="0.00"
            step="0.01"
            value={unitPrice}
            onChange={(e) => setUnitPrice(Number(e.target.value))}
            className="text-right w-28 pl-8 bg-gray-100 border-none selection:bg-primary selection:text-primary-foreground flex h-8 min-w-0 rounded-md border text-base transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:ring-ring/30 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive"
          />
        </div>
      </div>
      <div className="-ml-[80px]"></div>
      <div className="-ml-[20px]">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSave}
          className="h-7 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
          title="Update quantity, unit price, and status for this property"
        >
          <span className="text-sm">Save</span>
        </Button>
      </div>
    </div>
  );
};

export default OptionRow;
