import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import PropertyQuery from '@/services/queries/PropertyQuery';
import PropertyTable from './components/property_table';

export const metadata: Metadata = {
  title: 'Goodkey | Property',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: PropertyQuery.tags,
    queryFn: () => PropertyQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Property',
          link: '/dashboard/setup/master-setup/property',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <PropertyTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
