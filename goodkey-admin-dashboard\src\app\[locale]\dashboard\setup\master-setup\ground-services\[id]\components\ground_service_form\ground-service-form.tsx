'use client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import GroundServiceQuery from '@/services/queries/GroundServiceQuery';
import { GroundServiceCreate } from '@/models/GroundService';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import Field from '@/components/ui/inputs/field';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { Spinner } from '@/components/ui/spinner';
import { GroundServiceCreateSchema } from '@/schema/GroundServiceSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: GroundServiceCreate;
  id?: number;
}) {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const form = useForm<GroundServiceCreate>({
    resolver: zodResolver(GroundServiceCreateSchema),
    defaultValues: defaultValues || {
      name: '',
      description: '',
      conditions: '',
      localCartageAppliable: false,
      flatRate: undefined,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (f: GroundServiceCreate) =>
      id ? GroundServiceQuery.update(id)(f) : GroundServiceQuery.create(f),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ground-services'] });
      toast({
        title: 'Success',
        description: id ? 'Ground service updated' : 'Ground service created',
        variant: 'success',
      });
      push('/dashboard/setup/master-setup/ground-services');
    },
    onError: (e: any) =>
      toast({ title: e.message || 'Failed to save', variant: 'destructive' }),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((formData) => mutate(formData))}
        className="space-y-4"
      >
        <div className="flex flex-col gap-4 overflow-y-auto scrollbar-hide pr-1 pb-1">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required
            type="text"
          />
          <Field
            control={form.control}
            name="description"
            label="Description"
            required
            type="LightRichText"
          />
          <Field
            control={form.control}
            name="conditions"
            label="Conditions"
            required
            type="LightRichText"
          />
          <Field
            control={form.control}
            name="localCartageAppliable"
            label="Local Cartage Appliable"
            type="checkbox"
          />
          <Field
            control={form.control}
            name="flatRate"
            label="Flat Rate"
            required
            type="number"
          />
        </div>
        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              push('/dashboard/setup/master-setup/ground-services')
            }
          >
            Cancel
          </Button>
          <div className="flex justify-end w-full gap-4">
            <Button
              variant="main"
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: 'text-white' }}
              type="submit"
            >
              {isPending ? 'Please wait...' : id ? 'Update' : 'Add'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

export default function GroundServiceForm({
  serviceId,
}: {
  serviceId?: number;
}) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ground-services', serviceId],
    queryFn: () =>
      serviceId ? GroundServiceQuery.getById(serviceId) : undefined,
    enabled: !!serviceId,
  });

  let defaultValues: GroundServiceCreate | undefined = undefined;
  if (serviceId && data) {
    defaultValues = {
      name: data.name,
      description: data.description,
      conditions: data.conditions,
      localCartageAppliable: data.localCartageAppliable,
      flatRate: data.flatRate,
    };
  }

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {isLoading && isPaused ? (
        <Spinner />
      ) : (
        <FormContent defaultValues={defaultValues} id={serviceId} />
      )}
    </Suspense>
  );
}
