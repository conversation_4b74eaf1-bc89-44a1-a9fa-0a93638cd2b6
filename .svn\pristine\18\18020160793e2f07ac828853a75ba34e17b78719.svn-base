using goodkey_common.Models;
using goodkey_common.Context;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowExhibitorRepository
    {
        // Basic CRUD operations
        IEnumerable<ShowExhibitors> GetAll();
        ShowExhibitors? GetById(int id);
        ShowExhibitors CreateShowExhibitor(ShowExhibitors exhibitor);
        ShowExhibitors UpdateShowExhibitor(ShowExhibitors exhibitor);
        bool DeleteShowExhibitor(int id);

        // Show-specific operations
        IEnumerable<ShowExhibitors> GetExhibitorsByShowId(int showId);
        IEnumerable<ShowExhibitors> GetExhibitorsByCompanyId(int companyId);
        ShowExhibitors? GetExhibitorByShowAndCompany(int showId, int companyId);

        // Archive operations
        bool ArchiveShowExhibitor(int id, int archivedById, string? reason = null);
        bool UnarchiveShowExhibitor(int id, int unarchiveById);

        // Search and filter operations
        IEnumerable<ShowExhibitors> SearchExhibitors(int showId, string? companyName = null, 
            string? contactName = null, string? boothNumber = null, bool? isActive = null, 
            bool? isArchived = null, string? searchTerm = null);

        // Booth management
        bool IsBoothNumberAvailable(int showId, string boothNumber, int? excludeExhibitorId = null);
        IEnumerable<string> GetUsedBoothNumbers(int showId);

        // Statistics
        int GetExhibitorCount(int showId, bool? isActive = null, bool? isArchived = null);
        Dictionary<string, int> GetExhibitorStatsByCompany(int showId);
    }

    public class ShowExhibitorRepository : IShowExhibitorRepository
    {
        private readonly GoodkeyContext _context;

        public ShowExhibitorRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<ShowExhibitors> GetAll()
        {
            return _context.ShowExhibitors
                .Include(e => e.Show)
                .Include(e => e.Company)
                .Include(e => e.Contact)
                .Include(e => e.CreatedBy)
                .Include(e => e.UpdatedBy)
                .Include(e => e.ArchivedBy)
                .OrderBy(e => e.Company.CompanyName)
                .ToList();
        }

        public ShowExhibitors? GetById(int id)
        {
            return _context.ShowExhibitors
                .Include(e => e.Show)
                .Include(e => e.Company)
                .Include(e => e.Contact)
                .Include(e => e.CreatedBy)
                .Include(e => e.UpdatedBy)
                .Include(e => e.ArchivedBy)
                .FirstOrDefault(e => e.Id == id);
        }

        public ShowExhibitors CreateShowExhibitor(ShowExhibitors exhibitor)
        {
            exhibitor.CreatedAt = DateTime.Now;
            exhibitor.IsActive = true;
            exhibitor.IsArchived = false;

            _context.ShowExhibitors.Add(exhibitor);
            _context.SaveChanges();
            return exhibitor;
        }

        public ShowExhibitors UpdateShowExhibitor(ShowExhibitors exhibitor)
        {
            exhibitor.UpdatedAt = DateTime.Now;
            _context.ShowExhibitors.Update(exhibitor);
            _context.SaveChanges();
            return exhibitor;
        }

        public bool DeleteShowExhibitor(int id)
        {
            var exhibitor = _context.ShowExhibitors.Find(id);
            if (exhibitor == null)
                return false;

            _context.ShowExhibitors.Remove(exhibitor);
            _context.SaveChanges();
            return true;
        }

        public IEnumerable<ShowExhibitors> GetExhibitorsByShowId(int showId)
        {
            return _context.ShowExhibitors
                .Include(e => e.Company)
                .Include(e => e.Contact)
                .Where(e => e.ShowId == showId)
                .OrderBy(e => e.Company.CompanyName)
                .ToList();
        }

        public IEnumerable<ShowExhibitors> GetExhibitorsByCompanyId(int companyId)
        {
            return _context.ShowExhibitors
                .Include(e => e.Show)
                .Include(e => e.Contact)
                .Where(e => e.CompanyId == companyId)
                .OrderBy(e => e.Show.StartDate)
                .ToList();
        }

        public ShowExhibitors? GetExhibitorByShowAndCompany(int showId, int companyId)
        {
            return _context.ShowExhibitors
                .Include(e => e.Show)
                .Include(e => e.Company)
                .Include(e => e.Contact)
                .FirstOrDefault(e => e.ShowId == showId && e.CompanyId == companyId);
        }

        public bool ArchiveShowExhibitor(int id, int archivedById, string? reason = null)
        {
            var exhibitor = _context.ShowExhibitors.Find(id);
            if (exhibitor == null)
                return false;

            exhibitor.IsArchived = true;
            exhibitor.ArchivedAt = DateTime.Now;
            exhibitor.ArchivedById = archivedById;
            exhibitor.IsActive = false;

            _context.SaveChanges();
            return true;
        }

        public bool UnarchiveShowExhibitor(int id, int unarchiveById)
        {
            var exhibitor = _context.ShowExhibitors.Find(id);
            if (exhibitor == null)
                return false;

            exhibitor.IsArchived = false;
            exhibitor.ArchivedAt = null;
            exhibitor.ArchivedById = null;
            exhibitor.IsActive = true;
            exhibitor.UpdatedAt = DateTime.Now;
            exhibitor.UpdatedById = unarchiveById;

            _context.SaveChanges();
            return true;
        }

        public IEnumerable<ShowExhibitors> SearchExhibitors(int showId, string? companyName = null, 
            string? contactName = null, string? boothNumber = null, bool? isActive = null, 
            bool? isArchived = null, string? searchTerm = null)
        {
            var query = _context.ShowExhibitors
                .Include(e => e.Company)
                .Include(e => e.Contact)
                .Where(e => e.ShowId == showId);

            if (!string.IsNullOrEmpty(companyName))
            {
                query = query.Where(e => e.Company.CompanyName.Contains(companyName));
            }

            if (!string.IsNullOrEmpty(contactName))
            {
                query = query.Where(e => e.Contact != null && 
                    (e.Contact.FirstName + " " + e.Contact.LastName).Contains(contactName));
            }

            if (!string.IsNullOrEmpty(boothNumber))
            {
                query = query.Where(e => e.BoothNumber != null && e.BoothNumber.Contains(boothNumber));
            }

            if (isActive.HasValue)
            {
                query = query.Where(e => e.IsActive == isActive.Value);
            }

            if (isArchived.HasValue)
            {
                query = query.Where(e => e.IsArchived == isArchived.Value);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(e => 
                    e.Company.CompanyName.Contains(searchTerm) ||
                    (e.Contact != null && (e.Contact.FirstName + " " + e.Contact.LastName).Contains(searchTerm)) ||
                    (e.Contact != null && e.Contact.Email.Contains(searchTerm)) ||
                    (e.BoothNumber != null && e.BoothNumber.Any(b => b.Contains(searchTerm))));
            }

            return query.OrderBy(e => e.Company.CompanyName).ToList();
        }

        public bool IsBoothNumberAvailable(int showId, string boothNumber, int? excludeExhibitorId = null)
        {
            var query = _context.ShowExhibitors
                .Where(e => e.ShowId == showId && e.BoothNumber != null && e.BoothNumber.Contains(boothNumber));

            if (excludeExhibitorId.HasValue)
            {
                query = query.Where(e => e.Id != excludeExhibitorId.Value);
            }

            return !query.Any();
        }

        public IEnumerable<string> GetUsedBoothNumbers(int showId)
        {
            return _context.ShowExhibitors
                .Where(e => e.ShowId == showId && e.BoothNumber != null)
                .SelectMany(e => e.BoothNumber)
                .Distinct()
                .OrderBy(b => b)
                .ToList();
        }

        public int GetExhibitorCount(int showId, bool? isActive = null, bool? isArchived = null)
        {
            var query = _context.ShowExhibitors.Where(e => e.ShowId == showId);

            if (isActive.HasValue)
            {
                query = query.Where(e => e.IsActive == isActive.Value);
            }

            if (isArchived.HasValue)
            {
                query = query.Where(e => e.IsArchived == isArchived.Value);
            }

            return query.Count();
        }

        public Dictionary<string, int> GetExhibitorStatsByCompany(int showId)
        {
            return _context.ShowExhibitors
                .Include(e => e.Company)
                .Where(e => e.ShowId == showId)
                .GroupBy(e => e.Company.CompanyName)
                .ToDictionary(g => g.Key, g => g.Count());
        }
    }
}
