"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_berry_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/berry.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/berry.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Berry\\\",\\\"name\\\":\\\"berry\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#controls\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#member\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#operator\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"\\\\\\\\#\\\\\\\\-\\\",\\\"end\\\":\\\"\\\\\\\\-#\\\",\\\"name\\\":\\\"comment.berry\\\",\\\"patterns\\\":[{}]},\\\"comments\\\":{\\\"begin\\\":\\\"\\\\\\\\#\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.berry\\\",\\\"patterns\\\":[{}]},\\\"controls\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(if|elif|else|for|while|do|end|break|continue|return|try|except|raise)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.berry\\\"}]},\\\"function\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z_][a-zA-Z0-9_]*(?=\\\\\\\\s*\\\\\\\\())\\\",\\\"name\\\":\\\"entity.name.function.berry\\\"}]},\\\"identifier\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[_A-Za-z]\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"identifier.berry\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(var|static|def|class|true|false|nil|self|super|import|as|_class)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.berry\\\"}]},\\\"member\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.other.attribute-name.berry\\\"}},\\\"match\\\":\\\"\\\\\\\\.([a-zA-Z_][a-zA-Z0-9_]*)\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"0x[a-fA-F0-9]+|\\\\\\\\d+|(\\\\\\\\d+\\\\\\\\.?|\\\\\\\\.\\\\\\\\d)\\\\\\\\d*([eE][+-]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.numeric.berry\\\"}]},\\\"operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\(|\\\\\\\\)|\\\\\\\\[|\\\\\\\\]|\\\\\\\\.|-|\\\\\\\\!|~|\\\\\\\\*|/|%|\\\\\\\\+|&|\\\\\\\\^|\\\\\\\\||<|>|=|:\\\",\\\"name\\\":\\\"keyword.operator.berry\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\"|')\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.double.berry\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\x[\\\\\\\\h]{2})|(\\\\\\\\\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)|(\\\\\\\\\\\\\\\\\\\\\\\")|(\\\\\\\\\\\\\\\\')|(\\\\\\\\\\\\\\\\a)|(\\\\\\\\\\\\\\\\b)|(\\\\\\\\\\\\\\\\f)|(\\\\\\\\\\\\\\\\n)|(\\\\\\\\\\\\\\\\r)|(\\\\\\\\\\\\\\\\t)|(\\\\\\\\\\\\\\\\v)\\\",\\\"name\\\":\\\"constant.character.escape.berry\\\"}]},{\\\"begin\\\":\\\"f(\\\\\\\"|')\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.quoted.other.berry\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\x[\\\\\\\\h]{2})|(\\\\\\\\\\\\\\\\[0-7]{3})|(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)|(\\\\\\\\\\\\\\\\\\\\\\\")|(\\\\\\\\\\\\\\\\')|(\\\\\\\\\\\\\\\\a)|(\\\\\\\\\\\\\\\\b)|(\\\\\\\\\\\\\\\\f)|(\\\\\\\\\\\\\\\\n)|(\\\\\\\\\\\\\\\\r)|(\\\\\\\\\\\\\\\\t)|(\\\\\\\\\\\\\\\\v)\\\",\\\"name\\\":\\\"constant.character.escape.berry\\\"},{\\\"match\\\":\\\"\\\\\\\\{\\\\\\\\{[^\\\\\\\\}]*\\\\\\\\}\\\\\\\\}\\\",\\\"name\\\":\\\"string.quoted.other.berry\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"keyword.other.unit.berry\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#identifier\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#member\\\"},{\\\"include\\\":\\\"#function\\\"}]}]}]}},\\\"scopeName\\\":\\\"source.berry\\\",\\\"aliases\\\":[\\\"be\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/berry.mjs\n"));

/***/ })

}]);