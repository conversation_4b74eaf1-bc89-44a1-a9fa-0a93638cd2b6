{"c": ["app/layout", "app/[locale]/dashboard/event/[id]/exhibitors/import/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ComprehensiveDataFixingStep.tsx", "(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/DuplicateResolutionStep.tsx", "(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ValidationStep.tsx"]}