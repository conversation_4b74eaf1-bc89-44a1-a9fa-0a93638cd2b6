namespace goodkey_common.DTO
{
    // DTO classes to replace database models
    public class ValidationMessageDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string FieldValue { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty; // "Error", "Warning"
        public string ValidationRule { get; set; } = string.Empty;
        public string MessageCode { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class AppliedFixDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string OriginalValue { get; set; } = string.Empty;
        public string FixedValue { get; set; } = string.Empty;
        public string FixType { get; set; } = string.Empty;
        public bool SuggestionUsed { get; set; }
        public string AppliedByUsername { get; set; } = string.Empty;
        public DateTime AppliedAt { get; set; }
    }

    public class FixSuggestionDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string SuggestionType { get; set; } = string.Empty; // "AutoFix", "ExistingMatch", "CreateNew", "Manual", "Skip"
        public string? SuggestedValue { get; set; }
        public string? SuggestedDisplayValue { get; set; }
        public string Description { get; set; } = string.Empty;
        public float Confidence { get; set; } // 0.0 to 1.0
        public string ActionType { get; set; } = string.Empty; // "ReplaceValue", "SelectExisting", "CreateNew", "SkipRow", "ManualInput"
        public object? Metadata { get; set; } // Additional data for the suggestion
    }


    public class ValidationErrorWithSuggestionsDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string FieldValue { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public List<FixSuggestionDto> Suggestions { get; set; } = new List<FixSuggestionDto>();
    }


    public class RowValidationWithSuggestionsDto
    {
        public int RowId { get; set; }
        public int RowNumber { get; set; }
        public string Status { get; set; } = string.Empty; // "Valid", "Error", "Warning", "Fixed", "Skipped"
        public object? OriginalData { get; set; }
        public List<ValidationErrorWithSuggestionsDto> Errors { get; set; } = new List<ValidationErrorWithSuggestionsDto>();
        public List<ValidationMessageDto> Warnings { get; set; } = new List<ValidationMessageDto>();
        public List<AppliedFixDto> AppliedFixes { get; set; } = new List<AppliedFixDto>();
    }


    public class ValidationResultsWithSuggestionsDto
    {
        public Guid SessionId { get; set; }
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public int FixedRows { get; set; }
        public int SkippedRows { get; set; }
        public List<RowValidationWithSuggestionsDto> Rows { get; set; } = new List<RowValidationWithSuggestionsDto>();
        public bool CanProceedToReview { get; set; }
        public List<DuplicateInfoDto> Duplicates { get; set; } = new List<DuplicateInfoDto>();
        public int TotalDuplicates { get; set; }
        public int UnresolvedDuplicates { get; set; }
    }

    public class DuplicateInfoDto
    {
        public string DuplicateType { get; set; } = string.Empty;
        public string DuplicateValue { get; set; } = string.Empty;
        public List<int> RowNumbers { get; set; } = new List<int>();
        public string ConflictResolution { get; set; } = string.Empty;
        public List<DuplicateConflictDetailDto> ConflictDetails { get; set; } = new List<DuplicateConflictDetailDto>();
        public List<DuplicateResolutionOptionDto> ResolutionOptions { get; set; } = new List<DuplicateResolutionOptionDto>();
        public bool RequiresUserDecision { get; set; }
        public string ConflictDescription { get; set; } = string.Empty;
        public bool IsResolved { get; set; }
    }

    public class DuplicateConflictDetailDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public string CurrentValue { get; set; } = string.Empty;
        public string ConflictingValue { get; set; } = string.Empty;
        public string ExistingRecordInfo { get; set; } = string.Empty;
        public int? ExistingRecordId { get; set; }
    }

    public class DuplicateResolutionOptionDto
    {
        public string OptionType { get; set; } = string.Empty;
        public string OptionLabel { get; set; } = string.Empty;
        public string OptionDescription { get; set; } = string.Empty;
        public int? ExistingRecordId { get; set; }
        public Dictionary<string, object> OptionData { get; set; } = new Dictionary<string, object>();
    }


    public class ApplyFixRequestDto
    {
        public Guid SessionId { get; set; }
        public int RowId { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public string FixedValue { get; set; } = string.Empty;
        public string FixType { get; set; } = string.Empty;
        public bool SuggestionUsed { get; set; } = false;
    }


    public class BulkApplyFixDto
    {
        public Guid SessionId { get; set; }
        public List<int> RowIds { get; set; } = new List<int>();
        public string FieldName { get; set; } = string.Empty;
        public string FixedValue { get; set; } = string.Empty;
        public string FixType { get; set; } = string.Empty;
    }


    public class ReviewSummaryDto
    {
        public Guid SessionId { get; set; }
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int SkippedRows { get; set; }
        public int ErrorRows { get; set; }
        public int WarningRows { get; set; }
        public int WillBeProcessed { get; set; }
        public int WillBeSkipped { get; set; }
        public int NewCompanies { get; set; }
        public int ExistingCompanies { get; set; }
        public Dictionary<string, int> AppliedFixes { get; set; } = new Dictionary<string, int>();
        public bool CanExecute { get; set; }
        public List<ProcessingPreviewDto> ProcessingPreview { get; set; } = new List<ProcessingPreviewDto>();
    }


    public class ProcessingPreviewDto
    {
        public int RowNumber { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string[] BoothNumbers { get; set; } = Array.Empty<string>();
    }


    public class ApplyFixResponseDto
    {
        public string NewStatus { get; set; } = string.Empty;
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public List<ValidationMessageDto> RemainingErrors { get; set; } = new List<ValidationMessageDto>();
    }
}
