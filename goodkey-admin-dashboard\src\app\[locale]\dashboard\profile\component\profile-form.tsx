'use client';
import { useQuery } from '@tanstack/react-query';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
  Card,
} from '@/components/ui/card';
import Suspense from '@/components/ui/Suspense';
import { TabsTrigger, TabsList, TabsContent, Tabs } from '@/components/ui/tabs';
import UsersQuery from '@/services/queries/UsersQuery';

import ProfilePersonalInformationForm from './profile-personal-detail-form';
import ProfileSecurityForm from './profile-security-form';
import { Badge } from '@/components/ui/Badge';

export default function ProfileForm() {
  const { isSuccess, data, isLoading } = useQuery({
    queryKey: ['profile'],
    queryFn: UsersQuery.getCurrent,
  });

  console.log('data', data);

  return (
    <Suspense isLoading={isLoading}>
      {isSuccess && (
        <div className="mx-auto max-w-3xl space-y-8 py-12 flex flex-col items-center">
          <Card className="w-fit">
            <CardHeader className="flex items-center gap-4">
              <Avatar>
                <AvatarFallback>
                  {data.name
                    .split(' ')
                    .map((name) => name[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>

              <div className="space-y-1">
                <h4 className="text-lg font-medium">{data.name}</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data.email}
                </p>
                {/* Updated role display */}
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">
                    Role:
                  </span>
                  <Badge variant="outline">{data.role.name}</Badge>
                </div>
              </div>
            </CardHeader>
          </Card>
          <div className="space-y-4 self-start">
            <h1 className="text-3xl font-bold">Profile Settings</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Manage your account information and security settings.
            </p>
          </div>

          <Tabs className="w-full" defaultValue="personal">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="personal">Personal Details</TabsTrigger>

              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>Security</CardTitle>
                  <CardDescription>
                    Update your password to keep your account secure.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ProfileSecurityForm />
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="personal">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Details</CardTitle>
                  <CardDescription>
                    Update your first and last name to keep your profile
                    up-to-date.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ProfilePersonalInformationForm user={data} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </Suspense>
  );
}
