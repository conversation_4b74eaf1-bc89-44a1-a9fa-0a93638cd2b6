'use client';

import { useQuery } from '@tanstack/react-query';
import { Spinner } from '@/components/ui/spinner';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';
import WarehouseQuery from '@/services/queries/WarehouseQuery';

import { Badge } from '@/components/ui/Badge';
import React from 'react';

const ShowAllProducts = () => {
  const { data: warehouses, isLoading: loadingWarehouses } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
    select: (data) => data.filter((wh) => wh.isActive),
  });

  const { data, isLoading: loadingProducts } = useQuery({
    queryKey: ['products-all'],
    queryFn: () => OfferingRateQuery.getAll(1),
  });

  if (loadingWarehouses || loadingProducts) {
    return <Spinner />;
  }

  const warehouseCodes = warehouses && warehouses.map((w) => w.code);

  return (
    <div className="overflow-x-auto w-full">
      <table className="table-auto w-full border-collapse text-sm">
        <thead className="bg-slate-100 text-slate-700">
          <tr className="border-b border-gray-200">
            <th className="text-left p-2">Product Name</th>
            {warehouseCodes &&
              warehouseCodes.map((code) => (
                <th key={code} className="text-center p-2">
                  <div>{code}</div>
                  <div className="text-xs">Qty × Price</div>
                </th>
              ))}
            <th className="text-left p-2">Discontinued</th>
          </tr>
        </thead>
        <tbody>
          {data &&
            data.group.map((g) => (
              <React.Fragment key={g.groupId}>
                {/* Group Header Row */}
                <tr className="bg-gray-50">
                  <td
                    colSpan={(warehouseCodes?.length ?? 0) + 2}
                    className="p-2 font-semibold"
                  >
                    {g.groupName}{' '}
                    <span className="text-pink-700 font-mono">({g.code})</span>
                  </td>
                </tr>

                {/* Category Rows */}
                {g.categories.map((c) => (
                  <React.Fragment key={c.categoryId}>
                    <tr className="bg-gray-100">
                      <td
                        colSpan={(warehouseCodes?.length ?? 0) + 2}
                        className="p-2 pl-4 font-medium"
                      >
                        {c.categoryName}{' '}
                        <span className="text-pink-700 font-mono">
                          ({c.code})
                        </span>
                      </td>
                    </tr>

                    {/* Offering Rows */}
                    {c.offerings.map((off) => (
                      <React.Fragment key={off.id}>
                        <tr className="bg-white">
                          <td
                            colSpan={(warehouseCodes?.length ?? 0) + 2}
                            className="p-2 pl-8"
                          >
                            {off.name}{' '}
                            <span className="text-pink-700 font-mono">
                              ({off.code})
                            </span>
                            <Badge
                              variant="secondary"
                              className="ml-2 text-xs text-primary"
                            >
                              {off.quantity || 0}
                            </Badge>
                          </td>
                        </tr>

                        {/* Option Rows */}
                        {off.options && off.options?.length > 0 ? (
                          off.options.map((option) => (
                            <tr key={option.id} className="border-b text-sm">
                              <td className="p-2 pl-12">
                                <span
                                  className={
                                    option.isDiscontinued
                                      ? 'line-through text-gray-400'
                                      : ''
                                  }
                                >
                                  {option.name}
                                </span>{' '}
                                <span className="text-xs font-mono text-pink-600">
                                  ({option.code})
                                </span>
                              </td>

                              {/* Quantity × Price per warehouse */}
                              {warehouseCodes &&
                                warehouseCodes.map((whCode) => {
                                  const warehouseId = warehouses.find(
                                    (wh) => wh.code === whCode,
                                  )?.id;
                                  const w =
                                    option.warehousePriceQuantities?.find(
                                      (q) => q.warehouseId === warehouseId,
                                    );

                                  return (
                                    <td
                                      key={whCode}
                                      className="p-2 text-center"
                                    >
                                      {w?.quantity ?? 0} ×{' '}
                                      {(w?.unitPrice ?? 0).toFixed(2)}
                                    </td>
                                  );
                                })}

                              <td className="p-2 text-center">
                                {option.isDiscontinued ? (
                                  <Badge
                                    variant="destructive"
                                    className="text-xs"
                                  >
                                    Discontinued
                                  </Badge>
                                ) : (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    Active
                                  </Badge>
                                )}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td
                              colSpan={(warehouseCodes?.length ?? 0) + 2}
                              className="italic text-sm text-gray-500 p-2 pl-12"
                            >
                              No property for this product.
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}
        </tbody>
      </table>
    </div>
  );
};

export default ShowAllProducts;
