'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import Field from '@/components/ui/inputs/field';
import {
  ContactCreateSchema,
  ContactUpdateSchema,
  ContactCreateData,
} from '@/schema/ContactSchema';
import type { Contact } from '@/models/Contact';

type ContactFormData = {
  firstName: string;
  lastName: string;
  email: string;
  telephone: string;
  ext: string;
  cellphone: string;
  contactTypeId: string;
  isArchived: boolean;
};
import CompanyQuery from '@/services/queries/CompanyQuery';
import ContactTypeQuery from '@/services/queries/ContactTypeQuery';
import { getQueryClient } from '@/utils/query-client';
import ContactCredentials from '../contact_credentials/contact-credentials';

function FormContent({
  defaultValues,
  companyId,
  contactId,
  contactData,
}: {
  companyId: number;
  contactId?: number;
  defaultValues?: ContactFormData;
  contactData?: Contact;
}) {
  const { push } = useRouter();
  const { toast } = useToast();
  const isEdit = !!contactId;

  const { data: contactTypes } = useQuery({
    queryKey: [...ContactTypeQuery.tags],
    queryFn: () => ContactTypeQuery.getAll(),
  });

  const form = useForm<any>({
    resolver: zodResolver(isEdit ? ContactUpdateSchema : ContactCreateSchema),
    defaultValues: defaultValues ?? {
      firstName: '',
      lastName: '',
      email: '',
      telephone: '',
      ext: '',
      cellphone: '',
      contactTypeId: '',
      isArchived: false,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: isEdit
      ? CompanyQuery.contacts.update(companyId, contactId!)
      : (data: ContactCreateData) =>
          CompanyQuery.contacts.create(companyId, data),
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [...CompanyQuery.tags, 'contacts', companyId],
      });

      toast({
        variant: 'success',
        title: isEdit
          ? 'Contact updated successfully'
          : 'Contact created successfully',
      });

      // Navigate back to company page
      push('/dashboard/setup/company-contact/show-company');
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Something went wrong',
      });
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
          <Field
            control={form.control}
            name="firstName"
            label="First Name"
            placeholder="Enter first name"
            type="text"
          />
          <Field
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Enter last name"
            type="text"
          />
          <Field
            control={form.control}
            name="email"
            label="Email"
            placeholder="Enter email address"
            type="email"
          />
          <Field
            control={form.control}
            name="telephone"
            label="Telephone"
            placeholder="Enter telephone number"
            type="text"
          />
          <Field
            control={form.control}
            name="ext"
            label="Extension"
            placeholder="Enter extension"
            type="text"
          />
          <Field
            control={form.control}
            name="cellphone"
            label="Cellphone"
            placeholder="Enter cellphone number"
            type="text"
          />
          <Field
            control={form.control}
            name="contactTypeId"
            label="Contact Type"
            type={{
              type: 'select',
              props: {
                placeholder: 'Select contact type',
                options:
                  contactTypes?.map((type) => ({
                    label: type.name,
                    value: String(type.id),
                  })) || [],
              },
            }}
            required
          />

          <Field
            control={form.control}
            name="isArchived"
            label="Archived"
            type="checkbox"
            description="Mark this contact as archived"
          />
        </div>

        {isEdit &&
          contactData &&
          contactData.username &&
          contactData.password && (
            <ContactCredentials
              username={contactData.username}
              password={contactData.password}
            />
          )}

        <div className="flex justify-end items-center gap-4 pt-4">
          <Button
            variant="main"
            disabled={isPending}
            iconName={
              isPending ? 'LoadingIcon' : isEdit ? 'SaveIcon' : 'AddIcon'
            }
            iconProps={{ className: isPending ? 'animate-spin' : '' }}
          >
            {isPending
              ? 'Saving...'
              : isEdit
                ? 'Update Contact'
                : 'Create Contact'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

interface ContactFormProps {
  companyId: number;
  contactId?: number;
}

export default function ContactForm({
  companyId,
  contactId,
}: ContactFormProps) {
  const {
    data: rawContactData,
    isLoading,
    isPaused,
  } = useQuery({
    queryKey: [...CompanyQuery.tags, 'contacts', companyId, contactId],
    queryFn: () => CompanyQuery.contacts.getOne(companyId, contactId!),
    enabled: contactId !== undefined,
  });

  const formData = rawContactData
    ? {
        firstName: rawContactData.firstName || '',
        lastName: rawContactData.lastName || '',
        email: rawContactData.email || '',
        telephone: rawContactData.telephone || '',
        ext: rawContactData.ext || '',
        cellphone: rawContactData.cellphone || '',
        contactTypeId: String(rawContactData.contactTypeId),
        isArchived: rawContactData.isArchived,
      }
    : undefined;

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent
        companyId={companyId}
        contactId={contactId}
        defaultValues={formData}
        contactData={rawContactData}
      />
    </Suspense>
  );
}
