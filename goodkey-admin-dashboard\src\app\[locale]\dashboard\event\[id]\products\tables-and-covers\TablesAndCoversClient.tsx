'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FloatingCartButton from '@/components/floating-cart-button';
import { useRouter } from 'next/navigation';
import { ArrowLeft, ShoppingCart } from 'lucide-react';
import WelcomeBanner from '@/components/ui/welcome-banner';

// Define the product type
interface Product {
  id: string;
  name: string;
  description?: string;
  price?: string;
  image?: string;
}

// List of table and cover products
const tableProducts: Product[] = [
  {
    id: 'banjo-skirts-counter',
    name: 'Banjo skirts for counter height tables - 40" h',
    image:
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/220_skirts40.jpg-oCsulWYU1iDGj4ZIcKO91PdLfbQBlo.jpeg',
    description:
      'Available in multiple colors including green, teal, blue, purple, red, black, gray, and white',
  },
  {
    id: 'banjo-skirts-regular',
    name: 'Banjo skirts for regular height tables - 29" h',
  },
  {
    id: 'bistro-table-black',
    name: 'Bistro table black - 42" h x 30" d',
  },
  {
    id: 'bistro-table-chrome',
    name: 'Bistro table chrome - 42" h x 30" d',
  },
  {
    id: 'boardroom-combo',
    name: 'Boardroom Combo',
  },
  {
    id: 'chrome-black-glass-table',
    name: 'Chrome black glass table',
  },
  {
    id: 'chrome-coffee-table',
    name: 'Chrome coffee table',
  },
  {
    id: 'chrome-combo',
    name: 'Chrome Combo',
  },
  {
    id: 'chrome-cruiser-table',
    name: 'Chrome cruiser table - 42" h x 30" d',
  },
  {
    id: 'chrome-end-table',
    name: 'Chrome end table',
  },
  {
    id: 'chrome-square-cruiser',
    name: 'Chrome square cruiser table - 24" x 24" x 40"',
  },
  {
    id: 'coffee-table',
    name: 'Coffee table - 18" h x 30" diameter',
  },
  {
    id: 'counter-height-skirted',
    name: 'Counter height skirted table - 40" h x 24" w',
  },
  {
    id: 'cruiser-table',
    name: 'Cruiser table - 40" h x 30" diameter',
  },
  {
    id: 'desk',
    name: 'Desk',
  },
];

export default function TablesAndCoversClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const eventId = params.id;

  const handleOrderClick = (productId: string) => {
    // Navigate to the product detail page
    router.push(`/event/${eventId}/products/tables-and-covers/${productId}`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <WelcomeBanner userName="Harry Hekimian" showInstructions={false} />
      {/* <HorizontalMenu activeItem="MANAGEMENT" /> */}

      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => router.push(`/event/${eventId}/products`)}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Products
          </Button>
          <h1 className="text-2xl font-bold text-slate-800">
            TABLES AND COVERS
          </h1>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/event/${eventId}`)}
        >
          Back to Event
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle></CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tableProducts.map((product) => (
              <Card
                key={product.id}
                className="overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="relative h-52 bg-slate-100">
                  <img
                    src={
                      product.image ||
                      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-IO0AaV5mDRRZQQdj1CQyu04tcm8Acw.png' ||
                      '/placeholder.svg' ||
                      '/placeholder.svg' ||
                      '/placeholder.svg'
                    }
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-5">
                  <h3 className="font-medium text-slate-800 mb-2">
                    {product.name}
                  </h3>
                  {product.description && (
                    <p className="text-sm text-slate-600 mb-3">
                      {product.description}
                    </p>
                  )}
                  <Button
                    onClick={() => handleOrderClick(product.id)}
                    variant="outline"
                    size="sm"
                    className="w-full text-[#00646C] hover:bg-[#00646C]/10 border-[#00646C]"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Floating Cart Button */}
      <FloatingCartButton eventId={eventId} itemCount={4} />
    </div>
  );
}
