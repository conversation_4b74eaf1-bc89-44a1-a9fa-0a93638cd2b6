import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShowPromoter } from '@/models/Show';
import { useQuery } from '@tanstack/react-query';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Company } from '@/models/Company';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { Province } from '@/models/Province';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { BriefData } from '@/models/BriefData';

export default function ShowManagement({
  promoter,
}: {
  promoter?: ShowPromoter;
}) {
  const {
    data: company,
    isLoading: isCompanyLoading,
    isError: isCompanyError,
  } = useQuery<Company, Error>({
    queryKey: [CompanyQuery.tags, promoter?.companyId],
    queryFn: () => CompanyQuery.getOne(promoter!.companyId),
    enabled: !!promoter?.companyId,
  });

  const {
    data: provinces,
    isLoading: isProvincesLoading,
    isError: isProvincesError,
  } = useQuery<Province[], Error>({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const {
    data: countries,
    isLoading: isCountriesLoading,
    isError: isCountriesError,
  } = useQuery<BriefData[], Error>({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const isLoading =
    isCompanyLoading || isProvincesLoading || isCountriesLoading;
  const isError = isCompanyError || isProvincesError || isCountriesError;

  const provinceName = provinces?.find(
    (p) => p.id === company?.provinceId,
  )?.name;

  const countryName = countries?.find((c) => c.id === company?.countryId)?.name;

  if (!promoter || isLoading) {
    return (
      <Card>
        <CardHeader className="pb-5">
          <CardTitle>Show Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
              <p className="text-slate-600">Loading company info...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Show Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-600 text-center py-4">
            Failed to load company information.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle>Show Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="space-y-2">
            <div>
              <span className="font-medium text-slate-700">Company Name:</span>
              <span className="ml-2">
                {company?.name || promoter.companyName}
              </span>
            </div>
            {company?.email && (
              <div>
                <span className="font-medium text-slate-700">Email:</span>
                <span className="ml-2">{company.email}</span>
              </div>
            )}
            {company?.phone && (
              <div>
                <span className="font-medium text-slate-700">Phone:</span>
                <span className="ml-2">{company.phone}</span>
              </div>
            )}
            {company?.address1 && (
              <div>
                <span className="font-medium text-slate-700">Address:</span>
                <span className="ml-2">
                  {company.address1}
                  {company.city && `, ${company.city}`}
                  {provinceName && `, ${provinceName}`}
                  {company.postalCode && ` ${company.postalCode}`}
                  {countryName && `, ${countryName}`}
                </span>
              </div>
            )}
            {company?.websiteUrl && (
              <div>
                <span className="font-medium text-slate-700">Website:</span>
                <span className="ml-2">
                  <a
                    href={company.websiteUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#00646C] hover:underline"
                  >
                    {company.websiteUrl}
                  </a>
                </span>
              </div>
            )}
            <div>
              <span className="font-medium text-slate-700">
                Show Subcontact:
              </span>
              <span className="ml-2">
                {promoter.showSubcontact ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium text-slate-700">
                Floor Plan Required:
              </span>
              <span className="ml-2">
                {promoter.floorPlanRequired ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
