﻿using goodkey_cms.DTO;

namespace goodkey_common.DTO.Company
{
    public class CompanyDto : BasicDetail
    {
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public int? ProvinceId { get; set; }
        public string? PostalCode { get; set; }
        public int? CountryId { get; set; }
        public string? WebsiteUrl { get; set; }
        public string? AccountNumber { get; set; }
        public string? CompanyGroup { get; set; }
        public string? Note { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class CompanyDetail : BasicDetail
    {
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? Country { get; set; }
        public string? WebsiteUrl { get; set; }
        public string? AccountNumber { get; set; }
        public string? CompanyGroup { get; set; }
        public string? Note { get; set; }
        public bool? IsArchived { get; set; }

        // Show-related fields (only for Show manager companies)
        public int? NumberOfShows { get; set; }
    }

    public class CompanyShowDto
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? VenueName { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public bool? Archive { get; set; }
    }
}
