"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_powerquery_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/powerquery.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/powerquery.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"PowerQuery\\\",\\\"fileTypes\\\":[\\\"pq\\\",\\\"pqm\\\"],\\\"name\\\":\\\"powerquery\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#Noise\\\"},{\\\"include\\\":\\\"#LiteralExpression\\\"},{\\\"include\\\":\\\"#Keywords\\\"},{\\\"include\\\":\\\"#ImplicitVariable\\\"},{\\\"include\\\":\\\"#IntrinsicVariable\\\"},{\\\"include\\\":\\\"#Operators\\\"},{\\\"include\\\":\\\"#DotOperators\\\"},{\\\"include\\\":\\\"#TypeName\\\"},{\\\"include\\\":\\\"#RecordExpression\\\"},{\\\"include\\\":\\\"#Punctuation\\\"},{\\\"include\\\":\\\"#QuotedIdentifier\\\"},{\\\"include\\\":\\\"#Identifier\\\"}],\\\"repository\\\":{\\\"BlockComment\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.powerquery\\\"},\\\"DecimalNumber\\\":{\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(\\\\\\\\d*\\\\\\\\.\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.powerquery\\\"},\\\"DotOperators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ellipsis.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.list.powerquery\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\.)(?:(\\\\\\\\.\\\\\\\\.\\\\\\\\.)|(\\\\\\\\.\\\\\\\\.))(?!\\\\\\\\.)\\\"},\\\"EscapeSequence\\\":{\\\"begin\\\":\\\"#\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.escapesequence.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.escapesequence.end.powerquery\\\"}},\\\"name\\\":\\\"constant.character.escapesequence.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(#|\\\\\\\\h{4}|\\\\\\\\h{8}|cr|lf|tab)(?:,(#|\\\\\\\\h{4}|\\\\\\\\h{8}|cr|lf|tab))*\\\"},{\\\"match\\\":\\\"[^\\\\\\\\)]\\\",\\\"name\\\":\\\"invalid.illegal.escapesequence.powerquery\\\"}]},\\\"FloatNumber\\\":{\\\"match\\\":\\\"(\\\\\\\\d*\\\\\\\\.)?\\\\\\\\d+(e|E)(\\\\\\\\+|-)?\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.float.powerquery\\\"},\\\"HexNumber\\\":{\\\"match\\\":\\\"0(x|X)\\\\\\\\h+\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.powerquery\\\"},\\\"Identifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.inclusiveidentifier.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.powerquery\\\"}},\\\"match\\\":\\\"(?:(?<![\\\\\\\\._\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Nd}\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}])(@?)([_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}][_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Nd}\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}]*(?:\\\\\\\\.[_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}][_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Nd}\\\\\\\\p{Pc}\\\\\\\\p{Mn}\\\\\\\\p{Mc}\\\\\\\\p{Cf}])*)\\\\\\\\b)\\\"},\\\"ImplicitVariable\\\":{\\\"match\\\":\\\"\\\\\\\\b_\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.implicitvariable.powerquery\\\"},\\\"InclusiveIdentifier\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"inclusiveidentifier.powerquery\\\"}},\\\"match\\\":\\\"@\\\"},\\\"IntNumber\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integer.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\"},\\\"IntrinsicVariable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.intrinsicvariable.powerquery\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(#sections|#shared)\\\\\\\\b\\\"},\\\"Keywords\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.word.logical.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.conditional.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.exception.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(and|or|not)|(if|then|else)|(try|otherwise)|(as|each|in|is|let|meta|type|error)|(section|shared))\\\\\\\\b\\\"},\\\"LineComment\\\":{\\\"match\\\":\\\"//.*\\\",\\\"name\\\":\\\"comment.line.double-slash.powerquery\\\"},\\\"LiteralExpression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#String\\\"},{\\\"include\\\":\\\"#NumericConstant\\\"},{\\\"include\\\":\\\"#LogicalConstant\\\"},{\\\"include\\\":\\\"#NullConstant\\\"},{\\\"include\\\":\\\"#FloatNumber\\\"},{\\\"include\\\":\\\"#DecimalNumber\\\"},{\\\"include\\\":\\\"#HexNumber\\\"},{\\\"include\\\":\\\"#IntNumber\\\"}]},\\\"LogicalConstant\\\":{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.logical.powerquery\\\"},\\\"Noise\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#BlockComment\\\"},{\\\"include\\\":\\\"#LineComment\\\"},{\\\"include\\\":\\\"#Whitespace\\\"}]},\\\"NullConstant\\\":{\\\"match\\\":\\\"\\\\\\\\b(null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.null.powerquery\\\"},\\\"NumericConstant\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.numeric.float.powerquery\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\d\\\\\\\\w])(#infinity|#nan)\\\\\\\\b\\\"},\\\"Operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.function.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment-or-comparison.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.comparison.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.combination.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.powerquery\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.sectionaccess.powerquery\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.optional.powerquery\\\"}},\\\"match\\\":\\\"(=>)|(=)|(<>|<|>|<=|>=)|(&)|(\\\\\\\\+|-|\\\\\\\\*|\\\\\\\\/)|(!)|(\\\\\\\\?)\\\"},\\\"Punctuation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.powerquery\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.powerquery\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.powerquery\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.powerquery\\\"}},\\\"match\\\":\\\"(,)|(\\\\\\\\()|(\\\\\\\\))|({)|(})\\\"},\\\"QuotedIdentifier\\\":{\\\"begin\\\":\\\"#\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.quotedidentifier.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.quotedidentifier.end.powerquery\\\"}},\\\"name\\\":\\\"entity.name.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.powerquery\\\"},{\\\"include\\\":\\\"#EscapeSequence\\\"}]},\\\"RecordExpression\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.begin.powerquery\\\"}},\\\"contentName\\\":\\\"meta.recordexpression.powerquery\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.end.powerquery\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"String\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.powerquery\\\"}},\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.powerquery\\\"}},\\\"name\\\":\\\"string.quoted.double.powerquery\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.powerquery\\\"},{\\\"include\\\":\\\"#EscapeSequence\\\"}]},\\\"TypeName\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.powerquery\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.powerquery\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(optional|nullable)|(action|any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|null|number|record|table|text|type))\\\\\\\\b\\\"},\\\"Whitespace\\\":{\\\"match\\\":\\\"\\\\\\\\s+\\\"}},\\\"scopeName\\\":\\\"source.powerquery\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/powerquery.mjs\n"));

/***/ })

}]);