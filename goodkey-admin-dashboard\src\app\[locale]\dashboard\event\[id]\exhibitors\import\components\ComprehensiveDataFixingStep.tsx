'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle2,
  Edit3,
  FileSpreadsheet,
  Users,
  Building,
  Mail,
  Phone,
  Save,
  RotateCcw,
  ArrowLeftRight,
  Info,
  Search,
  ChevronRight,
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import ExhibitorImportQuery from '@/services/queries/ExhibitorImportQuery';
import type {
  ExhibitorImportValidationResponseDto,
  ExhibitorImportRowDto,
  ExhibitorImportValidationMessageDto,
  ExhibitorImportDuplicateDto,
} from '@/models/ExhibitorImport';
import type {
  FieldEditDto,
  FieldEditState,
  RowEditState,
  DataFixingSessionState,
} from '@/models/ExhibitorImportFix';

interface DuplicateResolverProps {
  duplicates: ExhibitorImportDuplicateDto[];
  sessionId: string;
  onDuplicateResolved: () => void;
}

const DuplicateResolver: React.FC<DuplicateResolverProps> = ({
  duplicates,
  sessionId,
  onDuplicateResolved,
}) => {
  if (duplicates.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-800 mb-2">
            No Duplicates Found!
          </h3>
          <p className="text-muted-foreground">
            All records are unique and ready for import.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Alert className="border-orange-200 bg-orange-50">
        <Info className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-800">
          <strong>Duplicate Resolution Required:</strong> We found{' '}
          {duplicates.length} duplicate conflict
          {duplicates.length > 1 ? 's' : ''} that need your attention. Choose
          how to handle each conflict below.
        </AlertDescription>
      </Alert>

      {duplicates.map((duplicate, index) => (
        <Card
          key={duplicate.duplicateId}
          className="border-l-4 border-l-orange-500"
        >
          <CardHeader className="bg-gradient-to-r from-orange-50 to-transparent">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-700 font-bold text-sm">
                  {index + 1}
                </div>
                <div>
                  <div className="text-lg font-semibold text-orange-800">
                    {duplicate.duplicateType} Conflict
                  </div>
                  <div className="text-sm font-normal text-muted-foreground">
                    {duplicate.conflictDescription}
                  </div>
                </div>
              </CardTitle>
              <Badge
                variant="outline"
                className="bg-orange-100 text-orange-800 border-orange-300"
              >
                {duplicate.duplicateValue}
              </Badge>
            </div>
            <div className="mt-2 text-sm text-muted-foreground">
              <strong>Affected Rows:</strong> {duplicate.rowNumbers.join(', ')}
            </div>
          </CardHeader>

          <CardContent className="p-4">
            <div className="text-center">
              <Button
                variant="outline"
                onClick={() => {
                  // Navigate to detailed duplicate resolution
                  // This would open the enhanced DuplicateResolutionStep component
                }}
                className="flex items-center space-x-2"
              >
                <ArrowLeftRight className="h-4 w-4" />
                <span>Resolve This Conflict</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

interface FieldEditorProps {
  fieldState: FieldEditState;
  validationMessages: ExhibitorImportValidationMessageDto[];
  onFieldChange: (
    rowNumber: number,
    fieldName: string,
    newValue: string,
  ) => void;
  onFieldReset: (rowNumber: number, fieldName: string) => void;
}

const FieldEditor: React.FC<FieldEditorProps> = ({
  fieldState,
  validationMessages,
  onFieldChange,
  onFieldReset,
}) => {
  const getFieldIcon = (fieldName: string) => {
    if (fieldName.toLowerCase().includes('email'))
      return <Mail className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('phone'))
      return <Phone className="h-4 w-4" />;
    if (fieldName.toLowerCase().includes('company'))
      return <Building className="h-4 w-4" />;
    return <Users className="h-4 w-4" />;
  };

  const formatFieldName = (fieldName: string) => {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  return (
    <div
      className={`border-2 rounded-lg p-4 transition-all ${
        validationMessages.length > 0
          ? 'border-red-500 bg-red-50 shadow-red-100 shadow-lg'
          : fieldState.isModified
            ? 'border-blue-300 bg-blue-50'
            : 'border-gray-200 bg-white'
      }`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="p-1 bg-white rounded shadow-sm">
            {getFieldIcon(fieldState.fieldName)}
          </div>
          <Label className="font-medium text-gray-800">
            {formatFieldName(fieldState.fieldName)}
          </Label>
          {fieldState.isModified && (
            <Badge
              variant="outline"
              className="bg-blue-100 text-blue-800 text-xs"
            >
              Modified
            </Badge>
          )}
          {validationMessages.length > 0 && (
            <Badge
              variant="destructive"
              className="text-xs font-semibold bg-red-600 text-white animate-pulse"
            >
              ⚠️ {validationMessages.length} Error
              {validationMessages.length > 1 ? 's' : ''}
            </Badge>
          )}
        </div>

        {fieldState.isModified && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              onFieldReset(fieldState.rowNumber, fieldState.fieldName)
            }
            className="text-gray-500 hover:text-gray-700 h-6 px-2"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
        )}
      </div>

      <div className="space-y-2">
        <Input
          value={fieldState.currentValue}
          onChange={(e) =>
            onFieldChange(
              fieldState.rowNumber,
              fieldState.fieldName,
              e.target.value,
            )
          }
          className={`${
            validationMessages.length > 0
              ? 'border-red-500 focus:border-red-600 bg-red-50 text-red-900 placeholder-red-400'
              : fieldState.isModified
                ? 'border-blue-400 focus:border-blue-500'
                : ''
          }`}
          placeholder={`Enter ${formatFieldName(fieldState.fieldName).toLowerCase()}`}
        />

        {/* Validation Messages */}
        {validationMessages.length > 0 && (
          <div className="space-y-1">
            {validationMessages.map((msg, idx) => (
              <div
                key={idx}
                className="text-sm flex items-start space-x-2 text-red-600"
              >
                <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>{msg.message}</span>
              </div>
            ))}
          </div>
        )}

        {/* Original Value Display */}
        {fieldState.isModified && (
          <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded border">
            <strong>Original:</strong> {fieldState.originalValue || '(empty)'}
          </div>
        )}

        {/* Field Suggestions (placeholder for future enhancement) */}
        {fieldState.suggestions.length > 0 && (
          <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded border">
            <strong>Suggestions:</strong>{' '}
            {fieldState.suggestions.map((s) => s.suggestedValue).join(', ')}
          </div>
        )}
      </div>
    </div>
  );
};

interface RowEditorProps {
  row: ExhibitorImportRowDto;
  rowState?: RowEditState;
  validationMessages: ExhibitorImportValidationMessageDto[];
  onFieldChange: (
    rowNumber: number,
    fieldName: string,
    newValue: string,
  ) => void;
  onFieldReset: (rowNumber: number, fieldName: string) => void;
}

const RowEditor: React.FC<RowEditorProps> = ({
  row,
  rowState,
  validationMessages,
  onFieldChange,
  onFieldReset,
}) => {
  const [isExpanded, setIsExpanded] = useState(row.hasErrors);

  if (!rowState) return null;

  return (
    <Card
      className={`border-l-4 ${
        row.hasErrors
          ? 'border-l-red-500'
          : row.hasWarnings
            ? 'border-l-yellow-500'
            : rowState.hasModifications
              ? 'border-l-blue-500'
              : 'border-l-gray-200'
      }`}
    >
      <CardHeader
        className="cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-3">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                row.hasErrors
                  ? 'bg-red-500'
                  : row.hasWarnings
                    ? 'bg-yellow-500'
                    : rowState.hasModifications
                      ? 'bg-blue-500'
                      : 'bg-gray-400'
              }`}
            >
              {row.rowNumber}
            </div>
            <div>
              <div className="text-lg font-semibold">
                {row.companyName || 'Unnamed Company'}
              </div>
              <div className="text-sm font-normal text-muted-foreground">
                {row.contactFirstName} {row.contactLastName} •{' '}
                {row.contactEmail}
              </div>
            </div>
          </CardTitle>

          <div className="flex items-center space-x-2">
            {row.hasErrors && (
              <Badge
                variant="destructive"
                className="flex items-center space-x-1"
              >
                <AlertCircle className="h-3 w-3" />
                <span>
                  {row.errorCount} Error{row.errorCount > 1 ? 's' : ''}
                </span>
              </Badge>
            )}
            {row.hasWarnings && !row.hasErrors && (
              <Badge
                variant="secondary"
                className="flex items-center space-x-1 bg-yellow-100 text-yellow-800"
              >
                <AlertTriangle className="h-3 w-3" />
                <span>
                  {row.warningCount} Warning{row.warningCount > 1 ? 's' : ''}
                </span>
              </Badge>
            )}
            {rowState.hasModifications && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                Modified
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(rowState.fields).map(([fieldName, fieldState]) => {
              const fieldMessages = validationMessages.filter(
                (m) => m.fieldName === fieldName,
              );

              return (
                <FieldEditor
                  key={fieldName}
                  fieldState={fieldState}
                  validationMessages={fieldMessages}
                  onFieldChange={onFieldChange}
                  onFieldReset={onFieldReset}
                />
              );
            })}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

interface ComprehensiveDataFixingStepProps {
  validationData: ExhibitorImportValidationResponseDto;
  onDataFixed: (fixedData: any) => void;
  isLoading: boolean;
}

const ComprehensiveDataFixingStep: React.FC<
  ComprehensiveDataFixingStepProps
> = ({ validationData, onDataFixed, isLoading }) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current tab from URL or default to 'all'
  const currentTab = searchParams.get('tab') || 'all';

  // Get filters from URL
  const showOnlyErrors = searchParams.get('showErrors') === 'true';
  const showOnlyWarnings = searchParams.get('showWarnings') === 'true';
  const showOnlyModified = searchParams.get('showModified') === 'true';

  const [sessionState, setSessionState] = useState<DataFixingSessionState>({
    sessionId: validationData.sessionId,
    rows: {},
    duplicates: {},
    summary: {
      totalRows: validationData.summary.totalRows,
      validRows: validationData.summary.validRows,
      errorRows: validationData.summary.errorRows,
      warningRows: validationData.summary.warningRows,
      hasErrors: validationData.summary.hasErrors,
      hasWarnings: validationData.summary.hasWarnings,
      hasDuplicates: validationData.summary.hasDuplicates,
      unresolvedDuplicates: validationData.duplicates.length,
    },
    hasUnsavedChanges: false,
    isLoading: false,
    autoSave: false,
  });

  const getFieldValue = (
    row: ExhibitorImportRowDto,
    fieldName: string,
  ): string => {
    const fieldMap: Record<string, keyof ExhibitorImportRowDto> = {
      companyName: 'companyName',
      companyEmail: 'companyEmail',
      companyPhone: 'companyPhone',
      companyAddress1: 'companyAddress1',
      companyAddress2: 'companyAddress2',
      contactFirstName: 'contactFirstName',
      contactLastName: 'contactLastName',
      contactEmail: 'contactEmail',
      contactPhone: 'contactPhone',
      contactMobile: 'contactMobile',
      boothNumbers: 'boothNumbers',
    };

    const mappedField = fieldMap[fieldName];
    return mappedField ? String(row[mappedField] || '') : '';
  };

  // Helper function to update URL params
  const updateUrlParams = (updates: Record<string, string | null>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '' || value === 'false') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const [searchQuery, setSearchQuery] = useState('');

  const { rows, validationMessages, duplicates } = validationData;

  // Initialize row states
  useEffect(() => {
    const initialRows: Record<number, RowEditState> = {};

    rows.forEach((row) => {
      const rowMessages = validationMessages.filter(
        (m) => m.rowNumber === row.rowNumber,
      );
      const fields: Record<string, FieldEditState> = {};

      // Initialize field states for fields with errors or all fields
      const fieldNames = [
        'companyName',
        'companyEmail',
        'companyPhone',
        'companyAddress',
        'contactFirstName',
        'contactLastName',
        'contactEmail',
        'contactPhone',
        'contactMobile',
        'boothNumbers',
      ];

      fieldNames.forEach((fieldName) => {
        const fieldMessages = rowMessages.filter(
          (m) => m.fieldName === fieldName,
        );
        const originalValue = getFieldValue(row, fieldName);

        fields[fieldName] = {
          rowNumber: row.rowNumber,
          fieldName,
          originalValue,
          currentValue: originalValue,
          isModified: false,
          isValid: fieldMessages.length === 0,
          validationErrors: fieldMessages.map((m) => m.message),
          suggestions: [],
          isEditing: false,
        };
      });

      initialRows[row.rowNumber] = {
        rowNumber: row.rowNumber,
        fields,
        hasModifications: false,
        hasErrors: row.hasErrors,
        isExpanded: row.hasErrors, // Auto-expand rows with errors
      };
    });

    setSessionState((prev) => ({ ...prev, rows: initialRows }));
  }, [rows, validationMessages]);

  const updateFieldValue = (
    rowNumber: number,
    fieldName: string,
    newValue: string,
  ) => {
    setSessionState((prev) => {
      const updatedRows = { ...prev.rows };
      const row = updatedRows[rowNumber];

      if (row && row.fields[fieldName]) {
        const field = row.fields[fieldName];
        const isModified = newValue !== field.originalValue;

        updatedRows[rowNumber] = {
          ...row,
          fields: {
            ...row.fields,
            [fieldName]: {
              ...field,
              currentValue: newValue,
              isModified,
            },
          },
          hasModifications: Object.values(row.fields).some((f) =>
            f.fieldName === fieldName ? isModified : f.isModified,
          ),
        };
      }

      return {
        ...prev,
        rows: updatedRows,
        hasUnsavedChanges: true,
      };
    });
  };

  const resetFieldValue = (rowNumber: number, fieldName: string) => {
    setSessionState((prev) => {
      const updatedRows = { ...prev.rows };
      const row = updatedRows[rowNumber];

      if (row && row.fields[fieldName]) {
        const field = row.fields[fieldName];

        updatedRows[rowNumber] = {
          ...row,
          fields: {
            ...row.fields,
            [fieldName]: {
              ...field,
              currentValue: field.originalValue,
              isModified: false,
            },
          },
          hasModifications: Object.values(row.fields).some((f) =>
            f.fieldName === fieldName ? false : f.isModified,
          ),
        };
      }

      return {
        ...prev,
        rows: updatedRows,
        hasUnsavedChanges: Object.values(updatedRows).some(
          (r) => r.hasModifications,
        ),
      };
    });
  };

  const getModifiedFieldsCount = (): number => {
    return Object.values(sessionState.rows).reduce((count, row) => {
      return (
        count +
        Object.values(row.fields).filter((field) => field.isModified).length
      );
    }, 0);
  };

  const getFilteredRows = () => {
    let filteredRows = rows;

    // Apply filters from URL params
    if (showOnlyErrors) {
      filteredRows = filteredRows.filter((row) => row.hasErrors);
    }
    if (showOnlyWarnings) {
      filteredRows = filteredRows.filter(
        (row) => row.hasWarnings && !row.hasErrors,
      );
    }
    if (showOnlyModified) {
      filteredRows = filteredRows.filter(
        (row) => sessionState.rows[row.rowNumber]?.hasModifications,
      );
    }

    // Apply search
    if (searchQuery) {
      filteredRows = filteredRows.filter((row) => {
        const searchLower = searchQuery.toLowerCase();
        return (
          row.companyName?.toLowerCase().includes(searchLower) ||
          row.contactEmail?.toLowerCase().includes(searchLower) ||
          row.contactFirstName?.toLowerCase().includes(searchLower) ||
          row.contactLastName?.toLowerCase().includes(searchLower) ||
          row.rowNumber.toString().includes(searchLower)
        );
      });
    }

    return filteredRows;
  };

  const handleSaveChanges = async () => {
    try {
      setSessionState((prev) => ({ ...prev, isLoading: true }));

      const fieldEdits: FieldEditDto[] = [];

      Object.values(sessionState.rows).forEach((row) => {
        Object.values(row.fields).forEach((field) => {
          if (field.isModified) {
            fieldEdits.push({
              rowNumber: field.rowNumber,
              fieldName: field.fieldName,
              newValue: field.currentValue,
              originalValue: field.originalValue,
              editReason: 'UserEdit',
            });
          }
        });
      });

      if (fieldEdits.length === 0) {
        toast({
          title: 'No changes to save',
          description: "You haven't made any modifications to the data.",
        });
        return;
      }

      console.log('🔧 Starting field edit request:', {
        sessionId: sessionState.sessionId,
        fieldEditsCount: fieldEdits.length,
        fieldEdits: fieldEdits,
      });

      const response = await ExhibitorImportQuery.editFields({
        sessionId: sessionState.sessionId,
        fieldEdits,
      });

      console.log('🔧 Raw API Response:', response);

      if (response.success) {
        console.log('🔧 Field Edit Response:', {
          message: response.message,
          updatedSummary: response.updatedSummary,
          fieldEditsCount: fieldEdits.length,
          results: response.results,
        });

        console.log('🔧 Summary Details:', {
          errorRows: response.updatedSummary?.errorRows,
          warningRows: response.updatedSummary?.warningRows,
          validRows: response.updatedSummary?.validRows,
          totalRows: response.updatedSummary?.totalRows,
          hasErrors: response.updatedSummary?.hasErrors,
          hasWarnings: response.updatedSummary?.hasWarnings,
          unresolvedDuplicates: response.updatedSummary?.unresolvedDuplicates,
        });

        toast({
          title: 'Changes saved successfully',
          description:
            response.message ||
            `Updated ${fieldEdits.length} field${fieldEdits.length > 1 ? 's' : ''}.`,
        });

        // Update session state with results
        console.log('🔧 Updating session state:', {
          oldSummary: sessionState.summary,
          newSummary: response.updatedSummary,
          fieldEditsCount: fieldEdits.length,
        });

        setSessionState((prev) => ({
          ...prev,
          hasUnsavedChanges: false,
          summary: response.updatedSummary,
        }));

        // Only proceed to next step if no errors remain
        if (response.updatedSummary.errorRows === 0) {
          onDataFixed({
            fieldEdits,
            summary: response.updatedSummary,
          });
        } else {
          // Stay on current step, show user that errors remain with details
          const remainingErrorsText = response.results
            .filter((r) => r.remainingErrors && r.remainingErrors.length > 0)
            .map((r) => `Row ${r.rowNumber}: ${r.remainingErrors.join(', ')}`)
            .join('; ');

          toast({
            title: 'Errors still remain',
            description:
              remainingErrorsText ||
              `${response.updatedSummary.errorRows} validation error${response.updatedSummary.errorRows > 1 ? 's' : ''} still need to be fixed.`,
            variant: 'destructive',
            duration: 8000, // Show longer for detailed error info
          });
        }
      } else {
        toast({
          title: 'Failed to save changes',
          description: response.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error saving changes',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setSessionState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <Edit3 className="h-8 w-8 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-semibold">
            {sessionState.summary.errorRows === 0 &&
            sessionState.summary.warningRows === 0
              ? 'Review Data'
              : 'Fix Data Issues'}
          </h2>
          <p className="text-muted-foreground">
            {sessionState.summary.errorRows === 0 &&
            sessionState.summary.warningRows === 0
              ? 'All data looks good! Review your data and proceed to the next step.'
              : 'Review and fix validation errors, warnings, and duplicate conflicts field by field.'}
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {sessionState.summary.errorRows > 0 && (
        <Alert className="border-red-500 bg-red-50 mb-6">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>⚠️ Action Required:</strong> There are{' '}
            <strong>
              {sessionState.summary.errorRows} validation error
              {sessionState.summary.errorRows > 1 ? 's' : ''}
            </strong>{' '}
            that must be fixed before you can proceed. Please edit the
            highlighted fields below.
          </AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {sessionState.summary.errorRows}
            </div>
            <div className="text-sm text-muted-foreground">
              Rows with Errors
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {sessionState.summary.warningRows}
            </div>
            <div className="text-sm text-muted-foreground">
              Rows with Warnings
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {sessionState.summary.unresolvedDuplicates}
            </div>
            <div className="text-sm text-muted-foreground">
              Duplicate Conflicts
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {getModifiedFieldsCount()}
            </div>
            <div className="text-sm text-muted-foreground">Fields Modified</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {sessionState.summary.validRows}
            </div>
            <div className="text-sm text-muted-foreground">Valid Rows</div>
          </CardContent>
        </Card>
      </div>

      {/* Controls and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search rows by company, contact, or row number..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-errors"
                    checked={showOnlyErrors}
                    onCheckedChange={(checked) =>
                      updateUrlParams({ showErrors: checked ? 'true' : null })
                    }
                  />
                  <Label htmlFor="show-errors" className="text-sm">
                    Errors Only
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-modified"
                    checked={showOnlyModified}
                    onCheckedChange={(checked) =>
                      updateUrlParams({ showModified: checked ? 'true' : null })
                    }
                  />
                  <Label htmlFor="show-modified" className="text-sm">
                    Modified Only
                  </Label>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              {sessionState.hasUnsavedChanges ? (
                <Button
                  onClick={handleSaveChanges}
                  disabled={sessionState.isLoading}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Save Changes ({getModifiedFieldsCount()})</span>
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    // Check if there are still errors
                    if (sessionState.summary.errorRows > 0) {
                      toast({
                        title: 'Cannot proceed',
                        description:
                          'Please fix all validation errors before proceeding.',
                        variant: 'destructive',
                      });
                      return;
                    }

                    // Proceed without changes
                    toast({
                      title: 'Proceeding to next step',
                      description: 'All data validated successfully.',
                    });
                    onDataFixed({}); // Call with empty changes
                  }}
                  disabled={
                    sessionState.isLoading || sessionState.summary.errorRows > 0
                  }
                  className={`flex items-center space-x-2 ${
                    sessionState.summary.errorRows > 0
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  <ChevronRight className="h-4 w-4" />
                  <span>
                    {sessionState.summary.errorRows > 0
                      ? `Fix ${sessionState.summary.errorRows} Error${sessionState.summary.errorRows > 1 ? 's' : ''} First`
                      : 'Proceed to Next Step'}
                  </span>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Fixing Tabs */}
      <Tabs
        value={currentTab}
        onValueChange={(value: string) => updateUrlParams({ tab: value })}
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="errors" className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4" />
            <span>Errors ({sessionState.summary.errorRows})</span>
          </TabsTrigger>
          <TabsTrigger value="warnings" className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Warnings ({sessionState.summary.warningRows})</span>
          </TabsTrigger>
          <TabsTrigger
            value="duplicates"
            className="flex items-center space-x-2"
          >
            <ArrowLeftRight className="h-4 w-4" />
            <span>
              Duplicates ({sessionState.summary.unresolvedDuplicates})
            </span>
          </TabsTrigger>
          <TabsTrigger value="all" className="flex items-center space-x-2">
            <FileSpreadsheet className="h-4 w-4" />
            <span>All Rows ({sessionState.summary.totalRows})</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="errors" className="space-y-4">
          {getFilteredRows().filter((row) => row.hasErrors).length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  No Errors Found!
                </h3>
                <p className="text-muted-foreground">
                  All rows have been validated successfully.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {getFilteredRows()
                .filter((row) => row.hasErrors)
                .map((row) => (
                  <RowEditor
                    key={row.rowNumber}
                    row={row}
                    rowState={sessionState.rows[row.rowNumber]}
                    validationMessages={validationMessages.filter(
                      (m) => m.rowNumber === row.rowNumber,
                    )}
                    onFieldChange={updateFieldValue}
                    onFieldReset={resetFieldValue}
                  />
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="warnings" className="space-y-4">
          {getFilteredRows()
            .filter((row) => row.hasWarnings && !row.hasErrors)
            .map((row) => (
              <RowEditor
                key={row.rowNumber}
                row={row}
                rowState={sessionState.rows[row.rowNumber]}
                validationMessages={validationMessages.filter(
                  (m) => m.rowNumber === row.rowNumber,
                )}
                onFieldChange={updateFieldValue}
                onFieldReset={resetFieldValue}
              />
            ))}
        </TabsContent>

        <TabsContent value="duplicates" className="space-y-4">
          <DuplicateResolver
            duplicates={duplicates}
            sessionId={sessionState.sessionId}
            onDuplicateResolved={() => {
              // Refresh data
            }}
          />
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          {getFilteredRows().map((row) => (
            <RowEditor
              key={row.rowNumber}
              row={row}
              rowState={sessionState.rows[row.rowNumber]}
              validationMessages={validationMessages.filter(
                (m) => m.rowNumber === row.rowNumber,
              )}
              onFieldChange={updateFieldValue}
              onFieldReset={resetFieldValue}
            />
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComprehensiveDataFixingStep;
