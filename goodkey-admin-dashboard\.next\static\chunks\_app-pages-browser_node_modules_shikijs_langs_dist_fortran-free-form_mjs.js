"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fortran-free-form_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fortran-free-form.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fortran-free-form.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fortran (Free Form)\\\",\\\"fileTypes\\\":[\\\"f90\\\",\\\"F90\\\",\\\"f95\\\",\\\"F95\\\",\\\"f03\\\",\\\"F03\\\",\\\"f08\\\",\\\"F08\\\",\\\"f18\\\",\\\"F18\\\",\\\"fpp\\\",\\\"FPP\\\",\\\".pf\\\",\\\".PF\\\"],\\\"firstLineMatch\\\":\\\"(?i)-[*]- mode: fortran free -[*]-\\\",\\\"injections\\\":{\\\"source.fortran.free - ( string | comment | meta.preprocessor )\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-continuation-operator\\\"},{\\\"include\\\":\\\"#preprocessor\\\"}]},\\\"string.quoted.double.fortran\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-line-continuation-operator\\\"}]},\\\"string.quoted.single.fortran\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-line-continuation-operator\\\"}]}},\\\"name\\\":\\\"fortran-free-form\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#include-statement\\\"},{\\\"include\\\":\\\"#import-statement\\\"},{\\\"include\\\":\\\"#block-data-definition\\\"},{\\\"include\\\":\\\"#function-definition\\\"},{\\\"include\\\":\\\"#module-definition\\\"},{\\\"include\\\":\\\"#program-definition\\\"},{\\\"include\\\":\\\"#submodule-definition\\\"},{\\\"include\\\":\\\"#subroutine-definition\\\"},{\\\"include\\\":\\\"#procedure-definition\\\"},{\\\"include\\\":\\\"#derived-type-definition\\\"},{\\\"include\\\":\\\"#enum-block-construct\\\"},{\\\"include\\\":\\\"#interface-block-constructs\\\"},{\\\"include\\\":\\\"#procedure-specification-statement\\\"},{\\\"include\\\":\\\"#type-specification-statements\\\"},{\\\"include\\\":\\\"#specification-statements\\\"},{\\\"include\\\":\\\"#control-constructs\\\"},{\\\"include\\\":\\\"#control-statements\\\"},{\\\"include\\\":\\\"#execution-statements\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}],\\\"repository\\\":{\\\"IO-item-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z0-9\\\\\\\"'])\\\",\\\"comment\\\":\\\"Name list.\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[\\\\\\\\);!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"IO-keywords\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(read)|(write))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.read.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.write.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"IO generic specification.\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.formatted.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.unformatted.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(formatted)|(unformatted))\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"IO-statements\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(?:(backspace)|(close)|(endfile)|(format)|(inquire)|(open)|(read)|(rewind)|(write))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.backspace.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.close.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.endfile.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.format.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.inquire.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.open.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.control.read.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.control.rewind.fortran\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.control.write.fortran\\\"},\\\"10\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.IO.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"},{\\\"include\\\":\\\"#IO-item-list\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.backspace.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.endfile.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.format.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.print.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.read.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.rewind.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\b(?:(backspace)|(endfile)|(format)|(print)|(read)|(rewind))\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(flush)|(wait))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flush.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.wait.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flush.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\b(flush)\\\\\\\\b\\\"}]},\\\"abstract-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.fortran.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(abstract)\\\\\\\\b\\\"},\\\"abstract-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(abstract)\\\\\\\\s+(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.attribute.fortran.modern\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran.modern\\\"}},\\\"name\\\":\\\"meta.interface.abstract.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"access-attribute\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#private-attribute\\\"},{\\\"include\\\":\\\"#public-attribute\\\"}]},\\\"allocatable-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.allocatable.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(allocatable)\\\\\\\\b\\\"},\\\"allocate-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(allocate)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.allocate.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.allocate.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"arithmetic-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.subtraction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.addition.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.division.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.power.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(\\\\\\\\-)|(\\\\\\\\+)|\\\\\\\\/(?!\\\\\\\\/|\\\\\\\\=|\\\\\\\\\\\\\\\\)|(\\\\\\\\*\\\\\\\\*)|(\\\\\\\\*)\\\"},\\\"array-constructor\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*(\\\\\\\\[|\\\\\\\\(\\\\\\\\/))\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.contructor.array\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\(\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\/\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]}]},\\\"assign-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(assign)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.assign.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.assign.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.to.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(to)\\\\\\\\b\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"assignment-keyword\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(assignment)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.assignment.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Assignment generic specification.\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#assignment-operator\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"assignment-operator\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?<!\\\\\\\\/|\\\\\\\\=|\\\\\\\\<|\\\\\\\\>)(\\\\\\\\=)(?!\\\\\\\\=|\\\\\\\\>)\\\",\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"associate-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(associate)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"contentName\\\":\\\"meta.block.associate.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*associate)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"asynchronous-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.asynchronous.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(asynchronous)\\\\\\\\b\\\"},\\\"attribute-specification-statement\\\":{\\\"begin\\\":\\\"(?ix)(?=\\\\\\\\b(?:allocatable|asynchronous|contiguous |external|intrinsic|optional|parameter|pointer|private|protected|public|save|target|value|volatile)\\\\\\\\b |(bind|dimension|intent)\\\\\\\\s*\\\\\\\\( |(codimension)\\\\\\\\s*\\\\\\\\[)\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#asynchronous-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#external-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#parameter-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#target-attribute\\\"},{\\\"include\\\":\\\"#value-attribute\\\"},{\\\"include\\\":\\\"#volatile-attribute\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*::)\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.normal.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(block)\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\bdata\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"contentName\\\":\\\"meta.block.block.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*block)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"block-data-definition\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(block\\\\\\\\s*data)\\\\\\\\b(?:\\\\\\\\s+([a-z]\\\\\\\\w*)\\\\\\\\b)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.block-data.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.block-data.fortran\\\"}},\\\"end\\\":\\\"(?ix)\\\\\\\\b(?:(end\\\\\\\\s*block\\\\\\\\s*data)(?:\\\\\\\\s+(\\\\\\\\2))?|(end))\\\\\\\\b (?:\\\\\\\\s*(\\\\\\\\S((?!\\\\\\\\n).)*))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end-block-data.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.block-data.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.end-block-data.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.block-data-definition.fortran\\\"}},\\\"name\\\":\\\"meta.block-data.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.left.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"call-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(call)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.call.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.call.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*([a-z]\\\\\\\\w*)(%)([a-z]\\\\\\\\w*)\\\\\\\\b\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.accessor.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"comment\\\":\\\"type-bound subroutines\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"include\\\":\\\"#intrinsic-subroutines\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"User defined subroutine.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"comment\\\":\\\"User defined subroutine.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b(?=\\\\\\\\s*[;!\\\\\\\\n])\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"character-type\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(character)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(character)\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"codimension-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(codimension)(?=\\\\\\\\s*\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.codimension.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"}]},\\\"comments\\\":{\\\"begin\\\":\\\"!\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.fortran\\\"},\\\"common-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(common)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.common.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"concurrent-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(concurrent)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-constant\\\"},{\\\"include\\\":\\\"#numeric-constant\\\"},{\\\"include\\\":\\\"#string-constant\\\"}]},\\\"contiguous-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.contigous.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(contiguous)\\\\\\\\b\\\"},\\\"continue-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(continue)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.continue.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.continue.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]}]},\\\"control-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#named-control-constructs\\\"},{\\\"include\\\":\\\"#unnamed-control-constructs\\\"}]},\\\"control-statements\\\":{\\\"comment\\\":\\\"Statements controlling the flow of the program\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#assign-statement\\\"},{\\\"include\\\":\\\"#call-statement\\\"},{\\\"include\\\":\\\"#continue-statement\\\"},{\\\"include\\\":\\\"#cycle-statement\\\"},{\\\"include\\\":\\\"#entry-statement\\\"},{\\\"include\\\":\\\"#error-stop-statement\\\"},{\\\"include\\\":\\\"#exit-statement\\\"},{\\\"include\\\":\\\"#goto-statement\\\"},{\\\"include\\\":\\\"#pause-statement\\\"},{\\\"include\\\":\\\"#return-statement\\\"},{\\\"include\\\":\\\"#stop-statement\\\"},{\\\"include\\\":\\\"#where-statement\\\"},{\\\"include\\\":\\\"#image-control-statement\\\"}]},\\\"cpp-numeric-constant\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=.)\\\",\\\"beginCaptures\\\":{},\\\"end\\\":\\\"$\\\",\\\"endCaptures\\\":{},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.cpp\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"11\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.cpp\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?(?:(?<!')([pP])((?:\\\\\\\\+)?)((?:\\\\\\\\-)?)([0-9](?:[0-9]|(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))*))?([lLfF](?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9a-fA-FpP])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.cpp\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"\\\\\\\\G(?=[0-9.])(?!0[xXbB])([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?(?:(?<!')([eE])((?:\\\\\\\\+)?)((?:\\\\\\\\-)?)([0-9](?:[0-9]|(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))*))?([lLfF](?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9eE])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:[01]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?:[uU]|(?:[uU]ll?)|(?:[uU]LL?)|(?:ll?[uU]?)|(?:LL?[uU]?)|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:[0-7]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))+)((?:[uU]|(?:[uU]ll?)|(?:[uU]LL?)|(?:ll?[uU]?)|(?:LL?[uU]?)|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.cpp\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.cpp\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)(?:(?<!')([pP])((?:\\\\\\\\+)?)((?:\\\\\\\\-)?)([0-9](?:[0-9]|(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))*))?((?:[uU]|(?:[uU]ll?)|(?:[uU]LL?)|(?:ll?[uU]?)|(?:LL?[uU]?)|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9a-fA-FpP])\\\\\\\\w*)?$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.cpp\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.cpp\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.cpp\\\"},\\\"6\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.cpp\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.cpp\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.cpp\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.user-defined.cpp\\\"}},\\\"match\\\":\\\"\\\\\\\\G(?=[0-9.])(?!0[xXbB])([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)(?:(?<!')([eE])((?:\\\\\\\\+)?)((?:\\\\\\\\-)?)([0-9](?:[0-9]|(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))*))?((?:[uU]|(?:[uU]ll?)|(?:[uU]LL?)|(?:ll?[uU]?)|(?:LL?[uU]?)|[fF])(?!\\\\\\\\w))?((?:\\\\\\\\w(?<![0-9eE])\\\\\\\\w*)?$)\\\"},{\\\"match\\\":\\\"(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric.cpp\\\"}]}]}},\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\.?\\\\\\\\d(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])*\\\"},\\\"critical-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(critical)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.associate.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"contentName\\\":\\\"meta.block.critical.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*critical)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endassociate.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"cycle-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(cycle)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.cycle.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.fortran\\\",\\\"patterns\\\":[]}]},\\\"data-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(data)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.data.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"deallocate-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(deallocate)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.deallocate.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.deallocate.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"deferred-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.deferred.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(deferred)\\\\\\\\b\\\"},\\\"derived-type\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(class)|(type))\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(([a-z]\\\\\\\\w*)|\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.type.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.specification.type.derived.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"derived-type-component-attribute-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*\\\\\\\\b(?:private|sequence)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#sequence-attribute\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"derived-type-component-parameter-specification\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.attribute.derived-type.parameter.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.derived-type.parameter.fortran\\\"}},\\\"comment\\\":\\\"Derived type parameter.\\\",\\\"match\\\":\\\"(?ix)\\\\\\\\b(integer)\\\\\\\\s*(,)\\\\\\\\s*(kind|len)\\\\\\\\s*(?:(::)\\\\\\\\s*([a-z]\\\\\\\\w*)?)?\\\\\\\\s*(?=[;!\\\\\\\\n])\\\"},\\\"derived-type-component-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:procedure)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-component-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#pass-attribute\\\"},{\\\"include\\\":\\\"#nopass-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"derived-type-component-type-specification\\\":{\\\"begin\\\":\\\"(?ix)(?=\\\\\\\\b(?:character|class|complex|double\\\\\\\\s*precision|double\\\\\\\\s*complex|integer|logical|real|type)\\\\\\\\b(?![^:'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.derived-type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#line-continuation-operator\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-component-type.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"derived-type-contains-attribute-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:private)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.attribute-specification.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"derived-type-contains-final-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(final)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.final-procedure.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.final.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(::))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"name\\\":\\\"meta.attribute-list.derived-type-contains-final-procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#procedure-name\\\"}]},\\\"derived-type-contains-generic-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(generic)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.generic.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.generic.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-contains-generic-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?=\\\\\\\\s*[a-z])\\\",\\\"comment\\\":\\\"Name list.\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#IO-keywords\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#procedure-name\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"}]}]},\\\"derived-type-contains-procedure-specification\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:procedure)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type-contains-procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,&;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.something.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#deferred-attribute\\\"},{\\\"include\\\":\\\"#non-overridable-attribute\\\"},{\\\"include\\\":\\\"#nopass-attribute\\\"},{\\\"include\\\":\\\"#pass-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"derived-type-definition\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(type)\\\\\\\\b(?!\\\\\\\\s*(\\\\\\\\(|is\\\\\\\\b|\\\\\\\\=))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.type.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.derived-type.definition.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*(,|::))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.derived-type.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#abstract-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#extends-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"}},\\\"end\\\":\\\"(?i)(?:^|(?<=;))\\\\\\\\s*(end\\\\\\\\s*type)(?:\\\\\\\\s+(?:(\\\\\\\\1)|(\\\\\\\\w+)))?\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endtype.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"invalid.error.derived-type.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?i)^(?!\\\\\\\\s*\\\\\\\\b(?:contains|end\\\\\\\\s*type)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Derived type specification block.\\\",\\\"end\\\":\\\"(?i)^(?=\\\\\\\\s*\\\\\\\\b(?:contains|end\\\\\\\\s*type)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.specification.derived-type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#derived-type-component-attribute-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-parameter-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-component-type-specification\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Derived type contains block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*end\\\\\\\\s*type\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#derived-type-contains-attribute-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-final-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-generic-procedure-specification\\\"},{\\\"include\\\":\\\"#derived-type-contains-procedure-specification\\\"}]}]}]},\\\"derived-type-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.selector.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\%)\\\"},\\\"dimension-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(dimension)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.dimension.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"do-construct\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.enddo.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*do)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(do)\\\\\\\\s+(\\\\\\\\d{1,5})\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.do.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?i)(?:^|(?<=;))(?=\\\\\\\\s*\\\\\\\\b\\\\\\\\2\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.do.labeled.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?:\\\\\\\\s*(,)|(?!\\\\\\\\s*[;!\\\\\\\\n]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"comment\\\":\\\"Loop control.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#concurrent-attribute\\\"},{\\\"include\\\":\\\"#while-attribute\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(do)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.do.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(?:(continue)|(end\\\\\\\\s*do))\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.continue.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.enddo.fortran\\\"}},\\\"name\\\":\\\"meta.block.do.unlabeled.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?:\\\\\\\\s*(,)|(?!\\\\\\\\s*[;!\\\\\\\\n]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"comment\\\":\\\"Loop control.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.loop-control.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#concurrent-attribute\\\"},{\\\"include\\\":\\\"#while-attribute\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(continue|end\\\\\\\\s*do)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Loop body.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*\\\\\\\\b(continue|end\\\\\\\\s*do)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"dummy-variable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.fortran\\\"}},\\\"comment\\\":\\\"dummy variable\\\",\\\"match\\\":\\\"(?i)(?:^|(?<=[&,\\\\\\\\(]))\\\\\\\\s*([a-z]\\\\\\\\w*)\\\"},\\\"dummy-variable-list\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.fortran\\\"}},\\\"name\\\":\\\"meta.dummy-variable-list\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable\\\"}]},\\\"elemental-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.elemental.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(elemental)\\\\\\\\b\\\"},\\\"entry-statement\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(entry)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.entry.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.entry.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.entry.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#result-statement\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]}]}]},\\\"enum-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(enum)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.enum.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end-enum.fortran\\\"}},\\\"name\\\":\\\"meta.enum.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*enum)\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.block.specification.enum.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(enumerator)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.enumerator.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.enumerator-specification.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.enum.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#name-list\\\"}]}]}]},\\\"equivalence-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(equivalence)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.common.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\G|(,))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"puntuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"error-stop-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(error\\\\\\\\s+stop)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.errorstop.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.errorstop.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"event-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(event post|event wait)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.event.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2018 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.event.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"execution-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#allocate-statement\\\"},{\\\"include\\\":\\\"#deallocate-statement\\\"},{\\\"include\\\":\\\"#IO-statements\\\"},{\\\"include\\\":\\\"#nullify-statement\\\"}]},\\\"exit-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(exit)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.exit.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.exit.fortran\\\",\\\"patterns\\\":[]},\\\"explicit-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(interface)\\\\\\\\b(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran.modern\\\"}},\\\"name\\\":\\\"meta.interface.explicit.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"extends-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(extends)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.extends.fortran\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\)|(?=\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.fortran\\\"}]},\\\"external-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.external.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(external)\\\\\\\\b\\\"},\\\"fail-image-statement\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.fail-image.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2018 standard.\\\",\\\"match\\\":\\\"\\\\\\\\b(fail image)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.statement.fail-image.fortran\\\"},\\\"forall-construct\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(forall)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.forall.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"comment\\\":\\\"Loop control.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.loop-control.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\))(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*forall)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endforall.fortran\\\"}},\\\"name\\\":\\\"meta.block.forall.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\))(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.statement.control.forall.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},\\\"form-team-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(form team)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.form-team.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2018 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.form-team.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"function-definition\\\":{\\\"begin\\\":\\\"(?i)(?=([^:'\\\\\\\";!\\\\\\\\n](?!\\\\\\\\bend)(?!\\\\\\\\bsubroutine\\\\\\\\b))*\\\\\\\\bfunction\\\\\\\\b)\\\",\\\"comment\\\":\\\"Function program unit. Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.function.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"comment\\\":\\\"Function attribute list.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bfunction\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.function.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#elemental-attribute\\\"},{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#pure-attribute\\\"},{\\\"include\\\":\\\"#recursive-attribute\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(function)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.function.fortran\\\"}},\\\"comment\\\":\\\"Captures the function keyword\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"comment\\\":\\\"Function body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*function)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b \\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endfunction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endfunction.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.function.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"comment\\\":\\\"Rest of the first line in function construct.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.function.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#result-statement\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*function\\\\\\\\b))\\\",\\\"comment\\\":\\\"Specification and execution block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*function\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.function.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Contains block.\\\",\\\"end\\\":\\\"(?i)(?=(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*function\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"generic-interface-block-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(interface)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.interface.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.interface.generic.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(assignment)\\\\\\\\s* (\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\=)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.assignment.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.generic-interface.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"comment\\\":\\\"Assignment generic interface.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b (?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\3)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.assignment.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.assignment.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-end.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(operator)\\\\\\\\s* (\\\\\\\\()\\\\\\\\s*(?: (\\\\\\\\.[a-z]+\\\\\\\\.|\\\\\\\\=\\\\\\\\=|\\\\\\\\/\\\\\\\\=|\\\\\\\\>\\\\\\\\=|\\\\\\\\>|\\\\\\\\<|\\\\\\\\<\\\\\\\\=|\\\\\\\\-|\\\\\\\\+|\\\\\\\\/|\\\\\\\\/\\\\\\\\/|\\\\\\\\*\\\\\\\\*|\\\\\\\\*) |(\\\\\\\\S.*) )\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.operator.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-op.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"comment\\\":\\\"Operator generic interface.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b (?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\3)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.operator.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-op-end.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(?:(read)|(write))\\\\\\\\s* (\\\\\\\\()\\\\\\\\s*(?:(formatted)|(unformatted)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.read.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.write.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.formatted.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unformatted.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"comment\\\":\\\"Read/Write generic interface.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(?:(\\\\\\\\2)|(\\\\\\\\3))\\\\\\\\b\\\\\\\\s* (\\\\\\\\()\\\\\\\\s*(?:(\\\\\\\\4)|(\\\\\\\\5)|(\\\\\\\\S.*))\\\\\\\\s*(\\\\\\\\)))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.read.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.write.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.formatted.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.other.unformatted.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"invalid.error.generic-interface-block-end.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"comment\\\":\\\"Generic interface.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*interface)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b(\\\\\\\\1)\\\\\\\\b)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endinterface.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interface-procedure-statement\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"goto-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(go\\\\\\\\s*to)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.goto.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.goto.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"if-construct\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-control-expression\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(then)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.then.fortran\\\"}},\\\"contentName\\\":\\\"meta.block.if.fortran\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endif.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(else\\\\\\\\s*if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.elseif.fortran\\\"}},\\\"comment\\\":\\\"else if statement\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.then.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.label.elseif.fortran\\\"}},\\\"comment\\\":\\\"capture the label if present\\\",\\\"match\\\":\\\"(?i)\\\\\\\\b(then)\\\\\\\\b(\\\\\\\\s*[a-z]\\\\\\\\w*)?\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(else)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.else.fortran\\\"}},\\\"comment\\\":\\\"else block\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!(\\\\\\\\s*(;|!|\\\\\\\\n)))\\\",\\\"comment\\\":\\\"rest of else line\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.label.else.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.error.label.else.fortran\\\"}},\\\"comment\\\":\\\"capture the label if present\\\",\\\"match\\\":\\\"\\\\\\\\s*([a-z]\\\\\\\\w*)?\\\\\\\\s*\\\\\\\\b(\\\\\\\\w*)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(end\\\\\\\\s*if)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.if.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"image-control-statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#sync-all-statement\\\"},{\\\"include\\\":\\\"#sync-statement\\\"},{\\\"include\\\":\\\"#event-statement\\\"},{\\\"include\\\":\\\"#form-team-statement\\\"},{\\\"include\\\":\\\"#fail-image-statement\\\"}]},\\\"implicit-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(implicit)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.implicit.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.implicit.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.none.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(none)\\\\\\\\b\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"import-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(import)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.include.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.include.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(?:(::)|(?=[a-z]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#name-list\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.all.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(all)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.none.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(none)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(only)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.only.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.colon.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#name-list\\\"}]},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},\\\"include-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(include)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.include.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.include.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-constant\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"intent-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(intent)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intent.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(\\\\\\\\))|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intent.in-out.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.intent.in.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.modifier.intent.out.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(?:(in\\\\\\\\s*out)|(in)|(out))\\\\\\\\b\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"interface-block-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#abstract-interface-block-construct\\\"},{\\\"include\\\":\\\"#explicit-interface-block-construct\\\"},{\\\"include\\\":\\\"#generic-interface-block-construct\\\"}]},\\\"interface-procedure-statement\\\":{\\\"begin\\\":\\\"(?i)(?=[^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.procedure.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bprocedure\\\\\\\\b))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bprocedure\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.interface.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(procedure)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.procedure.fortran\\\"}},\\\"comment\\\":\\\"Procedure statement.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"match\\\":\\\"\\\\\\\\G\\\\\\\\s*(::)\\\"},{\\\"include\\\":\\\"#procedure-name-list\\\"}]}]},\\\"intrinsic-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.intrinsic.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(intrinsic)\\\\\\\\b\\\"},\\\"intrinsic-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(acosh|asinh|atanh|bge|bgt|ble|blt|dshiftl|dshiftr| findloc|hypot|iall|iany|image_index|iparity|is_contiguous|lcobound| leadz|mask[lr]|merge_bits|norm2|num_images|parity|popcnt|poppar| shift[alr]|storage_size|this_image|trailz|ucobound)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic functions introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(bessel_[jy][01n]|erf(c(_scaled)?)?|gamma|log_gamma)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Functions accessable through the intrinsic FORTRAN_SPECIAL_FUNCTIONS module. Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(command_argument_count|extends_type_of|is_iostat_end| is_iostat_eor|new_line|same_type_as|selected_char_kind)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic functions introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(ieee_( class|copy_sign|is_(finite|nan|negative|normal)|logb|next_after|rem| rint|scalb|selected_real_kind| support_(datatype|denormal|divide|inf|io|nan|rounding|sqrt|standard|underflow_control)| unordered|value))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Functions accessable through the intrinsic IEEE_ARITHMETIC module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(ieee_support_(flag|halting))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Functions accessable through the intrinsic IEEE_EXCEPTIONS module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(c_(associated|funloc|loc|sizeof))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"}},\\\"comment\\\":\\\"Functions accessable through the intrinsic ISO_C_BINDING module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(compiler_(options|version))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Functions accessable through the intrinsic ISO_FORTRAN_ENV module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(null)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic functions introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b(achar|adjustl|adjustr|all|allocated|associated|any|bit_size|btest|ceiling|count|cshift|digits|dot_product|eoshift|epsilon|exponent|floor|fraction|huge|iachar|iand|ibclr|ibits|ibset|ieor|ior|ishftc?| kind|lbound|len_trim|logical|matmul|maxexponent|maxloc|maxval|merge|minexponent|minloc|minval|modulo|nearest|not|pack|precision|present|product|radix|range|repeat|reshape|rrspacing|scale|scan|selected_(int|real)_kind|set_exponent|shape|size|spacing|spread|sum|tiny|transfer|transpose|trim|ubound|unpack|verify)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic functions introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\b([icd]?abs|acos|[ad]int|[ad]nint|aimag|amax[01]| amin[01]|d?asin|d?atan|d?atan2|char|conjg|[cd]?cos|d?cosh|cmplx|dble| i?dim|dmax1|dmin1|dprod|[cd]?exp|float|ichar|idint|ifix|index|int|len| lge|lgt|lle|llt|[acd]?log|[ad]?log10|max[01]?|min[01]?|[ad]?mod| (id)?nint|real|[di]?sign|[cd]?sin|d?sinh|sngl|[cd]?sqrt|d?tan|d?tanh) \\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.intrinsic.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic functions introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"intrinsic-subroutines\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(date_and_time|mvbits|random_number|random_seed| system_clock)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic subroutines introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(cpu_time)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic subroutines introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ieee_(get|set)_(rounding|underflow)_mode)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Subroutines accessable through the intrinsic IEEE_ARITHMETIC module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ieee_(get|set)_(flag|halting_mode|status))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Subroutines accessable through the intrinsic IEEE_EXCEPTIONS module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(c_f_(pointer|procpointer))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Subroutines accessable through the intrinsic ISO_C_BINDING module. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"begin\\\":\\\"(?ix)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(execute_command_line|get_command| get_command_argument|get_environment_variable|move_alloc)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Intrinsic subroutines introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]}]},\\\"invalid-character\\\":{\\\"match\\\":\\\"(?i)[^\\\\\\\\s;!\\\\\\\\n]+\\\",\\\"name\\\":\\\"invalid.error.character.fortran\\\"},\\\"invalid-word\\\":{\\\"match\\\":\\\"(?i)\\\\\\\\b\\\\\\\\w+\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.error.word.fortran\\\"},\\\"language-binding-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(bind)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.bind.fortran\\\"}},\\\"comment\\\":\\\"Introduced in Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?:\\\\\\\\)|(?=\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)\\\\\\\\b(c)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.fortran\\\"},{\\\"include\\\":\\\"#dummy-variable\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"line-continuation-operator\\\":{\\\"comment\\\":\\\"Operator that allows a line to be continued on the next line.\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"match\\\":\\\"(?:^|(?<=;))\\\\\\\\s*(&)\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"contentName\\\":\\\"meta.line-continuation.fortran\\\",\\\"end\\\":\\\"(?i)^(?:\\\\\\\\s*(&))?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\S[^!]*\\\",\\\"name\\\":\\\"invalid.error.line-cont.fortran\\\"}]}]},\\\"logical-constant\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.logical.false.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.language.logical.true.fortran\\\"}},\\\"comment\\\":\\\"Logical constants\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*(?:(\\\\\\\\.false\\\\\\\\.)|(\\\\\\\\.true\\\\\\\\.))\\\"},\\\"logical-control-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.expression.control.logical.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"}]},\\\"logical-operators\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?ix)(\\\\\\\\s*\\\\\\\\.(and|eq|eqv|le|lt|ge|gt|ne|neqv|not|or)\\\\\\\\.)\\\",\\\"name\\\":\\\"keyword.logical.fortran\\\"},{\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(\\\\\\\\=\\\\\\\\=|\\\\\\\\/\\\\\\\\=|\\\\\\\\>\\\\\\\\=|(?<!\\\\\\\\=)\\\\\\\\>|\\\\\\\\<\\\\\\\\=|\\\\\\\\<)\\\",\\\"name\\\":\\\"keyword.logical.fortran.modern\\\"}]},\\\"logical-type\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(logical)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.logical.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.character.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(logical)\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"module-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.module.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?ix)\\\\\\\\s*\\\\\\\\b(module)\\\\\\\\b(?=\\\\\\\\s*(?:[;!\\\\\\\\n]| [^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\b(?:function|procedure|subroutine)\\\\\\\\b))\\\"},\\\"module-definition\\\":{\\\"begin\\\":\\\"(?ix)(?=\\\\\\\\b(module)\\\\\\\\b)(?![^'\\\\\\\";!\\\\\\\\n]* \\\\\\\\b(?:function|procedure|subroutine)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.module.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.program.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(module)\\\\\\\\b\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"}},\\\"comment\\\":\\\"Module body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(?:(end\\\\\\\\s*module)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b \\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endmodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endmodule.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.module-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"comment\\\":\\\"Module specification block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*module\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.module.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Module contains block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*module\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"name-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"comment\\\":\\\"Name list.\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[\\\\\\\\);!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#assignment-keyword\\\"},{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"named-control-constructs\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?ix)([a-z]\\\\\\\\w*)\\\\\\\\s*(:)(?=\\\\\\\\s*(?:associate|block(?!\\\\\\\\s*data)|critical|do|forall|if|select\\\\\\\\s*case|select\\\\\\\\s*type|select\\\\\\\\s*rank|where)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"contentName\\\":\\\"meta.named-construct.fortran.modern\\\",\\\"end\\\":\\\"(?i)(?!\\\\\\\\s*\\\\\\\\b(?:associate|block(?!\\\\\\\\s*data)|critical|do|forall|if|select\\\\\\\\s*case|select\\\\\\\\s*type|select\\\\\\\\s*rank|where)\\\\\\\\b)(?:\\\\\\\\b(\\\\\\\\1)\\\\\\\\b)?([^\\\\\\\\s;!\\\\\\\\n]*?)?(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.label.end.name.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.error.named-control-constructs.fortran.modern\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#unnamed-control-constructs\\\"}]},\\\"namelist-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(namelist)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.namelist.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"non-intrinsic-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non-intrinsic.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(non_intrinsic)\\\\\\\\b\\\"},\\\"non-overridable-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non-overridable.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(non_overridable)\\\\\\\\b\\\"},\\\"nopass-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.nopass.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(nopass)\\\\\\\\b\\\"},\\\"nullify-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(nullify)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.nullify.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.nullify.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"numeric-constant\\\":{\\\"comment\\\":\\\"Numeric constants\\\",\\\"match\\\":\\\"(?ix)[\\\\\\\\+\\\\\\\\-]?(\\\\\\\\b\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*|\\\\\\\\.\\\\\\\\d+) (_\\\\\\\\w+|d[\\\\\\\\+\\\\\\\\-]?\\\\\\\\d+|e[\\\\\\\\+\\\\\\\\-]?\\\\\\\\d+(_\\\\\\\\w+)?)?(?![a-z_])\\\",\\\"name\\\":\\\"constant.numeric.fortran\\\"},\\\"numeric-type\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(complex)|(double\\\\\\\\s*precision)|(double\\\\\\\\s*complex)|(integer)|(real))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.complex.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.double.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.doublecomplex.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.real.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.complex.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.double.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.doublecomplex.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.integer.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"storage.type.real.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.dimension.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.fortran\\\"}},\\\"match\\\":\\\"(?ix)\\\\\\\\b(?:(complex)|(double\\\\\\\\s*precision)|(double\\\\\\\\s*complex)|(integer)|(real)|(dimension))\\\\\\\\b(?:\\\\\\\\s*(\\\\\\\\*)\\\\\\\\s*(\\\\\\\\d*))?\\\"}]},\\\"operator-keyword\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(operator)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.generic-spec.operator.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Operator generic specification.\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic-operators\\\"},{\\\"include\\\":\\\"#logical-operators\\\"},{\\\"include\\\":\\\"#user-defined-operators\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#arithmetic-operators\\\"},{\\\"include\\\":\\\"#assignment-operator\\\"},{\\\"include\\\":\\\"#derived-type-operators\\\"},{\\\"include\\\":\\\"#logical-operators\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#user-defined-operators\\\"}]},\\\"optional-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.optional.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(optional)\\\\\\\\b\\\"},\\\"parameter-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.parameter.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(parameter)\\\\\\\\b\\\"},\\\"parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"parentheses-dummy-variables\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-call-dummy-variable\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#array-constructor\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#intrinsic-functions\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"pass-attribute\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pass)\\\\\\\\s*\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pass.fortran\\\"}},\\\"comment\\\":\\\"Pass attribute with argument.\\\",\\\"end\\\":\\\"\\\\\\\\)|(?=\\\\\\\\n)\\\",\\\"patterns\\\":[]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pass.fortran\\\"}},\\\"comment\\\":\\\"Pass attribute without argument.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pass)\\\\\\\\b\\\"}]},\\\"pause-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pause)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.pause.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.pause.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"pointer-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.pointer.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(pointer)\\\\\\\\b\\\"},\\\"pointer-operators\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(\\\\\\\\=\\\\\\\\>)\\\",\\\"name\\\":\\\"keyword.other.point.fortran\\\"},\\\"preprocessor\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#:?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.indicator.fortran\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.preprocessor\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-if-construct\\\"},{\\\"include\\\":\\\"#preprocessor-statements\\\"}]},\\\"preprocessor-arithmetic-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.subtraction.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.addition.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.division.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.multiplication.fortran\\\"}},\\\"comment\\\":\\\"division regex is different than in main fortran\\\",\\\"match\\\":\\\"(\\\\\\\\-)|(\\\\\\\\+)|(\\\\\\\\/)|(\\\\\\\\*)\\\"},\\\"preprocessor-assignment-operator\\\":{\\\"comment\\\":\\\"assignments with = are not allowed\\\",\\\"match\\\":\\\"(?<!\\\\\\\\=)(\\\\\\\\=)(?!\\\\\\\\=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.preprocessor.fortran\\\"},\\\"preprocessor-comments\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.preprocessor\\\"},\\\"preprocessor-constants\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"}]},\\\"preprocessor-define-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(define)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.define.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-constants\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"preprocessor-defined-function\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.defined.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(defined)\\\\\\\\b\\\"},\\\"preprocessor-error-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(error)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.error.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"preprocessor-if-construct\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.if.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.conditional.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-defined-function\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ifdef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.ifdef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(ifndef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.ifndef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(else)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.else.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(elif)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.elif.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#cpp-numeric-constant\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"},{\\\"include\\\":\\\"#preprocessor-defined-function\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(endif)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.endif.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"}]}]},\\\"preprocessor-include-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*(include)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.include.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.preprocessor.fortran\\\"},{\\\"include\\\":\\\"#line-continuation-operator\\\"}]},\\\"preprocessor-line-continuation-operator\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.preprocessor.fortran\\\"}},\\\"end\\\":\\\"(?i)^\\\"},\\\"preprocessor-logical-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.and.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.equals.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.not_equals.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.or.fortran\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.less_eq.fortran\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.more_eq.fortran\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.less.fortran\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.more.fortran\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.complementary.fortran\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.xor.fortran\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.bitand.fortran\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.not.fortran\\\"},\\\"13\\\":{\\\"name\\\":\\\"keyword.operator.logical.preprocessor.bitor.fortran\\\"}},\\\"comment\\\":\\\"and:&&, bitand:&, or:||, bitor:|, not eq:!=, not:!, xor:^, compl:~\\\",\\\"match\\\":\\\"(&&)|(==)|(\\\\\\\\!=)|(\\\\\\\\|\\\\\\\\|)|(\\\\\\\\<\\\\\\\\=)|(\\\\\\\\>=)|(\\\\\\\\<)|(\\\\\\\\>)|(~)|(\\\\\\\\^)|(&)|(\\\\\\\\!)|(\\\\\\\\|)\\\",\\\"name\\\":\\\"keyword.operator.logical.preprocessor.fortran\\\"},\\\"preprocessor-operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"},{\\\"include\\\":\\\"#preprocessor-logical-operators\\\"},{\\\"include\\\":\\\"#preprocessor-arithmetic-operators\\\"}]},\\\"preprocessor-pragma-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(pragma)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.pragma.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-string-constant\\\"}]},\\\"preprocessor-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-define-statement\\\"},{\\\"include\\\":\\\"#preprocessor-error-statement\\\"},{\\\"include\\\":\\\"#preprocessor-include-statement\\\"},{\\\"include\\\":\\\"#preprocessor-preprocessor-pragma-statement\\\"},{\\\"include\\\":\\\"#preprocessor-undefine-statement\\\"}]},\\\"preprocessor-string-constant\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"comment\\\":\\\"Double quote string\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.double.include.preprocessor.fortran\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.preprocessor.fortran\\\"}},\\\"comment\\\":\\\"Single quote string\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.preprocessor.fortran\\\"}},\\\"name\\\":\\\"string.quoted.single.include.preprocessor.fortran\\\"}]},\\\"preprocessor-undefine-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(undef)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.preprocessor.undef.fortran\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.undef.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-comments\\\"},{\\\"include\\\":\\\"#preprocessor-line-continuation-operator\\\"}]},\\\"private-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.private.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(private)\\\\\\\\b\\\"},\\\"procedure-call-dummy-variable\\\":{\\\"match\\\":\\\"(?i)\\\\\\\\s*([a-z]\\\\\\\\w*)(?=\\\\\\\\s*\\\\\\\\=)(?!\\\\\\\\s*\\\\\\\\=\\\\\\\\=)\\\",\\\"name\\\":\\\"variable.parameter.dummy-variable.fortran.modern\\\"},\\\"procedure-definition\\\":{\\\"begin\\\":\\\"(?i)(?=[^'\\\\\\\";!\\\\\\\\n]*\\\\\\\\bmodule\\\\\\\\s+procedure\\\\\\\\b)\\\",\\\"comment\\\":\\\"Procedure program unit. Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.procedure.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(module\\\\\\\\s+procedure)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.procedure.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"}},\\\"comment\\\":\\\"Procedure body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*procedure)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b \\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endprocedure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endprocedure.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.procedure-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"comment\\\":\\\"Rest of the first line in procedure construct - should be empty.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\s*(?:contains\\\\\\\\b|end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"comment\\\":\\\"Specification and execution block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*(?:contains\\\\\\\\b|end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Contains block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*procedure\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]}]}]},\\\"procedure-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.procedure.fortran\\\"}},\\\"comment\\\":\\\"Procedure name.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\"},\\\"procedure-name-list\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"comment\\\":\\\"Name list.\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?!\\\\\\\\s*\\\\\\\\n)\\\",\\\"end\\\":\\\"(,)|(?=[!;\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-name\\\"},{\\\"include\\\":\\\"#pointer-operators\\\"}]}]},\\\"procedure-specification-statement\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(?:procedure)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.procedure.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#procedure-type\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.procedure.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#procedure-name-list\\\"}]},\\\"procedure-type\\\":{\\\"comment\\\":\\\"Introduced in the Fortran ???? standard.\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(procedure)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"contentName\\\":\\\"meta.type-spec.fortran\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#procedure-name\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.procedure.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(procedure)\\\\\\\\b\\\"}]},\\\"program-definition\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(program)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.program.fortran\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.program.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(program)\\\\\\\\b\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.program.fortran\\\"}},\\\"comment\\\":\\\"Program body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(?:(end\\\\\\\\s*program)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b\\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endprogram.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.program.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.endprogram.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.program-definition.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"comment\\\":\\\"Program specification block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*program\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.program.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Program contains block.\\\",\\\"end\\\":\\\"(?i)(?=(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*program\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"protected-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.protected.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(protected)\\\\\\\\b\\\"},\\\"public-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.public.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(public)\\\\\\\\b\\\"},\\\"pure-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.impure.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.pure.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(impure)|(pure))\\\\\\\\b\\\"},\\\"recursive-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.non_recursive.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.recursive.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(?:(non_recursive)|(recursive))\\\\\\\\b\\\"},\\\"result-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(result)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.result.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable\\\"}]},\\\"return-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(return)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.return.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.return.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"save-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.save.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(save)\\\\\\\\b\\\"},\\\"select-case-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*case)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selectcase.fortran\\\"}},\\\"comment\\\":\\\"Select case construct. Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.case.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(case)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.case.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"select-rank-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*rank)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selectrank.fortran\\\"}},\\\"comment\\\":\\\"Select rank construct. Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.rank.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(rank)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.rank.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"select-type-construct\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(select\\\\\\\\s*type)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.selecttype.fortran\\\"}},\\\"comment\\\":\\\"Select type construct. Introduced in the Fortran 2003 standard.\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*select)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endselect.fortran\\\"}},\\\"name\\\":\\\"meta.block.select.type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(?:(class)|(type))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.class.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.type.fortran\\\"}},\\\"end\\\":\\\"(?i)(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(default)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.is.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(is)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"sequence-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.sequence.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 20?? standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(sequence)\\\\\\\\b\\\"},\\\"specification-statements\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attribute-specification-statement\\\"},{\\\"include\\\":\\\"#common-statement\\\"},{\\\"include\\\":\\\"#data-statement\\\"},{\\\"include\\\":\\\"#equivalence-statement\\\"},{\\\"include\\\":\\\"#implicit-statement\\\"},{\\\"include\\\":\\\"#namelist-statement\\\"},{\\\"include\\\":\\\"#use-statement\\\"}]},\\\"stop-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(stop)\\\\\\\\b(?:\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.stop.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.label.stop.stop\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.control.stop.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#string-operators\\\"},{\\\"include\\\":\\\"#invalid-character\\\"}]},\\\"string-constant\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 1977 standard.\\\",\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fortran\\\"}},\\\"comment\\\":\\\"String\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fortran\\\"}},\\\"name\\\":\\\"string.quoted.single.fortran\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\",\\\"name\\\":\\\"constant.character.escape.apostrophe.fortran\\\"}]},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fortran\\\"}},\\\"comment\\\":\\\"String\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fortran\\\"}},\\\"name\\\":\\\"string.quoted.double.fortran\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.quote.fortran\\\"}]}]},\\\"string-line-continuation-operator\\\":{\\\"begin\\\":\\\"(&)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"comment\\\":\\\"Operator that allows a line to be continued on the next line.\\\",\\\"end\\\":\\\"(?i)^(?:(?=\\\\\\\\s*[^\\\\\\\\s!&])|\\\\\\\\s*(&))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.line-continuation.fortran\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\S.*\\\",\\\"name\\\":\\\"invalid.error.string-line-cont.fortran\\\"}]},\\\"string-operators\\\":{\\\"comment\\\":\\\"Introduced in the Fortran 19?? standard.\\\",\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\/)\\\",\\\"name\\\":\\\"keyword.other.concatination.fortran\\\"},\\\"submodule-definition\\\":{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\b(submodule)\\\\\\\\s*\\\\\\\\()\\\",\\\"comment\\\":\\\"Introduced in the Fortran 2008 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.submodule.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(submodule)\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*(\\\\\\\\w+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.submodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.class.submodule.fortran\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"patterns\\\":[]},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.module.submodule.fortran\\\"}},\\\"comment\\\":\\\"Submodule body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\s*\\\\\\\\b(?:(end\\\\\\\\s*submodule)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b \\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endsubmodule.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.module.submodule.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endsubmodule.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.submodule.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"comment\\\":\\\"Submodule specification block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*submodule\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.submodule.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Submodule contains block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\s*(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*submodule\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"subroutine-definition\\\":{\\\"begin\\\":\\\"(?i)(?=([^:'\\\\\\\";!\\\\\\\\n](?!\\\\\\\\bend))*\\\\\\\\bsubroutine\\\\\\\\b)\\\",\\\"comment\\\":\\\"Subroutine program unit. Introduced in the Fortran 1977 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\G\\\\\\\\s*(?!\\\\\\\\bsubroutine\\\\\\\\b))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\bsubroutine\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.attribute-list.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#elemental-attribute\\\"},{\\\"include\\\":\\\"#module-attribute\\\"},{\\\"include\\\":\\\"#pure-attribute\\\"},{\\\"include\\\":\\\"#recursive-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(subroutine)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.subroutine.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"}},\\\"comment\\\":\\\"Subroutine body.\\\",\\\"end\\\":\\\"(?ix)\\\\\\\\b(?:(end\\\\\\\\s*subroutine)(?:\\\\\\\\s+([a-z_]\\\\\\\\w*))?|(end))\\\\\\\\b \\\\\\\\s*([^;!\\\\\\\\n]+)?(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.endsubroutine.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.subroutine.fortran\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.endsubroutine.fortran\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.error.subroutine.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"comment\\\":\\\"Rest of the first line in subroutine construct.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.first-line.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dummy-variable-list\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"}]},{\\\"begin\\\":\\\"(?i)(?!\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"comment\\\":\\\"Specification and execution block.\\\",\\\"end\\\":\\\"(?i)(?=\\\\\\\\b(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.specification.subroutine.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\b(contains)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.contains.fortran\\\"}},\\\"comment\\\":\\\"Contains block.\\\",\\\"end\\\":\\\"(?i)(?=(?:end\\\\\\\\s*[;!\\\\\\\\n]|end\\\\\\\\s*subroutine\\\\\\\\b))\\\",\\\"name\\\":\\\"meta.block.contains.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"sync-all-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(sync all|sync memory)(\\\\\\\\s*(?=\\\\\\\\())?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sync-all-memory.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2018 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.sync-all-memory.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"sync-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(sync images|sync team)\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.sync-images-team.fortran\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.parentheses.left.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2018 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.parentheses.right.fortran\\\"}},\\\"name\\\":\\\"meta.statement.sync-images-team.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"}]},\\\"target-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.target.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(target)\\\\\\\\b\\\"},\\\"type-specification-statements\\\":{\\\"begin\\\":\\\"(?ix)(?=\\\\\\\\b(?:character|class|complex|double\\\\\\\\s*precision|double\\\\\\\\s*complex|integer|logical|real|type|dimension)\\\\\\\\b(?![^'\\\\\\\";!\\\\\\\\n:]*\\\\\\\\bfunction\\\\\\\\b))\\\",\\\"comment\\\":\\\"Supported types for function and escape :: if function is used as a variable name (which is bad practice).\\\",\\\"end\\\":\\\"(?=[\\\\\\\\);!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.specification.type.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#types\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::))\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.type-specification-statements.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)|^|(?<=&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,&;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#access-attribute\\\"},{\\\"include\\\":\\\"#allocatable-attribute\\\"},{\\\"include\\\":\\\"#asynchronous-attribute\\\"},{\\\"include\\\":\\\"#codimension-attribute\\\"},{\\\"include\\\":\\\"#contiguous-attribute\\\"},{\\\"include\\\":\\\"#dimension-attribute\\\"},{\\\"include\\\":\\\"#external-attribute\\\"},{\\\"include\\\":\\\"#intent-attribute\\\"},{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#language-binding-attribute\\\"},{\\\"include\\\":\\\"#optional-attribute\\\"},{\\\"include\\\":\\\"#parameter-attribute\\\"},{\\\"include\\\":\\\"#pointer-attribute\\\"},{\\\"include\\\":\\\"#protected-attribute\\\"},{\\\"include\\\":\\\"#save-attribute\\\"},{\\\"include\\\":\\\"#target-attribute\\\"},{\\\"include\\\":\\\"#value-attribute\\\"},{\\\"include\\\":\\\"#volatile-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"include\\\":\\\"#name-list\\\"}]},\\\"types\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#character-type\\\"},{\\\"include\\\":\\\"#derived-type\\\"},{\\\"include\\\":\\\"#logical-type\\\"},{\\\"include\\\":\\\"#numeric-type\\\"}]},\\\"unnamed-control-constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#associate-construct\\\"},{\\\"include\\\":\\\"#block-construct\\\"},{\\\"include\\\":\\\"#critical-construct\\\"},{\\\"include\\\":\\\"#do-construct\\\"},{\\\"include\\\":\\\"#forall-construct\\\"},{\\\"include\\\":\\\"#if-construct\\\"},{\\\"include\\\":\\\"#select-case-construct\\\"},{\\\"include\\\":\\\"#select-type-construct\\\"},{\\\"include\\\":\\\"#select-rank-construct\\\"},{\\\"include\\\":\\\"#where-construct\\\"}]},\\\"use-statement\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\b(use)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.use.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"name\\\":\\\"meta.statement.use.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*(,|::|\\\\\\\\())\\\",\\\"comment\\\":\\\"Attribute list.\\\",\\\"contentName\\\":\\\"meta.attribute-list.namelist.fortran\\\",\\\"end\\\":\\\"(::)|(?=[;!\\\\\\\\n])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.fortran\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[,;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#intrinsic-attribute\\\"},{\\\"include\\\":\\\"#non-intrinsic-attribute\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]}]},{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.class.module.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.fortran\\\"}},\\\"end\\\":\\\"(?=::|[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(only\\\\\\\\s*:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.only.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?=\\\\\\\\s*[a-z])\\\",\\\"contentName\\\":\\\"meta.name-list.fortran\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator-keyword\\\"},{\\\"include\\\":\\\"$base\\\"}]}]}]}]},\\\"user-defined-operators\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.user-defined.fortran\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\s*(\\\\\\\\.[a-z]+\\\\\\\\.)\\\"},\\\"value-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.value.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(value)\\\\\\\\b\\\"},\\\"variable\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(?=[a-z])\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.parameter.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#derived-type-operators\\\"},{\\\"include\\\":\\\"#parentheses-dummy-variables\\\"},{\\\"include\\\":\\\"#word\\\"}]},\\\"volatile-attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.volatile.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 2003 standard.\\\",\\\"match\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(volatile)\\\\\\\\b\\\"},\\\"where-construct\\\":{\\\"patterns\\\":[{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?i)\\\\\\\\b(where)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.where.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1990 standard.\\\",\\\"end\\\":\\\"(?<!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical-control-expression\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\))(?=\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"(?i)\\\\\\\\b(end\\\\\\\\s*where)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.endwhere.fortran\\\"}},\\\"name\\\":\\\"meta.block.where.fortran\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\s*\\\\\\\\b(else\\\\\\\\s*where)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.elsewhere.fortran\\\"}},\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?i)(?<=\\\\\\\\))(?!\\\\\\\\s*[;!\\\\\\\\n])\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"meta.statement.control.where.fortran\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"while-attribute\\\":{\\\"begin\\\":\\\"(?i)\\\\\\\\G\\\\\\\\s*\\\\\\\\b(while)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.fortran\\\"}},\\\"comment\\\":\\\"Introduced in the Fortran 1995 standard.\\\",\\\"end\\\":\\\"(?=[;!\\\\\\\\n])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#invalid-word\\\"}]},\\\"word\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?:\\\\\\\\G|(?<=\\\\\\\\%))\\\\\\\\s*\\\\\\\\b([a-z]\\\\\\\\w*)\\\\\\\\b\\\"}]}},\\\"scopeName\\\":\\\"source.fortran.free\\\",\\\"aliases\\\":[\\\"f90\\\",\\\"f95\\\",\\\"f03\\\",\\\"f08\\\",\\\"f18\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fortran-free-form.mjs\n"));

/***/ })

}]);