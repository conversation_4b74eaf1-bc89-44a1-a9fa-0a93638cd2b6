"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_haskell_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/haskell.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/haskell.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Haskell\\\",\\\"fileTypes\\\":[\\\"hs\\\",\\\"hs-boot\\\",\\\"hsig\\\"],\\\"name\\\":\\\"haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#liquid_haskell\\\"},{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#numeric_literals\\\"},{\\\"include\\\":\\\"#string_literal\\\"},{\\\"include\\\":\\\"#char_literal\\\"},{\\\"match\\\":\\\"(?<!@|#)-\\\\\\\\}\\\",\\\"name\\\":\\\"invalid\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(\\\\\\\\))\\\",\\\"name\\\":\\\"constant.language.unit.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"constant.language.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*,[\\\\\\\\s,]*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*,[\\\\\\\\s,]*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\[)\\\\\\\\s*(\\\\\\\\])\\\",\\\"name\\\":\\\"constant.language.empty-list.haskell\\\"},{\\\"begin\\\":\\\"(\\\\\\\\b(?<!')(module)|^(signature))(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.module.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.signature.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')where\\\\\\\\b(?!'))\\\",\\\"name\\\":\\\"meta.declaration.module.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#module_exports\\\"},{\\\"match\\\":\\\"[a-z]+\\\",\\\"name\\\":\\\"invalid\\\"}]},{\\\"include\\\":\\\"#ffi\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(class)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.class.haskell\\\"}},\\\"end\\\":\\\"(?=(?<!')\\\\\\\\bwhere\\\\\\\\b(?!'))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.class.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(data|newtype)(?:\\\\\\\\s+(instance))?\\\\\\\\s+((?:(?!(?:(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))|(?:\\\\\\\\b(?<!')(?:where|deriving)\\\\\\\\b(?!'))|{-).)*)(?=\\\\\\\\b(?<!'')where\\\\\\\\b(?!''))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=(?<!')\\\\\\\\bderiving\\\\\\\\b(?!'))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.$2.generalized.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(where)\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.where.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#gadt_constructor\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!')(where)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.where.haskell\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#gadt_constructor\\\"}]},{\\\"include\\\":\\\"#role_annotation\\\"},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(pattern)\\\\\\\\s+(.*?)\\\\\\\\s+(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#data_constructor\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.pattern.type.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(pattern)\\\\\\\\b(?!')\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.pattern.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(data|newtype)(?:\\\\\\\\s+(family|instance))?\\\\\\\\s+(((?!(?:(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))|(?:\\\\\\\\b(?<!')(?:where|deriving)\\\\\\\\b(?!'))|{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.$3.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.$2.algebraic.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"include\\\":\\\"#adt_constructor\\\"},{\\\"include\\\":\\\"#context\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)\\\\\\\\s+(family)\\\\\\\\b(?!')(((?!(?:(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))|\\\\\\\\b(?<!')where\\\\\\\\b(?!')|{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.family.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.type.family.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)(?:\\\\\\\\s+(instance))?\\\\\\\\s+(((?!(?:(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:=|--+|::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))|{-).)*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.type.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(instance)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')(where)\\\\\\\\b(?!'))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.declaration.instance.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(import)(\\\\\\\\b(?!'))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.import.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!')(where)\\\\\\\\b(?!'))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.import.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#where\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"}},\\\"match\\\":\\\"(qualified|as|hiding)\\\"},{\\\"include\\\":\\\"#module_name\\\"},{\\\"include\\\":\\\"#module_exports\\\"}]},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#layout_herald\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"include\\\":\\\"#infix_op\\\"}]}},\\\"match\\\":\\\"^\\\\\\\\s*(infix[lr]?)\\\\\\\\s+(.*)\\\",\\\"name\\\":\\\"meta.fixity-declaration.haskell\\\"},{\\\"include\\\":\\\"#overloaded_label\\\"},{\\\"include\\\":\\\"#type_application\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#fun_decl\\\"},{\\\"include\\\":\\\"#qualifier\\\"},{\\\"include\\\":\\\"#data_constructor\\\"},{\\\"include\\\":\\\"#start_type_signature\\\"},{\\\"include\\\":\\\"#prefix_op\\\"},{\\\"include\\\":\\\"#infix_op\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"}},\\\"end\\\":\\\"(#)(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#quasi_quote\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#record\\\"}],\\\"repository\\\":{\\\"adt_constructor\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:(=)|(\\\\\\\\|))(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.eq.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.pipe.haskell\\\"}},\\\"end\\\":\\\"(?:\\\\\\\\G|^)\\\\\\\\s*(?:(?:(?<!')\\\\\\\\b((?:[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'\\\\\\\\.])+)|('?(?<paren>\\\\\\\\((?:[^\\\\\\\\(\\\\\\\\)]*|\\\\\\\\g<paren>)*\\\\\\\\)))|('?(?<brac>\\\\\\\\((?:[^\\\\\\\\[\\\\\\\\]]*|\\\\\\\\g<brac>)*\\\\\\\\])))\\\\\\\\s*(?:(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(:[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)|(`)([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)(`)))|(?:(?<!')\\\\\\\\b([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*))|(\\\\\\\\()\\\\\\\\s*(:[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"11\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"12\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"13\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"include\\\":\\\"#context\\\"}]}]},\\\"block_comment\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"-\\\\\\\\}\\\",\\\"name\\\":\\\"comment.block.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]},\\\"char_literal\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.character.escape.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.escape.octal.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.character.escape.hexadecimal.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.character.escape.control.haskell\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.haskell\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'])(')(?:[\\\\\\\\ -\\\\\\\\[\\\\\\\\]-~]|(\\\\\\\\\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\\\\\\\\\&]))|(\\\\\\\\\\\\\\\\o[0-7]+)|(\\\\\\\\\\\\\\\\x[0-9A-Fa-f]+)|(\\\\\\\\\\\\\\\\\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\\\\\\\\\^_]))(')\\\",\\\"name\\\":\\\"string.quoted.single.haskell\\\"},\\\"comma\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.haskell\\\"},\\\"comment_like\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cpp\\\"},{\\\"include\\\":\\\"#pragma\\\"},{\\\"include\\\":\\\"#comments\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(--\\\\\\\\s[\\\\\\\\|\\\\\\\\$])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"end\\\":\\\"(?=^(?!\\\\\\\\1--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])))\\\",\\\"name\\\":\\\"comment.block.documentation.haskell\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(--\\\\\\\\s[\\\\\\\\^\\\\\\\\*])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.documentation.haskell\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\{-\\\\\\\\s?[\\\\\\\\|\\\\\\\\$\\\\\\\\*\\\\\\\\^]\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"-\\\\\\\\}\\\",\\\"name\\\":\\\"comment.block.documentation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_comment\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.haskell\\\"}},\\\"comment\\\":\\\"Operators may begin with '--' as long as they are not entirely composed of '-' characters. This means comments can't be immediately followed by an allowable operator character.\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.haskell\\\"}]},{\\\"include\\\":\\\"#block_comment\\\"}]},\\\"context\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.big-arrow.haskell\\\"}},\\\"match\\\":\\\"(.*)(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(=>|⇒)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\"},\\\"cpp\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.preprocessor.c\\\"}},\\\"comment\\\":\\\"In addition to Haskell's \\\\\\\"native\\\\\\\" syntax, GHC permits the C preprocessor to be run on a source file.\\\",\\\"match\\\":\\\"^(#).*$\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"data_constructor\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*(?![\\\\\\\\.'\\\\\\\\w])\\\",\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"deriving\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(deriving)\\\\\\\\s+(?:(via|stock|newtype|anyclass)\\\\\\\\s+)?\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$3.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.deriving.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b(instance)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.instance.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$1.haskell\\\"}},\\\"match\\\":\\\"(?<!')\\\\\\\\b(via|stock|newtype|anyclass)\\\\\\\\b(?!')\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(deriving)(?:\\\\\\\\s+(stock|newtype|anyclass))?\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$2.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.deriving.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.deriving.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.$2.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.deriving.strategy.via.haskell\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}},\\\"match\\\":\\\"(deriving)(?:\\\\\\\\s+(stock|newtype|anyclass))?\\\\\\\\s+([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)(\\\\\\\\s+(via)\\\\\\\\s+(.*)$)?\\\",\\\"name\\\":\\\"meta.deriving.haskell\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b(via)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.deriving.strategy.via.haskell\\\"}]},\\\"double_colon\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\\\\\\s*\\\"},\\\"export_constructs\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(pattern)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.pattern.haskell\\\"}},\\\"end\\\":\\\"([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)|(\\\\\\\\()\\\\\\\\s*(:[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(type)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"}},\\\"end\\\":\\\"([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)|(\\\\\\\\()\\\\\\\\s*([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"}]},{\\\"match\\\":\\\"(?<!')\\\\\\\\b[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\",\\\"name\\\":\\\"entity.name.function.haskell\\\"},{\\\"match\\\":\\\"(?<!')\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\",\\\"name\\\":\\\"storage.type.haskell\\\"},{\\\"include\\\":\\\"#record_wildcard\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#prefix_op\\\"}]},\\\"ffi\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(foreign)\\\\\\\\s+(import|export)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.foreign.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.$3.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.$3.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.calling-convention.$1.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(ccall|cplusplus|dotnet|jvm|stdcall|prim|capi)\\\\\\\\s+\\\"},{\\\"begin\\\":\\\"(?=\\\\\\\")|(?=\\\\\\\\b(?<!')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)\\\\\\\\b(?!'))\\\",\\\"end\\\":\\\"(?=(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.safety.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(safe|unsafe|interruptible)\\\\\\\\b(?!')\\\\\\\\s*(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")?\\\\\\\\s*(?:(?:\\\\\\\\b(?<!'')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)\\\\\\\\b(?!'))|(?:\\\\\\\\(\\\\\\\\s*(?!--+\\\\\\\\))([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*\\\\\\\\)))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.safety.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(safe|unsafe|interruptible)\\\\\\\\b(?!')\\\\\\\\s*(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")?\\\\\\\\s*$\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.foreign.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\"\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\b(?<!'')([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)\\\\\\\\b(?!'))|(?:(\\\\\\\\()\\\\\\\\s*(?!--+\\\\\\\\))([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\)))\\\"}]},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"float_literals\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.floating.decimal.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.floating.hexadecimal.haskell\\\"}},\\\"comment\\\":\\\"Floats are decimal or hexadecimal\\\",\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:([0-9][_0-9]*\\\\\\\\.[0-9][_0-9]*(?:[eE][-+]?[0-9][_0-9]*)?|[0-9][_0-9]*[eE][-+]?[0-9][_0-9]*)|(0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*\\\\\\\\.[0-9a-fA-F][_0-9a-fA-F]*(?:[pP][-+]?[0-9][_0-9]*)?|0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*[pP][-+]?[0-9][_0-9]*))\\\\\\\\b(?!')\\\"},\\\"forall\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?<!')(forall|∀)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.forall.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\.)|(->|→)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.period.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arrow.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_variable\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"fun_decl\\\":{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?<fn>(?:[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\#*|\\\\\\\\(\\\\\\\\s*(?!--+\\\\\\\\))[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),:;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']][\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*\\\\\\\\s*\\\\\\\\))(?:\\\\\\\\s*,\\\\\\\\s*\\\\\\\\g<fn>)?)\\\\\\\\s*(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\),;\\\\\\\\]`}_\\\\\\\"']])(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\(,;\\\\\\\\[`{_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#prefix_op\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])((<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']|[^\\\\\\\\p{S}\\\\\\\\p{P}]))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.function.type-declaration.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},\\\"gadt_constructor\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?:(\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)|(\\\\\\\\()\\\\\\\\s*(:[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\b(?<!'')deriving\\\\\\\\b(?!'))|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}]*)|(\\\\\\\\()\\\\\\\\s*(:[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]*)\\\\\\\\s*(\\\\\\\\))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.other.operator.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#deriving\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#record_decl\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"infix_op\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.infix.haskell\\\"}},\\\"comment\\\":\\\"In case this regex seems overly general, note that Haskell permits  the definition of new operators which can be nearly any string of  punctuation characters, such as $%^&*.\\\\n\\\",\\\"match\\\":\\\"((?:(?<!'')('')?[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'']*\\\\\\\\.)*)(\\\\\\\\#+|[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!\\\\\\\\#))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#data_constructor\\\"}]},\\\"4\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"}},\\\"comment\\\":\\\"In case this regex seems unusual for an infix operator, note that Haskell\\\\nallows any ordinary function application (elem 4 [1..10]) to be rewritten\\\\nas an infix expression (4 `elem` [1..10]).\\\\n\\\",\\\"match\\\":\\\"(`)((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'']*\\\\\\\\.)*)([\\\\\\\\p{Ll}\\\\\\\\p{Lu}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'']*)(`)\\\",\\\"name\\\":\\\"keyword.operator.function.infix.haskell\\\"}]},\\\"inline_phase\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"name\\\":\\\"meta.inlining-phase.haskell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"~\\\",\\\"name\\\":\\\"punctuation.tilde.haskell\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"match\\\":\\\"\\\\\\\\w*\\\",\\\"name\\\":\\\"invalid\\\"}]},\\\"integer_literals\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.integral.decimal.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.integral.hexadecimal.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.integral.octal.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.integral.binary.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:([0-9][_0-9]*)|(0[xX]_*[0-9a-fA-F][_0-9a-fA-F]*)|(0[oO]_*[0-7][_0-7]*)|(0[bB]_*[01][_01]*))\\\\\\\\b(?!')\\\"},\\\"keyword\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.$2.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(?:(where|let|in|default)|(m?do|if|then|else|case|of|proc|rec))\\\\\\\\b(?!')\\\"},\\\"layout_herald\\\":{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(?:(where|let|m?do)|(of))\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.$1.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.of.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},\\\"liquid_haskell\\\":{\\\"begin\\\":\\\"\\\\\\\\{-@\\\",\\\"end\\\":\\\"@-\\\\\\\\}\\\",\\\"name\\\":\\\"block.liquidhaskell.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"module_exports\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.declaration.exports.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.module.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(module)\\\\\\\\b(?!')\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#export_constructs\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_wildcard\\\"},{\\\"include\\\":\\\"#export_constructs\\\"},{\\\"include\\\":\\\"#comma\\\"}]}]},\\\"module_name\\\":{\\\"match\\\":\\\"(?<conid>[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*(\\\\\\\\.\\\\\\\\g<conid>)?)\\\",\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"numeric_literals\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#float_literals\\\"},{\\\"include\\\":\\\"#integer_literals\\\"}]},\\\"overloaded_label\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.hash.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}},\\\"match\\\":\\\"(?<![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\p{S}\\\\\\\\p{P}&&[^(,;\\\\\\\\[`{]])(\\\\\\\\#)(?:(\\\\\\\"(?:\\\\\\\\\\\\\\\\\\\\\\\"|[^\\\\\\\"])*\\\\\\\")|[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'\\\\\\\\.]+)\\\",\\\"name\\\":\\\"entity.name.label.haskell\\\"}]},\\\"pragma\\\":{\\\"begin\\\":\\\"\\\\\\\\{-#\\\",\\\"end\\\":\\\"#-\\\\\\\\}\\\",\\\"name\\\":\\\"meta.preprocessor.haskell\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?xi) \\\\\\\\b(?<!')(LANGUAGE)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"}},\\\"end\\\":\\\"(?=#-\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?:No)?(?:AutoDeriveTypeable|DatatypeContexts|DoRec|IncoherentInstances|MonadFailDesugaring|MonoPatBinds|NullaryTypeClasses|OverlappingInstances|PatternSignatures|RecordPuns|RelaxedPolyRec)\\\",\\\"name\\\":\\\"invalid.deprecated\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.extension.haskell\\\"}},\\\"match\\\":\\\"((?:No)?(?:AllowAmbiguousTypes|AlternativeLayoutRule|AlternativeLayoutRuleTransitional|Arrows|BangPatterns|BinaryLiterals|CApiFFI|CPP|CUSKs|ConstrainedClassMethods|ConstraintKinds|DataKinds|DefaultSignatures|DeriveAnyClass|DeriveDataTypeable|DeriveFoldable|DeriveFunctor|DeriveGeneric|DeriveLift|DeriveTraversable|DerivingStrategies|DerivingVia|DisambiguateRecordFields|DoAndIfThenElse|BlockArguments|DuplicateRecordFields|EmptyCase|EmptyDataDecls|EmptyDataDeriving|ExistentialQuantification|ExplicitForAll|ExplicitNamespaces|ExtendedDefaultRules|FlexibleContexts|FlexibleInstances|ForeignFunctionInterface|FunctionalDependencies|GADTSyntax|GADTs|GHCForeignImportPrim|Generali(?:s|z)edNewtypeDeriving|ImplicitParams|ImplicitPrelude|ImportQualifiedPost|ImpredicativeTypes|TypeFamilyDependencies|InstanceSigs|ApplicativeDo|InterruptibleFFI|JavaScriptFFI|KindSignatures|LambdaCase|LiberalTypeSynonyms|MagicHash|MonadComprehensions|MonoLocalBinds|MonomorphismRestriction|MultiParamTypeClasses|MultiWayIf|NumericUnderscores|NPlusKPatterns|NamedFieldPuns|NamedWildCards|NegativeLiterals|HexFloatLiterals|NondecreasingIndentation|NumDecimals|OverloadedLabels|OverloadedLists|OverloadedStrings|PackageImports|ParallelArrays|ParallelListComp|PartialTypeSignatures|PatternGuards|PatternSynonyms|PolyKinds|PolymorphicComponents|QuantifiedConstraints|PostfixOperators|QuasiQuotes|Rank2Types|RankNTypes|RebindableSyntax|RecordWildCards|RecursiveDo|RelaxedLayout|RoleAnnotations|ScopedTypeVariables|StandaloneDeriving|StarIsType|StaticPointers|Strict|StrictData|TemplateHaskell|TemplateHaskellQuotes|StandaloneKindSignatures|TraditionalRecordSyntax|TransformListComp|TupleSections|TypeApplications|TypeInType|TypeFamilies|TypeOperators|TypeSynonymInstances|UnboxedTuples|UnboxedSums|UndecidableInstances|UndecidableSuperClasses|UnicodeSyntax|UnliftedFFITypes|UnliftedNewtypes|ViewPatterns))\\\"},{\\\"include\\\":\\\"#comma\\\"}]},{\\\"begin\\\":\\\"(?xi)\\\\n  \\\\\\\\b(?<!')(SPECIALI(?:S|Z)E)\\\\n  (?:\\\\n  \\\\\\\\s*( \\\\\\\\[ [^\\\\\\\\[\\\\\\\\]]* \\\\\\\\])?\\\\\\\\s*\\\\n  |\\\\\\\\s+\\\\n  )\\\\n  (instance)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_phase\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.instance.haskell\\\"}},\\\"end\\\":\\\"(?=#-\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?xi)\\\\n  \\\\\\\\b(?<!')(SPECIALI(?:S|Z)E)\\\\\\\\b(?!')\\\\n  (?:\\\\\\\\s+(INLINE)\\\\\\\\b(?!'))?\\\\n  (?:\\\\\\\\s*(\\\\\\\\[ [^\\\\\\\\[\\\\\\\\]]* \\\\\\\\])?)\\\\n  \\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_phase\\\"}]}},\\\"end\\\":\\\"(?=#-\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"match\\\":\\\"(?xi) \\\\\\\\b(?<!')\\\\n  (LANGUAGE|OPTIONS_GHC|INCLUDE\\\\n  |MINIMAL|UNPACK|OVERLAPS|INCOHERENT\\\\n  |NOUNPACK|SOURCE|OVERLAPPING|OVERLAPPABLE|INLINE\\\\n  |NOINLINE|INLINE?ABLE|CONLIKE|LINE|COLUMN|RULES\\\\n  |COMPLETE)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.preprocessor.haskell\\\"},{\\\"begin\\\":\\\"(?i)\\\\\\\\b(DEPRECATED|WARNING)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.preprocessor.pragma.haskell\\\"}},\\\"end\\\":\\\"(?=#-\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]}]},\\\"prefix_op\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.infix.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"comment\\\":\\\"An operator cannot be composed entirely of '-' characters;  instead, it should be matched as a comment.\\\\n\\\",\\\"match\\\":\\\"(\\\\\\\\()\\\\\\\\s*(?!(?:--+|\\\\\\\\.\\\\\\\\.)\\\\\\\\))(\\\\\\\\#+|[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!\\\\\\\\#))\\\\\\\\s*(\\\\\\\\))\\\"}]},\\\"qualifier\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\.\\\",\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"quasi_quote\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\[)(e|d|p)?(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\3\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\[)(t)(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\3\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\[)(?:(\\\\\\\\$\\\\\\\\$)|(\\\\\\\\$))?((?:[^\\\\\\\\s\\\\\\\\p{S}\\\\\\\\p{P}]|[\\\\\\\\.'_])*)(\\\\\\\\|\\\\\\\\|?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.prefix.double-dollar.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.prefix.dollar.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.quasi-quoter.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualifier\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\5\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.quasi-quotation.end.haskell\\\"}},\\\"name\\\":\\\"meta.quasi-quotation.haskell\\\"}]},\\\"record\\\":{\\\"begin\\\":\\\"({)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(?<!-)(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"name\\\":\\\"meta.record.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_field\\\"}]},\\\"record_decl\\\":{\\\"begin\\\":\\\"({)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(?<!-)(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"name\\\":\\\"meta.record.definition.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#record_decl_field\\\"}]},\\\"record_decl_field\\\":{\\\"begin\\\":\\\"(?:([\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)|(\\\\\\\\()\\\\\\\\s*([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.definition.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.member.definition.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#double_colon\\\"},{\\\"include\\\":\\\"#type_signature\\\"},{\\\"include\\\":\\\"#record_decl_field\\\"}]},\\\"record_field\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:([\\\\\\\\p{Ll}\\\\\\\\p{Lu}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\.']*)|(\\\\\\\\()\\\\\\\\s*([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualifier\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.member.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(,)|(?=})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comma.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"#record_wildcard\\\"}]},\\\"record_wildcard\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.member.wildcard.haskell\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(\\\\\\\\.\\\\\\\\.)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\"},\\\"reserved_symbol\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-dot.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.colon.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.eq.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.lambda.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.pipe.haskell\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.haskell\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.arrow.haskell\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.tail.haskell\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.arrow.left.tail.double.haskell\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.arrow.tail.haskell\\\"},\\\"11\\\":{\\\"name\\\":\\\"keyword.operator.arrow.tail.double.haskell\\\"},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.forall.haskell\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"'']])(?:(\\\\\\\\.\\\\\\\\.)|(:)|(=)|(\\\\\\\\\\\\\\\\)|(\\\\\\\\|)|(<-|←)|(->|→)|(-<|↢)|(-<<|⤛)|(>-|⤚)|(>>-|⤜)|(∀))(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"'']])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.postfix.hash.haskell\\\"}},\\\"match\\\":\\\"(?<=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\#,;\\\\\\\\[`{]])(\\\\\\\\#+)(?![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\p{S}\\\\\\\\p{P}&&[^),;\\\\\\\\]`}]])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.infix.tight.at.haskell\\\"}},\\\"match\\\":\\\"(?<=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\)\\\\\\\\}\\\\\\\\]])(@)(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\(\\\\\\\\[\\\\\\\\{])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.tilde.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.prefix.bang.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.prefix.minus.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.prefix.dollar.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.prefix.double-dollar.haskell\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\p{S}\\\\\\\\p{P}&&[^(,;\\\\\\\\[`{]])(?:(~)|(!)|(-)|(\\\\\\\\$)|(\\\\\\\\$\\\\\\\\$))(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}\\\\\\\\(\\\\\\\\{\\\\\\\\[])\\\"}]},\\\"role_annotation\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(type)\\\\\\\\s+(role)\\\\\\\\b(?!')\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.other.type.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.role.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s+\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$))\\\",\\\"name\\\":\\\"meta.role-annotation.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"include\\\":\\\"#type_constructor\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.role.$1.haskell\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!')(nominal|representational|phantom)\\\\\\\\b(?!')\\\"}]}]},\\\"start_type_signature\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\(,;\\\\\\\\[`{_\\\\\\\"']])\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\#?\\\\\\\\)|\\\\\\\\]|,|(?<!')\\\\\\\\b(in|then|else|of)\\\\\\\\b(?!')|(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:(\\\\\\\\\\\\\\\\|λ)|(<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']|[^\\\\\\\\p{S}\\\\\\\\p{P}])|(\\\\\\\\#|@)-\\\\\\\\}|(?=\\\\\\\\}|;)|^(?!\\\\\\\\1\\\\\\\\s*\\\\\\\\S|\\\\\\\\s*(?:$|\\\\\\\\{-[^@]|--+(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]{}`_\\\\\\\"']]).*$)))\\\",\\\"name\\\":\\\"meta.type-declaration.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\(,;\\\\\\\\[`{_\\\\\\\"']])(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^\\\\\\\\(,;\\\\\\\\[`{_\\\\\\\"']])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\#?\\\\\\\\)|\\\\\\\\]|,|\\\\\\\\b(?<!')(in|then|else|of)\\\\\\\\b(?!')|(\\\\\\\\#|@)-\\\\\\\\}|(?<![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])(?:(\\\\\\\\\\\\\\\\|λ)|(<-|←)|(=)|(-<|↢)|(-<<|⤛))([(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']|[^\\\\\\\\p{S}\\\\\\\\p{P}])|(?=\\\\\\\\}|;)|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"string_literal\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.haskell\\\"}},\\\"name\\\":\\\"string.quoted.double.haskell\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\&])\\\",\\\"name\\\":\\\"constant.character.escape.haskell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\o[0-7]+|\\\\\\\\\\\\\\\\x[0-9A-Fa-f]+|\\\\\\\\\\\\\\\\[0-9]+\\\",\\\"name\\\":\\\"constant.character.escape.octal.haskell\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\^[A-Z@\\\\\\\\[\\\\\\\\]\\\\\\\\\\\\\\\\\\\\\\\\^_]\\\",\\\"name\\\":\\\"constant.character.escape.control.haskell\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\s\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.character.escape.begin.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\\\\\\\\\\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.character.escape.end.haskell\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\S+\\\",\\\"name\\\":\\\"invalid.illegal.character-not-allowed-here.haskell\\\"}]}]},\\\"type_application\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(')?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(')?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(?=\\\\\\\\\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\\\\\\\\")\\\",\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_literal\\\"}]},{\\\"begin\\\":\\\"(?<=[\\\\\\\\s,;\\\\\\\\[\\\\\\\\]{}\\\\\\\"])(@)(?=[\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.prefix.at.haskell\\\"}},\\\"end\\\":\\\"(?![\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}'])\\\",\\\"name\\\":\\\"meta.type-application.haskell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#type_signature\\\"}]}]},\\\"type_constructor\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.haskell\\\"}},\\\"match\\\":\\\"(')?((?:\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\.)*)(\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.operator.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\.)*)([\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+)\\\\\\\\s*(\\\\\\\\))\\\"}]},\\\"type_operator\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.operator.infix.haskell\\\"}},\\\"match\\\":\\\"(?:(?<!')('))?((?:\\\\\\\\b[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\.)*)(?![#@]?-})(\\\\\\\\#+|[\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']]+(?<!\\\\\\\\#))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.namespace.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.infix.haskell\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.backtick.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\`)((?:[\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\\\\\\.)*)([\\\\\\\\p{Lu}\\\\\\\\p{Lt}][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*)(`)\\\"}]},\\\"type_signature\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_like\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\()\\\\\\\\s*,[\\\\\\\\s,]*(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.unit.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(#)\\\\\\\\s*,[\\\\\\\\s,]*(#)(\\\\\\\\))\\\",\\\"name\\\":\\\"support.constant.tuple.unboxed.haskell\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"match\\\":\\\"(')?(\\\\\\\\[)\\\\\\\\s*(\\\\\\\\])\\\",\\\"name\\\":\\\"support.constant.empty-list.haskell\\\"},{\\\"include\\\":\\\"#integer_literals\\\"},{\\\"match\\\":\\\"(::|∷)(?![\\\\\\\\p{S}\\\\\\\\p{P}&&[^(),;\\\\\\\\[\\\\\\\\]`{}_\\\\\\\"']])\\\",\\\"name\\\":\\\"keyword.operator.double-colon.haskell\\\"},{\\\"include\\\":\\\"#forall\\\"},{\\\"match\\\":\\\"=>|⇒\\\",\\\"name\\\":\\\"keyword.operator.big-arrow.haskell\\\"},{\\\"include\\\":\\\"#string_literal\\\"},{\\\"match\\\":\\\"'[^']'\\\",\\\"name\\\":\\\"invalid\\\"},{\\\"include\\\":\\\"#type_application\\\"},{\\\"include\\\":\\\"#reserved_symbol\\\"},{\\\"include\\\":\\\"#type_operator\\\"},{\\\"include\\\":\\\"#type_constructor\\\"},{\\\"begin\\\":\\\"(\\\\\\\\()(#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"}},\\\"end\\\":\\\"(#)(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.hash.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(')?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.paren.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"begin\\\":\\\"(')?(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.promotion.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.bracket.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comma\\\"},{\\\"include\\\":\\\"#type_signature\\\"}]},{\\\"include\\\":\\\"#type_variable\\\"}]},\\\"type_variable\\\":{\\\"match\\\":\\\"\\\\\\\\b(?<!')(?!(?:forall|deriving)\\\\\\\\b(?!'))[\\\\\\\\p{Ll}_][\\\\\\\\p{Ll}_\\\\\\\\p{Lu}\\\\\\\\p{Lt}\\\\\\\\p{Nd}']*\\\",\\\"name\\\":\\\"variable.other.generic-type.haskell\\\"},\\\"where\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!')\\\\\\\\b(where)\\\\\\\\s*(\\\\\\\\{)(?!-)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.where.haskell\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.brace.haskell\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.semicolon.haskell\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(?<!')(where)\\\\\\\\b(?!')\\\",\\\"name\\\":\\\"keyword.other.where.haskell\\\"}]}},\\\"scopeName\\\":\\\"source.haskell\\\",\\\"aliases\\\":[\\\"hs\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/haskell.mjs\n"));

/***/ })

}]);