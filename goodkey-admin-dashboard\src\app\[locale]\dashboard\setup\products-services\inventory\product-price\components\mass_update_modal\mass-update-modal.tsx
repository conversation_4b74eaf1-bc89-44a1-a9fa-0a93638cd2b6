'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useState } from 'react';
import { OfferingRateUpsertDto } from '@/models/Offering';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';
import { modal } from '@/components/ui/overlay';

interface MassUpdateModalProps {
  id: number;
  warehouseId: number;
  name: string;
}

function MassUpdateModal({ id, warehouseId, name }: MassUpdateModalProps) {
  const { toast } = useToast();
  const [quantity, setQuantity] = useState<number>(0);
  const [unitPrice, setUnitPrice] = useState<number>(0);
  const [isDiscontinued, setIsDiscontinued] = useState<boolean>(false);

  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (payload: OfferingRateUpsertDto) =>
      OfferingRateQuery.saveOfferingRate(payload),
    onSuccess: () => {
      toast({
        title: 'Saved',
        description: `All product's variants updated successfully`,
        variant: 'success',
      });
      queryClient.invalidateQueries({
        queryKey: ['products', Number(warehouseId)],
      });
      queryClient.invalidateQueries({
        queryKey: ['products-all'],
      });
      modal.close();
    },
    onError: (err: any) => {
      toast({
        title: 'Error',
        description: err.message || 'Failed to save',
        variant: 'destructive',
      });
    },
  });

  const handleSave = (e?: React.FormEvent) => {
    e?.preventDefault(); // prevents the full page reload
    const payload: OfferingRateUpsertDto = {
      offeringId: id,
      offeringPropertyId: null,
      warehouseId,
      quantity,
      unitPrice,
      isDiscontinued,
    };
    mutation.mutate(payload);
  };

  return (
    <ModalContainer
      title={`${name} Mass Update`}
      description={
        "Fill in the form below to update all the product's variants at once."
      }
      onSubmit={handleSave}
      controls={
        <div className="flex justify-end items-center gap-4">
          <Button
            variant="main"
            iconName="SaveIcon"
            iconProps={{
              className: 'text-white',
            }}
          >
            Save
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 my-4">
        <div className="flex flex-row items-center gap-2">
          <div className="w-full sm:min-w-[100px] sm:max-w-[160px] flex flex-col justify-start">
            <label className="leading-normal font-medium block cursor-not-allowed ">
              Quantity
            </label>
          </div>
          <Input
            type="number"
            min="0"
            placeholder="0"
            className={`flex text-right w-full text-foreground text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50 p-2 border border-gray-300 rounded-sm `}
            value={quantity}
            onChange={(e) => setQuantity(Number(e.target.value))}
            disabled={isDiscontinued === true}
          />
        </div>
        <div className="flex flex-row items-center gap-2">
          <div className="w-full sm:min-w-[100px] sm:max-w-[160px] flex flex-col justify-start">
            <label className="leading-normal font-medium block cursor-not-allowed ">
              Price
            </label>
          </div>
          <div className="relative w-full">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              $
            </span>
            <Input
              disabled={isDiscontinued === true}
              type="number"
              placeholder="0.00"
              min="0.00"
              step="0.01"
              value={unitPrice}
              onChange={(e) => setUnitPrice(Number(e.target.value))}
              className={`text-right w-full pl-8 text-foreground text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50 p-2 border border-gray-300 rounded-sm`}
            />
          </div>
        </div>
        <div className="flex flex-row items-center gap-2">
          <div className="w-full sm:min-w-[100px] sm:max-w-[160px] flex flex-col justify-start">
            <label className="leading-normal font-medium block cursor-not-allowed ">
              Discontinued
            </label>
          </div>
          <Checkbox
            checked={isDiscontinued === true}
            onCheckedChange={(checked) => {
              setIsDiscontinued(!isDiscontinued);
            }}
          />
        </div>
      </div>
      <p className="text-red-700 text-sm">
        * Once updated, the product's variants will be updated and overridden
        all the existing variants' value.
      </p>
    </ModalContainer>
  );
}

export default MassUpdateModal;
