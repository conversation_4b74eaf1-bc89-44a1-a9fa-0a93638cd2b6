import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShowPromoter } from '@/models/Show';
import { useQuery } from '@tanstack/react-query';
import ShowQuery from '@/services/queries/ShowQuery';
import { ContactDetail } from '@/models/Contact';
import { useState } from 'react';
import { PlusCircle, MinusCircle } from 'lucide-react';

export default function PromoterInfo({
  promoter,
}: {
  promoter?: ShowPromoter;
}) {
  const [showAllContacts, setShowAllContacts] = useState(false);

  const { data: showContacts, isLoading } = useQuery({
    queryKey: [ShowQuery.tags, promoter?.showId, 'contacts'],
    queryFn: () => ShowQuery.getShowContacts(promoter!.showId!),
    enabled: !!promoter?.showId,
  });

  const billedToContacts = showContacts?.billedToContacts || [];
  const managerContacts = showContacts?.managerContacts || [];

  // Get the first contact from each type for the main display
  const primaryManager = managerContacts[0];
  const primaryBillingContact = billedToContacts[0];

  // Get remaining contacts (excluding the first ones already shown)
  const additionalManagerContacts = managerContacts.slice(1);
  const additionalBillingContacts = billedToContacts.slice(1);

  // Check if there are additional contacts
  const hasMoreContacts =
    additionalManagerContacts.length > 0 ||
    additionalBillingContacts.length > 0;

  // Helper function to render contact information
  const renderContactInfo = (contact: ContactDetail, title: string) => (
    <div className="flex-1">
      <h3 className="text-base font-semibold text-gray-900 mb-3">{title}</h3>
      <div className="space-y-2 text-sm ">
        <div>
          <span className="font-semibold">Name: </span>
          <span className=" font-semibold">{contact.fullName}</span>
        </div>
        <div>
          <span className="">Company: </span>
          <span className="text-gray-700">{contact.companyName}</span>
        </div>
        <div>
          <span className="">Email: </span>
          <a
            href={`mailto:${contact.email}`}
            className="text-blue-600 hover:underline"
          >
            {contact.email}
          </a>
        </div>
        <div>
          <span className="">Tel: </span>
          <a
            href={`tel:${contact.telephone}`}
            className="text-blue-600 hover:underline"
          >
            {contact.telephone}
            {contact.ext && ` ext ${contact.ext}`}
          </a>
        </div>
      </div>
    </div>
  );

  if (!promoter || isLoading) {
    return (
      <Card>
        <CardHeader className="pb-5">
          <CardTitle>Show Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
              <p className="text-slate-600">Loading show management info...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-5">
        <CardTitle>Show Management</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Main contact display - show only first contact from each type */}
        {(primaryManager || primaryBillingContact) && (
          <div className="flex gap-8 mb-6">
            {primaryManager && renderContactInfo(primaryManager, 'Manager')}
            {primaryBillingContact &&
              renderContactInfo(primaryBillingContact, 'Billing Contact')}
          </div>
        )}

        {/* Show more contacts button if there are additional contacts */}
        {hasMoreContacts && !showAllContacts && (
          <div className="text-center">
            <Button
              variant="transparent"
              onClick={() => setShowAllContacts(true)}
              className="text-[#00646C] border-[#00646C] w-fit h-fit p-1 rounded-full"
            >
              <PlusCircle className="h-8 w-8" />
            </Button>
          </div>
        )}

        {/* Additional contacts display - continue with same layout */}
        {showAllContacts && hasMoreContacts && (
          <div className="mt-6 pt-6 border-t border-slate-200">
            {/* Show additional contacts in pairs using the same layout */}
            {(additionalManagerContacts.length > 0 ||
              additionalBillingContacts.length > 0) && (
              <div className="space-y-6">
                {/* Process contacts in pairs to maintain the same two-column layout */}
                {Array.from({
                  length: Math.max(
                    Math.ceil(additionalManagerContacts.length / 1),
                    Math.ceil(additionalBillingContacts.length / 1),
                  ),
                }).map((_, pairIndex) => {
                  const managerContact = additionalManagerContacts[pairIndex];
                  const billingContact = additionalBillingContacts[pairIndex];

                  // Only render if at least one contact exists in this pair
                  if (!managerContact && !billingContact) return null;

                  return (
                    <div key={pairIndex} className="flex gap-8">
                      {managerContact ? (
                        renderContactInfo(managerContact, 'Manager')
                      ) : (
                        <div className="flex-1"></div> // Empty space to maintain layout
                      )}
                      {billingContact ? (
                        renderContactInfo(billingContact, 'Billing Contact')
                      ) : (
                        <div className="flex-1"></div> // Empty space to maintain layout
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            <div className="text-center mt-6">
              <Button
                variant="transparent"
                onClick={() => setShowAllContacts(false)}
                className="text-[#00646C] border-[#00646C] w-fit h-fit p-1 rounded-full"
              >
                <MinusCircle className="h-8 w-8" />
              </Button>
            </div>
          </div>
        )}

        {/* No contacts message */}
        {billedToContacts.length === 0 && managerContacts.length === 0 && (
          <p className="text-center text-gray-500">
            No show management contacts found.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
