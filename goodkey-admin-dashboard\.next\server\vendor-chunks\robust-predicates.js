"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robust-predicates";
exports.ids = ["vendor-chunks/robust-predicates"];
exports.modules = {

/***/ "(ssr)/./node_modules/robust-predicates/esm/incircle.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/incircle.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* binding */ incircle),\n/* harmony export */   incirclefast: () => (/* binding */ incirclefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst iccerrboundA = (10 + 96 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundB = (4 + 48 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundC = (44 + 576 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst aa = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst axtbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst aytbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bxtca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bytca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cxtab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cytab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abtt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bctt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst catt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _32 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _32b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _64 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(64);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adx, _8), _8, adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdx, _8), _8, bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdx, _8), _8, cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, 2 * adx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, 2 * ady, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, 2 * bdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, 2 * cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, 2 * cdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, adxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, adytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * ady, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, bdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, bdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, cdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, cdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nfunction incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/incircle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/insphere.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/insphere.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insphere: () => (/* binding */ insphere),\n/* harmony export */   inspherefast: () => (/* binding */ inspherefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst isperrboundA = (16 + 224 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundB = (5 + 72 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundC = (71 + 1408 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst de = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst da = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst eb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst abc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bcd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cde = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst dea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst abd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cda = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst deb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\n\nconst adet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst bdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst cdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst ddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst edet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst abdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cdedet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(3456);\nconst deter = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(5760);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _24 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _48b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _96 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst _192 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nconst _384x = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384y = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384z = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _768 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, a, az, _8), _8,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, b, bz, _8b), _8b,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(alen, a, blen, b, _48), _48,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, x, _192), _192, x, _384x), _384x,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, y, _192), _192, y, _384y), _384y,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst ydet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst zdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, x, _48), _48, x, xdet), xdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, y, _48), _48, y, ydet), ydet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nfunction insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nfunction inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/insphere.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient2d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient2d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient2d: () => (/* binding */ orient2d),\n/* harmony export */   orient2dfast: () => (/* binding */ orient2dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst ccwerrboundA = (3 + 16 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundB = (2 + 12 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundC = (9 + 64 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst B = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst C1 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst C2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nconst D = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nfunction orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nfunction orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvZXNtL29yaWVudDJkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRjs7QUFFaEYsK0JBQStCLDZDQUFPLElBQUksNkNBQU87QUFDakQsK0JBQStCLDZDQUFPLElBQUksNkNBQU87QUFDakQsK0JBQStCLDZDQUFPLElBQUksNkNBQU8sR0FBRyw2Q0FBTzs7QUFFM0QsVUFBVSw2Q0FBRztBQUNiLFdBQVcsNkNBQUc7QUFDZCxXQUFXLDZDQUFHO0FBQ2QsVUFBVSw2Q0FBRztBQUNiLFVBQVUsNkNBQUc7O0FBRWI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsY0FBYyxrREFBUTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSx1Q0FBdUMsb0RBQWM7QUFDckQ7QUFDQTs7QUFFQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDZDQUFHOztBQUVyQjtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDZDQUFHOztBQUVyQjtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDZDQUFHOztBQUVwQjtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccm9idXN0LXByZWRpY2F0ZXNcXGVzbVxcb3JpZW50MmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtlcHNpbG9uLCBzcGxpdHRlciwgcmVzdWx0ZXJyYm91bmQsIGVzdGltYXRlLCB2ZWMsIHN1bX0gZnJvbSAnLi91dGlsLmpzJztcblxuY29uc3QgY2N3ZXJyYm91bmRBID0gKDMgKyAxNiAqIGVwc2lsb24pICogZXBzaWxvbjtcbmNvbnN0IGNjd2VycmJvdW5kQiA9ICgyICsgMTIgKiBlcHNpbG9uKSAqIGVwc2lsb247XG5jb25zdCBjY3dlcnJib3VuZEMgPSAoOSArIDY0ICogZXBzaWxvbikgKiBlcHNpbG9uICogZXBzaWxvbjtcblxuY29uc3QgQiA9IHZlYyg0KTtcbmNvbnN0IEMxID0gdmVjKDgpO1xuY29uc3QgQzIgPSB2ZWMoMTIpO1xuY29uc3QgRCA9IHZlYygxNik7XG5jb25zdCB1ID0gdmVjKDQpO1xuXG5mdW5jdGlvbiBvcmllbnQyZGFkYXB0KGF4LCBheSwgYngsIGJ5LCBjeCwgY3ksIGRldHN1bSkge1xuICAgIGxldCBhY3h0YWlsLCBhY3l0YWlsLCBiY3h0YWlsLCBiY3l0YWlsO1xuICAgIGxldCBidmlydCwgYywgYWhpLCBhbG8sIGJoaSwgYmxvLCBfaSwgX2osIF8wLCBzMSwgczAsIHQxLCB0MCwgdTM7XG5cbiAgICBjb25zdCBhY3ggPSBheCAtIGN4O1xuICAgIGNvbnN0IGJjeCA9IGJ4IC0gY3g7XG4gICAgY29uc3QgYWN5ID0gYXkgLSBjeTtcbiAgICBjb25zdCBiY3kgPSBieSAtIGN5O1xuXG4gICAgczEgPSBhY3ggKiBiY3k7XG4gICAgYyA9IHNwbGl0dGVyICogYWN4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGFjeCk7XG4gICAgYWxvID0gYWN4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJjeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBiY3kpO1xuICAgIGJsbyA9IGJjeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBhY3kgKiBiY3g7XG4gICAgYyA9IHNwbGl0dGVyICogYWN5O1xuICAgIGFoaSA9IGMgLSAoYyAtIGFjeSk7XG4gICAgYWxvID0gYWN5IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJjeDtcbiAgICBiaGkgPSBjIC0gKGMgLSBiY3gpO1xuICAgIGJsbyA9IGJjeCAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBCWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgQlsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgQlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIEJbM10gPSB1MztcblxuICAgIGxldCBkZXQgPSBlc3RpbWF0ZSg0LCBCKTtcbiAgICBsZXQgZXJyYm91bmQgPSBjY3dlcnJib3VuZEIgKiBkZXRzdW07XG4gICAgaWYgKGRldCA+PSBlcnJib3VuZCB8fCAtZGV0ID49IGVycmJvdW5kKSB7XG4gICAgICAgIHJldHVybiBkZXQ7XG4gICAgfVxuXG4gICAgYnZpcnQgPSBheCAtIGFjeDtcbiAgICBhY3h0YWlsID0gYXggLSAoYWN4ICsgYnZpcnQpICsgKGJ2aXJ0IC0gY3gpO1xuICAgIGJ2aXJ0ID0gYnggLSBiY3g7XG4gICAgYmN4dGFpbCA9IGJ4IC0gKGJjeCArIGJ2aXJ0KSArIChidmlydCAtIGN4KTtcbiAgICBidmlydCA9IGF5IC0gYWN5O1xuICAgIGFjeXRhaWwgPSBheSAtIChhY3kgKyBidmlydCkgKyAoYnZpcnQgLSBjeSk7XG4gICAgYnZpcnQgPSBieSAtIGJjeTtcbiAgICBiY3l0YWlsID0gYnkgLSAoYmN5ICsgYnZpcnQpICsgKGJ2aXJ0IC0gY3kpO1xuXG4gICAgaWYgKGFjeHRhaWwgPT09IDAgJiYgYWN5dGFpbCA9PT0gMCAmJiBiY3h0YWlsID09PSAwICYmIGJjeXRhaWwgPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICBlcnJib3VuZCA9IGNjd2VycmJvdW5kQyAqIGRldHN1bSArIHJlc3VsdGVycmJvdW5kICogTWF0aC5hYnMoZGV0KTtcbiAgICBkZXQgKz0gKGFjeCAqIGJjeXRhaWwgKyBiY3kgKiBhY3h0YWlsKSAtIChhY3kgKiBiY3h0YWlsICsgYmN4ICogYWN5dGFpbCk7XG4gICAgaWYgKGRldCA+PSBlcnJib3VuZCB8fCAtZGV0ID49IGVycmJvdW5kKSByZXR1cm4gZGV0O1xuXG4gICAgczEgPSBhY3h0YWlsICogYmN5O1xuICAgIGMgPSBzcGxpdHRlciAqIGFjeHRhaWw7XG4gICAgYWhpID0gYyAtIChjIC0gYWN4dGFpbCk7XG4gICAgYWxvID0gYWN4dGFpbCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiY3k7XG4gICAgYmhpID0gYyAtIChjIC0gYmN5KTtcbiAgICBibG8gPSBiY3kgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYWN5dGFpbCAqIGJjeDtcbiAgICBjID0gc3BsaXR0ZXIgKiBhY3l0YWlsO1xuICAgIGFoaSA9IGMgLSAoYyAtIGFjeXRhaWwpO1xuICAgIGFsbyA9IGFjeXRhaWwgLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYmN4O1xuICAgIGJoaSA9IGMgLSAoYyAtIGJjeCk7XG4gICAgYmxvID0gYmN4IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIHVbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICB1WzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICB1WzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgdVszXSA9IHUzO1xuICAgIGNvbnN0IEMxbGVuID0gc3VtKDQsIEIsIDQsIHUsIEMxKTtcblxuICAgIHMxID0gYWN4ICogYmN5dGFpbDtcbiAgICBjID0gc3BsaXR0ZXIgKiBhY3g7XG4gICAgYWhpID0gYyAtIChjIC0gYWN4KTtcbiAgICBhbG8gPSBhY3ggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYmN5dGFpbDtcbiAgICBiaGkgPSBjIC0gKGMgLSBiY3l0YWlsKTtcbiAgICBibG8gPSBiY3l0YWlsIC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGFjeSAqIGJjeHRhaWw7XG4gICAgYyA9IHNwbGl0dGVyICogYWN5O1xuICAgIGFoaSA9IGMgLSAoYyAtIGFjeSk7XG4gICAgYWxvID0gYWN5IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJjeHRhaWw7XG4gICAgYmhpID0gYyAtIChjIC0gYmN4dGFpbCk7XG4gICAgYmxvID0gYmN4dGFpbCAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICB1WzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgdVsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgdVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIHVbM10gPSB1MztcbiAgICBjb25zdCBDMmxlbiA9IHN1bShDMWxlbiwgQzEsIDQsIHUsIEMyKTtcblxuICAgIHMxID0gYWN4dGFpbCAqIGJjeXRhaWw7XG4gICAgYyA9IHNwbGl0dGVyICogYWN4dGFpbDtcbiAgICBhaGkgPSBjIC0gKGMgLSBhY3h0YWlsKTtcbiAgICBhbG8gPSBhY3h0YWlsIC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJjeXRhaWw7XG4gICAgYmhpID0gYyAtIChjIC0gYmN5dGFpbCk7XG4gICAgYmxvID0gYmN5dGFpbCAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBhY3l0YWlsICogYmN4dGFpbDtcbiAgICBjID0gc3BsaXR0ZXIgKiBhY3l0YWlsO1xuICAgIGFoaSA9IGMgLSAoYyAtIGFjeXRhaWwpO1xuICAgIGFsbyA9IGFjeXRhaWwgLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYmN4dGFpbDtcbiAgICBiaGkgPSBjIC0gKGMgLSBiY3h0YWlsKTtcbiAgICBibG8gPSBiY3h0YWlsIC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIHVbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICB1WzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICB1WzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgdVszXSA9IHUzO1xuICAgIGNvbnN0IERsZW4gPSBzdW0oQzJsZW4sIEMyLCA0LCB1LCBEKTtcblxuICAgIHJldHVybiBEW0RsZW4gLSAxXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIG9yaWVudDJkKGF4LCBheSwgYngsIGJ5LCBjeCwgY3kpIHtcbiAgICBjb25zdCBkZXRsZWZ0ID0gKGF5IC0gY3kpICogKGJ4IC0gY3gpO1xuICAgIGNvbnN0IGRldHJpZ2h0ID0gKGF4IC0gY3gpICogKGJ5IC0gY3kpO1xuICAgIGNvbnN0IGRldCA9IGRldGxlZnQgLSBkZXRyaWdodDtcblxuICAgIGNvbnN0IGRldHN1bSA9IE1hdGguYWJzKGRldGxlZnQgKyBkZXRyaWdodCk7XG4gICAgaWYgKE1hdGguYWJzKGRldCkgPj0gY2N3ZXJyYm91bmRBICogZGV0c3VtKSByZXR1cm4gZGV0O1xuXG4gICAgcmV0dXJuIC1vcmllbnQyZGFkYXB0KGF4LCBheSwgYngsIGJ5LCBjeCwgY3ksIGRldHN1bSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBvcmllbnQyZGZhc3QoYXgsIGF5LCBieCwgYnksIGN4LCBjeSkge1xuICAgIHJldHVybiAoYXkgLSBjeSkgKiAoYnggLSBjeCkgLSAoYXggLSBjeCkgKiAoYnkgLSBjeSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient2d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient3d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient3d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient3d: () => (/* binding */ orient3d),\n/* harmony export */   orient3dfast: () => (/* binding */ orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst o3derrboundA = (7 + 56 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundB = (3 + 28 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundC = (26 + 288 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _12 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adz, _8), _8,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdz, _8b), _8b, _16), _16,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdz, _8), _8, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adz, _16), _16);\n\n    const catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdz, _16), _16);\n\n    const abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nfunction orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvZXNtL29yaWVudDNkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1Rjs7QUFFdkYsK0JBQStCLDZDQUFPLElBQUksNkNBQU87QUFDakQsK0JBQStCLDZDQUFPLElBQUksNkNBQU87QUFDakQsaUNBQWlDLDZDQUFPLElBQUksNkNBQU8sR0FBRyw2Q0FBTzs7QUFFN0QsV0FBVyw2Q0FBRztBQUNkLFdBQVcsNkNBQUc7QUFDZCxXQUFXLDZDQUFHO0FBQ2QsYUFBYSw2Q0FBRztBQUNoQixhQUFhLDZDQUFHO0FBQ2hCLGFBQWEsNkNBQUc7QUFDaEIsYUFBYSw2Q0FBRztBQUNoQixhQUFhLDZDQUFHO0FBQ2hCLGFBQWEsNkNBQUc7QUFDaEIsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2YsVUFBVSw2Q0FBRzs7QUFFYixXQUFXLDZDQUFHO0FBQ2QsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHOztBQUVmLFVBQVUsNkNBQUc7QUFDYixXQUFXLDZDQUFHOztBQUVkO0FBQ0EsYUFBYSw2Q0FBRztBQUNoQixxQkFBcUIsWUFBWTtBQUNqQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDhDQUFRO0FBQ3hCO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsZ0JBQWdCLDhDQUFRO0FBQ3hCO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDhDQUFRO0FBQ3hCO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQVE7QUFDeEI7QUFDQTtBQUNBLGdCQUFnQiw4Q0FBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw4Q0FBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQSxZQUFZLDhDQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw4Q0FBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLDZDQUFHO0FBQ2hCLFFBQVEsNkNBQUc7QUFDWCxZQUFZLCtDQUFLO0FBQ2pCLFlBQVksK0NBQUs7QUFDakIsUUFBUSwrQ0FBSzs7QUFFYixjQUFjLGtEQUFRO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDBDQUEwQyxvREFBYztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLDZDQUFHO0FBQ3RCLDRCQUE0QiwrQ0FBSzs7QUFFakMsbUJBQW1CLDZDQUFHO0FBQ3RCLDRCQUE0QiwrQ0FBSzs7QUFFakMsbUJBQW1CLDZDQUFHO0FBQ3RCLDRCQUE0QiwrQ0FBSzs7QUFFakM7QUFDQSxnQ0FBZ0MsK0NBQUs7QUFDckMsZ0NBQWdDLCtDQUFLO0FBQ3JDO0FBQ0E7QUFDQSxnQ0FBZ0MsK0NBQUs7QUFDckMsZ0NBQWdDLCtDQUFLO0FBQ3JDO0FBQ0E7QUFDQSxnQ0FBZ0MsK0NBQUs7QUFDckMsZ0NBQWdDLCtDQUFLO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccm9idXN0LXByZWRpY2F0ZXNcXGVzbVxcb3JpZW50M2QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtlcHNpbG9uLCBzcGxpdHRlciwgcmVzdWx0ZXJyYm91bmQsIGVzdGltYXRlLCB2ZWMsIHN1bSwgc2NhbGV9IGZyb20gJy4vdXRpbC5qcyc7XG5cbmNvbnN0IG8zZGVycmJvdW5kQSA9ICg3ICsgNTYgKiBlcHNpbG9uKSAqIGVwc2lsb247XG5jb25zdCBvM2RlcnJib3VuZEIgPSAoMyArIDI4ICogZXBzaWxvbikgKiBlcHNpbG9uO1xuY29uc3QgbzNkZXJyYm91bmRDID0gKDI2ICsgMjg4ICogZXBzaWxvbikgKiBlcHNpbG9uICogZXBzaWxvbjtcblxuY29uc3QgYmMgPSB2ZWMoNCk7XG5jb25zdCBjYSA9IHZlYyg0KTtcbmNvbnN0IGFiID0gdmVjKDQpO1xuY29uc3QgYXRfYiA9IHZlYyg0KTtcbmNvbnN0IGF0X2MgPSB2ZWMoNCk7XG5jb25zdCBidF9jID0gdmVjKDQpO1xuY29uc3QgYnRfYSA9IHZlYyg0KTtcbmNvbnN0IGN0X2EgPSB2ZWMoNCk7XG5jb25zdCBjdF9iID0gdmVjKDQpO1xuY29uc3QgYmN0ID0gdmVjKDgpO1xuY29uc3QgY2F0ID0gdmVjKDgpO1xuY29uc3QgYWJ0ID0gdmVjKDgpO1xuY29uc3QgdSA9IHZlYyg0KTtcblxuY29uc3QgXzggPSB2ZWMoOCk7XG5jb25zdCBfOGIgPSB2ZWMoOCk7XG5jb25zdCBfMTYgPSB2ZWMoOCk7XG5jb25zdCBfMTIgPSB2ZWMoMTIpO1xuXG5sZXQgZmluID0gdmVjKDE5Mik7XG5sZXQgZmluMiA9IHZlYygxOTIpO1xuXG5mdW5jdGlvbiBmaW5hZGQoZmlubGVuLCBhbGVuLCBhKSB7XG4gICAgZmlubGVuID0gc3VtKGZpbmxlbiwgZmluLCBhbGVuLCBhLCBmaW4yKTtcbiAgICBjb25zdCB0bXAgPSBmaW47IGZpbiA9IGZpbjI7IGZpbjIgPSB0bXA7XG4gICAgcmV0dXJuIGZpbmxlbjtcbn1cblxuZnVuY3Rpb24gdGFpbGluaXQoeHRhaWwsIHl0YWlsLCBheCwgYXksIGJ4LCBieSwgYSwgYikge1xuICAgIGxldCBidmlydCwgYywgYWhpLCBhbG8sIGJoaSwgYmxvLCBfaSwgX2osIF9rLCBfMCwgczEsIHMwLCB0MSwgdDAsIHUzLCBuZWdhdGU7XG4gICAgaWYgKHh0YWlsID09PSAwKSB7XG4gICAgICAgIGlmICh5dGFpbCA9PT0gMCkge1xuICAgICAgICAgICAgYVswXSA9IDA7XG4gICAgICAgICAgICBiWzBdID0gMDtcbiAgICAgICAgICAgIHJldHVybiAxO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbmVnYXRlID0gLXl0YWlsO1xuICAgICAgICAgICAgczEgPSBuZWdhdGUgKiBheDtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIG5lZ2F0ZTtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIG5lZ2F0ZSk7XG4gICAgICAgICAgICBhbG8gPSBuZWdhdGUgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBheDtcbiAgICAgICAgICAgIGJoaSA9IGMgLSAoYyAtIGF4KTtcbiAgICAgICAgICAgIGJsbyA9IGF4IC0gYmhpO1xuICAgICAgICAgICAgYVswXSA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBhWzFdID0gczE7XG4gICAgICAgICAgICBzMSA9IHl0YWlsICogYng7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiB5dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIHl0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IHl0YWlsIC0gYWhpO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYng7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBieCk7XG4gICAgICAgICAgICBibG8gPSBieCAtIGJoaTtcbiAgICAgICAgICAgIGJbMF0gPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgYlsxXSA9IHMxO1xuICAgICAgICAgICAgcmV0dXJuIDI7XG4gICAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoeXRhaWwgPT09IDApIHtcbiAgICAgICAgICAgIHMxID0geHRhaWwgKiBheTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIHh0YWlsO1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0geHRhaWwpO1xuICAgICAgICAgICAgYWxvID0geHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBheTtcbiAgICAgICAgICAgIGJoaSA9IGMgLSAoYyAtIGF5KTtcbiAgICAgICAgICAgIGJsbyA9IGF5IC0gYmhpO1xuICAgICAgICAgICAgYVswXSA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICBhWzFdID0gczE7XG4gICAgICAgICAgICBuZWdhdGUgPSAteHRhaWw7XG4gICAgICAgICAgICBzMSA9IG5lZ2F0ZSAqIGJ5O1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogbmVnYXRlO1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0gbmVnYXRlKTtcbiAgICAgICAgICAgIGFsbyA9IG5lZ2F0ZSAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGJ5O1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gYnkpO1xuICAgICAgICAgICAgYmxvID0gYnkgLSBiaGk7XG4gICAgICAgICAgICBiWzBdID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICAgICAgICAgIGJbMV0gPSBzMTtcbiAgICAgICAgICAgIHJldHVybiAyO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgczEgPSB4dGFpbCAqIGF5O1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogeHRhaWw7XG4gICAgICAgICAgICBhaGkgPSBjIC0gKGMgLSB4dGFpbCk7XG4gICAgICAgICAgICBhbG8gPSB4dGFpbCAtIGFoaTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIGF5O1xuICAgICAgICAgICAgYmhpID0gYyAtIChjIC0gYXkpO1xuICAgICAgICAgICAgYmxvID0gYXkgLSBiaGk7XG4gICAgICAgICAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgICAgICB0MSA9IHl0YWlsICogYXg7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiB5dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIHl0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IHl0YWlsIC0gYWhpO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYXg7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBheCk7XG4gICAgICAgICAgICBibG8gPSBheCAtIGJoaTtcbiAgICAgICAgICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICAgICAgICAgIF9pID0gczAgLSB0MDtcbiAgICAgICAgICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICAgICAgICAgIGFbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICAgICAgICAgIF9qID0gczEgKyBfaTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICAgICAgICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgICAgICAgICBfaSA9IF8wIC0gdDE7XG4gICAgICAgICAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgICAgICAgICBhWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgICAgICAgICB1MyA9IF9qICsgX2k7XG4gICAgICAgICAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgICAgICAgICBhWzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgICAgICAgICBhWzNdID0gdTM7XG4gICAgICAgICAgICBzMSA9IHl0YWlsICogYng7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiB5dGFpbDtcbiAgICAgICAgICAgIGFoaSA9IGMgLSAoYyAtIHl0YWlsKTtcbiAgICAgICAgICAgIGFsbyA9IHl0YWlsIC0gYWhpO1xuICAgICAgICAgICAgYyA9IHNwbGl0dGVyICogYng7XG4gICAgICAgICAgICBiaGkgPSBjIC0gKGMgLSBieCk7XG4gICAgICAgICAgICBibG8gPSBieCAtIGJoaTtcbiAgICAgICAgICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICAgICAgICAgIHQxID0geHRhaWwgKiBieTtcbiAgICAgICAgICAgIGMgPSBzcGxpdHRlciAqIHh0YWlsO1xuICAgICAgICAgICAgYWhpID0gYyAtIChjIC0geHRhaWwpO1xuICAgICAgICAgICAgYWxvID0geHRhaWwgLSBhaGk7XG4gICAgICAgICAgICBjID0gc3BsaXR0ZXIgKiBieTtcbiAgICAgICAgICAgIGJoaSA9IGMgLSAoYyAtIGJ5KTtcbiAgICAgICAgICAgIGJsbyA9IGJ5IC0gYmhpO1xuICAgICAgICAgICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgICAgICAgICAgX2kgPSBzMCAtIHQwO1xuICAgICAgICAgICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgICAgICAgICAgYlswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgICAgICAgICAgX2ogPSBzMSArIF9pO1xuICAgICAgICAgICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgICAgICAgICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIF9pID0gXzAgLSB0MTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICAgICAgICAgIGJbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICAgICAgICAgIHUzID0gX2ogKyBfaTtcbiAgICAgICAgICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICAgICAgICAgIGJbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICAgICAgICAgIGJbM10gPSB1MztcbiAgICAgICAgICAgIHJldHVybiA0O1xuICAgICAgICB9XG4gICAgfVxufVxuXG5mdW5jdGlvbiB0YWlsYWRkKGZpbmxlbiwgYSwgYiwgaywgeikge1xuICAgIGxldCBidmlydCwgYywgYWhpLCBhbG8sIGJoaSwgYmxvLCBfaSwgX2osIF9rLCBfMCwgczEsIHMwLCB1MztcbiAgICBzMSA9IGEgKiBiO1xuICAgIGMgPSBzcGxpdHRlciAqIGE7XG4gICAgYWhpID0gYyAtIChjIC0gYSk7XG4gICAgYWxvID0gYSAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiO1xuICAgIGJoaSA9IGMgLSAoYyAtIGIpO1xuICAgIGJsbyA9IGIgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIGMgPSBzcGxpdHRlciAqIGs7XG4gICAgYmhpID0gYyAtIChjIC0gayk7XG4gICAgYmxvID0gayAtIGJoaTtcbiAgICBfaSA9IHMwICogaztcbiAgICBjID0gc3BsaXR0ZXIgKiBzMDtcbiAgICBhaGkgPSBjIC0gKGMgLSBzMCk7XG4gICAgYWxvID0gczAgLSBhaGk7XG4gICAgdVswXSA9IGFsbyAqIGJsbyAtIChfaSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2ogPSBzMSAqIGs7XG4gICAgYyA9IHNwbGl0dGVyICogczE7XG4gICAgYWhpID0gYyAtIChjIC0gczEpO1xuICAgIGFsbyA9IHMxIC0gYWhpO1xuICAgIF8wID0gYWxvICogYmxvIC0gKF9qIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfayA9IF9pICsgXzA7XG4gICAgYnZpcnQgPSBfayAtIF9pO1xuICAgIHVbMV0gPSBfaSAtIChfayAtIGJ2aXJ0KSArIChfMCAtIGJ2aXJ0KTtcbiAgICB1MyA9IF9qICsgX2s7XG4gICAgdVsyXSA9IF9rIC0gKHUzIC0gX2opO1xuICAgIHVbM10gPSB1MztcbiAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCA0LCB1KTtcbiAgICBpZiAoeiAhPT0gMCkge1xuICAgICAgICBjID0gc3BsaXR0ZXIgKiB6O1xuICAgICAgICBiaGkgPSBjIC0gKGMgLSB6KTtcbiAgICAgICAgYmxvID0geiAtIGJoaTtcbiAgICAgICAgX2kgPSBzMCAqIHo7XG4gICAgICAgIGMgPSBzcGxpdHRlciAqIHMwO1xuICAgICAgICBhaGkgPSBjIC0gKGMgLSBzMCk7XG4gICAgICAgIGFsbyA9IHMwIC0gYWhpO1xuICAgICAgICB1WzBdID0gYWxvICogYmxvIC0gKF9pIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICAgICAgX2ogPSBzMSAqIHo7XG4gICAgICAgIGMgPSBzcGxpdHRlciAqIHMxO1xuICAgICAgICBhaGkgPSBjIC0gKGMgLSBzMSk7XG4gICAgICAgIGFsbyA9IHMxIC0gYWhpO1xuICAgICAgICBfMCA9IGFsbyAqIGJsbyAtIChfaiAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgICAgIF9rID0gX2kgKyBfMDtcbiAgICAgICAgYnZpcnQgPSBfayAtIF9pO1xuICAgICAgICB1WzFdID0gX2kgLSAoX2sgLSBidmlydCkgKyAoXzAgLSBidmlydCk7XG4gICAgICAgIHUzID0gX2ogKyBfaztcbiAgICAgICAgdVsyXSA9IF9rIC0gKHUzIC0gX2opO1xuICAgICAgICB1WzNdID0gdTM7XG4gICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIDQsIHUpO1xuICAgIH1cbiAgICByZXR1cm4gZmlubGVuO1xufVxuXG5mdW5jdGlvbiBvcmllbnQzZGFkYXB0KGF4LCBheSwgYXosIGJ4LCBieSwgYnosIGN4LCBjeSwgY3osIGR4LCBkeSwgZHosIHBlcm1hbmVudCkge1xuICAgIGxldCBmaW5sZW47XG4gICAgbGV0IGFkeHRhaWwsIGJkeHRhaWwsIGNkeHRhaWw7XG4gICAgbGV0IGFkeXRhaWwsIGJkeXRhaWwsIGNkeXRhaWw7XG4gICAgbGV0IGFkenRhaWwsIGJkenRhaWwsIGNkenRhaWw7XG4gICAgbGV0IGJ2aXJ0LCBjLCBhaGksIGFsbywgYmhpLCBibG8sIF9pLCBfaiwgX2ssIF8wLCBzMSwgczAsIHQxLCB0MCwgdTM7XG5cbiAgICBjb25zdCBhZHggPSBheCAtIGR4O1xuICAgIGNvbnN0IGJkeCA9IGJ4IC0gZHg7XG4gICAgY29uc3QgY2R4ID0gY3ggLSBkeDtcbiAgICBjb25zdCBhZHkgPSBheSAtIGR5O1xuICAgIGNvbnN0IGJkeSA9IGJ5IC0gZHk7XG4gICAgY29uc3QgY2R5ID0gY3kgLSBkeTtcbiAgICBjb25zdCBhZHogPSBheiAtIGR6O1xuICAgIGNvbnN0IGJkeiA9IGJ6IC0gZHo7XG4gICAgY29uc3QgY2R6ID0gY3ogLSBkejtcblxuICAgIHMxID0gYmR4ICogY2R5O1xuICAgIGMgPSBzcGxpdHRlciAqIGJkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBiZHgpO1xuICAgIGFsbyA9IGJkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjZHk7XG4gICAgYmhpID0gYyAtIChjIC0gY2R5KTtcbiAgICBibG8gPSBjZHkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gY2R4ICogYmR5O1xuICAgIGMgPSBzcGxpdHRlciAqIGNkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBjZHgpO1xuICAgIGFsbyA9IGNkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiZHk7XG4gICAgYmhpID0gYyAtIChjIC0gYmR5KTtcbiAgICBibG8gPSBiZHkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgYmNbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBiY1sxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgYmNbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBiY1szXSA9IHUzO1xuICAgIHMxID0gY2R4ICogYWR5O1xuICAgIGMgPSBzcGxpdHRlciAqIGNkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBjZHgpO1xuICAgIGFsbyA9IGNkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBhZHk7XG4gICAgYmhpID0gYyAtIChjIC0gYWR5KTtcbiAgICBibG8gPSBhZHkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYWR4ICogY2R5O1xuICAgIGMgPSBzcGxpdHRlciAqIGFkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBhZHgpO1xuICAgIGFsbyA9IGFkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjZHk7XG4gICAgYmhpID0gYyAtIChjIC0gY2R5KTtcbiAgICBibG8gPSBjZHkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgY2FbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBjYVsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgY2FbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBjYVszXSA9IHUzO1xuICAgIHMxID0gYWR4ICogYmR5O1xuICAgIGMgPSBzcGxpdHRlciAqIGFkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBhZHgpO1xuICAgIGFsbyA9IGFkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiZHk7XG4gICAgYmhpID0gYyAtIChjIC0gYmR5KTtcbiAgICBibG8gPSBiZHkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYmR4ICogYWR5O1xuICAgIGMgPSBzcGxpdHRlciAqIGJkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBiZHgpO1xuICAgIGFsbyA9IGJkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBhZHk7XG4gICAgYmhpID0gYyAtIChjIC0gYWR5KTtcbiAgICBibG8gPSBhZHkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgYWJbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBhYlsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgYWJbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBhYlszXSA9IHUzO1xuXG4gICAgZmlubGVuID0gc3VtKFxuICAgICAgICBzdW0oXG4gICAgICAgICAgICBzY2FsZSg0LCBiYywgYWR6LCBfOCksIF84LFxuICAgICAgICAgICAgc2NhbGUoNCwgY2EsIGJkeiwgXzhiKSwgXzhiLCBfMTYpLCBfMTYsXG4gICAgICAgIHNjYWxlKDQsIGFiLCBjZHosIF84KSwgXzgsIGZpbik7XG5cbiAgICBsZXQgZGV0ID0gZXN0aW1hdGUoZmlubGVuLCBmaW4pO1xuICAgIGxldCBlcnJib3VuZCA9IG8zZGVycmJvdW5kQiAqIHBlcm1hbmVudDtcbiAgICBpZiAoZGV0ID49IGVycmJvdW5kIHx8IC1kZXQgPj0gZXJyYm91bmQpIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICBidmlydCA9IGF4IC0gYWR4O1xuICAgIGFkeHRhaWwgPSBheCAtIChhZHggKyBidmlydCkgKyAoYnZpcnQgLSBkeCk7XG4gICAgYnZpcnQgPSBieCAtIGJkeDtcbiAgICBiZHh0YWlsID0gYnggLSAoYmR4ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZHgpO1xuICAgIGJ2aXJ0ID0gY3ggLSBjZHg7XG4gICAgY2R4dGFpbCA9IGN4IC0gKGNkeCArIGJ2aXJ0KSArIChidmlydCAtIGR4KTtcbiAgICBidmlydCA9IGF5IC0gYWR5O1xuICAgIGFkeXRhaWwgPSBheSAtIChhZHkgKyBidmlydCkgKyAoYnZpcnQgLSBkeSk7XG4gICAgYnZpcnQgPSBieSAtIGJkeTtcbiAgICBiZHl0YWlsID0gYnkgLSAoYmR5ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZHkpO1xuICAgIGJ2aXJ0ID0gY3kgLSBjZHk7XG4gICAgY2R5dGFpbCA9IGN5IC0gKGNkeSArIGJ2aXJ0KSArIChidmlydCAtIGR5KTtcbiAgICBidmlydCA9IGF6IC0gYWR6O1xuICAgIGFkenRhaWwgPSBheiAtIChhZHogKyBidmlydCkgKyAoYnZpcnQgLSBkeik7XG4gICAgYnZpcnQgPSBieiAtIGJkejtcbiAgICBiZHp0YWlsID0gYnogLSAoYmR6ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZHopO1xuICAgIGJ2aXJ0ID0gY3ogLSBjZHo7XG4gICAgY2R6dGFpbCA9IGN6IC0gKGNkeiArIGJ2aXJ0KSArIChidmlydCAtIGR6KTtcblxuICAgIGlmIChhZHh0YWlsID09PSAwICYmIGJkeHRhaWwgPT09IDAgJiYgY2R4dGFpbCA9PT0gMCAmJlxuICAgICAgICBhZHl0YWlsID09PSAwICYmIGJkeXRhaWwgPT09IDAgJiYgY2R5dGFpbCA9PT0gMCAmJlxuICAgICAgICBhZHp0YWlsID09PSAwICYmIGJkenRhaWwgPT09IDAgJiYgY2R6dGFpbCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gZGV0O1xuICAgIH1cblxuICAgIGVycmJvdW5kID0gbzNkZXJyYm91bmRDICogcGVybWFuZW50ICsgcmVzdWx0ZXJyYm91bmQgKiBNYXRoLmFicyhkZXQpO1xuICAgIGRldCArPVxuICAgICAgICBhZHogKiAoYmR4ICogY2R5dGFpbCArIGNkeSAqIGJkeHRhaWwgLSAoYmR5ICogY2R4dGFpbCArIGNkeCAqIGJkeXRhaWwpKSArIGFkenRhaWwgKiAoYmR4ICogY2R5IC0gYmR5ICogY2R4KSArXG4gICAgICAgIGJkeiAqIChjZHggKiBhZHl0YWlsICsgYWR5ICogY2R4dGFpbCAtIChjZHkgKiBhZHh0YWlsICsgYWR4ICogY2R5dGFpbCkpICsgYmR6dGFpbCAqIChjZHggKiBhZHkgLSBjZHkgKiBhZHgpICtcbiAgICAgICAgY2R6ICogKGFkeCAqIGJkeXRhaWwgKyBiZHkgKiBhZHh0YWlsIC0gKGFkeSAqIGJkeHRhaWwgKyBiZHggKiBhZHl0YWlsKSkgKyBjZHp0YWlsICogKGFkeCAqIGJkeSAtIGFkeSAqIGJkeCk7XG4gICAgaWYgKGRldCA+PSBlcnJib3VuZCB8fCAtZGV0ID49IGVycmJvdW5kKSB7XG4gICAgICAgIHJldHVybiBkZXQ7XG4gICAgfVxuXG4gICAgY29uc3QgYXRfbGVuID0gdGFpbGluaXQoYWR4dGFpbCwgYWR5dGFpbCwgYmR4LCBiZHksIGNkeCwgY2R5LCBhdF9iLCBhdF9jKTtcbiAgICBjb25zdCBidF9sZW4gPSB0YWlsaW5pdChiZHh0YWlsLCBiZHl0YWlsLCBjZHgsIGNkeSwgYWR4LCBhZHksIGJ0X2MsIGJ0X2EpO1xuICAgIGNvbnN0IGN0X2xlbiA9IHRhaWxpbml0KGNkeHRhaWwsIGNkeXRhaWwsIGFkeCwgYWR5LCBiZHgsIGJkeSwgY3RfYSwgY3RfYik7XG5cbiAgICBjb25zdCBiY3RsZW4gPSBzdW0oYnRfbGVuLCBidF9jLCBjdF9sZW4sIGN0X2IsIGJjdCk7XG4gICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoYmN0bGVuLCBiY3QsIGFkeiwgXzE2KSwgXzE2KTtcblxuICAgIGNvbnN0IGNhdGxlbiA9IHN1bShjdF9sZW4sIGN0X2EsIGF0X2xlbiwgYXRfYywgY2F0KTtcbiAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzY2FsZShjYXRsZW4sIGNhdCwgYmR6LCBfMTYpLCBfMTYpO1xuXG4gICAgY29uc3QgYWJ0bGVuID0gc3VtKGF0X2xlbiwgYXRfYiwgYnRfbGVuLCBidF9hLCBhYnQpO1xuICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHNjYWxlKGFidGxlbiwgYWJ0LCBjZHosIF8xNiksIF8xNik7XG5cbiAgICBpZiAoYWR6dGFpbCAhPT0gMCkge1xuICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzY2FsZSg0LCBiYywgYWR6dGFpbCwgXzEyKSwgXzEyKTtcbiAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoYmN0bGVuLCBiY3QsIGFkenRhaWwsIF8xNiksIF8xNik7XG4gICAgfVxuICAgIGlmIChiZHp0YWlsICE9PSAwKSB7XG4gICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHNjYWxlKDQsIGNhLCBiZHp0YWlsLCBfMTIpLCBfMTIpO1xuICAgICAgICBmaW5sZW4gPSBmaW5hZGQoZmlubGVuLCBzY2FsZShjYXRsZW4sIGNhdCwgYmR6dGFpbCwgXzE2KSwgXzE2KTtcbiAgICB9XG4gICAgaWYgKGNkenRhaWwgIT09IDApIHtcbiAgICAgICAgZmlubGVuID0gZmluYWRkKGZpbmxlbiwgc2NhbGUoNCwgYWIsIGNkenRhaWwsIF8xMiksIF8xMik7XG4gICAgICAgIGZpbmxlbiA9IGZpbmFkZChmaW5sZW4sIHNjYWxlKGFidGxlbiwgYWJ0LCBjZHp0YWlsLCBfMTYpLCBfMTYpO1xuICAgIH1cblxuICAgIGlmIChhZHh0YWlsICE9PSAwKSB7XG4gICAgICAgIGlmIChiZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBmaW5sZW4gPSB0YWlsYWRkKGZpbmxlbiwgYWR4dGFpbCwgYmR5dGFpbCwgY2R6LCBjZHp0YWlsKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY2R5dGFpbCAhPT0gMCkge1xuICAgICAgICAgICAgZmlubGVuID0gdGFpbGFkZChmaW5sZW4sIC1hZHh0YWlsLCBjZHl0YWlsLCBiZHosIGJkenRhaWwpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChiZHh0YWlsICE9PSAwKSB7XG4gICAgICAgIGlmIChjZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBmaW5sZW4gPSB0YWlsYWRkKGZpbmxlbiwgYmR4dGFpbCwgY2R5dGFpbCwgYWR6LCBhZHp0YWlsKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYWR5dGFpbCAhPT0gMCkge1xuICAgICAgICAgICAgZmlubGVuID0gdGFpbGFkZChmaW5sZW4sIC1iZHh0YWlsLCBhZHl0YWlsLCBjZHosIGNkenRhaWwpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChjZHh0YWlsICE9PSAwKSB7XG4gICAgICAgIGlmIChhZHl0YWlsICE9PSAwKSB7XG4gICAgICAgICAgICBmaW5sZW4gPSB0YWlsYWRkKGZpbmxlbiwgY2R4dGFpbCwgYWR5dGFpbCwgYmR6LCBiZHp0YWlsKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYmR5dGFpbCAhPT0gMCkge1xuICAgICAgICAgICAgZmlubGVuID0gdGFpbGFkZChmaW5sZW4sIC1jZHh0YWlsLCBiZHl0YWlsLCBhZHosIGFkenRhaWwpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGZpbltmaW5sZW4gLSAxXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIG9yaWVudDNkKGF4LCBheSwgYXosIGJ4LCBieSwgYnosIGN4LCBjeSwgY3osIGR4LCBkeSwgZHopIHtcbiAgICBjb25zdCBhZHggPSBheCAtIGR4O1xuICAgIGNvbnN0IGJkeCA9IGJ4IC0gZHg7XG4gICAgY29uc3QgY2R4ID0gY3ggLSBkeDtcbiAgICBjb25zdCBhZHkgPSBheSAtIGR5O1xuICAgIGNvbnN0IGJkeSA9IGJ5IC0gZHk7XG4gICAgY29uc3QgY2R5ID0gY3kgLSBkeTtcbiAgICBjb25zdCBhZHogPSBheiAtIGR6O1xuICAgIGNvbnN0IGJkeiA9IGJ6IC0gZHo7XG4gICAgY29uc3QgY2R6ID0gY3ogLSBkejtcblxuICAgIGNvbnN0IGJkeGNkeSA9IGJkeCAqIGNkeTtcbiAgICBjb25zdCBjZHhiZHkgPSBjZHggKiBiZHk7XG5cbiAgICBjb25zdCBjZHhhZHkgPSBjZHggKiBhZHk7XG4gICAgY29uc3QgYWR4Y2R5ID0gYWR4ICogY2R5O1xuXG4gICAgY29uc3QgYWR4YmR5ID0gYWR4ICogYmR5O1xuICAgIGNvbnN0IGJkeGFkeSA9IGJkeCAqIGFkeTtcblxuICAgIGNvbnN0IGRldCA9XG4gICAgICAgIGFkeiAqIChiZHhjZHkgLSBjZHhiZHkpICtcbiAgICAgICAgYmR6ICogKGNkeGFkeSAtIGFkeGNkeSkgK1xuICAgICAgICBjZHogKiAoYWR4YmR5IC0gYmR4YWR5KTtcblxuICAgIGNvbnN0IHBlcm1hbmVudCA9XG4gICAgICAgIChNYXRoLmFicyhiZHhjZHkpICsgTWF0aC5hYnMoY2R4YmR5KSkgKiBNYXRoLmFicyhhZHopICtcbiAgICAgICAgKE1hdGguYWJzKGNkeGFkeSkgKyBNYXRoLmFicyhhZHhjZHkpKSAqIE1hdGguYWJzKGJkeikgK1xuICAgICAgICAoTWF0aC5hYnMoYWR4YmR5KSArIE1hdGguYWJzKGJkeGFkeSkpICogTWF0aC5hYnMoY2R6KTtcblxuICAgIGNvbnN0IGVycmJvdW5kID0gbzNkZXJyYm91bmRBICogcGVybWFuZW50O1xuICAgIGlmIChkZXQgPiBlcnJib3VuZCB8fCAtZGV0ID4gZXJyYm91bmQpIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICByZXR1cm4gb3JpZW50M2RhZGFwdChheCwgYXksIGF6LCBieCwgYnksIGJ6LCBjeCwgY3ksIGN6LCBkeCwgZHksIGR6LCBwZXJtYW5lbnQpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gb3JpZW50M2RmYXN0KGF4LCBheSwgYXosIGJ4LCBieSwgYnosIGN4LCBjeSwgY3osIGR4LCBkeSwgZHopIHtcbiAgICBjb25zdCBhZHggPSBheCAtIGR4O1xuICAgIGNvbnN0IGJkeCA9IGJ4IC0gZHg7XG4gICAgY29uc3QgY2R4ID0gY3ggLSBkeDtcbiAgICBjb25zdCBhZHkgPSBheSAtIGR5O1xuICAgIGNvbnN0IGJkeSA9IGJ5IC0gZHk7XG4gICAgY29uc3QgY2R5ID0gY3kgLSBkeTtcbiAgICBjb25zdCBhZHogPSBheiAtIGR6O1xuICAgIGNvbnN0IGJkeiA9IGJ6IC0gZHo7XG4gICAgY29uc3QgY2R6ID0gY3ogLSBkejtcblxuICAgIHJldHVybiBhZHggKiAoYmR5ICogY2R6IC0gYmR6ICogY2R5KSArXG4gICAgICAgIGJkeCAqIChjZHkgKiBhZHogLSBjZHogKiBhZHkpICtcbiAgICAgICAgY2R4ICogKGFkeSAqIGJkeiAtIGFkeiAqIGJkeSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient3d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/util.js":
/*!****************************************************!*\
  !*** ./node_modules/robust-predicates/esm/util.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   estimate: () => (/* binding */ estimate),\n/* harmony export */   negate: () => (/* binding */ negate),\n/* harmony export */   resulterrbound: () => (/* binding */ resulterrbound),\n/* harmony export */   scale: () => (/* binding */ scale),\n/* harmony export */   splitter: () => (/* binding */ splitter),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   sum_three: () => (/* binding */ sum_three),\n/* harmony export */   vec: () => (/* binding */ vec)\n/* harmony export */ });\nconst epsilon = 1.1102230246251565e-16;\nconst splitter = 134217729;\nconst resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nfunction sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nfunction scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nfunction estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nfunction vec(n) {\n    return new Float64Array(n);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/index.js":
/*!*************************************************!*\
  !*** ./node_modules/robust-predicates/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incircle),\n/* harmony export */   incirclefast: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incirclefast),\n/* harmony export */   insphere: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.insphere),\n/* harmony export */   inspherefast: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.inspherefast),\n/* harmony export */   orient2d: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2d),\n/* harmony export */   orient2dfast: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2dfast),\n/* harmony export */   orient3d: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3d),\n/* harmony export */   orient3dfast: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./esm/orient2d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient2d.js\");\n/* harmony import */ var _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./esm/orient3d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient3d.js\");\n/* harmony import */ var _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./esm/incircle.js */ \"(ssr)/./node_modules/robust-predicates/esm/incircle.js\");\n/* harmony import */ var _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./esm/insphere.js */ \"(ssr)/./node_modules/robust-predicates/esm/insphere.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUN5RDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyb2J1c3QtcHJlZGljYXRlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQge29yaWVudDJkLCBvcmllbnQyZGZhc3R9IGZyb20gJy4vZXNtL29yaWVudDJkLmpzJztcbmV4cG9ydCB7b3JpZW50M2QsIG9yaWVudDNkZmFzdH0gZnJvbSAnLi9lc20vb3JpZW50M2QuanMnO1xuZXhwb3J0IHtpbmNpcmNsZSwgaW5jaXJjbGVmYXN0fSBmcm9tICcuL2VzbS9pbmNpcmNsZS5qcyc7XG5leHBvcnQge2luc3BoZXJlLCBpbnNwaGVyZWZhc3R9IGZyb20gJy4vZXNtL2luc3BoZXJlLmpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/index.js\n");

/***/ })

};
;