import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { getQueryClient } from '@/utils/query-client';
import ShowFacilitySection from './components/show_facility_section';
import AppLayout from '@/components/ui/app_layout';
import FacilityDetailBox from './components/facility_detail_box';
import FacilityInfoBox from './components/facility_info_box';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{ id: string }>;
}>) {
  const resolvedParams = await params;
  const { id } = resolvedParams;

  const isEdit = !Number.isNaN(Number(id)) && id !== 'add';

  if (!isEdit && id !== 'add') {
    redirect('/dashboard/setup/show-facility/add');
  }

  return (
    <div className="flex flex-col self-stretch w-full gap-8">
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <AppLayout
          items={[
            { title: 'Setup', link: '/dashboard/setup' },
            { title: 'Master-Setup', link: '/dashboard/setup' },
            {
              title: 'Show Facility',
              link: '/dashboard/setup/show-facility',
            },
            {
              title: 'Facility Detail',
              link: `/dashboard/setup/show-facility/${id}`,
            },
          ]}
          headerExtras={
            isEdit ? (
              <HydrationBoundary state={dehydrate(getQueryClient())}>
                <FacilityDetailBox id={Number(id)} />
              </HydrationBoundary>
            ) : undefined
          }
        >
          <div className="flex flex-col w-full">
            {
              <HydrationBoundary state={dehydrate(getQueryClient())}>
                <FacilityInfoBox id={isEdit ? Number(id) : undefined} />
              </HydrationBoundary>
            }
            <div className="w-full flex">
              <ShowFacilitySection id={isEdit ? Number(id) : undefined} />
              <div className="w-full overflow-y-auto scrollbar-hide">
                {children}
              </div>
            </div>
          </div>
        </AppLayout>
      </HydrationBoundary>
    </div>
  );
}
