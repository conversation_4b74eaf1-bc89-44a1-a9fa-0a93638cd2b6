"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_pascal_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/pascal.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/pascal.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Pascal\\\",\\\"fileTypes\\\":[\\\"pas\\\",\\\"p\\\",\\\"pp\\\",\\\"dfm\\\",\\\"fmx\\\",\\\"dpr\\\",\\\"dpk\\\",\\\"lfm\\\",\\\"lpr\\\"],\\\"name\\\":\\\"pascal\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?i:(absolute|abstract|add|all|and_then|array|as|asc|asm|assembler|async|attribute|autoreleasepool|await|begin|bindable|block|by|case|cdecl|class|concat|const|constref|copy|cppdecl|contains|default|delegate|deprecated|desc|distinct|div|each|else|empty|end|ensure|enum|equals|event|except|export|exports|extension|external|far|file|finalization|finalizer|finally|flags|forward|from|future|generic|goto|group|has|helper|if|implements|implies|import|in|index|inherited|initialization|inline|interrupt|into|invariants|is|iterator|label|library|join|lazy|lifetimestrategy|locked|locking|loop|mapped|matching|message|method|mod|module|name|namespace|near|nested|new|nostackframe|not|notify|nullable|object|of|old|oldfpccall|on|only|operator|optional|or_else|order|otherwise|out|override|package|packed|parallel|params|partial|pascal|pinned|platform|pow|private|program|protected|public|published|interface|implementation|qualified|queryable|raises|read|readonly|record|reference|register|remove|resident|require|requires|resourcestring|restricted|result|reverse|safecall|sealed|segment|select|selector|sequence|set|shl|shr|skip|specialize|soft|static|stored|stdcall|step|strict|strong|take|then|threadvar|to|try|tuple|type|unconstrained|unit|unmanaged|unretained|unsafe|uses|using|var|view|virtual|volatile|weak|dynamic|overload|reintroduce|where|with|write|xor|yield))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.pascal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.prototype.pascal\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.prototype.pascal\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?i:(function|procedure|constructor|destructor))\\\\\\\\b\\\\\\\\s+(\\\\\\\\w+(\\\\\\\\.\\\\\\\\w+)?)(\\\\\\\\(.*?\\\\\\\\))?;\\\\\\\\s*(?=(?i:attribute|forward|external))\\\",\\\"name\\\":\\\"meta.function.prototype.pascal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.pascal\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.pascal\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?i:(function|procedure|constructor|destructor|property|read|write))\\\\\\\\b\\\\\\\\s+(\\\\\\\\w+(\\\\\\\\.\\\\\\\\w+)?)\\\",\\\"name\\\":\\\"meta.function.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(self|result))\\\\\\\\b\\\",\\\"name\\\":\\\"token.variable\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(and|or))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(break|continue|exit|abort|while|do|downto|for|raise|repeat|until))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.pascal\\\"},{\\\"begin\\\":\\\"\\\\\\\\{\\\\\\\\$\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"string.regexp\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"string.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(ansichar|ansistring|boolean|byte|cardinal|char|comp|currency|double|dword|extended|file|integer|int8|int16|int32|int64|longint|longword|nativeint|nativeuint|olevariant|pansichar|pchar|pwidechar|pointer|real|shortint|shortstring|single|smallint|string|uint8|uint16|uint32|uint64|variant|widechar|widestring|word|wordbool|uintptr|intptr))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.support.type.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)|(\\\\\\\\d*\\\\\\\\.\\\\\\\\d+([eE][\\\\\\\\-+]?\\\\\\\\d+)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\$[0-9a-fA-F]{1,16}\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hex.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(true|false|nil))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.pascal\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:(Assert))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.pascal\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.pascal\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.pascal.two\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.pascal\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\)\\\",\\\"name\\\":\\\"comment.block.pascal.one\\\"},{\\\"begin\\\":\\\"\\\\\\\\{(?!\\\\\\\\$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.pascal\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"comment.block.pascal.two\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.pascal\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.pascal\\\"}},\\\"name\\\":\\\"string.quoted.single.pascal\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\",\\\"name\\\":\\\"constant.character.escape.apostrophe.pascal\\\"}]},{\\\"match\\\":\\\"\\\\\\\\#\\\\\\\\d+\\\",\\\"name\\\":\\\"string.other.pascal\\\"}],\\\"scopeName\\\":\\\"source.pascal\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L3Bhc2NhbC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3QyxtSkFBbUosdzRDQUF3NEMsRUFBRSxjQUFjLE9BQU8sMkNBQTJDLFFBQVEsb0RBQW9ELG9IQUFvSCx3RkFBd0YsRUFBRSxjQUFjLE9BQU8sMENBQTBDLFFBQVEsMENBQTBDLDRKQUE0SixFQUFFLHVFQUF1RSxFQUFFLDJFQUEyRSxFQUFFLG9JQUFvSSxFQUFFLGlCQUFpQixzQkFBc0IsT0FBTyw0QkFBNEIsZ0JBQWdCLDhCQUE4QixFQUFFLHVaQUF1WixFQUFFLGdIQUFnSCxFQUFFLDZCQUE2QixLQUFLLGlEQUFpRCxFQUFFLG9GQUFvRixFQUFFLG1FQUFtRSxFQUFFLHFEQUFxRCxPQUFPLDREQUE0RCxzQ0FBc0Msb0NBQW9DLE9BQU8sb0RBQW9ELHFFQUFxRSxFQUFFLEVBQUUsdUNBQXVDLE9BQU8sb0RBQW9ELDhEQUE4RCxFQUFFLGlCQUFpQiwwQkFBMEIsT0FBTyxvREFBb0QsZ0JBQWdCLHlDQUF5QyxFQUFFLG1DQUFtQyxPQUFPLHlEQUF5RCxnQ0FBZ0MsT0FBTyx1REFBdUQseURBQXlELDBFQUEwRSxFQUFFLEVBQUUsMkRBQTJELGtDQUFrQzs7QUFFNzFJLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxccGFzY2FsLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsYW5nID0gT2JqZWN0LmZyZWV6ZShKU09OLnBhcnNlKFwie1xcXCJkaXNwbGF5TmFtZVxcXCI6XFxcIlBhc2NhbFxcXCIsXFxcImZpbGVUeXBlc1xcXCI6W1xcXCJwYXNcXFwiLFxcXCJwXFxcIixcXFwicHBcXFwiLFxcXCJkZm1cXFwiLFxcXCJmbXhcXFwiLFxcXCJkcHJcXFwiLFxcXCJkcGtcXFwiLFxcXCJsZm1cXFwiLFxcXCJscHJcXFwiXSxcXFwibmFtZVxcXCI6XFxcInBhc2NhbFxcXCIsXFxcInBhdHRlcm5zXFxcIjpbe1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYig/aTooYWJzb2x1dGV8YWJzdHJhY3R8YWRkfGFsbHxhbmRfdGhlbnxhcnJheXxhc3xhc2N8YXNtfGFzc2VtYmxlcnxhc3luY3xhdHRyaWJ1dGV8YXV0b3JlbGVhc2Vwb29sfGF3YWl0fGJlZ2lufGJpbmRhYmxlfGJsb2NrfGJ5fGNhc2V8Y2RlY2x8Y2xhc3N8Y29uY2F0fGNvbnN0fGNvbnN0cmVmfGNvcHl8Y3BwZGVjbHxjb250YWluc3xkZWZhdWx0fGRlbGVnYXRlfGRlcHJlY2F0ZWR8ZGVzY3xkaXN0aW5jdHxkaXZ8ZWFjaHxlbHNlfGVtcHR5fGVuZHxlbnN1cmV8ZW51bXxlcXVhbHN8ZXZlbnR8ZXhjZXB0fGV4cG9ydHxleHBvcnRzfGV4dGVuc2lvbnxleHRlcm5hbHxmYXJ8ZmlsZXxmaW5hbGl6YXRpb258ZmluYWxpemVyfGZpbmFsbHl8ZmxhZ3N8Zm9yd2FyZHxmcm9tfGZ1dHVyZXxnZW5lcmljfGdvdG98Z3JvdXB8aGFzfGhlbHBlcnxpZnxpbXBsZW1lbnRzfGltcGxpZXN8aW1wb3J0fGlufGluZGV4fGluaGVyaXRlZHxpbml0aWFsaXphdGlvbnxpbmxpbmV8aW50ZXJydXB0fGludG98aW52YXJpYW50c3xpc3xpdGVyYXRvcnxsYWJlbHxsaWJyYXJ5fGpvaW58bGF6eXxsaWZldGltZXN0cmF0ZWd5fGxvY2tlZHxsb2NraW5nfGxvb3B8bWFwcGVkfG1hdGNoaW5nfG1lc3NhZ2V8bWV0aG9kfG1vZHxtb2R1bGV8bmFtZXxuYW1lc3BhY2V8bmVhcnxuZXN0ZWR8bmV3fG5vc3RhY2tmcmFtZXxub3R8bm90aWZ5fG51bGxhYmxlfG9iamVjdHxvZnxvbGR8b2xkZnBjY2FsbHxvbnxvbmx5fG9wZXJhdG9yfG9wdGlvbmFsfG9yX2Vsc2V8b3JkZXJ8b3RoZXJ3aXNlfG91dHxvdmVycmlkZXxwYWNrYWdlfHBhY2tlZHxwYXJhbGxlbHxwYXJhbXN8cGFydGlhbHxwYXNjYWx8cGlubmVkfHBsYXRmb3JtfHBvd3xwcml2YXRlfHByb2dyYW18cHJvdGVjdGVkfHB1YmxpY3xwdWJsaXNoZWR8aW50ZXJmYWNlfGltcGxlbWVudGF0aW9ufHF1YWxpZmllZHxxdWVyeWFibGV8cmFpc2VzfHJlYWR8cmVhZG9ubHl8cmVjb3JkfHJlZmVyZW5jZXxyZWdpc3RlcnxyZW1vdmV8cmVzaWRlbnR8cmVxdWlyZXxyZXF1aXJlc3xyZXNvdXJjZXN0cmluZ3xyZXN0cmljdGVkfHJlc3VsdHxyZXZlcnNlfHNhZmVjYWxsfHNlYWxlZHxzZWdtZW50fHNlbGVjdHxzZWxlY3RvcnxzZXF1ZW5jZXxzZXR8c2hsfHNocnxza2lwfHNwZWNpYWxpemV8c29mdHxzdGF0aWN8c3RvcmVkfHN0ZGNhbGx8c3RlcHxzdHJpY3R8c3Ryb25nfHRha2V8dGhlbnx0aHJlYWR2YXJ8dG98dHJ5fHR1cGxlfHR5cGV8dW5jb25zdHJhaW5lZHx1bml0fHVubWFuYWdlZHx1bnJldGFpbmVkfHVuc2FmZXx1c2VzfHVzaW5nfHZhcnx2aWV3fHZpcnR1YWx8dm9sYXRpbGV8d2Vha3xkeW5hbWljfG92ZXJsb2FkfHJlaW50cm9kdWNlfHdoZXJlfHdpdGh8d3JpdGV8eG9yfHlpZWxkKSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5wYXNjYWxcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5wcm90b3R5cGUucGFzY2FsXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLmZ1bmN0aW9uLnByb3RvdHlwZS5wYXNjYWxcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD9pOihmdW5jdGlvbnxwcm9jZWR1cmV8Y29uc3RydWN0b3J8ZGVzdHJ1Y3RvcikpXFxcXFxcXFxiXFxcXFxcXFxzKyhcXFxcXFxcXHcrKFxcXFxcXFxcLlxcXFxcXFxcdyspPykoXFxcXFxcXFwoLio/XFxcXFxcXFwpKT87XFxcXFxcXFxzKig/PSg/aTphdHRyaWJ1dGV8Zm9yd2FyZHxleHRlcm5hbCkpXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuZnVuY3Rpb24ucHJvdG90eXBlLnBhc2NhbFxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3RvcmFnZS50eXBlLmZ1bmN0aW9uLnBhc2NhbFxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJlbnRpdHkubmFtZS5mdW5jdGlvbi5wYXNjYWxcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD9pOihmdW5jdGlvbnxwcm9jZWR1cmV8Y29uc3RydWN0b3J8ZGVzdHJ1Y3Rvcnxwcm9wZXJ0eXxyZWFkfHdyaXRlKSlcXFxcXFxcXGJcXFxcXFxcXHMrKFxcXFxcXFxcdysoXFxcXFxcXFwuXFxcXFxcXFx3Kyk/KVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmZ1bmN0aW9uLnBhc2NhbFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoP2k6KHNlbGZ8cmVzdWx0KSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwidG9rZW4udmFyaWFibGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD9pOihhbmR8b3IpKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnBhc2NhbFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoP2k6KGJyZWFrfGNvbnRpbnVlfGV4aXR8YWJvcnR8d2hpbGV8ZG98ZG93bnRvfGZvcnxyYWlzZXxyZXBlYXR8dW50aWwpKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLmNvbnRyb2wucGFzY2FsXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxce1xcXFxcXFxcJFxcXCIsXFxcImNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInN0cmluZy5yZWdleHBcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcfVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucmVnZXhwXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYig/aTooYW5zaWNoYXJ8YW5zaXN0cmluZ3xib29sZWFufGJ5dGV8Y2FyZGluYWx8Y2hhcnxjb21wfGN1cnJlbmN5fGRvdWJsZXxkd29yZHxleHRlbmRlZHxmaWxlfGludGVnZXJ8aW50OHxpbnQxNnxpbnQzMnxpbnQ2NHxsb25naW50fGxvbmd3b3JkfG5hdGl2ZWludHxuYXRpdmV1aW50fG9sZXZhcmlhbnR8cGFuc2ljaGFyfHBjaGFyfHB3aWRlY2hhcnxwb2ludGVyfHJlYWx8c2hvcnRpbnR8c2hvcnRzdHJpbmd8c2luZ2xlfHNtYWxsaW50fHN0cmluZ3x1aW50OHx1aW50MTZ8dWludDMyfHVpbnQ2NHx2YXJpYW50fHdpZGVjaGFyfHdpZGVzdHJpbmd8d29yZHx3b3JkYm9vbHx1aW50cHRyfGludHB0cikpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2Uuc3VwcG9ydC50eXBlLnBhc2NhbFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoXFxcXFxcXFxkKyl8KFxcXFxcXFxcZCpcXFxcXFxcXC5cXFxcXFxcXGQrKFtlRV1bXFxcXFxcXFwtK10/XFxcXFxcXFxkKyk/KVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLnBhc2NhbFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXCRbMC05YS1mQS1GXXsxLDE2fVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5udW1lcmljLmhleC5wYXNjYWxcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKD9pOih0cnVlfGZhbHNlfG5pbCkpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlLnBhc2NhbFxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCJcXFxcXFxcXGIoP2k6KEFzc2VydCkpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQuY29udHJvbFxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCIoXlsgXFxcXFxcXFx0XSspPyg/PS8vKVxcXCIsXFxcImJlZ2luQ2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwicHVuY3R1YXRpb24ud2hpdGVzcGFjZS5jb21tZW50LmxlYWRpbmcucGFzY2FsXFxcIn19LFxcXCJlbmRcXFwiOlxcXCIoPyFcXFxcXFxcXEcpXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiLy9cXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uY29tbWVudC5wYXNjYWxcXFwifX0sXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcblxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuZG91YmxlLXNsYXNoLnBhc2NhbC50d29cXFwifV19LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcXChcXFxcXFxcXCpcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQucGFzY2FsXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXCpcXFxcXFxcXClcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wYXNjYWwub25lXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIlxcXFxcXFxceyg/IVxcXFxcXFxcJClcXFwiLFxcXCJjYXB0dXJlc1xcXCI6e1xcXCIwXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJwdW5jdHVhdGlvbi5kZWZpbml0aW9uLmNvbW1lbnQucGFzY2FsXFxcIn19LFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXH1cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5wYXNjYWwudHdvXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIidcXFwiLFxcXCJiZWdpbkNhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmJlZ2luLnBhc2NhbFxcXCJ9fSxcXFwiZW5kXFxcIjpcXFwiJ1xcXCIsXFxcImVuZENhcHR1cmVzXFxcIjp7XFxcIjBcXFwiOntcXFwibmFtZVxcXCI6XFxcInB1bmN0dWF0aW9uLmRlZmluaXRpb24uc3RyaW5nLmVuZC5wYXNjYWxcXFwifX0sXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLnNpbmdsZS5wYXNjYWxcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwibWF0Y2hcXFwiOlxcXCInJ1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb25zdGFudC5jaGFyYWN0ZXIuZXNjYXBlLmFwb3N0cm9waGUucGFzY2FsXFxcIn1dfSx7XFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFwjXFxcXFxcXFxkK1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcub3RoZXIucGFzY2FsXFxcIn1dLFxcXCJzY29wZU5hbWVcXFwiOlxcXCJzb3VyY2UucGFzY2FsXFxcIn1cIikpXG5cbmV4cG9ydCBkZWZhdWx0IFtcbmxhbmdcbl1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/pascal.mjs\n"));

/***/ })

}]);