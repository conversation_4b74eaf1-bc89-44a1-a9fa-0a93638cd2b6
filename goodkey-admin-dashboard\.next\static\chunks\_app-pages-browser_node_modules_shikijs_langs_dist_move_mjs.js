"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_move_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/move.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/move.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Move\\\",\\\"name\\\":\\\"move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#address\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module\\\"},{\\\"include\\\":\\\"#script\\\"},{\\\"include\\\":\\\"#annotation\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#annotation\\\"},{\\\"include\\\":\\\"#entry\\\"},{\\\"include\\\":\\\"#public-scope\\\"},{\\\"include\\\":\\\"#public\\\"},{\\\"include\\\":\\\"#native\\\"},{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#friend\\\"},{\\\"include\\\":\\\"#const\\\"},{\\\"include\\\":\\\"#struct\\\"},{\\\"include\\\":\\\"#has_ability\\\"},{\\\"include\\\":\\\"#enum\\\"},{\\\"include\\\":\\\"#macro\\\"},{\\\"include\\\":\\\"#fun\\\"},{\\\"include\\\":\\\"#spec\\\"}],\\\"repository\\\":{\\\"=== DEPRECATED_BELOW ===\\\":{},\\\"abilities\\\":{\\\"comment\\\":\\\"Ability\\\",\\\"match\\\":\\\"\\\\\\\\b(store|key|drop|copy)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.ability.move\\\"},\\\"address\\\":{\\\"begin\\\":\\\"\\\\\\\\b(address)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.address.keyword.move\\\"}},\\\"comment\\\":\\\"Address block\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.address_block.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?<=address)\\\",\\\"comment\\\":\\\"Address value/const\\\",\\\"end\\\":\\\"(?=[{])\\\",\\\"name\\\":\\\"meta.address.definition.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#address_literal\\\"},{\\\"comment\\\":\\\"Named Address\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.move\\\"}]},{\\\"include\\\":\\\"#module\\\"}]},\\\"annotation\\\":{\\\"begin\\\":\\\"#\\\\\\\\[\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"support.constant.annotation.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Annotation name\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\s*(?=\\\\\\\\=)\\\",\\\"name\\\":\\\"meta.annotation.name.move\\\"},{\\\"begin\\\":\\\"=\\\",\\\"comment\\\":\\\"Annotation value\\\",\\\"end\\\":\\\"(?=[,\\\\\\\\]])\\\",\\\"name\\\":\\\"meta.annotation.value.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literals\\\"}]}]},\\\"as\\\":{\\\"comment\\\":\\\"Keyword as (highlighted)\\\",\\\"match\\\":\\\"\\\\\\\\b(as)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.as.move\\\"},\\\"as-import\\\":{\\\"comment\\\":\\\"Keyword as in import statement; not highlighted\\\",\\\"match\\\":\\\"\\\\\\\\b(as)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.import.as.move\\\"},\\\"block\\\":{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Block expression or definition\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.block.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expr\\\"}]},\\\"block-comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*[\\\\\\\\*!](?![\\\\\\\\*/])\\\",\\\"comment\\\":\\\"Block documentation comment\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.move\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"comment\\\":\\\"Block comment\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.move\\\"}]},\\\"capitalized\\\":{\\\"comment\\\":\\\"MyType - capitalized type name\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z][a-zA-Z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.use.move\\\"},\\\"comments\\\":{\\\"name\\\":\\\"meta.comments.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#doc-comments\\\"},{\\\"include\\\":\\\"#line-comments\\\"},{\\\"include\\\":\\\"#block-comments\\\"}]},\\\"const\\\":{\\\"begin\\\":\\\"\\\\\\\\b(const)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.const.move\\\"}},\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"meta.const.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#primitives\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Z_0-9]+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.move\\\"},{\\\"include\\\":\\\"#error_const\\\"}]},\\\"control\\\":{\\\"comment\\\":\\\"Control flow\\\",\\\"match\\\":\\\"\\\\\\\\b(return|while|loop|if|else|break|continue|abort)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.move\\\"},\\\"doc-comments\\\":{\\\"begin\\\":\\\"///\\\",\\\"comment\\\":\\\"Documentation comment\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.block.documentation.move\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.underline.link.move\\\"}},\\\"comment\\\":\\\"Escaped member / link\\\",\\\"match\\\":\\\"`(\\\\\\\\w+)`\\\"}]},\\\"entry\\\":{\\\"comment\\\":\\\"entry\\\",\\\"match\\\":\\\"\\\\\\\\b(entry)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.visibility.entry.move\\\"},\\\"enum\\\":{\\\"begin\\\":\\\"\\\\\\\\b(enum)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.enum.move\\\"}},\\\"comment\\\":\\\"Enum syntax\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.enum.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#type_param\\\"},{\\\"comment\\\":\\\"Enum name (ident)\\\",\\\"match\\\":\\\"\\\\\\\\b[A-Z][a-zA-Z_0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.enum.move\\\"},{\\\"include\\\":\\\"#has\\\"},{\\\"include\\\":\\\"#abilities\\\"},{\\\"begin\\\":\\\"{\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.enum.definition.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Za-z_0-9]*)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.enum.move\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Za-z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.enum.move\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.enum.tuple.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expr_generic\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.enum.struct.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#expr_generic\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"}]}]}]},\\\"error_const\\\":{\\\"match\\\":\\\"\\\\\\\\b(E[A-Z][A-Za-z0-9_]*)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.error.const.move\\\"},\\\"escaped_identifier\\\":{\\\"begin\\\":\\\"`\\\",\\\"comment\\\":\\\"Escaped variable\\\",\\\"end\\\":\\\"`\\\",\\\"name\\\":\\\"variable.language.escaped.move\\\"},\\\"expr\\\":{\\\"comment\\\":\\\"Aggregate Expression\\\",\\\"name\\\":\\\"meta.expression.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#expr_generic\\\"},{\\\"include\\\":\\\"#packed_field\\\"},{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#as\\\"},{\\\"include\\\":\\\"#mut\\\"},{\\\"include\\\":\\\"#let\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#control\\\"},{\\\"include\\\":\\\"#move_copy\\\"},{\\\"include\\\":\\\"#resource_methods\\\"},{\\\"include\\\":\\\"#self_access\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#label\\\"},{\\\"include\\\":\\\"#macro_call\\\"},{\\\"include\\\":\\\"#local_call\\\"},{\\\"include\\\":\\\"#method_call\\\"},{\\\"include\\\":\\\"#path_access\\\"},{\\\"include\\\":\\\"#match_expression\\\"},{\\\"match\\\":\\\"\\\\\\\\$(?=[a-z])\\\",\\\"name\\\":\\\"keyword.operator.macro.dollar.move\\\"},{\\\"match\\\":\\\"(?<=[$])[a-z][A-Z_0-9a-z]*\\\",\\\"name\\\":\\\"variable.other.meta.move\\\"},{\\\"comment\\\":\\\"ALL_CONST_CAPS\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z][A-Z_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.move\\\"},{\\\"include\\\":\\\"#error_const\\\"},{\\\"comment\\\":\\\"CustomType\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z][a-zA-Z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.move\\\"},{\\\"include\\\":\\\"#paren\\\"},{\\\"include\\\":\\\"#block\\\"}]},\\\"expr_generic\\\":{\\\"begin\\\":\\\"<(?=([\\\\\\\\sa-z_,0-9A-Z<>]+>))\\\",\\\"comment\\\":\\\"< angle brackets >\\\",\\\"end\\\":\\\">\\\",\\\"name\\\":\\\"meta.expression.generic.type.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#expr_generic\\\"}]},\\\"friend\\\":{\\\"begin\\\":\\\"\\\\\\\\b(friend)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.move\\\"}},\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"meta.friend.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#address_literal\\\"},{\\\"comment\\\":\\\"Name of the imported module\\\",\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z][A-Za-z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.module.move\\\"}]},\\\"fun\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#fun_signature\\\"},{\\\"include\\\":\\\"#block\\\"}]},\\\"fun_body\\\":{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Function body\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.fun_body.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expr\\\"}]},\\\"fun_call\\\":{\\\"begin\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\s*(?:<[\\\\\\\\w\\\\\\\\s,]+>)?\\\\\\\\s*[(]\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.call.move\\\"}},\\\"comment\\\":\\\"Function call\\\",\\\"end\\\":\\\"[)]\\\",\\\"name\\\":\\\"meta.fun_call.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#resource_methods\\\"},{\\\"include\\\":\\\"#self_access\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#move_copy\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#fun_call\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#mut\\\"},{\\\"include\\\":\\\"#as\\\"}]},\\\"fun_signature\\\":{\\\"begin\\\":\\\"\\\\\\\\b(fun)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.fun.move\\\"}},\\\"comment\\\":\\\"Function signature\\\",\\\"end\\\":\\\"(?=[;{])\\\",\\\"name\\\":\\\"meta.fun_signature.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#mut\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\bfun)\\\",\\\"comment\\\":\\\"Function name\\\",\\\"end\\\":\\\"(?=[<(])\\\",\\\"name\\\":\\\"meta.function_name.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.move\\\"}]},{\\\"include\\\":\\\"#type_param\\\"},{\\\"begin\\\":\\\"[(]\\\",\\\"comment\\\":\\\"Parentheses\\\",\\\"end\\\":\\\"[)]\\\",\\\"name\\\":\\\"meta.parentheses.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#self_access\\\"},{\\\"include\\\":\\\"#expr_generic\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#mut\\\"}]},{\\\"comment\\\":\\\"Keyword acquires\\\",\\\"match\\\":\\\"\\\\\\\\b(acquires)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier\\\"}]},\\\"has\\\":{\\\"comment\\\":\\\"Has Abilities\\\",\\\"match\\\":\\\"\\\\\\\\b(has)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.ability.has.move\\\"},\\\"has_ability\\\":{\\\"begin\\\":\\\"(?<=[})])\\\\\\\\s+(has)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.move\\\"}},\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"meta.has.ability.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#abilities\\\"}]},\\\"ident\\\":{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z][A-Z_a-z0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.identifier.move\\\"},\\\"import\\\":{\\\"begin\\\":\\\"\\\\\\\\b(use)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.move\\\"}},\\\"end\\\":\\\";\\\",\\\"name\\\":\\\"meta.import.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#use_fun\\\"},{\\\"include\\\":\\\"#address_literal\\\"},{\\\"include\\\":\\\"#as-import\\\"},{\\\"comment\\\":\\\"Uppercase entities\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.move\\\"},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Module members\\\",\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#as-import\\\"},{\\\"comment\\\":\\\"Uppercase entities\\\",\\\"match\\\":\\\"\\\\\\\\b([A-Z]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.move\\\"}]},{\\\"comment\\\":\\\"Name of the imported module\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.entity.name.type.module.move\\\"}]},\\\"inline\\\":{\\\"comment\\\":\\\"inline\\\",\\\"match\\\":\\\"\\\\\\\\b(inline)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.visibility.inline.move\\\"},\\\"label\\\":{\\\"comment\\\":\\\"Label\\\",\\\"match\\\":\\\"'[a-z][a-z_0-9]*\\\",\\\"name\\\":\\\"string.quoted.single.label.move\\\"},\\\"let\\\":{\\\"comment\\\":\\\"Keyword let\\\",\\\"match\\\":\\\"\\\\\\\\b(let)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.move\\\"},\\\"line-comments\\\":{\\\"begin\\\":\\\"//\\\",\\\"comment\\\":\\\"Single-line comment\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.move\\\"},\\\"literals\\\":{\\\"comment\\\":\\\"Literals supported in Move\\\",\\\"name\\\":\\\"meta.literal.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"base16 address literal\\\",\\\"match\\\":\\\"@0x[A-F0-9a-f]+\\\",\\\"name\\\":\\\"support.constant.address.base16.move\\\"},{\\\"comment\\\":\\\"named address literal @[ident]\\\",\\\"match\\\":\\\"@[a-zA-Z][a-zA-Z_0-9]*\\\",\\\"name\\\":\\\"support.constant.address.name.move\\\"},{\\\"comment\\\":\\\"Hex literal\\\",\\\"match\\\":\\\"0x[_a-fA-F0-9]+(?:u(?:8|16|32|64|128|256))?\\\",\\\"name\\\":\\\"constant.numeric.hex.move\\\"},{\\\"comment\\\":\\\"Numeric literal\\\",\\\"match\\\":\\\"(?<!(?:\\\\\\\\w|(?:(?<!\\\\\\\\.)\\\\\\\\.)))[0-9][_0-9]*(?:\\\\\\\\.(?!\\\\\\\\.)(?:[0-9][_0-9]*)?)?(?:[eE][+\\\\\\\\-]?[_0-9]+)?(?:[u](?:8|16|32|64|128|256))?\\\",\\\"name\\\":\\\"constant.numeric.move\\\"},{\\\"begin\\\":\\\"\\\\\\\\bb\\\\\\\"\\\",\\\"comment\\\":\\\"vector ascii bytestring literal\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"meta.vector.literal.ascii.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"character escape\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.move\\\"},{\\\"comment\\\":\\\"Special symbol escape\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[nrt\\\\\\\\0\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.move\\\"},{\\\"comment\\\":\\\"HEX Escape\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x[a-fA-F0-9][A-Fa-f0-9]\\\",\\\"name\\\":\\\"constant.character.escape.hex.move\\\"},{\\\"comment\\\":\\\"ASCII Character\\\",\\\"match\\\":\\\"[\\\\\\\\x00-\\\\\\\\x7F]\\\",\\\"name\\\":\\\"string.quoted.double.raw.move\\\"}]},{\\\"begin\\\":\\\"x\\\\\\\"\\\",\\\"comment\\\":\\\"vector hex literal\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"meta.vector.literal.hex.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"vector hex literal\\\",\\\"match\\\":\\\"[A-Fa-f0-9]+\\\",\\\"name\\\":\\\"constant.character.move\\\"}]},{\\\"comment\\\":\\\"bool literal\\\",\\\"match\\\":\\\"\\\\\\\\b(?:true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.move\\\"},{\\\"begin\\\":\\\"vector\\\\\\\\[\\\",\\\"comment\\\":\\\"vector literal (macro?)\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"meta.vector.literal.macro.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expr\\\"}]}]},\\\"local_call\\\":{\\\"comment\\\":\\\"call to a local / imported fun\\\",\\\"match\\\":\\\"\\\\\\\\b([a-z][_a-z0-9]*)(?=[<\\\\\\\\(])\\\",\\\"name\\\":\\\"entity.name.function.call.local.move\\\"},\\\"macro\\\":{\\\"begin\\\":\\\"\\\\\\\\b(macro)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.macro.move\\\"}},\\\"comment\\\":\\\"macro fun [ident] {}\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.macro.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#fun\\\"}]},\\\"macro_call\\\":{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.move\\\"}},\\\"comment\\\":\\\"Macro fun call\\\",\\\"match\\\":\\\"(\\\\\\\\b|\\\\\\\\.)([a-z][A-Za-z0-9_]*)!\\\",\\\"name\\\":\\\"meta.macro.call\\\"},\\\"match_expression\\\":{\\\"begin\\\":\\\"\\\\\\\\b(match)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.match.move\\\"}},\\\"comment\\\":\\\"enum pattern matching\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.match.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Block expression or definition\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.match.block.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"arrow operator\\\",\\\"match\\\":\\\"\\\\\\\\b(=>)\\\\\\\\b\\\",\\\"name\\\":\\\"operator.match.move\\\"},{\\\"include\\\":\\\"#expr\\\"}]},{\\\"include\\\":\\\"#expr\\\"}]},\\\"method_call\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.call.path.move\\\"}},\\\"comment\\\":\\\"<expr>.[ident]<>?() call\\\",\\\"match\\\":\\\"\\\\\\\\.([a-z][_a-z0-9]*)(?=[<\\\\\\\\(])\\\",\\\"name\\\":\\\"meta.path.call.move\\\"},\\\"module\\\":{\\\"begin\\\":\\\"\\\\\\\\b(module)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.move\\\"}},\\\"comment\\\":\\\"Module definition\\\",\\\"end\\\":\\\"(?<=[;}])\\\",\\\"name\\\":\\\"meta.module.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\b(module)\\\\\\\\b)\\\",\\\"comment\\\":\\\"Module name\\\",\\\"end\\\":\\\"(?=[;{])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\b(module))\\\",\\\"comment\\\":\\\"Module namespace / address\\\",\\\"end\\\":\\\"(?=[(::){])\\\",\\\"name\\\":\\\"constant.other.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"}]},{\\\"begin\\\":\\\"(?<=::)\\\",\\\"comment\\\":\\\"Module name\\\",\\\"end\\\":\\\"(?=[\\\\\\\\s;{])\\\",\\\"name\\\":\\\"entity.name.type.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"}]}]},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Module scope\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.module_scope.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#annotation\\\"},{\\\"include\\\":\\\"#entry\\\"},{\\\"include\\\":\\\"#public-scope\\\"},{\\\"include\\\":\\\"#public\\\"},{\\\"include\\\":\\\"#native\\\"},{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#friend\\\"},{\\\"include\\\":\\\"#const\\\"},{\\\"include\\\":\\\"#struct\\\"},{\\\"include\\\":\\\"#has_ability\\\"},{\\\"include\\\":\\\"#enum\\\"},{\\\"include\\\":\\\"#macro\\\"},{\\\"include\\\":\\\"#fun\\\"},{\\\"include\\\":\\\"#spec\\\"}]}]},\\\"module_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.entity.name.type.accessed.module.move\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.call.move\\\"}},\\\"comment\\\":\\\"Use of module type or method\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)::(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.module_access.move\\\"},\\\"module_label\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(module)\\\\\\\\b\\\",\\\"comment\\\":\\\"Module label, inline module definition\\\",\\\"end\\\":\\\";\\\\\\\\s*$\\\",\\\"name\\\":\\\"meta.module.label.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\bmodule\\\\\\\\b)\\\",\\\"comment\\\":\\\"Module namespace / address\\\",\\\"end\\\":\\\"(?=[(::){])\\\",\\\"name\\\":\\\"constant.other.move\\\"},{\\\"begin\\\":\\\"(?<=::)\\\",\\\"comment\\\":\\\"Module name\\\",\\\"end\\\":\\\"(?=[\\\\\\\\s{])\\\",\\\"name\\\":\\\"entity.name.type.move\\\"}]},\\\"move_copy\\\":{\\\"comment\\\":\\\"Keywords move and copy\\\",\\\"match\\\":\\\"\\\\\\\\b(move|copy)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.move\\\"},\\\"mut\\\":{\\\"comment\\\":\\\"Mutable reference and let mut\\\",\\\"match\\\":\\\"\\\\\\\\b(mut)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.mut.move\\\"},\\\"native\\\":{\\\"comment\\\":\\\"native\\\",\\\"match\\\":\\\"\\\\\\\\b(native)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.visibility.native.move\\\"},\\\"packed_field\\\":{\\\"comment\\\":\\\"[ident]: \\\",\\\"match\\\":\\\"[a-z][a-z0-9_]+\\\\\\\\s*:\\\\\\\\s*(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"meta.struct.field.move\\\"},\\\"paren\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.paren.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expr\\\"}]},\\\"path_access\\\":{\\\"comment\\\":\\\"<expr>.[ident] access\\\",\\\"match\\\":\\\"\\\\\\\\.[a-z][_a-z0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"meta.path.access.move\\\"},\\\"phantom\\\":{\\\"comment\\\":\\\"Keyword phantom inside type parameters\\\",\\\"match\\\":\\\"\\\\\\\\b(phantom)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.phantom.move\\\"},\\\"primitives\\\":{\\\"comment\\\":\\\"Primitive types\\\",\\\"match\\\":\\\"\\\\\\\\b(u8|u16|u32|u64|u128|u256|address|bool|signer)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.primitives.move\\\"},\\\"public\\\":{\\\"comment\\\":\\\"public\\\",\\\"match\\\":\\\"\\\\\\\\b(public)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.visibility.public.move\\\"},\\\"public-scope\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\b(public))\\\\\\\\s*\\\\\\\\(\\\",\\\"comment\\\":\\\"public (friend/script/package)\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.public.scoped.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(friend|script|package)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.public.scope.move\\\"}]},\\\"resource_methods\\\":{\\\"comment\\\":\\\"Methods to work with resource\\\",\\\"match\\\":\\\"\\\\\\\\b(borrow_global|borrow_global_mut|exists|move_from|move_to_sender|move_to)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.typed.move\\\"},\\\"script\\\":{\\\"begin\\\":\\\"\\\\\\\\b(script)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.script.move\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.script.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Script scope\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.script_scope.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#const\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#fun\\\"}]}]},\\\"self_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.language.self.move\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.call.move\\\"}},\\\"comment\\\":\\\"Use of Self\\\",\\\"match\\\":\\\"\\\\\\\\b(Self)::(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.self_access.move\\\"},\\\"spec\\\":{\\\"begin\\\":\\\"\\\\\\\\b(spec)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.spec.move\\\"}},\\\"end\\\":\\\"(?<=[;}])\\\",\\\"name\\\":\\\"meta.spec.move\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Spec target\\\",\\\"match\\\":\\\"\\\\\\\\b(module|schema|struct|fun)\\\",\\\"name\\\":\\\"storage.modifier.spec.target.move\\\"},{\\\"comment\\\":\\\"Spec define inline\\\",\\\"match\\\":\\\"\\\\\\\\b(define)\\\",\\\"name\\\":\\\"storage.modifier.spec.define.move\\\"},{\\\"comment\\\":\\\"Target name\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.move\\\"},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Spec block\\\",\\\"end\\\":\\\"}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#spec_block\\\"},{\\\"include\\\":\\\"#spec_types\\\"},{\\\"include\\\":\\\"#spec_define\\\"},{\\\"include\\\":\\\"#spec_keywords\\\"},{\\\"include\\\":\\\"#control\\\"},{\\\"include\\\":\\\"#fun_call\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#let\\\"}]}]},\\\"spec_block\\\":{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Spec block\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.spec_block.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#spec_block\\\"},{\\\"include\\\":\\\"#spec_types\\\"},{\\\"include\\\":\\\"#fun_call\\\"},{\\\"include\\\":\\\"#literals\\\"},{\\\"include\\\":\\\"#control\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#let\\\"}]},\\\"spec_define\\\":{\\\"begin\\\":\\\"\\\\\\\\b(define)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.move.spec\\\"}},\\\"comment\\\":\\\"Spec define keyword\\\",\\\"end\\\":\\\"(?=[;{])\\\",\\\"name\\\":\\\"meta.spec_define.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#spec_types\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"begin\\\":\\\"(?<=\\\\\\\\bdefine)\\\",\\\"comment\\\":\\\"Function name\\\",\\\"end\\\":\\\"(?=[(])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.move\\\"}]}]},\\\"spec_keywords\\\":{\\\"match\\\":\\\"\\\\\\\\b(global|pack|unpack|pragma|native|include|ensures|requires|invariant|apply|aborts_if|modifies)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.move.spec\\\"},\\\"spec_types\\\":{\\\"comment\\\":\\\"Spec-only types\\\",\\\"match\\\":\\\"\\\\\\\\b(range|num|vector|bool|u8|u16|u32|u64|u128|u256|address)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.vector.move\\\"},\\\"struct\\\":{\\\"begin\\\":\\\"\\\\\\\\b(struct)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.type.move\\\"}},\\\"end\\\":\\\"(?<=[};\\\\\\\\)])\\\",\\\"name\\\":\\\"meta.struct.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#has\\\"},{\\\"include\\\":\\\"#abilities\\\"},{\\\"comment\\\":\\\"Struct name (ident)\\\",\\\"match\\\":\\\"\\\\\\\\b[A-Z][a-zA-Z_0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.struct.move\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"comment\\\":\\\"Positional fields\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.struct.paren.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"}]},{\\\"include\\\":\\\"#type_param\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"comment\\\":\\\"Simple struct\\\",\\\"end\\\":\\\"(?<=[)])\\\",\\\"name\\\":\\\"meta.struct.paren.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#types\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"comment\\\":\\\"Struct body\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.struct.body.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#self_access\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#expr_generic\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#types\\\"}]},{\\\"include\\\":\\\"#has_ability\\\"}]},\\\"struct_pack\\\":{\\\"begin\\\":\\\"(?<=[A-Za-z0-9_>])\\\\\\\\s*{\\\",\\\"comment\\\":\\\"Struct { field: value... }; identified as generic / ident followed by curly's\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"meta.struct.pack.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"}]},\\\"type_param\\\":{\\\"begin\\\":\\\"<\\\",\\\"comment\\\":\\\"Generic type param\\\",\\\"end\\\":\\\">\\\",\\\"name\\\":\\\"meta.generic_param.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#phantom\\\"},{\\\"include\\\":\\\"#capitalized\\\"},{\\\"include\\\":\\\"#module_access\\\"},{\\\"include\\\":\\\"#abilities\\\"}]},\\\"types\\\":{\\\"comment\\\":\\\"Built-in types + vector\\\",\\\"name\\\":\\\"meta.types.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#primitives\\\"},{\\\"include\\\":\\\"#vector\\\"}]},\\\"use_fun\\\":{\\\"begin\\\":\\\"\\\\\\\\b(fun)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.fun.move\\\"}},\\\"comment\\\":\\\"use { fun } internals\\\",\\\"end\\\":\\\"(?=;)\\\",\\\"name\\\":\\\"meta.import.fun.move\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"comment\\\":\\\"as keyword\\\",\\\"match\\\":\\\"\\\\\\\\b(as)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.as.move\\\"},{\\\"comment\\\":\\\"Self keyword\\\",\\\"match\\\":\\\"\\\\\\\\b(Self)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.self.use.fun.move\\\"},{\\\"comment\\\":\\\"Function name\\\",\\\"match\\\":\\\"\\\\\\\\b(_______[a-z][a-z_0-9]+)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.use.move\\\"},{\\\"include\\\":\\\"#types\\\"},{\\\"include\\\":\\\"#escaped_identifier\\\"},{\\\"include\\\":\\\"#capitalized\\\"}]},\\\"vector\\\":{\\\"comment\\\":\\\"vector type\\\",\\\"match\\\":\\\"\\\\\\\\b(vector)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.vector.move\\\"}},\\\"scopeName\\\":\\\"source.move\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/move.mjs\n"));

/***/ })

}]);