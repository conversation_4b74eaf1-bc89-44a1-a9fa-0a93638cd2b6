"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_kusto_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/kusto.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/kusto.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Kusto\\\",\\\"fileTypes\\\":[\\\"csl\\\",\\\"kusto\\\",\\\"kql\\\"],\\\"name\\\":\\\"kusto\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Tabular operators: common helper operators\\\",\\\"match\\\":\\\"\\\\\\\\b(by|from|of|to|step|with)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"comment\\\":\\\"Query statements: https://docs.microsoft.com/en-us/azure/kusto/query/statements\\\",\\\"match\\\":\\\"\\\\\\\\b(let|set|alias|declare|pattern|query_parameters|restrict|access|set)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/datatypes-string-operators\\\",\\\"match\\\":\\\"\\\\\\\\b(and|or|has_all|has_any|matches|regex)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Strings\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/clusterfunction\\\",\\\"match\\\":\\\"\\\\\\\\b(cluster|database)(?:\\\\\\\\s*\\\\\\\\(\\\\\\\\s*(.+?)\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.special.database.kusto\\\"},{\\\"comment\\\":\\\"Special functions: https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/tablefunction\\\",\\\"match\\\":\\\"\\\\\\\\b(external_table|materialized_view|materialize|table|toscalar)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/betweenoperator\\\",\\\"match\\\":\\\"(?<!\\\\\\\\w)(!?between)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binoperators\\\",\\\"match\\\":\\\"\\\\\\\\b(binary_and|binary_or|binary_shift_left|binary_shift_right|binary_xor)(?:\\\\\\\\s*\\\\\\\\(\\\\\\\\s*(\\\\\\\\w+)\\\\\\\\s*,\\\\\\\\s*(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.scalar.bitwise.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binary-notfunction\\\",\\\"match\\\":\\\"\\\\\\\\b(binary_not|bitset_count_ones)(?:\\\\\\\\s*\\\\\\\\(\\\\\\\\s*(\\\\\\\\w+)\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.scalar.bitwise.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/in-cs-operator\\\",\\\"match\\\":\\\"(?<!\\\\\\\\w)(!?in~?)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/datatypes-string-operators\\\",\\\"match\\\":\\\"(?<!\\\\\\\\w)(!?(?:contains|endswith|hasprefix|hassuffix|has|startswith)(?:_cs)?)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"},{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"},{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"},{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/rangefunction\\\",\\\"match\\\":\\\"\\\\\\\\b(range)\\\\\\\\s*\\\\\\\\((?:\\\\\\\\s*(\\\\\\\\w+(?:\\\\\\\\(.*?\\\\\\\\))?)\\\\\\\\s*,\\\\\\\\s*(\\\\\\\\w+(?:\\\\\\\\(.*?\\\\\\\\))?)\\\\\\\\s*,?(?:\\\\\\\\s*)?(\\\\\\\\w+(?:\\\\\\\\(.*?\\\\\\\\))?)?\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.scalar.function.range.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/scalarfunctions\\\",\\\"match\\\":\\\"\\\\\\\\b(abs|acos|around|array_concat|array_iff|array_index_of|array_length|array_reverse|array_rotate_left|array_rotate_right|array_shift_left|array_shift_right|array_slice|array_sort_asc|array_sort_desc|array_split|array_sum|asin|assert|atan2|atan|bag_has_key|bag_keys|bag_merge|bag_remove_keys|base64_decode_toarray|base64_decode_tostring|base64_decode_toguid|base64_encode_fromarray|base64_encode_tostring|base64_encode_fromguid|beta_cdf|beta_inv|beta_pdf|bin_at|bin_auto|case|ceiling|coalesce|column_ifexists|convert_angle|convert_energy|convert_force|convert_length|convert_mass|convert_speed|convert_temperature|convert_volume|cos|cot|countof|current_cluster_endpoint|current_database|current_principal_details|current_principal_is_member_of|current_principal|cursor_after|cursor_before_or_at|cursor_current|current_cursor|dcount_hll|degrees|dynamic_to_json|estimate_data_size|exp10|exp2|exp|extent_id|extent_tags|extract_all|extract_json|extractjson|extract|floor|format_bytes|format_ipv4_mask|format_ipv4|gamma|gettype|gzip_compress_to_base64_string|gzip_decompress_from_base64_string|has_any_index|has_any_ipv4_prefix|has_any_ipv4|has_ipv4_prefix|has_ipv4|hash_combine|hash_many|hash_md5|hash_sha1|hash_sha256|hash_xxhash64|hash|iff|iif|indexof_regex|indexof|ingestion_time|ipv4_compare|ipv4_is_in_range|ipv4_is_in_any_range|ipv4_is_match|ipv4_is_private|ipv4_netmask_suffix|ipv6_compare|ipv6_is_match|isascii|isempty|isfinite|isinf|isnan|isnotempty|notempty|isnotnull|notnull|isnull|isutf8|jaccard_index|log10|log2|loggamma|log|make_string|max_of|min_of|new_guid|not|bag_pack|pack_all|pack_array|pack_dictionary|pack|parse_command_line|parse_csv|parse_ipv4_mask|parse_ipv4|parse_ipv6_mask|parse_ipv6|parse_path|parse_urlquery|parse_url|parse_user_agent|parse_version|parse_xml|percentile_tdigest|percentile_array_tdigest|percentrank_tdigest|pi|pow|radians|rand|rank_tdigest|regex_quote|repeat|replace_regex|replace_string|reverse|round|set_difference|set_has_element|set_intersect|set_union|sign|sin|split|sqrt|strcat_array|strcat_delim|strcmp|strcat|string_size|strlen|strrep|substring|tan|to_utf8|tobool|todecimal|todouble|toreal|toguid|tohex|toint|tolong|tolower|tostring|toupper|translate|treepath|trim_end|trim_start|trim|unixtime_microseconds_todatetime|unixtime_milliseconds_todatetime|unixtime_nanoseconds_todatetime|unixtime_seconds_todatetime|url_decode|url_encode_component|url_encode|welch_test|zip|zlib_compress_to_base64_string|zlib_decompress_from_base64_string)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"},{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/binfunction\\\",\\\"match\\\":\\\"\\\\\\\\b(bin)(?:\\\\\\\\s*\\\\\\\\(\\\\\\\\s*(.+?)\\\\\\\\s*,\\\\\\\\s*(.+?)\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.scalar.function.bin.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/count-aggfunction\\\",\\\"match\\\":\\\"\\\\\\\\b(count)\\\\\\\\s*\\\\\\\\(\\\\\\\\s*\\\\\\\\)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/aggregation-functions\\\",\\\"match\\\":\\\"\\\\\\\\b(arg_max|arg_min|avgif|avg|binary_all_and|binary_all_or|binary_all_xor|buildschema|countif|dcount|dcountif|hll|hll_merge|make_bag_if|make_bag|make_list_with_nulls|make_list_if|make_list|make_set_if|make_set|maxif|max|minif|min|percentilesw_array|percentiles_array|percentilesw|percentilew|percentiles|percentile|stdevif|stdevp|stdev|sumif|sum|take_anyif|take_any|tdigest_merge|merge_tdigest|tdigest|varianceif|variancep|variance)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/geospatial-grid-systems\\\",\\\"match\\\":\\\"\\\\\\\\b(geo_distance_2points|geo_distance_point_to_line|geo_distance_point_to_polygon|geo_intersects_2lines|geo_intersects_2polygons|geo_intersects_line_with_polygon|geo_intersection_2lines|geo_intersection_2polygons|geo_intersection_line_with_polygon|geo_line_centroid|geo_line_densify|geo_line_length|geo_line_simplify|geo_polygon_area|geo_polygon_centroid|geo_polygon_densify|geo_polygon_perimeter|geo_polygon_simplify|geo_polygon_to_s2cells|geo_point_in_circle|geo_point_in_polygon|geo_point_to_geohash|geo_point_to_h3cell|geo_point_to_s2cell|geo_geohash_to_central_point|geo_geohash_neighbors|geo_geohash_to_polygon|geo_s2cell_to_central_point|geo_s2cell_neighbors|geo_s2cell_to_polygon|geo_h3cell_to_central_point|geo_h3cell_neighbors|geo_h3cell_to_polygon|geo_h3cell_parent|geo_h3cell_children|geo_h3cell_level|geo_h3cell_rings|geo_simplify_polygons_array|geo_union_lines_array|geo_union_polygons_array)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/windowsfunctions\\\",\\\"match\\\":\\\"\\\\\\\\b(next|prev|row_cumsum|row_number|row_rank|row_window_session)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"User-defined functions: https://docs.microsoft.com/en-us/azure/kusto/query/functions/user-defined-functions\\\",\\\"match\\\":\\\"\\\\\\\\.(create-or-alter|replace)\\\",\\\"name\\\":\\\"keyword.control.kusto\\\"},{\\\"comment\\\":\\\"User-defined functions: https://docs.microsoft.com/en-us/azure/kusto/query/functions/user-defined-functions\\\",\\\"match\\\":\\\"(?<=let )[^\\\\\\\\n]+(?=\\\\\\\\W*=)\\\",\\\"name\\\":\\\"entity.function.name.lambda.kusto\\\"},{\\\"comment\\\":\\\"User-defined functions: https://docs.microsoft.com/en-us/azure/kusto/query/functions/user-defined-functions\\\",\\\"match\\\":\\\"\\\\\\\\b(folder|docstring|skipvalidation)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"match\\\":\\\"\\\\\\\\b(function)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.kusto\\\"},{\\\"comment\\\":\\\"Data types: https://docs.microsoft.com/en-us/azure/kusto/query/scalar-data-types\\\",\\\"match\\\":\\\"\\\\\\\\b(bool|decimal|dynamic|guid|int|long|real|string)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/asoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(as)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.as.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/datatableoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(datatable)(?=\\\\\\\\W*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.other.query.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/facetoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(facet)(?:\\\\\\\\s+(by))?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.facet.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/invokeoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(invoke)(?:\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.invoke.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/orderoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(order)(?:\\\\\\\\s+(by)\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.order.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"8\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/rangeoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(range)\\\\\\\\s+(\\\\\\\\w+)\\\\\\\\s+(from)\\\\\\\\s+(\\\\\\\\w+(?:\\\\\\\\(\\\\\\\\w*\\\\\\\\))?)\\\\\\\\s+(to)\\\\\\\\s+(\\\\\\\\w+(?:\\\\\\\\(\\\\\\\\w*\\\\\\\\))?)\\\\\\\\s+(step)\\\\\\\\s+(\\\\\\\\w+(?:\\\\\\\\(\\\\\\\\w*\\\\\\\\))?)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.range.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/sampleoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(sample)(?:\\\\\\\\s+(\\\\\\\\d+))?(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"meta.query.sample.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/sampledistinctoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(sample-distinct)(?:\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s+(of)\\\\\\\\s+(\\\\\\\\w+))?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.sample-distinct.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/sortoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(sort)(?:\\\\\\\\s+(by))?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.sort.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/takeoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(take|limit)(?:\\\\\\\\s+(\\\\\\\\d+))\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.take.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/topoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(top)(?:\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s+(by)\\\\\\\\s+(\\\\\\\\w+))?(?![\\\\\\\\w-])\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.top.kusto\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.query.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"4\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.other.column.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/tophittersoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(top-hitters)(?:\\\\\\\\s+(\\\\\\\\d+)\\\\\\\\s+(of)\\\\\\\\s+(\\\\\\\\w+)(?:\\\\\\\\s+(by)\\\\\\\\s+(\\\\\\\\w+))?)?\\\\\\\\b\\\",\\\"name\\\":\\\"meta.query.top-hitters.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/queries\\\",\\\"match\\\":\\\"\\\\\\\\b(consume|count|distinct|evaluate|extend|externaldata|find|fork|getschema|join|lookup|make-series|mv-apply|mv-expand|project-away|project-keep|project-rename|project-reorder|project|parse|parse-where|parse-kv|partition|print|reduce|render|scan|search|serialize|shuffle|summarize|top-nested|union|where)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.query.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: evalute (plugins): https://docs.microsoft.com/en-us/azure/kusto/query/evaluateoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(active_users_count|activity_counts_metrics|activity_engagement|new_activity_metrics|activity_metrics|autocluster|azure_digital_twins_query_request|bag_unpack|basket|cosmosdb_sql_request|dcount_intersect|diffpatterns|funnel_sequence_completion|funnel_sequence|http_request_post|http_request|infer_storage_schema|ipv4_lookup|mysql_request|narrow|pivot|preview|rolling_percentile|rows_near|schema_merge|session_count|sequence_detect|sliding_window_counts|sql_request)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: join: https://docs.microsoft.com/en-us/azure/kusto/query/joinoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(on|kind|hint\\\\\\\\.remote|hint\\\\\\\\.strategy)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: join ($left, $right): https://docs.microsoft.com/en-us/azure/kusto/query/joinoperator\\\",\\\"match\\\":\\\"(\\\\\\\\$left|\\\\\\\\$right)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: join (kinds, strategies): https://docs.microsoft.com/en-us/azure/kusto/query/joinoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(innerunique|inner|leftouter|rightouter|fullouter|leftanti|anti|leftantisemi|rightanti|rightantisemi|leftsemi|rightsemi|broadcast)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.kusto\\\"},{\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/machine-learning-and-tsa\\\",\\\"match\\\":\\\"\\\\\\\\b(series_abs|series_acos|series_add|series_asin|series_atan|series_cos|series_decompose|series_decompose_anomalies|series_decompose_forecast|series_divide|series_equals|series_exp|series_fft|series_fill_backward|series_fill_const|series_fill_forward|series_fill_linear|series_fir|series_fit_2lines_dynamic|series_fit_2lines|series_fit_line_dynamic|series_fit_line|series_fit_poly|series_greater_equals|series_greater|series_ifft|series_iir|series_less_equals|series_less|series_multiply|series_not_equals|series_outliers|series_pearson_correlation|series_periods_detect|series_periods_validate|series_pow|series_seasonal|series_sign|series_sin|series_stats|series_stats_dynamic|series_subtract|series_tan)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: mv-expand (bagexpand options): https://docs.microsoft.com/en-us/azure/kusto/query/mvexpandoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(bag|array)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.operator.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: order: https://docs.microsoft.com/en-us/azure/kusto/query/orderoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(asc|desc|nulls first|nulls last)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.kusto\\\"},{\\\"comment\\\":\\\"Tabular operators: parse: https://docs.microsoft.com/en-us/azure/kusto/query/parseoperator\\\",\\\"match\\\":\\\"\\\\\\\\b(regex|simple|relaxed)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.kusto\\\"},{\\\"match\\\":\\\"\\\\\\\\b(anomalychart|areachart|barchart|card|columnchart|ladderchart|linechart|piechart|pivotchart|scatterchart|stackedareachart|timechart|timepivot)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.kusto\\\"},{\\\"include\\\":\\\"#Strings\\\"},{\\\"match\\\":\\\"\\\\\\\\{.*?\\\\\\\\}\\\",\\\"name\\\":\\\"string.other.kusto\\\"},{\\\"comment\\\":\\\"Comments\\\",\\\"match\\\":\\\"//.*\\\",\\\"name\\\":\\\"comment.line.kusto\\\"},{\\\"include\\\":\\\"#TimeSpanLiterals\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanFunctions\\\"},{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"},{\\\"include\\\":\\\"#Numeric\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.kusto\\\"},{\\\"comment\\\":\\\"Deprecated functions\\\",\\\"match\\\":\\\"\\\\\\\\b(anyif|any|array_strcat|base64_decodestring|base64_encodestring|make_dictionary|makelist|makeset|mvexpand|todynamic|parse_json|replace|weekofyear)(?=\\\\\\\\W*\\\\\\\\(|\\\\\\\\b)\\\",\\\"name\\\":\\\"invalid.deprecated.kusto\\\"}],\\\"repository\\\":{\\\"DateTimeTimeSpanDataTypes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(datetime|timespan|time)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.kusto\\\"}]},\\\"DateTimeTimeSpanFunctions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.kusto\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#DateTimeTimeSpanDataTypes\\\"}]},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#Strings\\\"}]}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/format-datetimefunction\\\",\\\"match\\\":\\\"\\\\\\\\b(format_datetime)(?:\\\\\\\\s*\\\\\\\\(\\\\\\\\s*(.+?)\\\\\\\\s*,\\\\\\\\s*(['\\\\\\\"].*?['\\\\\\\"])\\\\\\\\s*\\\\\\\\))?(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.scalar.function.format_datetime.kusto\\\"},{\\\"comment\\\":\\\"Scalar function: DateTime/Timespan Functions: https://docs.microsoft.com/en-us/azure/kusto/query/scalarfunctions#datetimetimespan-functions\\\",\\\"match\\\":\\\"\\\\\\\\b(ago|datetime_add|datetime_diff|datetime_local_to_utc|datetime_part|datetime_utc_to_local|dayofmonth|dayofweek|dayofyear|endofday|endofmonth|endofweek|endofyear|format_timespan|getmonth|getyear|hourofday|make_datetime|make_timespan|monthofyear|now|startofday|startofmonth|startofweek|startofyear|todatetime|totimespan|week_of_year)(?=\\\\\\\\W*\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.kusto\\\"}]},\\\"Escapes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\['\\\\\\\"]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.kusto\\\"}]},\\\"Numeric\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\\\\\.?[0-9]*+)|(\\\\\\\\.[0-9]+))((e|E)(\\\\\\\\+|-)?[0-9]+)?)(L|l|UL|ul|u|U|F|f|ll|LL|ull|ULL)?(?=\\\\\\\\b|\\\\\\\\w)\\\",\\\"name\\\":\\\"constant.numeric.kusto\\\"}]},\\\"Strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([@h]?\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/scalar-data-types/string\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"name\\\":\\\"string.quoted.double.kusto\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#Escapes\\\"}]},{\\\"begin\\\":\\\"([@h]?')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/scalar-data-types/string\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"name\\\":\\\"string.quoted.single.kusto\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#Escapes\\\"}]},{\\\"begin\\\":\\\"([@h]?```)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"comment\\\":\\\"https://learn.microsoft.com/en-us/azure/data-explorer/kusto/query/scalar-data-types/string#multi-line-string-literals\\\",\\\"end\\\":\\\"```\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.kusto\\\"}},\\\"name\\\":\\\"string.quoted.multi.kusto\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#Escapes\\\"}]}]},\\\"TimeSpanLiterals\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"timespan literals: https://docs.microsoft.com/en-us/azure/kusto/query/scalar-data-types/timespan#timespan-literals\\\",\\\"match\\\":\\\"[+-]?(?:\\\\\\\\d*\\\\\\\\.)?\\\\\\\\d+(?:microseconds?|ticks?|seconds?|ms|d|h|m|s)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.kusto\\\"}]}},\\\"scopeName\\\":\\\"source.kusto\\\",\\\"aliases\\\":[\\\"kql\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/kusto.mjs\n"));

/***/ })

}]);