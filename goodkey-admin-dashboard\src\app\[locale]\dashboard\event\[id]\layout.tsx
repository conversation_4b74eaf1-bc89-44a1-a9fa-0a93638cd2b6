import ShowQuery from '@/services/queries/ShowQuery';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import EventInformation from './components/EventInformation';
import EventSidebarWrapper from './components/EventSidebarWrapper';

interface EventLayoutProps {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}

export default async function EventLayout({
  children,
  params,
}: EventLayoutProps) {
  const { id } = await params;
  const showId = Number(id);

  // Fetch show data for EventInformation component
  const show = await ShowQuery.getOne(showId);

  // Fetch location data
  const locations = await ShowLocationQuery.getAll();
  const locationName = locations?.find(
    (location) => location.id == Number(show?.locationId),
  )?.name;

  return (
    <div className="container mx-auto py-8 px-4">
      <EventInformation show={show} locationName={locationName} />

      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-64 mb-6 md:mb-0">
          <EventSidebarWrapper eventId={id} />
        </div>

        <div className="flex-1 flex flex-col gap-6">{children}</div>
      </div>
    </div>
  );
}
