'use client';

import { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

export interface AutocompleteOption {
  id: string | number;
  label: string;
  value: string;
  subtitle?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  metadata?: any;
}

interface AutocompleteInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  options: AutocompleteOption[];
  className?: string;
  maxResults?: number;
  minCharacters?: number;
  noResultsText?: string;
  filterFunction?: (option: AutocompleteOption, searchValue: string) => boolean;
}

export const AutocompleteInput = ({
  value,
  onChange,
  placeholder = 'Search...',
  options,
  className,
  maxResults = 8,
  minCharacters = 1,
  noResultsText = 'No results found',
  filterFunction,
}: AutocompleteInputProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const defaultFilter = (option: AutocompleteOption, searchValue: string) =>
    option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    (option.subtitle &&
      option.subtitle.toLowerCase().includes(searchValue.toLowerCase()));

  const filteredOptions =
    value.length >= minCharacters
      ? options
          .filter((option) =>
            filterFunction
              ? filterFunction(option, value)
              : defaultFilter(option, value),
          )
          .filter((option) => !option.disabled)
          .slice(0, maxResults)
      : [];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setIsOpen(newValue.length > 0);
    setHighlightedIndex(-1);
  };

  const handleSuggestionClick = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || filteredOptions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0,
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1,
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleSuggestionClick(filteredOptions[highlightedIndex].value);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => value.length > 0 && setIsOpen(true)}
          placeholder={placeholder}
          className={`h-9 pr-8 ${className}`}
        />
        <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      </div>

      {isOpen && value.length >= minCharacters && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <div
                key={option.id}
                className={`px-3 py-2 cursor-pointer flex items-center gap-2 text-sm ${
                  index === highlightedIndex
                    ? 'bg-brand-brown/10 text-brand-brown'
                    : 'hover:bg-gray-50 text-gray-900'
                } ${option.disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
                onClick={() =>
                  !option.disabled && handleSuggestionClick(option.value)
                }
                onMouseEnter={() => setHighlightedIndex(index)}
              >
                {option.icon && (
                  <div className="flex-shrink-0">{option.icon}</div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{option.label}</div>
                  {option.subtitle && (
                    <div className="text-xs text-gray-500 truncate">
                      {option.subtitle}
                    </div>
                  )}
                </div>
                {option.metadata?.badge && (
                  <span className="text-xs bg-gray-200 text-gray-600 px-1.5 py-0.5 rounded-full flex-shrink-0">
                    {option.metadata.badge}
                  </span>
                )}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500 text-center">
              {noResultsText.includes('{value}')
                ? noResultsText.replace('{value}', value)
                : noResultsText}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
