"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_journeyDiagram-U35MCT3I_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawBackgroundRect: () => (/* binding */ drawBackgroundRect),\n/* harmony export */   drawEmbeddedImage: () => (/* binding */ drawEmbeddedImage),\n/* harmony export */   drawImage: () => (/* binding */ drawImage),\n/* harmony export */   drawRect: () => (/* binding */ drawRect),\n/* harmony export */   drawText: () => (/* binding */ drawText),\n/* harmony export */   getNoteRect: () => (/* binding */ getNoteRect),\n/* harmony export */   getTextObj: () => (/* binding */ getTextObj)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-U35MCT3I.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-U35MCT3I.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-D6G4REZN.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n// src/diagrams/user-journey/parser/journey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 18], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 14];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"journey\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"taskName\": 18, \"taskData\": 19, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"journey\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 18: \"taskName\", 19: \"taskData\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 13:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 15, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 16] }, { 15: [1, 17] }, o($V0, [2, 11]), o($V0, [2, 12]), { 19: [1, 18] }, o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 13])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 18;\n            break;\n          case 16:\n            return 19;\n            break;\n          case 17:\n            return \":\";\n            break;\n          case 18:\n            return 6;\n            break;\n          case 19:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:journey\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar journey_default = parser;\n\n// src/diagrams/user-journey/journeyDb.js\nvar currentSection = \"\";\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar clear2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.clear)();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar updateActors = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n}, \"updateActors\");\nvar addTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, taskData) {\n  const pieces = taskData.substr(1).split(\":\");\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(\",\");\n  }\n  const peopleList = peeps.map((s) => s.trim());\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar getActors = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return updateActors();\n}, \"getActors\");\nvar journeyDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(() => (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey, \"getConfig\"),\n  clear: clear2,\n  setDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setDiagramTitle,\n  getDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getDiagramTitle,\n  setAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccTitle,\n  getAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccTitle,\n  setAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccDescription,\n  getAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors\n};\n\n// src/diagrams/user-journey/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((options) => `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n    font-family: ${options.fontFamily};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : \"fill: #FFF8DC\"};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : \"\"};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : \"\"};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : \"\"};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : \"\"};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : \"\"};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : \"\"};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : \"\"};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : \"\"};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : \"\"};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : \"\"};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : \"\"};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : \"\"};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : \"\"};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : \"\"};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/user-journey/journeyRenderer.ts\n\n\n// src/diagrams/user-journey/svgDraw.js\n\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, rectData) {\n  return (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_2__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(smile, \"smile\");\n  function sad(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_2__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, textData) {\n  return (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawText)(elem, textData);\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText2(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, section, conf2) {\n  const g = elem.append(\"g\");\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf2.width * section.taskCount + // width of the tasks\n  conf2.diagramMarginX * (section.taskCount - 1);\n  rect.height = conf2.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  _drawTextCandidateFunc(conf2)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf2,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, task, conf2) {\n  const center = task.x + conf2.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf2.width;\n  rect.height = conf2.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      title: person,\n      pos: task.actors[person].position\n    };\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n  _drawTextCandidateFunc(conf2)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf2,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(elem, bounds2) {\n  (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawBackgroundRect)(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {\n    const { taskFontSize, taskFontFamily } = conf2;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawCircle,\n  drawSection,\n  drawText: drawText2,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect: drawBackgroundRect2,\n  initGraphics\n};\n\n// src/diagrams/user-journey/journeyRenderer.ts\nvar setConf = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(cnf) {\n  const keys = Object.keys(cnf);\n  keys.forEach(function(key) {\n    conf[key] = cnf[key];\n  });\n}, \"setConf\");\nvar actors = {};\nfunction drawActorLegend(diagram2) {\n  const conf2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      pos: actors[person].position\n    };\n    svgDraw_default.drawCircle(diagram2, circleData);\n    const labelData = {\n      x: 40,\n      y: yPos + 7,\n      fill: \"#666\",\n      text: person,\n      textMargin: conf2.boxTextMargin | 5\n    };\n    svgDraw_default.drawText(diagram2, labelData);\n    yPos += 20;\n  });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawActorLegend, \"drawActorLegend\");\nvar conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\nvar LEFT_MARGIN = conf.leftMargin;\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(text, id, version, diagObj) {\n  const conf2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  const securityLevel = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_2__.select)(\"body\");\n  bounds.init();\n  const diagram2 = root.select(\"#\" + id);\n  svgDraw_default.initGraphics(diagram2);\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf2.actorColours[actorPos % conf2.actorColours.length],\n      position: actorPos\n    };\n    actorPos++;\n  });\n  drawActorLegend(diagram2);\n  bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);\n  drawTasks(diagram2, tasks2, 0);\n  const box = bounds.getBounds();\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 25);\n  }\n  const height = box.stopy - box.starty + 2 * conf2.diagramMarginY;\n  const width = LEFT_MARGIN + box.stopx + 2 * conf2.diagramMarginX;\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.configureSvgSize)(diagram2, height, width, conf2.useMaxWidth);\n  diagram2.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", conf2.height * 4).attr(\"x2\", width - LEFT_MARGIN - 4).attr(\"y2\", conf2.height * 4).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  const extraVertForTitle = title ? 70 : 0;\n  diagram2.attr(\"viewBox\", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram2.attr(\"preserveAspectRatio\", \"xMinYMin meet\");\n  diagram2.attr(\"height\", height + extraVertForTitle + 25);\n}, \"draw\");\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  init: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    this.sequenceItems = [];\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startx, starty, stopx, stopy) {\n    const conf2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf2.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf2.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf2.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf2.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  bumpVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n    return this.data;\n  }, \"getBounds\")\n};\nvar fills = conf.sectionFills;\nvar textColours = conf.sectionColours;\nvar drawTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(diagram2, tasks2, verticalPos) {\n  const conf2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().journey;\n  let lastSection = \"\";\n  const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n  let sectionNumber = 0;\n  let fill = \"#CCC\";\n  let colour = \"black\";\n  let num = 0;\n  for (const [i, task] of tasks2.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n      let taskInSectionCount = 0;\n      const currentSection2 = task.section;\n      for (let taskIndex = i; taskIndex < tasks2.length; taskIndex++) {\n        if (tasks2[taskIndex].section == currentSection2) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n      const section = {\n        x: i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount\n      };\n      svgDraw_default.drawSection(diagram2, section, conf2);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n      return acc;\n    }, {});\n    task.x = i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN;\n    task.y = taskPos;\n    task.width = conf2.diagramMarginX;\n    task.height = conf2.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n    svgDraw_default.drawTask(diagram2, task, conf2);\n    bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);\n  }\n}, \"drawTasks\");\nvar journeyRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/user-journey/journeyDiagram.ts\nvar diagram = {\n  parser: journey_default,\n  db: journeyDb_default,\n  renderer: journeyRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((cnf) => {\n    journeyRenderer_default.setConf(cnf.journey);\n    journeyDb_default.clear();\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-U35MCT3I.mjs\n"));

/***/ })

}]);