import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import PropertyQuery from '@/services/queries/PropertyQuery';
import PropertySelect from '../components/property_select';

export default async function PropertyPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error('Invalid property id');
      await client.prefetchQuery({
        queryKey: PropertyQuery.tags,
        queryFn: PropertyQuery.getAll,
      });
    }

    return (
      <div className="space-y-4 px-2">
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Select Property
        </h2>
        <HydrationBoundary state={dehydrate(client)}>
          <PropertySelect categoryId={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/products-services/category/add');
  }
}
