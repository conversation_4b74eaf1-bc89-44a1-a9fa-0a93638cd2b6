﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportDuplicates
    {
        public ExhibitorImportDuplicates()
        {
            ExhibitorImportFieldResolutions = new HashSet<ExhibitorImportFieldResolutions>();
        }

        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public string DuplicateType { get; set; }
        public string DuplicateValue { get; set; }
        public string RowNumbers { get; set; }
        public string ConflictResolution { get; set; }
        public bool? IsResolved { get; set; }
        public bool? RequiresUserDecision { get; set; }
        public string ConflictDescription { get; set; }
        public int? ExistingRecordId { get; set; }
        public string ExistingRecordType { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public int? ResolvedBy { get; set; }
        public DateTime? CreatedAt { get; set; }

        public virtual ExhibitorImportSessions Session { get; set; }
        public virtual ICollection<ExhibitorImportFieldResolutions> ExhibitorImportFieldResolutions { get; set; }
    }
}