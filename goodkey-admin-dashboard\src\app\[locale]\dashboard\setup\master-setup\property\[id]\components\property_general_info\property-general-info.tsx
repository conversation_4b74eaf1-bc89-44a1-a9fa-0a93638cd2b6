'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { ChevronRight } from 'lucide-react';
import {
  PropertyCreateSchema,
  PropertyCreateData,
  PropertyUpdateSchema,
  PropertyUpdateData,
} from '@/schema/PropertySchema';
import { getQueryClient } from '@/utils/query-client';

interface PropertyGeneralInfoProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: PropertyCreateData | PropertyUpdateData;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();

  const isEditMode = !!id;

  const form = useForm<PropertyCreateData | PropertyUpdateData>({
    resolver: zodResolver(
      isEditMode ? PropertyUpdateSchema : PropertyCreateSchema,
    ),
    defaultValues: defaultValues ?? {
      name: '',
      description: '',
      ...(isEditMode ? { code: '' } : {}),
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: PropertyCreateData | PropertyUpdateData) =>
      isEditMode
        ? PropertyQuery.update(id!)(data as PropertyUpdateData)
        : PropertyQuery.add(data as PropertyCreateData),
    onSuccess: async (result) => {
      const newId = result || id;
      if (newId) {
        push(
          `/dashboard/setup/master-setup/property/${newId}/property_options`,
        );
      }
      await getQueryClient().invalidateQueries({
        queryKey: PropertyQuery.tags,
      });
      toast({
        title: 'Success',
        description: isEditMode
          ? 'Property updated successfully.'
          : 'Property created successfully.',
        variant: 'success',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message ?? 'Something went wrong',
        variant: 'destructive',
      });
    },
  });
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-2"
      >
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          Property Information
        </h2>
        <Field
          control={form.control}
          name="name"
          label="Property Name"
          placeholder="Enter property name"
          type="text"
          required
        />
        <Field
          control={form.control}
          name="description"
          label="Description"
          placeholder="Enter description"
          type="textarea"
        />

        <div className="flex justify-between pt-6 border-t border-slate-200">
          <Button
            variant="outline"
            type="button"
            onClick={() => push('/dashboard/setup/master-setup/property')}
          >
            Cancel
          </Button>

          <Button variant="main" type="submit" disabled={isPending}>
            {isPending && <Spinner className="mr-2 text-white" />}
            Save & Continue
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function PropertyGeneralInfo({ id }: PropertyGeneralInfoProps) {
  const isEditMode = !!id;

  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Property', { id }],
    queryFn: () => PropertyQuery.get(id!),
    enabled: isEditMode,
    select: (res): PropertyUpdateData => ({
      name: res.name ?? '',
      code: res.code ?? '',
      description: res.description ?? '',
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}
