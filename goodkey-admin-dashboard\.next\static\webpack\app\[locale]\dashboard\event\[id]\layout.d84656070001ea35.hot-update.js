"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/dashboard/event/[id]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/event-sidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/event-sidebar.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSidebar: () => (/* binding */ EventSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square-quote.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/queries/MenuQuery */ \"(app-pages-browser)/./src/services/queries/MenuQuery.ts\");\n/* __next_internal_client_entry_do_not_use__ EventSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Icon mapping for dynamic menu items\nconst iconMap = {\n    MessageSquareQuote: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    ShoppingCart: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    FileText: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    Package: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    Users: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    BarChart: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Settings: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    ImageIcon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    CreditCard: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n};\nfunction EventSidebar(param) {\n    let { eventId, activeItem } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Fetch dynamic menu items from API\n    const { data: menuItems, isLoading, isError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            ..._services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"].tags,\n            'event_sidebar'\n        ],\n        queryFn: {\n            \"EventSidebar.useQuery\": ()=>_services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getBySection('event_sidebar')\n        }[\"EventSidebar.useQuery\"]\n    });\n    console.log(\"\", menuItems);\n    // Transform API data to component format\n    const transformedMenuItems = (menuItems === null || menuItems === void 0 ? void 0 : menuItems.map((item)=>{\n        var _item_url;\n        return {\n            name: item.name || 'Menu Item',\n            icon: iconMap[item.icon || ''] || _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: ((_item_url = item.url) === null || _item_url === void 0 ? void 0 : _item_url.replace('{eventId}', eventId)) || '#',\n            displayOrder: item.displayOrder,\n            isVisible: item.isVisible\n        };\n    })) || [];\n    // Fallback to static menu items if API fails or is loading\n    const fallbackMenuItems = [\n        {\n            name: 'RFQs',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/rfqs\"),\n            displayOrder: 1,\n            isVisible: true\n        },\n        {\n            name: 'ORDERS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/orders\"),\n            displayOrder: 2,\n            isVisible: true\n        },\n        {\n            name: 'ORDER FORMS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/order-forms\"),\n            displayOrder: 3,\n            isVisible: true\n        },\n        {\n            name: 'SHOW PACKAGES',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/show-packages\"),\n            displayOrder: 4,\n            isVisible: true\n        },\n        {\n            name: 'EXHIBITORS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/exhibitors\"),\n            displayOrder: 5,\n            isVisible: true\n        },\n        {\n            name: 'LISTS & REPORTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/reports\"),\n            displayOrder: 6,\n            isVisible: true\n        },\n        {\n            name: 'SHOW MANAGEMENT',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId),\n            displayOrder: 7,\n            isVisible: true\n        },\n        {\n            name: 'GRAPHICS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/graphics\"),\n            displayOrder: 8,\n            isVisible: true\n        },\n        {\n            name: 'PAYMENTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/payments\"),\n            displayOrder: 9,\n            isVisible: true\n        }\n    ];\n    // Use dynamic menu items if available, otherwise fallback\n    const finalMenuItems = isLoading || isError || !(menuItems === null || menuItems === void 0 ? void 0 : menuItems.length) ? fallbackMenuItems : transformedMenuItems.filter((item)=>item.isVisible);\n    const handleItemClick = (url)=>{\n        router.push(url);\n    };\n    const isActive = (name)=>{\n        return name === activeItem;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full md:w-64 bg-white rounded-md border border-slate-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: finalMenuItems.map((item)=>{\n                const IconComponent = item.icon;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full flex items-center p-2 rounded-md text-left \".concat(isActive(item.name) ? 'bg-slate-50 text-[#00646C]' : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'),\n                        onClick: ()=>handleItemClick(item.url),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 15\n                    }, this)\n                }, item.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(EventSidebar, \"Cq6nMVoWK52dAqismHSo1JWzXGM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery\n    ];\n});\n_c = EventSidebar;\nvar _c;\n$RefreshReg$(_c, \"EventSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/event-sidebar.tsx\n"));

/***/ })

});