﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportExecutionResults
    {
        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int RowId { get; set; }
        public int RowNumber { get; set; }
        public string Status { get; set; }
        public int? CreatedCompanyId { get; set; }
        public int? UpdatedCompanyId { get; set; }
        public int? CreatedContactId { get; set; }
        public int? UpdatedContactId { get; set; }
        public int? CreatedExhibitorId { get; set; }
        public int? CreatedUserId { get; set; }
        public string CompanyAction { get; set; }
        public string ContactAction { get; set; }
        public string ExhibitorAction { get; set; }
        public string UserAction { get; set; }
        public string CompanyName { get; set; }
        public string ContactName { get; set; }
        public string ContactEmail { get; set; }
        public string ErrorMessage { get; set; }
        public string SkipReason { get; set; }
        public DateTime? ProcessedAt { get; set; }

        public virtual ExhibitorImportRows Row { get; set; }
        public virtual ExhibitorImportSessions Session { get; set; }
    }
}