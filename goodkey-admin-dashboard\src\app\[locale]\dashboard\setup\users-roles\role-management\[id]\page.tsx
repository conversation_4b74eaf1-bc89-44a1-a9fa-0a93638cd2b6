import CardGenerator from '@/components/ui/card_generator';
import AddGeneralInfoForm from './components/general_info_section/add_general_info_form';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;

  return (
    <CardGenerator
      title="General Information"
      description="Role General Information"
    >
      <AddGeneralInfoForm id={params.id ? Number(params.id) : undefined} />
    </CardGenerator>
  );
}
