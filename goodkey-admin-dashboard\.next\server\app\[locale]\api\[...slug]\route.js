/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/api/[...slug]/route";
exports.ids = ["app/[locale]/api/[...slug]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&page=%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&page=%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_developer2_source_repos_Project_goodkey_admin_dashboard_src_app_locale_api_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/[locale]/api/[...slug]/route.ts */ \"(rsc)/./src/app/[locale]/api/[...slug]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/[locale]/api/[...slug]/route\",\n        pathname: \"/[locale]/api/[...slug]\",\n        filename: \"route\",\n        bundlePath: \"app/[locale]/api/[...slug]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\app\\\\[locale]\\\\api\\\\[...slug]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_developer2_source_repos_Project_goodkey_admin_dashboard_src_app_locale_api_slug_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&page=%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/[locale]/api/[...slug]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/[locale]/api/[...slug]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\nasync function modify(request, context) {\n    // Await params before destructuring\n    const { locale, slug } = await context.params;\n    const searchParams = Array.from(new URL(request.url).searchParams.entries()).map(([key, value])=>`${key}=${value}`).join('&');\n    // Fix: await the cookies() call\n    const cookiesStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')?.state;\n    const fullUrl = `${process.env.API_BASE_URL}/${slug.join('/')}?${searchParams}`;\n    const requestHeaders = new Headers(request.headers);\n    const headers = {\n        'content-type': requestHeaders.get('content-type'),\n        Authorization: requestHeaders.get('Authorization'),\n        'accept-language': locale,\n        ...auth?.accessToken ? {\n            Authorization: `Bearer ${auth.accessToken}`\n        } : {}\n    };\n    const contentType = requestHeaders.get('content-type');\n    const isFormData = contentType?.includes('multipart/form-data');\n    const respond = await fetch(fullUrl, {\n        body: contentType ? isFormData ? await request.formData() : JSON.stringify(await request.json()) : undefined,\n        method: request.method,\n        headers: Object.fromEntries(Object.entries(headers).filter(([_, value])=>value !== undefined && value !== '')),\n        mode: request.mode,\n        integrity: request.integrity,\n        redirect: request.redirect,\n        referrer: request.referrer,\n        referrerPolicy: request.referrerPolicy\n    });\n    const eencodedJson = await respond.text();\n    return new Response(eencodedJson, {\n        headers: {\n            'content-type': 'application/json'\n        },\n        status: respond.status,\n        statusText: respond.statusText\n    });\n}\n// Your GET function already has the correct type\nasync function GET(request, context) {\n    // Await params before destructuring\n    const { locale, slug } = await context.params;\n    const searchParams = Array.from(new URL(request.url).searchParams.entries()).map(([key, value])=>`${key}=${value}`).join('&');\n    // Fix: await the cookies() call\n    const cookiesStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')?.state;\n    const fullUrl = `${process.env.API_BASE_URL}/${slug.join('/')}?${searchParams}`;\n    const requestHeaders = new Headers(request.headers);\n    const headers = {\n        'content-type': requestHeaders.get('content-type'),\n        Authorization: requestHeaders.get('Authorization'),\n        'accept-language': locale,\n        ...auth?.accessToken ? {\n            Authorization: `Bearer ${auth.accessToken}`\n        } : {}\n    };\n    const respond = await fetch(fullUrl, {\n        ...request,\n        headers: Object.fromEntries(Object.entries(headers).filter(([_, value])=>value !== undefined && value !== '' && value !== null))\n    });\n    const eencodedJson = await respond.text();\n    return new Response(eencodedJson, {\n        headers: {\n            'content-type': 'application/json'\n        },\n        status: respond.status,\n        statusText: respond.statusText\n    });\n}\n// Export other methods\nconst POST = modify;\nconst PATCH = modify;\nconst DELETE = modify;\nconst PUT = modify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/api/[...slug]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&page=%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fapi%2F%5B...slug%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdeveloper2%5Csource%5Crepos%5CProject%5Cgoodkey-admin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();