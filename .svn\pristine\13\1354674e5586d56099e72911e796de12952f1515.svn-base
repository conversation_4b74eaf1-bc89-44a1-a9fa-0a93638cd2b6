﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportSessions
    {
        public ExhibitorImportSessions()
        {
            ExhibitorImportDuplicates = new HashSet<ExhibitorImportDuplicates>();
            ExhibitorImportExecutionResults = new HashSet<ExhibitorImportExecutionResults>();
            ExhibitorImportFieldResolutions = new HashSet<ExhibitorImportFieldResolutions>();
            ExhibitorImportRows = new HashSet<ExhibitorImportRows>();
            ExhibitorImportValidationMessages = new HashSet<ExhibitorImportValidationMessages>();
        }

        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int ShowId { get; set; }
        public int UserId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string OriginalFileName { get; set; }
        public string MimeType { get; set; }
        public long? FileSize { get; set; }
        public string Status { get; set; }
        public bool? CanProceed { get; set; }
        public int? TotalRows { get; set; }
        public int? ValidRows { get; set; }
        public int? ErrorRows { get; set; }
        public int? WarningRows { get; set; }
        public int? UnresolvedDuplicates { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ValidatedAt { get; set; }
        public DateTime? ExecutedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }

        public virtual ICollection<ExhibitorImportDuplicates> ExhibitorImportDuplicates { get; set; }
        public virtual ICollection<ExhibitorImportExecutionResults> ExhibitorImportExecutionResults { get; set; }
        public virtual ICollection<ExhibitorImportFieldResolutions> ExhibitorImportFieldResolutions { get; set; }
        public virtual ICollection<ExhibitorImportRows> ExhibitorImportRows { get; set; }
        public virtual ICollection<ExhibitorImportValidationMessages> ExhibitorImportValidationMessages { get; set; }
    }
}