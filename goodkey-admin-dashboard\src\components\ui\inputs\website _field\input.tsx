import * as React from 'react';

import { cn } from '@/lib/utils';

import { useFormField } from '../../form';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    const { error } = useFormField();
    return (
      <input
        type={type}
        className={cn(
          ' flex w-full text-foreground text-md font-medium shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50',
          ' p-2 border border-gray-300 rounded-sm ',
          error && 'border-red-500 ',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
