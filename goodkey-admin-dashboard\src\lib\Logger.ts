/* eslint-disable no-console */
import chalk from 'chalk';

import { staticColors as colors } from '@/assets/color';
const isBrowser = typeof window !== 'undefined';
export class Logger {
  static log(title: string, message?: any, ...optionalParams: any[]) {
    if (isBrowser)
      console.log(
        chalk.cyan(`[${title}] `) + (message != undefined ? message : ''),
        ...optionalParams,
      );
    return this;
  }

  static error(title: string, message?: string, ...optionalParams: any[]) {
    if (isBrowser)
      console.error(
        chalk.cyan(`[${title}] `) + (message != undefined ? message : ''),
        ...optionalParams,
      );
    return this;
  }

  static warn(title: string, message?: string, ...optionalParams: any[]) {
    if (isBrowser)
      console.warn(
        chalk.cyan(`[${title}] `) + (message != undefined ? message : ''),
        ...optionalParams,
      );
    return this;
  }

  static info(message?: any, ...optionalParams: any[]) {
    if (isBrowser) console.log(chalk.cyan(message), ...optionalParams);
    return this;
  }

  static scream(message: string, color: string) {
    if (isBrowser)
      console.log(
        `%c${message}`,
        `-webkit-text-stroke: 2px black;font-size: xxx-large;font-weight:bold;color:${
          color ?? colors.cold_blue
        };`,
      );
    return this;
  }
}
Logger.scream('STOP THERE!!!', colors.hot_red);
