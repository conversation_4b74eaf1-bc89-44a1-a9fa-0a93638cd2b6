"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_desktop_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/desktop.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/desktop.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Desktop\\\",\\\"name\\\":\\\"desktop\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#layout\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#values\\\"},{\\\"include\\\":\\\"#inCommands\\\"},{\\\"include\\\":\\\"#inCategories\\\"}],\\\"repository\\\":{\\\"inCategories\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^Categories.*)AudioVideo|(?<=^Categories.*)Audio|(?<=^Categories.*)Video|(?<=^Categories.*)Development|(?<=^Categories.*)Education|(?<=^Categories.*)Game|(?<=^Categories.*)Graphics|(?<=^Categories.*)Network|(?<=^Categories.*)Office|(?<=^Categories.*)Science|(?<=^Categories.*)Settings|(?<=^Categories.*)System|(?<=^Categories.*)Utility\\\",\\\"name\\\":\\\"markup.bold\\\"}]},\\\"inCommands\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^Exec.*\\\\\\\\s)-+\\\\\\\\S+\\\",\\\"name\\\":\\\"variable.parameter\\\"},{\\\"match\\\":\\\"(?<=^Exec.*)\\\\\\\\s\\\\\\\\%[fFuUick]\\\\\\\\s\\\",\\\"name\\\":\\\"variable.language\\\"},{\\\"match\\\":\\\"\\\\\\\".*\\\\\\\"\\\",\\\"name\\\":\\\"string\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"^Type\\\\\\\\b|^Version\\\\\\\\b|^Name\\\\\\\\b|^GenericName\\\\\\\\b|^NoDisplay\\\\\\\\b|^Comment\\\\\\\\b|^Icon\\\\\\\\b|^Hidden\\\\\\\\b|^OnlyShowIn\\\\\\\\b|^NotShowIn\\\\\\\\b|^DBusActivatable\\\\\\\\b|^TryExec\\\\\\\\b|^Exec\\\\\\\\b|^Path\\\\\\\\b|^Terminal\\\\\\\\b|^Actions\\\\\\\\b|^MimeType\\\\\\\\b|^Categories\\\\\\\\b|^Implements\\\\\\\\b|^Keywords\\\\\\\\b|^StartupNotify\\\\\\\\b|^StartupWMClass\\\\\\\\b|^URL\\\\\\\\b|^PrefersNonDefaultGPU\\\\\\\\b|^Encoding\\\\\\\\b\\\",\\\"name\\\":\\\"keyword\\\"},{\\\"match\\\":\\\"^X-[A-z 0-9 -]*\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"(?<!^)\\\\\\\\[.+\\\\\\\\]\\\",\\\"name\\\":\\\"constant.language\\\"},{\\\"match\\\":\\\"^GtkTheme\\\\\\\\b|^MetacityTheme\\\\\\\\b|^IconTheme\\\\\\\\b|^CursorTheme\\\\\\\\b|^ButtonLayout\\\\\\\\b|^ApplicationFont\\\\\\\\b\\\",\\\"name\\\":\\\"keyword\\\"}]},\\\"layout\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\[Desktop\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"markup.heading\\\"},{\\\"begin\\\":\\\"^\\\\\\\\[X-\\\\\\\\w*\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"markup.heading\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*#.*\\\",\\\"name\\\":\\\"comment\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"strong\\\"}]},\\\"values\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=^\\\\\\\\S+)=\\\",\\\"name\\\":\\\"keyword.operator\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b|\\\\\\\\bfalse\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other\\\"},{\\\"match\\\":\\\"(?<=^Version.*)\\\\\\\\d+(\\\\\\\\.{0,1}\\\\\\\\d*)\\\",\\\"name\\\":\\\"variable.other\\\"}]}},\\\"scopeName\\\":\\\"source.desktop\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/desktop.mjs\n"));

/***/ })

}]);