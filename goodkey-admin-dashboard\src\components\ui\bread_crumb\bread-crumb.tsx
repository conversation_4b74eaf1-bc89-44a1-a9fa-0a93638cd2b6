import { ChevronRightIcon } from '@radix-ui/react-icons';
import Link from 'next/link';
import React from 'react';

import { cn } from '@/lib/utils';

type BreadCrumbType = {
  title: string;
  link?: string;
};

type BreadCrumbPropsType = {
  items: BreadCrumbType[];
};

export default function BreadCrumb({ items }: BreadCrumbPropsType) {
  return (
    <div className=" flex flex-row items-center space-x-1 text-sm text-muted-foreground">
      <Link
        href={'/dashboard'}
        className="overflow-hidden text-ellipsis whitespace-nowrap"
      >
        Dashboard
      </Link>
      {items?.map((item: BreadCrumbType, index: number) => (
        <React.Fragment key={item.title}>
          <ChevronRightIcon className="h-4 w-4" />
          {item.link ? (
            <Link
              href={item.link}
              className={cn(
                'font-medium',
                index === 0 ? 'text-muted-foreground' : 'text-foreground',
              )}
            >
              {item.title}
            </Link>
          ) : (
            <span className="font-medium">{item.title}</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
