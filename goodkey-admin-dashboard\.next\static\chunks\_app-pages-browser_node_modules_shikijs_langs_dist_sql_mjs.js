"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_sql_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sql.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SQL\\\",\\\"name\\\":\\\"sql\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"((?<!@)@)\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"text.variable\\\"},{\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\]]*(\\\\\\\\])\\\",\\\"name\\\":\\\"text.bracketed\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(create(?:\\\\\\\\s+or\\\\\\\\s+replace)?)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)(['\\\\\\\"`]?)(\\\\\\\\w+)\\\\\\\\4\\\",\\\"name\\\":\\\"meta.create.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(drop)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.cascade.sql\\\"}},\\\"match\\\":\\\"(?i:\\\\\\\\s*(drop)\\\\\\\\s+(table)\\\\\\\\s+(\\\\\\\\w+)(\\\\\\\\s+cascade)?\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(alter)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)\\\",\\\"name\\\":\\\"meta.alter.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"9\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"11\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"12\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"13\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"14\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"15\\\":{\\\"name\\\":\\\"storage.type.sql\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# normal stuff, capture 1\\\\n\\\\t\\\\t\\\\t\\\\t \\\\\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\\\\\b\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# numeric suffix, capture 2 + 3i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(bit\\\\\\\\svarying|character\\\\\\\\s(?:varying)?|tinyint|var\\\\\\\\schar|float|interval)\\\\\\\\((\\\\\\\\d+)\\\\\\\\)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# optional numeric suffix, capture 4 + 5i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(char|number|varchar\\\\\\\\d?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, capture 6 + 7i + 8i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(numeric|decimal)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+),(\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 9, 10i, 11\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(times?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\swith(?:out)?\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 12, 13, 14i, 15\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(timestamp)(?:(s|tz))?\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\s(with|without)\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b((?:primary|foreign)\\\\\\\\s+key|references|on\\\\\\\\sdelete(\\\\\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.modifier.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(select(\\\\\\\\s+(all|distinct))?|insert\\\\\\\\s+(ignore\\\\\\\\s+)?into|update|delete|from|set|where|group\\\\\\\\s+by|or|like|and|union(\\\\\\\\s+all)?|having|order\\\\\\\\s+by|limit|cross\\\\\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\\\\\s+outer)?)\\\\\\\\s+join|natural(\\\\\\\\s+(inner|(left|right|full)(\\\\\\\\s+outer)?))?\\\\\\\\s+join)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(on|off|((is\\\\\\\\s+)?not\\\\\\\\s+)?null)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DDL.create.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bvalues\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(begin(\\\\\\\\s+work)?|start\\\\\\\\s+transaction|commit(\\\\\\\\s+work)?|rollback(\\\\\\\\s+work)?)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.LUW.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(grant(\\\\\\\\swith\\\\\\\\sgrant\\\\\\\\soption)?|revoke)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.authorization.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bin\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.data-integrity.sql\\\"},{\\\"match\\\":\\\"(?i:^\\\\\\\\s*(comment\\\\\\\\s+on\\\\\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\\\\\s+.*?\\\\\\\\s+(is)\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.other.object-comments.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\bAS\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.alias.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(DESC|ASC)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.order.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.star.sql\\\"},{\\\"match\\\":\\\"[!<>]?=|<>|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.sql\\\"},{\\\"match\\\":\\\"-|\\\\\\\\+|/\\\",\\\"name\\\":\\\"keyword.operator.math.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.concatenator.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.aggregate.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.analytic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.bitmanipulation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.conversion.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.collation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(collationproperty|tertiary_weights)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cryptographic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cursor.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cursor_status)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datetime.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datatype.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.expression.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(coalesce|nullif)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.globalvar.sql\\\"}},\\\"match\\\":\\\"(?<!@)@@(?i)\\\\\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.json.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.logical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(choose|iif|greatest|least)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.mathematical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.metadata.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.ranking.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(rank|dense_rank|ntile|row_number)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.rowset.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.security.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.string.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.system.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textimage.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(patindex|textptr|textvalid)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.database-name.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.table-name.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+?)\\\\\\\\.(\\\\\\\\w+)\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#regexps\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\\\\\\\\\s+or\\\\\\\\\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.sql\\\"}},\\\"comment\\\":\\\"Allow for special ↩ behavior\\\",\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.block.sql\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.sql\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[]},{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"regexps\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/(?=\\\\\\\\S.*/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\/\\\",\\\"name\\\":\\\"constant.character.escape.slash.sql\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"comment\\\":\\\"We should probably handle nested bracket pairs!?! -- Allan\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.modr.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]},\\\"string_escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.sql\\\"},\\\"string_interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(#\\\\\\\\{)([^\\\\\\\\}]*)(\\\\\\\\})\\\",\\\"name\\\":\\\"string.interpolated.sql\\\"},\\\"strings\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(N)?(')[^']*(')\\\",\\\"name\\\":\\\"string.quoted.single.sql\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.single.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(`)[^`\\\\\\\\\\\\\\\\]*(`)\\\",\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\"},{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"comment\\\":\\\"this is faster than the next begin/end rule since sub-pattern will match till end-of-line and SQL files tend to have very long lines.\\\",\\\"match\\\":\\\"(\\\\\\\")[^\\\\\\\"#]*(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sql\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.double.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},{\\\"begin\\\":\\\"%\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.other.quoted.brackets.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.sql\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sql.mjs\n"));

/***/ })

}]);