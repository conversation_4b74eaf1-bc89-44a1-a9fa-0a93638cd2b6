'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import { formatDateTime } from '@/utils/date-format';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';

interface IFacilityDetailBox {
  id?: number;
}

function FacilityDetailBox({ id }: IFacilityDetailBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['ShowLocation Detail', { id }],
    queryFn: () => ShowLocationQuery.getDetail(id!),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {data && (
        <div className="category-info-box">
          <div className="text-sm text-slate-700">
            {data?.locationCode && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">Code:</span>
                <span className="whitespace-nowrap">{data.locationCode}</span>
              </div>
            )}
            {data?.province && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">Province:</span>
                <span className="whitespace-nowrap">{data.province}</span>
              </div>
            )}

            {data?.createdAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Created At:&nbsp;
                </span>
                <span className="whitespace-nowrap">
                  {formatDateTime(data.createdAt)}
                </span>
              </div>
            )}
            {data?.updatedAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Last Updated:&nbsp;
                </span>
                <span className="whitespace-nowrap">
                  {formatDateTime(data.updatedAt)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </Suspense>
  );
}

export default FacilityDetailBox;
