import type { Organization, WithContext } from 'schema-dts';

export function OrganizationStructuredData() {
  const orgData: WithContext<Organization> = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'GOODKEY SHOW SERVICES LTD.',
    description: 'One great idea after another!',
    url: 'https://goodkeyshows.com',
    logo: 'https://goodkeyshows.com/gss-logo.svg',
    sameAs: [
      'https://www.facebook.com/goodkeyshowservices',
      'https://www.linkedin.com/company/goodkey-show-services-ltd',
      'https://twitter.com/goodkeyshows',
    ],
    address: {
      '@type': 'PostalAddress',
      streetAddress: '123 Exhibition Avenue',
      addressLocality: 'Edmonton',
      addressRegion: 'AB',
      postalCode: 'T5J 0A1',
      addressCountry: 'CA',
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(orgData) }}
    />
  );
}
