import { redirect } from 'next/navigation';

import PermissionQuery from '@/services/queries/PermissionQuery';
import { getQueryClient } from '@/utils/query-client';

import AddPermissionSection from './component/add_permission_section';

export default async function Permission(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  const data = await getQueryClient().fetchQuery({
    queryKey: PermissionQuery.tags,
    queryFn: PermissionQuery.getAll,
  });

  if (params.id === 'add' || isNaN(Number.parseInt(params.id))) {
    redirect('/dashboard/setup/users-roles/role-management/add');
  }
  return (
    <div>
      <AddPermissionSection data={data} id={Number.parseInt(params.id)} />
    </div>
  );
}
