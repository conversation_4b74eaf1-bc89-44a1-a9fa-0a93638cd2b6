'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Trash2,
  ShoppingCart,
  AlertTriangle,
  CreditCard,
  Check,
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import WelcomeBanner from '@/components/ui/welcome-banner';

// Define cart item type
interface CartItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  quantity: number;
  color?: string;
  image?: string;
}

// Mock cart data
const initialCartItems: CartItem[] = [
  {
    id: 'banjo-skirts-counter',
    name: 'Banjo skirts for counter height tables - 40" h',
    description: 'Color: Blue',
    price: 45.99,
    quantity: 2,
    color: 'blue',
    image:
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/220_skirts40.jpg-oCsulWYU1iDGj4ZIcKO91PdLfbQBlo.jpeg',
  },
  {
    id: 'bistro-table-black',
    name: 'Bistro table black - 42" h x 30" d',
    price: 89.99,
    quantity: 1,
  },
  {
    id: 'chrome-coffee-table',
    name: 'Chrome coffee table',
    price: 129.99,
    quantity: 1,
  },
];

export default function CartClient({ params }: { params: { id: string } }) {
  const router = useRouter();
  const eventId = params.id;

  const [cartItems, setCartItems] = useState<CartItem[]>(initialCartItems);
  const [isCheckoutDialogOpen, setIsCheckoutDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkoutComplete, setCheckoutComplete] = useState(false);

  // Calculate subtotal, tax, and total
  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );
  const taxRate = 0.05; // 5% tax rate
  const tax = subtotal * taxRate;
  const total = subtotal + tax;

  // Handle quantity change
  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 0) return;

    setCartItems(
      cartItems.map((item) =>
        item.id === id ? { ...item, quantity: newQuantity } : item,
      ),
    );
  };

  // Handle item removal
  const handleRemoveItem = (id: string) => {
    setCartItems(cartItems.filter((item) => item.id !== id));
  };

  // Handle checkout
  const handleCheckout = () => {
    setIsCheckoutDialogOpen(true);
  };

  // Process checkout
  const processCheckout = () => {
    setIsProcessing(true);

    // Simulate API call
    setTimeout(() => {
      setIsProcessing(false);
      setCheckoutComplete(true);
    }, 2000);
  };

  // Handle continue shopping
  const handleContinueShopping = () => {
    router.push(`/event/${eventId}/products`);
  };

  // Handle checkout completion
  const handleCheckoutComplete = () => {
    setIsCheckoutDialogOpen(false);
    setCartItems([]);
    router.push(`/event/${eventId}`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <WelcomeBanner userName="Harry Hekimian" showInstructions={false} />
      {/* <HorizontalMenu activeItem="MANAGEMENT" /> */}

      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => router.push(`/event/${eventId}/products`)}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Products
          </Button>
          <h1 className="text-2xl font-bold text-slate-800">Shopping Cart</h1>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/event/${eventId}`)}
        >
          Back to Event
        </Button>
      </div>

      {cartItems.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Cart Items */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  Cart Items (
                  {cartItems.reduce((sum, item) => sum + item.quantity, 0)})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="bg-slate-50">
                      <TableHead className="w-[100px]">Image</TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Quantity</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cartItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="h-16 w-16 bg-slate-100 rounded overflow-hidden">
                            <img
                              src={
                                item.image ||
                                '/placeholder.svg?height=64&width=64&query=table'
                              }
                              alt={item.name}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.name}</p>
                            {item.description && (
                              <p className="text-sm text-slate-500">
                                {item.description}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          ${item.price.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end">
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() =>
                                handleQuantityChange(item.id, item.quantity - 1)
                              }
                              disabled={item.quantity <= 1}
                            >
                              -
                            </Button>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) =>
                                handleQuantityChange(
                                  item.id,
                                  Number.parseInt(e.target.value) || 0,
                                )
                              }
                              className="h-8 w-16 mx-2 text-center"
                              min="1"
                            />
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() =>
                                handleQuantityChange(item.id, item.quantity + 1)
                              }
                            >
                              +
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          ${(item.price * item.quantity).toFixed(2)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleRemoveItem(item.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleContinueShopping}>
                  Continue Shopping
                </Button>
                <Button variant="destructive" onClick={() => setCartItems([])}>
                  Clear Cart
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Order Summary */}
          <div>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Subtotal:</span>
                    <span className="font-medium">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Tax (5%):</span>
                    <span className="font-medium">${tax.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg">
                    <span className="font-medium">Total:</span>
                    <span className="font-bold">${total.toFixed(2)}</span>
                  </div>

                  <div className="pt-4">
                    <Button
                      className="w-full bg-[#00646C] hover:bg-[#00646C]/90"
                      onClick={handleCheckout}
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      Proceed to Checkout
                    </Button>
                  </div>

                  <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
                    <div className="flex">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0" />
                      <div className="text-sm text-amber-800">
                        <p className="font-medium">Important Note:</p>
                        <p>
                          Orders placed after the deadline date will incur a 25%
                          surcharge.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <Card className="text-center py-12">
          <CardContent>
            <div className="flex flex-col items-center gap-4">
              <div className="rounded-full bg-slate-100 p-6">
                <ShoppingCart className="h-12 w-12 text-slate-400" />
              </div>
              <h2 className="text-2xl font-semibold text-slate-800">
                Your cart is empty
              </h2>
              <p className="text-slate-500 max-w-md mx-auto">
                Looks like you haven't added any products to your cart yet.
                Browse our products to find what you need for your event.
              </p>
              <Button
                className="mt-4 bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={handleContinueShopping}
              >
                Browse Products
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Checkout Dialog */}
      <Dialog
        open={isCheckoutDialogOpen}
        onOpenChange={setIsCheckoutDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {checkoutComplete ? 'Order Confirmed' : 'Complete Your Order'}
            </DialogTitle>
            <DialogDescription>
              {checkoutComplete
                ? 'Your order has been successfully placed.'
                : 'Review your order details before confirming.'}
            </DialogDescription>
          </DialogHeader>

          {!checkoutComplete ? (
            <>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Order Summary</h3>
                  <div className="text-sm space-y-1">
                    {cartItems.map((item) => (
                      <div key={item.id} className="flex justify-between">
                        <span>
                          {item.quantity} x {item.name}
                        </span>
                        <span>${(item.price * item.quantity).toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between font-medium">
                    <span>Total:</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                  <p className="text-sm text-blue-800">
                    In a real application, payment details would be collected
                    here.
                  </p>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCheckoutDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-[#00646C] hover:bg-[#00646C]/90"
                  onClick={processCheckout}
                  disabled={isProcessing}
                >
                  {isProcessing ? 'Processing...' : 'Confirm Order'}
                </Button>
              </DialogFooter>
            </>
          ) : (
            <>
              <div className="py-6 flex flex-col items-center gap-4">
                <div className="rounded-full bg-green-100 p-3">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-center">
                  Thank you for your order! Your order number is{' '}
                  <span className="font-bold">
                    GK-{Math.floor(100000 + Math.random() * 900000)}
                  </span>
                  .
                </p>
                <p className="text-sm text-slate-500 text-center">
                  A confirmation email has been sent to your registered email
                  address.
                </p>
              </div>

              <DialogFooter>
                <Button
                  className="bg-[#00646C] hover:bg-[#00646C]/90 w-full"
                  onClick={handleCheckoutComplete}
                >
                  Return to Event Page
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
