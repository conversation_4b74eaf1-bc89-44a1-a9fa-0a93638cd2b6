import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import DocumentFileTypeQuery from '@/services/queries/DocumentFileTypeQuery';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import GroundConditionForm from './components/ground_condition_form';

export default async function DocumentFileTypePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: [...DocumentFileTypeQuery.tags, { id: Number(id) }],
        queryFn: () => DocumentFileTypeQuery.getOne(Number(id)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Ground Conditions',
        link: '/dashboard/setup/master-setup/ground-conditions',
      },
      {
        title: isAdd ? 'Add Ground Condition' : 'Edit Ground Condition',
        link: `/dashboard/setup/master-setup/ground-conditions/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <GroundConditionForm conditionId={isAdd ? undefined : Number(id)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/ground-conditions/add');
  }
}
