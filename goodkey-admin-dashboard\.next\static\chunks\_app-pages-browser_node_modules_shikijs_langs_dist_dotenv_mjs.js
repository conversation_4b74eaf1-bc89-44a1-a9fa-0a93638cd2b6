"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_dotenv_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/dotenv.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/dotenv.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"dotEnv\\\",\\\"name\\\":\\\"dotenv\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"}]}},\\\"comment\\\":\\\"Full Line Comment\\\",\\\"match\\\":\\\"^\\\\\\\\s?(#.*$)\\\\\\\\n\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.dotenv\\\"},\\\"3\\\":{\\\"name\\\":\\\"property.value.dotenv\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line-comment\\\"},{\\\"include\\\":\\\"#double-quoted-string\\\"},{\\\"include\\\":\\\"#single-quoted-string\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]}},\\\"comment\\\":\\\"ENV entry\\\",\\\"match\\\":\\\"^\\\\\\\\s?(.*?)\\\\\\\\s?(\\\\\\\\=)(.*)$\\\"}],\\\"repository\\\":{\\\"double-quoted-string\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#escape-characters\\\"}]}},\\\"comment\\\":\\\"Double Quoted String\\\",\\\"match\\\":\\\"\\\\\\\"(.*)\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.dotenv\\\"},\\\"escape-characters\\\":{\\\"comment\\\":\\\"Escape characters\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[nrtfb\\\\\\\"'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\u[0123456789ABCDEF]{4}\\\",\\\"name\\\":\\\"constant.character.escape.dotenv\\\"},\\\"interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.interpolation.begin.dotenv\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.interpolation.dotenv\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.interpolation.end.dotenv\\\"}},\\\"comment\\\":\\\"Interpolation (variable substitution)\\\",\\\"match\\\":\\\"(\\\\\\\\$\\\\\\\\{)(.*)(\\\\\\\\})\\\"},\\\"key\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.key.export.dotenv\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.key.dotenv\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"}]}},\\\"comment\\\":\\\"Key\\\",\\\"match\\\":\\\"(export\\\\\\\\s)?(.*)\\\"},\\\"line-comment\\\":{\\\"comment\\\":\\\"Comment\\\",\\\"match\\\":\\\"#.*$\\\",\\\"name\\\":\\\"comment.line.dotenv\\\"},\\\"single-quoted-string\\\":{\\\"comment\\\":\\\"Single Quoted String\\\",\\\"match\\\":\\\"'(.*)'\\\",\\\"name\\\":\\\"string.quoted.single.dotenv\\\"},\\\"variable\\\":{\\\"comment\\\":\\\"env variable\\\",\\\"match\\\":\\\"[a-zA-Z_]+[a-zA-Z0-9_]*\\\"}},\\\"scopeName\\\":\\\"source.dotenv\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/dotenv.mjs\n"));

/***/ })

}]);