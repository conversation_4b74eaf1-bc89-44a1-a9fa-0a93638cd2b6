'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import Suspense from '@/components/ui/Suspense';
import { toast } from '@/components/ui/use-toast';
import { getQueryClient } from '@/utils/query-client';
import { UserDetail } from '@/models/User';
import { UserSchema, UserSchemaData } from '@/schema/UserSchema';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import UsersQuery from '@/services/queries/UsersQuery';
import { SalutationQuery } from '@/services/queries/SalutationQuery';
import DepartmentQuery from '@/services/queries/DepartmentQuery';

interface IUserForm {
  data?: UserDetail;
}
export default function EmployeeForm({ data }: IUserForm) {
  const c = useTranslations('Common');
  const t = useTranslations('employeePage');
  const { push } = useRouter();

  const {
    data: provinces,
    isLoading: isLoadingProvinces,
    isSuccess: isSuccessProvinces,
  } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const {
    data: departments,
    isLoading: isLoadingDepartments,
    isSuccess: isDepartmentsSuccess,
  } = useQuery({
    queryKey: DepartmentQuery.tags,
    queryFn: DepartmentQuery.getAll,
  });

  const {
    data: statuses,
    isLoading: isLoadingStatuses,
    isSuccess: isStatusesSuccess,
  } = useQuery({
    queryKey: ['Users', 'Statuses'],
    queryFn: UsersQuery.getStatuses,
  });

  const {
    data: salutations,
    isLoading: isLoadingSalutations,
    isSuccess: isSalutationsSuccess,
  } = useQuery({
    queryKey: SalutationQuery.tags,
    queryFn: SalutationQuery.getBrief,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: data ? UsersQuery.update(data.id) : UsersQuery.create,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: UsersQuery.tags,
      });

      toast({
        variant: 'success',
        title: 'Success',
        description: data ? 'User updated' : 'User created',
      });
      push('/dashboard/setup/user-management');
    },
  });

  const form = useForm<UserSchemaData>({
    resolver: zodResolver(UserSchema),
    defaultValues: data
      ? {
          firstName: data.firstName,
          lastName: data.lastName,
          departmentId: data.departmentId?.toString(),
          workEmail: data.workEmail,
          workPhoneNumber: data.workPhoneNumber,
          statusId: data.statusId?.toString(),
          salutationId: data.salutationId?.toString(),
          mobileNumber: data.mobileNumber,
          verificationEmail: data.verificationEmail,
          archive: data.archive ?? false,
        }
      : {
          firstName: '',
          lastName: '',
          departmentId: undefined,
          workEmail: '',
          workPhoneNumber: '',
          statusId: '1',
          mobileNumber: '',
          verificationEmail: '',
          archive: false,
        },
  });
  console.log('form errors', form.formState.errors);

  return (
    <Suspense
      isLoading={
        isLoadingDepartments ||
        isLoadingStatuses ||
        isLoadingSalutations ||
        isLoadingProvinces
      }
    >
      <Form {...form}>
        <form
          className="flex flex-col gap-6 w-full  rounded-[12px] "
          onSubmit={form.handleSubmit((d) => {
            console.log('d user', d);
            mutate(d);
          })}
        >
          <div className="grid md:grid-cols-2 gap-y-6 gap-x-16 ">
            <Field
              control={form.control}
              name="firstName"
              label={t('form.firstNameLabel')}
              placeholder={t('form.firstNamePlaceholder')}
              required={true}
              type="text"
            />
            <Field
              control={form.control}
              name="lastName"
              label={t('form.lastNameLabel')}
              placeholder={t('form.lastNamePlaceholder')}
              required={true}
              type="text"
            />

            <Field
              control={form.control}
              name="verificationEmail"
              label={'Verification Email'}
              placeholder={'Verification Email'}
              type="text"
            />
            <Field
              control={form.control}
              name="workEmail"
              label={t('form.workEmailLabel')}
              placeholder={t('form.workEmailPlaceholder')}
              type="text"
            />
            <Field
              control={form.control}
              name="mobileNumber"
              label={t('form.mobileNumberLabel')}
              placeholder={t('form.mobileNumberPlaceholder')}
              type="text"
            />
            <Field
              control={form.control}
              name="workPhoneNumber"
              label={t('form.workPhoneNumberLabel')}
              placeholder={t('form.workPhoneNumberPlaceholder')}
              type="text"
            />

            <Field
              control={form.control}
              name="departmentId"
              label={t('form.departmentLabel')}
              type={{
                type: 'select',
                props: {
                  options:
                    departments?.map((department) => ({
                      label: department.name,
                      value: department.id.toString(),
                    })) ?? [],
                  placeholder: t('form.departmentPlaceholder'),
                },
              }}
            />
            <Field
              control={form.control}
              name="statusId"
              label={t('form.statusLabel')}
              type={{
                type: 'select',
                props: {
                  options:
                    statuses?.map((status) => ({
                      label: status.name,
                      value: status.id.toString(),
                    })) ?? [],
                  placeholder: t('form.statusPlaceholder'),
                },
              }}
            />
            <Field
              control={form.control}
              name="salutationId"
              label={t('form.salutationLabel')}
              type={{
                type: 'select',
                props: {
                  options:
                    salutations?.map((salutation) => ({
                      label: salutation.name,
                      value: salutation.id.toString(),
                    })) ?? [],
                  placeholder: t('form.salutationPlaceholder'),
                },
              }}
            />
            <Field
              control={form.control}
              name="archive"
              label={'Is archived'}
              type="checkbox"
              containerClassName="flex flex-row gap-4 w-fit items-center justify-between"
            />
          </div>

          <div className="flex justify-end items-center gap-4">
            <Button
              variant="default"
              type="submit"
              disabled={isPending}
              iconName="CheckCircleIcon"
              iconProps={{ className: 'text-success-foreground' }}
            >
              {isPending
                ? c('saveLoading')
                : data?.id
                  ? 'Update User'
                  : 'Create User'}
            </Button>
          </div>
        </form>
      </Form>
    </Suspense>
  );
}
