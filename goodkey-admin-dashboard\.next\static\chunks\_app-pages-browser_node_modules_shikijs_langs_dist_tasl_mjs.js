"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tasl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tasl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tasl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Tasl\\\",\\\"fileTypes\\\":[\\\"tasl\\\"],\\\"name\\\":\\\"tasl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#namespace\\\"},{\\\"include\\\":\\\"#type\\\"},{\\\"include\\\":\\\"#class\\\"},{\\\"include\\\":\\\"#edge\\\"}],\\\"repository\\\":{\\\"class\\\":{\\\"begin\\\":\\\"(?:^\\\\\\\\s*)(class)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.class\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"},{\\\"include\\\":\\\"#export\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"comment\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tasl\\\"}},\\\"match\\\":\\\"(#).*$\\\",\\\"name\\\":\\\"comment.line.number-sign.tasl\\\"},\\\"component\\\":{\\\"begin\\\":\\\"->\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.tasl.component\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"coproduct\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.coproduct\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.coproduct\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#term\\\"},{\\\"include\\\":\\\"#option\\\"}]},\\\"datatype\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\\\",\\\"name\\\":\\\"string.regexp\\\"},\\\"edge\\\":{\\\"begin\\\":\\\"(?:^\\\\\\\\s*)(edge)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.edge\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"},{\\\"include\\\":\\\"#export\\\"},{\\\"match\\\":\\\"=/\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge.source\\\"},{\\\"match\\\":\\\"/=>\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge.target\\\"},{\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"punctuation.separator.tasl.edge\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"export\\\":{\\\"match\\\":\\\"::\\\",\\\"name\\\":\\\"keyword.operator.tasl.export\\\"},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#uri\\\"},{\\\"include\\\":\\\"#product\\\"},{\\\"include\\\":\\\"#coproduct\\\"},{\\\"include\\\":\\\"#reference\\\"},{\\\"include\\\":\\\"#optional\\\"},{\\\"include\\\":\\\"#identifier\\\"}]},\\\"identifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable\\\"}},\\\"match\\\":\\\"([a-zA-Z][a-zA-Z0-9]*)\\\\\\\\b\\\"},\\\"key\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\\\",\\\"name\\\":\\\"markup.bold entity.name.class\\\"},\\\"literal\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#datatype\\\"}]},\\\"namespace\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.namespace\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#namespaceURI\\\"},{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name\\\"}]}},\\\"match\\\":\\\"(?:^\\\\\\\\s*)(namespace)\\\\\\\\b(.*)\\\"},\\\"namespaceURI\\\":{\\\"match\\\":\\\"[a-z]+:[a-zA-Z0-9-._~:\\\\\\\\/?#\\\\\\\\[\\\\\\\\]@!$&'()*+,;%=]+\\\",\\\"name\\\":\\\"markup.underline.link\\\"},\\\"option\\\":{\\\"begin\\\":\\\"<-\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.tasl.option\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"optional\\\":{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"product\\\":{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.product\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.tasl.product\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#term\\\"},{\\\"include\\\":\\\"#component\\\"}]},\\\"reference\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.bold keyword.operator\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#key\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\*)\\\\\\\\s*(.*)\\\"},\\\"term\\\":{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\\\",\\\"name\\\":\\\"entity.other.tasl.key\\\"},\\\"type\\\":{\\\"begin\\\":\\\"(?:^\\\\\\\\s*)(type)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tasl.type\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"uri\\\":{\\\"match\\\":\\\"<>\\\",\\\"name\\\":\\\"variable.other.constant\\\"}},\\\"scopeName\\\":\\\"source.tasl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tasl.mjs\n"));

/***/ })

}]);