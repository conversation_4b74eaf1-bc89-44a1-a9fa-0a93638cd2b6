import * as React from 'react';
import { cn } from '@/lib/utils';
import { useFormField } from '../form';
import { StandaloneInput } from './standalone-input';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    // Try to use form context, but don't throw an error if it's not available
    let formError = false;
    try {
      const formField = useFormField();
      formError = !!formField?.error;
    } catch (e) {
      // If useFormField throws an error, we're not in a form context
      // Just use the standalone input instead
      return (
        <StandaloneInput
          ref={ref}
          type={type}
          className={className}
          {...props}
        />
      );
    }

    return (
      <input
        type={type}
        className={cn(
          'flex w-full text-foreground text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50',
          ' p-2 border border-gray-300 rounded-sm ',
          formError && 'border-red-500 ',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
