'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import { RoleSchema, RoleData } from '@/schema/RoleSchema';
import AuthQuery from '@/services/queries/AuthQuery';
import RoleQuery from '@/services/queries/RoleQuery';
import { getQueryClient } from '@/utils/query-client';
import Field from '@/components/ui/inputs/field';

function FormContent({
  defaultValues,
  id,
}: {
  id?: number;
  defaultValues?: RoleData;
}) {
  const { toast } = useToast();
  const { push } = useRouter();
  const searchParams = useSearchParams();

  // Get group information from URL parameters
  const minLevel = searchParams.get('minLevel');
  const maxLevel = searchParams.get('maxLevel');
  const groupName = searchParams.get('groupName');

  const { mutate, isPending } = useMutation({
    mutationFn: id ? RoleQuery.update(id) : RoleQuery.create,
    onSuccess: async (data) => {
      if (Number.isInteger(data))
        push('/dashboard/setup/users-roles/role-management/' + data);
      else {
        await getQueryClient().invalidateQueries({
          queryKey: [...RoleQuery.tags, { id }],
        });
      }
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });

      await getQueryClient().invalidateQueries({
        queryKey: [...RoleQuery.tags],
      });
      toast({
        variant: 'success',
        title: Number.isInteger(data) ? 'Role created' : 'Role updated',
      });
    },
  });

  const form = useForm<RoleData>({
    resolver: zodResolver(RoleSchema),
    defaultValues: defaultValues
      ? {
          name: defaultValues.name,
          description: defaultValues.description,
          level: defaultValues.level,
        }
      : {
          name: '',
          description: '',
          level: 1,
        },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="flex flex-col gap-6 w-full rounded-[12px] "
      >
        <div className="grid md:grid-cols-2 gap-6">
          <Field
            control={form.control}
            name="name"
            label="Name"
            required={true}
            type="text"
          />
          <Field
            control={form.control}
            name="description"
            label="Description"
            required={true}
            type="text"
          />
        </div>

        <div className="grid md:grid-cols-1 gap-6">
          <div className="max-w-md">
            <Field
              control={form.control}
              name="level"
              label="Level"
              required={true}
              type="number"
              placeholder="Enter role level"
              containerClassName="max-w-md"
            />
            {minLevel && maxLevel && groupName && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium text-blue-900">
                    Adding role to: {decodeURIComponent(groupName)}
                  </span>
                </div>
                <div className="text-xs text-blue-700 mt-1">
                  Recommended level range: {minLevel} - {maxLevel}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-end items-center gap-4">
          <Button
            variant={'primary'}
            disabled={isPending}
            iconName="SaveIcon"
            iconProps={{ className: 'text-success-foreground' }}
          >
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

interface IContent {
  id?: number;
}
export default function Content({ id }: IContent) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...RoleQuery.tags, { id }],
    queryFn: () => RoleQuery.getOne(id!),
    enabled: id != undefined,
    select: (d) => {
      return {
        name: d.name,
        description: d.description,
        level: (d as any).level || 1,
      };
    },
  });
  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent id={Number(id)} defaultValues={data} />
    </Suspense>
  );
}
