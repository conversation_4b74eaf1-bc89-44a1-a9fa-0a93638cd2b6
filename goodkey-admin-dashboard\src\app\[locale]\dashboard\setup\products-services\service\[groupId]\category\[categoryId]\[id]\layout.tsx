import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import ProductSection from './component/product_section';
import ServiceDetailBox from './component/service_detail_box';
import ServiceInfoBox from './component/service_info_box';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{
    groupId: string;
    categoryId: string;
    id: string;
  }>;
}>) {
  const { groupId, categoryId, id } = await params;

  const isEdit = !Number.isNaN(Number(id)) && id !== 'add';

  if (!isEdit && id !== 'add') {
    redirect(
      `/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/add`,
    );
  }

  return (
    <div className="flex flex-col self-stretch w-full gap-8">
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <AppLayout
          items={[
            { title: 'Setup', link: '/dashboard/setup' },
            {
              title: 'Products & Services',
              link: '/dashboard/setup',
            },
            {
              title: 'Service',
              link: '/dashboard/setup/products-services/product',
            },
            {
              title: 'Service Detail',
              link: `/dashboard/setup/products-services/service/${groupId}/category/${categoryId}/${id}`,
            },
          ]}
          headerExtras={
            isEdit ? (
              <HydrationBoundary state={dehydrate(getQueryClient())}>
                <ServiceDetailBox id={Number(id)} />
              </HydrationBoundary>
            ) : undefined
          }
        >
          <div className="flex flex-col w-full">
            {
              <HydrationBoundary state={dehydrate(getQueryClient())}>
                <ServiceInfoBox id={isEdit ? Number(id) : undefined} />
              </HydrationBoundary>
            }
            <div className="w-full flex">
              <ProductSection
                id={isEdit ? Number(id) : undefined}
                groupId={Number(groupId)}
                categoryId={Number(categoryId)}
              />
              <div className="w-full overflow-y-auto scrollbar-hide px-1">
                {children}
              </div>
            </div>
          </div>
        </AppLayout>
      </HydrationBoundary>
    </div>
  );
}
