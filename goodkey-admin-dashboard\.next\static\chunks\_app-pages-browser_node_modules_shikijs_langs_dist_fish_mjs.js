"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_fish_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/fish.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/fish.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Fish\\\",\\\"fileTypes\\\":[\\\"fish\\\"],\\\"firstLineMatch\\\":\\\"^#!.*\\\\\\\\bfish\\\\\\\\b\\\",\\\"foldingStartMarker\\\":\\\"^\\\\\\\\s*(function|while|if|switch|for|begin)\\\\\\\\s.*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*end\\\\\\\\s*$\\\",\\\"name\\\":\\\"fish\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fish\\\"}},\\\"comment\\\":\\\"Double quoted string\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fish\\\"}},\\\"name\\\":\\\"string.quoted.double.fish\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"comment\\\":\\\"https://fishshell.com/docs/current/#quotes\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\"|\\\\\\\\$|$|\\\\\\\\\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.fish\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.fish\\\"}},\\\"comment\\\":\\\"Single quoted string\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.fish\\\"}},\\\"name\\\":\\\"string.quoted.single.fish\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"https://fishshell.com/docs/current/#quotes\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\('|`|\\\\\\\\\\\\\\\\)\\\",\\\"name\\\":\\\"constant.character.escape.fish\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.fish\\\"}},\\\"comment\\\":\\\"line comment\\\",\\\"match\\\":\\\"(?<!\\\\\\\\$)(#)(?!\\\\\\\\{).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.fish\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.fish\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.function.command.fish\\\"}},\\\"comment\\\":\\\"name of command, either a function or a binary\\\",\\\"match\\\":\\\"(^\\\\\\\\s*|&&\\\\\\\\s*|\\\\\\\\|\\\\\\\\s*|\\\\\\\\(\\\\\\\\s*|[;]\\\\\\\\s*|\\\\\\\\b(if|while)\\\\\\\\b\\\\\\\\s+)(?!(?<!\\\\\\\\.)\\\\\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\\\\\b(?![?!]))([a-zA-Z_\\\\\\\\-0-9\\\\\\\\[\\\\\\\\].]+)\\\"},{\\\"comment\\\":\\\"keywords that affect control flow\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(function|while|if|else|switch|case|for|in|begin|end|continue|break|return|source|exit|wait|and|or|not)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.control.fish\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\bfunction\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"storage.type.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.pipe.fish\\\"},{\\\"comment\\\":\\\"IO Redirection\\\",\\\"match\\\":\\\"(?:<|#StandardInput(>|\\\\\\\\^|>>|\\\\\\\\^\\\\\\\\^)(&[012\\\\\\\\-])?|[012](<|>|>>)(&[012\\\\\\\\-])?)\\\",\\\"name\\\":\\\"keyword.operator.redirect.fish\\\"},{\\\"match\\\":\\\"&\\\",\\\"name\\\":\\\"keyword.operator.background.fish\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\*|\\\\\\\\*|\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.glob.fish\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"source.option.fish\\\"}},\\\"comment\\\":\\\"command short/long options\\\",\\\"match\\\":\\\"\\\\\\\\s(-{1,2}[a-zA-Z_\\\\\\\\-0-9]+|-\\\\\\\\w)\\\\\\\\b\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#escape\\\"}],\\\"repository\\\":{\\\"escape\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"single character character escape sequences\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[abefnrtv $*?~#(){}\\\\\\\\[\\\\\\\\]<>^&|;\\\\\\\"']\\\",\\\"name\\\":\\\"constant.character.escape.single.fish\\\"},{\\\"comment\\\":\\\"escapes the ascii character with the specified value (hexadecimal)\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\x[0-9a-fA-F]{1,2}\\\",\\\"name\\\":\\\"constant.character.escape.hex-ascii.fish\\\"},{\\\"comment\\\":\\\"escapes a byte of data with the specified value (hexadecimal). If you are using mutibyte encoding, this can be used to enter invalid strings. Only use this if you know what are doing.\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\X[0-9a-fA-F]{1,2}\\\",\\\"name\\\":\\\"constant.character.escape.hex-byte.fish\\\"},{\\\"comment\\\":\\\"escapes the ascii character with the specified value (octal)\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[0-7]{1,3}\\\",\\\"name\\\":\\\"constant.character.escape.octal.fish\\\"},{\\\"comment\\\":\\\"escapes the 16-bit unicode character with the specified value (hexadecimal)\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\u[0-9a-fA-F]{1,4}\\\",\\\"name\\\":\\\"constant.character.escape.unicode-16-bit.fish\\\"},{\\\"comment\\\":\\\"escapes the 32-bit unicode character with the specified value (hexadecimal)\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\U[0-9a-fA-F]{1,8}\\\",\\\"name\\\":\\\"constant.character.escape.unicode-32-bit.fish\\\"},{\\\"comment\\\":\\\"escapes the control sequence generated by pressing the control key and the specified letter\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\\c[a-zA-Z]\\\",\\\"name\\\":\\\"constant.character.escape.control.fish\\\"}]},\\\"variable\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.fish\\\"}},\\\"comment\\\":\\\"Built-in variables visible by pressing $ TAB TAB in a new shell\\\",\\\"match\\\":\\\"(\\\\\\\\$)(argv|CMD_DURATION|COLUMNS|fish_bind_mode|fish_color_autosuggestion|fish_color_cancel|fish_color_command|fish_color_comment|fish_color_cwd|fish_color_cwd_root|fish_color_end|fish_color_error|fish_color_escape|fish_color_hg_added|fish_color_hg_clean|fish_color_hg_copied|fish_color_hg_deleted|fish_color_hg_dirty|fish_color_hg_modified|fish_color_hg_renamed|fish_color_hg_unmerged|fish_color_hg_untracked|fish_color_history_current|fish_color_host|fish_color_host_remote|fish_color_match|fish_color_normal|fish_color_operator|fish_color_param|fish_color_quote|fish_color_redirection|fish_color_search_match|fish_color_selection|fish_color_status|fish_color_user|fish_color_valid_path|fish_complete_path|fish_function_path|fish_greeting|fish_key_bindings|fish_pager_color_completion|fish_pager_color_description|fish_pager_color_prefix|fish_pager_color_progress|fish_pid|fish_prompt_hg_status_added|fish_prompt_hg_status_copied|fish_prompt_hg_status_deleted|fish_prompt_hg_status_modified|fish_prompt_hg_status_order|fish_prompt_hg_status_unmerged|fish_prompt_hg_status_untracked|FISH_VERSION|history|hostname|IFS|LINES|pipestatus|status|umask|version)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.fish\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.fish\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)[a-zA-Z_][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.other.normal.fish\\\"}]}},\\\"scopeName\\\":\\\"source.fish\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/fish.mjs\n"));

/***/ })

}]);