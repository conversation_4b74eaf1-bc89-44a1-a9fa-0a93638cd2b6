'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { getQueryClient } from '@/utils/query-client';
import RoleQuery from '@/services/queries/RoleQuery';
import UsersQuery from '@/services/queries/UsersQuery';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/inputs/select';
import { modal } from '@/components/ui/overlay';

interface IUpdateRoleModal {
  userId: number;
  currentRoleId?: number;
}

export default function UpdateRoleModal({
  userId,
  currentRoleId,
}: IUpdateRoleModal) {
  const [selectedRole, setSelectedRole] = useState<string>(
    currentRoleId?.toString() || '',
  );
  const c = useTranslations('Common');
  const t = useTranslations('usersPage');

  const { data: roles, isLoading: isLoadingRoles } = useQuery({
    queryKey: RoleQuery.tags,
    queryFn: RoleQuery.getAll,
  });

  const { mutate, isPending } = useMutation({
    mutationKey: UsersQuery.tags,
    mutationFn: (roleId: number) => UsersQuery.updateRole(userId, { roleId }),
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });
      modal.close();
    },
  });

  if (currentRoleId === 7) {
    return (
      <ModalContainer
        className="gap-4"
        title={t('updateRole')}
        description={t('cannotChangeClientRole')}
        controls={
          <div className="flex flex-row gap-2">
            <Button onClick={() => modal.close()} variant="secondary">
              {c('close')}
            </Button>
          </div>
        }
      ></ModalContainer>
    );
  }

  // Filter out client role (id=7) from the role list for non-client users
  const availableRoles = roles?.filter((role) => role.id !== 7);

  return (
    <ModalContainer
      onSubmit={(e) => {
        e.preventDefault();
        if (selectedRole) {
          mutate(parseInt(selectedRole));
        }
      }}
      className="gap-4"
      title={t('updateRole')}
      description={t('selectNewRole')}
      controls={
        <div className="flex flex-row gap-2">
          <Button
            onClick={() => modal.close()}
            variant="secondary"
            disabled={isPending}
            iconName="ClearIcon"
          >
            Cancel
          </Button>
          <Button
            disabled={isPending || !selectedRole}
            variant="primary"
            iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
            iconProps={{ className: 'text-white' }}
          >
            {isPending ? 'Please wait...' : 'Update'}
          </Button>
        </div>
      }
    >
      <Select
        value={selectedRole}
        onValueChange={setSelectedRole}
        disabled={isLoadingRoles}
      >
        <SelectTrigger>
          <SelectValue placeholder={t('selectRole')} />
        </SelectTrigger>
        <SelectContent>
          {availableRoles?.map((role) => (
            <SelectItem key={role.id} value={role.id.toString()}>
              {role.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </ModalContainer>
  );
}
