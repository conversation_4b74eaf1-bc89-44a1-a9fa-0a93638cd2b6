﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace goodkey_common.Repositories
{
	public interface IOfferingRateRepository
	{
		Task<IEnumerable<OfferingRate>> GetAllAsync();
		Task<IEnumerable<OfferingRate>> GetByWarehouseIdAsync(int warehouseId);
		Task UpsertAsync(OfferingRate rate);
	}

	public class OfferingRateRepository : IOfferingRateRepository
	{
		private readonly GoodkeyContext _context;

		public OfferingRateRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<IEnumerable<OfferingRate>> GetAllAsync()
		{
			return await _context.OfferingRate.ToListAsync();
		}

		public async Task<IEnumerable<OfferingRate>> GetByWarehouseIdAsync(int warehouseId)
		{
			return await _context.OfferingRate.Where(or => or.WarehouseId == warehouseId).Include(x => x.OfferingProperty).ToListAsync();

		}

		public async Task UpsertAsync(OfferingRate rate)
		{
			var existing = await _context.OfferingRate.FirstOrDefaultAsync(r =>
				r.OfferingId == rate.OfferingId &&
				r.OfferingPropertyId == rate.OfferingPropertyId &&
				r.WarehouseId == rate.WarehouseId
			);

			if (existing != null)
			{
				existing.Quantity = rate.Quantity;
				existing.UnitPrice = rate.UnitPrice;			
				existing.UpdatedAt = DateTime.Now;
				existing.UpdatedById = rate.UpdatedById;
			}
			else
			{
				rate.CreatedAt = DateTime.Now;
				rate.UpdatedAt = DateTime.Now;
				_context.OfferingRate.Add(rate);
			}

			await _context.SaveChangesAsync();
		}
	}
}


