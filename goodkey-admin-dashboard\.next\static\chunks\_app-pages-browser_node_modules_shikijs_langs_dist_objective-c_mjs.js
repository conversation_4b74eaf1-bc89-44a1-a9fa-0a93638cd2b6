"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_objective-c_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/objective-c.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/objective-c.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Objective-C\\\",\\\"name\\\":\\\"objective-c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#anonymous_pattern_1\\\"},{\\\"include\\\":\\\"#anonymous_pattern_2\\\"},{\\\"include\\\":\\\"#anonymous_pattern_3\\\"},{\\\"include\\\":\\\"#anonymous_pattern_4\\\"},{\\\"include\\\":\\\"#anonymous_pattern_5\\\"},{\\\"include\\\":\\\"#apple_foundation_functional_macros\\\"},{\\\"include\\\":\\\"#anonymous_pattern_7\\\"},{\\\"include\\\":\\\"#anonymous_pattern_8\\\"},{\\\"include\\\":\\\"#anonymous_pattern_9\\\"},{\\\"include\\\":\\\"#anonymous_pattern_10\\\"},{\\\"include\\\":\\\"#anonymous_pattern_11\\\"},{\\\"include\\\":\\\"#anonymous_pattern_12\\\"},{\\\"include\\\":\\\"#anonymous_pattern_13\\\"},{\\\"include\\\":\\\"#anonymous_pattern_14\\\"},{\\\"include\\\":\\\"#anonymous_pattern_15\\\"},{\\\"include\\\":\\\"#anonymous_pattern_16\\\"},{\\\"include\\\":\\\"#anonymous_pattern_17\\\"},{\\\"include\\\":\\\"#anonymous_pattern_18\\\"},{\\\"include\\\":\\\"#anonymous_pattern_19\\\"},{\\\"include\\\":\\\"#anonymous_pattern_20\\\"},{\\\"include\\\":\\\"#anonymous_pattern_21\\\"},{\\\"include\\\":\\\"#anonymous_pattern_22\\\"},{\\\"include\\\":\\\"#anonymous_pattern_23\\\"},{\\\"include\\\":\\\"#anonymous_pattern_24\\\"},{\\\"include\\\":\\\"#anonymous_pattern_25\\\"},{\\\"include\\\":\\\"#anonymous_pattern_26\\\"},{\\\"include\\\":\\\"#anonymous_pattern_27\\\"},{\\\"include\\\":\\\"#anonymous_pattern_28\\\"},{\\\"include\\\":\\\"#anonymous_pattern_29\\\"},{\\\"include\\\":\\\"#anonymous_pattern_30\\\"},{\\\"include\\\":\\\"#bracketed_content\\\"},{\\\"include\\\":\\\"#c_lang\\\"}],\\\"repository\\\":{\\\"anonymous_pattern_1\\\":{\\\"begin\\\":\\\"((@)(interface|protocol))(?!.+;)\\\\\\\\s+([A-Za-z_][A-Za-z0-9_]*)\\\\\\\\s*((:)(?:\\\\\\\\s*)([A-Za-z][A-Za-z0-9]*))?(\\\\\\\\s|\\\\\\\\n)?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.entity.other.inherited-class.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.other.inherited-class.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"meta.divider.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"meta.inherited-class.objc\\\"}},\\\"contentName\\\":\\\"meta.scope.interface.objc\\\",\\\"end\\\":\\\"((@)end)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.interface-or-protocol.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},\\\"anonymous_pattern_10\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(defs|encode)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.objc\\\"},\\\"anonymous_pattern_11\\\":{\\\"match\\\":\\\"\\\\\\\\bid\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.id.objc\\\"},\\\"anonymous_pattern_12\\\":{\\\"match\\\":\\\"\\\\\\\\b(IBOutlet|IBAction|BOOL|SEL|id|unichar|IMP|Class|instancetype)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.objc\\\"},\\\"anonymous_pattern_13\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"match\\\":\\\"(@)(class|protocol)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.objc\\\"},\\\"anonymous_pattern_14\\\":{\\\"begin\\\":\\\"((@)selector)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"contentName\\\":\\\"meta.selector.method-name.objc\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"}},\\\"name\\\":\\\"meta.selector.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:[a-zA-Z_:][\\\\\\\\w]*)+\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"}]},\\\"anonymous_pattern_15\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.storage.modifier.objc\\\"}},\\\"match\\\":\\\"(@)(synchronized|public|package|private|protected)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.objc\\\"},\\\"anonymous_pattern_16\\\":{\\\"match\\\":\\\"\\\\\\\\b(YES|NO|Nil|nil)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},\\\"anonymous_pattern_17\\\":{\\\"match\\\":\\\"\\\\\\\\bNSApp\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.foundation.objc\\\"},\\\"anonymous_pattern_18\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.cocoa.leopard.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.cocoa.leopard.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(NS(Rect(ToCGRect|FromCGRect)|MakeCollectable|S(tringFromProtocol|ize(ToCGSize|FromCGSize))|Draw(NinePartImage|ThreePartImage)|P(oint(ToCGPoint|FromCGPoint)|rotocolFromString)|EventMaskFromType|Value))\\\\\\\\b\\\"},\\\"anonymous_pattern_19\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.leading.cocoa.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.cocoa.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(NS(R(ound(DownToMultipleOfPageSize|UpToMultipleOfPageSize)|un(CriticalAlertPanel(RelativeToWindow)?|InformationalAlertPanel(RelativeToWindow)?|AlertPanel(RelativeToWindow)?)|e(set(MapTable|HashTable)|c(ycleZone|t(Clip(List)?|F(ill(UsingOperation|List(UsingOperation|With(Grays|Colors(UsingOperation)?))?)?|romString))|ordAllocationEvent)|turnAddress|leaseAlertPanel|a(dPixel|l(MemoryAvailable|locateCollectable))|gisterServicesProvider)|angeFromString)|Get(SizeAndAlignment|CriticalAlertPanel|InformationalAlertPanel|UncaughtExceptionHandler|FileType(s)?|WindowServerMemory|AlertPanel)|M(i(n(X|Y)|d(X|Y))|ouseInRect|a(p(Remove|Get|Member|Insert(IfAbsent|KnownAbsent)?)|ke(R(ect|ange)|Size|Point)|x(Range|X|Y)))|B(itsPer(SampleFromDepth|PixelFromDepth)|e(stDepth|ep|gin(CriticalAlertSheet|InformationalAlertSheet|AlertSheet)))|S(ho(uldRetainWithZone|w(sServicesMenuItem|AnimationEffect))|tringFrom(R(ect|ange)|MapTable|S(ize|elector)|HashTable|Class|Point)|izeFromString|e(t(ShowsServicesMenuItem|ZoneName|UncaughtExceptionHandler|FocusRingStyle)|lectorFromString|archPathForDirectoriesInDomains)|wap(Big(ShortToHost|IntToHost|DoubleToHost|FloatToHost|Long(ToHost|LongToHost))|Short|Host(ShortTo(Big|Little)|IntTo(Big|Little)|DoubleTo(Big|Little)|FloatTo(Big|Little)|Long(To(Big|Little)|LongTo(Big|Little)))|Int|Double|Float|L(ittle(ShortToHost|IntToHost|DoubleToHost|FloatToHost|Long(ToHost|LongToHost))|ong(Long)?)))|H(ighlightRect|o(stByteOrder|meDirectory(ForUser)?)|eight|ash(Remove|Get|Insert(IfAbsent|KnownAbsent)?)|FSType(CodeFromFileType|OfFile))|N(umberOfColorComponents|ext(MapEnumeratorPair|HashEnumeratorItem))|C(o(n(tainsRect|vert(GlyphsToPackedGlyphs|Swapped(DoubleToHost|FloatToHost)|Host(DoubleToSwapped|FloatToSwapped)))|unt(MapTable|HashTable|Frames|Windows(ForContext)?)|py(M(emoryPages|apTableWithZone)|Bits|HashTableWithZone|Object)|lorSpaceFromDepth|mpare(MapTables|HashTables))|lassFromString|reate(MapTable(WithZone)?|HashTable(WithZone)?|Zone|File(namePboardType|ContentsPboardType)))|TemporaryDirectory|I(s(ControllerMarker|EmptyRect|FreedObject)|n(setRect|crementExtraRefCount|te(r(sect(sRect|ionR(ect|ange))|faceStyleForKey)|gralRect)))|Zone(Realloc|Malloc|Name|Calloc|Fr(omPointer|ee))|O(penStepRootDirectory|ffsetRect)|D(i(sableScreenUpdates|videRect)|ottedFrameRect|e(c(imal(Round|Multiply|S(tring|ubtract)|Normalize|Co(py|mpa(ct|re))|IsNotANumber|Divide|Power|Add)|rementExtraRefCountWasZero)|faultMallocZone|allocate(MemoryPages|Object))|raw(Gr(oove|ayBezel)|B(itmap|utton)|ColorTiledRects|TiledRects|DarkBezel|W(hiteBezel|indowBackground)|LightBezel))|U(serName|n(ionR(ect|ange)|registerServicesProvider)|pdateDynamicServices)|Java(Bundle(Setup|Cleanup)|Setup(VirtualMachine)?|Needs(ToLoadClasses|VirtualMachine)|ClassesF(orBundle|romPath)|ObjectNamedInPath|ProvidesClasses)|P(oint(InRect|FromString)|erformService|lanarFromDepth|ageSize)|E(n(d(MapTableEnumeration|HashTableEnumeration)|umerate(MapTable|HashTable)|ableScreenUpdates)|qual(R(ects|anges)|Sizes|Points)|raseRect|xtraRefCount)|F(ileTypeForHFSTypeCode|ullUserName|r(ee(MapTable|HashTable)|ame(Rect(WithWidth(UsingOperation)?)?|Address)))|Wi(ndowList(ForContext)?|dth)|Lo(cationInRange|g(v|PageSize)?)|A(ccessibility(R(oleDescription(ForUIElement)?|aiseBadArgumentException)|Unignored(Children(ForOnlyChild)?|Descendant|Ancestor)|PostNotification|ActionDescription)|pplication(Main|Load)|vailableWindowDepths|ll(MapTable(Values|Keys)|HashTableObjects|ocate(MemoryPages|Collectable|Object)))))\\\\\\\\b\\\"},\\\"anonymous_pattern_2\\\":{\\\"begin\\\":\\\"((@)(implementation))\\\\\\\\s+([A-Za-z_][A-Za-z0-9_]*)\\\\\\\\s*(?::\\\\\\\\s*([A-Za-z][A-Za-z0-9]*))?\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.storage.type.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.other.inherited-class.objc\\\"}},\\\"contentName\\\":\\\"meta.scope.implementation.objc\\\",\\\"end\\\":\\\"((@)end)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.implementation.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]},\\\"anonymous_pattern_20\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(RuleEditor|G(arbageCollector|radient)|MapTable|HashTable|Co(ndition|llectionView(Item)?)|T(oolbarItemGroup|extInputClient|r(eeNode|ackingArea))|InvocationOperation|Operation(Queue)?|D(ictionaryController|ockTile)|P(ointer(Functions|Array)|athC(o(ntrol(Delegate)?|mponentCell)|ell(Delegate)?)|r(intPanelAccessorizing|edicateEditor(RowTemplate)?))|ViewController|FastEnumeration|Animat(ionContext|ablePropertyContainer))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_21\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(u(nLoop|ler(Marker|View))|e(sponder|cursiveLock|lativeSpecifier)|an(domSpecifier|geSpecifier))|G(etCommand|lyph(Generator|Storage|Info)|raphicsContext)|XML(Node|D(ocument|TD(Node)?)|Parser|Element)|M(iddleSpecifier|ov(ie(View)?|eCommand)|utable(S(tring|et)|C(haracterSet|opying)|IndexSet|D(ictionary|ata)|URLRequest|ParagraphStyle|A(ttributedString|rray))|e(ssagePort(NameServer)?|nu(Item(Cell)?|View)?|t(hodSignature|adata(Item|Query(ResultGroup|AttributeValueTuple)?)))|a(ch(BootstrapServer|Port)|trix))|B(itmapImageRep|ox|u(ndle|tton(Cell)?)|ezierPath|rowser(Cell)?)|S(hadow|c(anner|r(ipt(SuiteRegistry|C(o(ercionHandler|mmand(Description)?)|lassDescription)|ObjectSpecifier|ExecutionContext|WhoseTest)|oll(er|View)|een))|t(epper(Cell)?|atus(Bar|Item)|r(ing|eam))|imple(HorizontalTypesetter|CString)|o(cketPort(NameServer)?|und|rtDescriptor)|p(e(cifierTest|ech(Recognizer|Synthesizer)|ll(Server|Checker))|litView)|e(cureTextField(Cell)?|t(Command)?|archField(Cell)?|rializer|gmentedC(ontrol|ell))|lider(Cell)?|avePanel)|H(ost|TTP(Cookie(Storage)?|URLResponse)|elpManager)|N(ib(Con(nector|trolConnector)|OutletConnector)?|otification(Center|Queue)?|u(ll|mber(Formatter)?)|etService(Browser)?|ameSpecifier)|C(ha(ngeSpelling|racterSet)|o(n(stantString|nection|trol(ler)?|ditionLock)|d(ing|er)|unt(Command|edSet)|pying|lor(Space|P(ick(ing(Custom|Default)|er)|anel)|Well|List)?|m(p(oundPredicate|arisonPredicate)|boBox(Cell)?))|u(stomImageRep|rsor)|IImageRep|ell|l(ipView|o(seCommand|neCommand)|assDescription)|a(ched(ImageRep|URLResponse)|lendar(Date)?)|reateCommand)|T(hread|ypesetter|ime(Zone|r)|o(olbar(Item(Validations)?)?|kenField(Cell)?)|ext(Block|Storage|Container|Tab(le(Block)?)?|Input|View|Field(Cell)?|List|Attachment(Cell)?)?|a(sk|b(le(Header(Cell|View)|Column|View)|View(Item)?))|reeController)|I(n(dex(S(pecifier|et)|Path)|put(Manager|S(tream|erv(iceProvider|er(MouseTracker)?)))|vocation)|gnoreMisspelledWords|mage(Rep|Cell|View)?)|O(ut(putStream|lineView)|pen(GL(Context|Pixel(Buffer|Format)|View)|Panel)|bj(CTypeSerializationCallBack|ect(Controller)?))|D(i(st(antObject(Request)?|ributed(NotificationCenter|Lock))|ctionary|rectoryEnumerator)|ocument(Controller)?|e(serializer|cimalNumber(Behaviors|Handler)?|leteCommand)|at(e(Components|Picker(Cell)?|Formatter)?|a)|ra(wer|ggingInfo))|U(ser(InterfaceValidations|Defaults(Controller)?)|RL(Re(sponse|quest)|Handle(Client)?|C(onnection|ache|redential(Storage)?)|Download(Delegate)?|Prot(ocol(Client)?|ectionSpace)|AuthenticationChallenge(Sender)?)?|n(iqueIDSpecifier|doManager|archiver))|P(ipe|o(sitionalSpecifier|pUpButton(Cell)?|rt(Message|NameServer|Coder)?)|ICTImageRep|ersistentDocument|DFImageRep|a(steboard|nel|ragraphStyle|geLayout)|r(int(Info|er|Operation|Panel)|o(cessInfo|tocolChecker|perty(Specifier|ListSerialization)|gressIndicator|xy)|edicate))|E(numerator|vent|PSImageRep|rror|x(ception|istsCommand|pression))|V(iew(Animation)?|al(idated(ToobarItem|UserInterfaceItem)|ue(Transformer)?))|Keyed(Unarchiver|Archiver)|Qui(ckDrawView|tCommand)|F(ile(Manager|Handle|Wrapper)|o(nt(Manager|Descriptor|Panel)?|rm(Cell|atter)))|W(hoseSpecifier|indow(Controller)?|orkspace)|L(o(c(k(ing)?|ale)|gicalTest)|evelIndicator(Cell)?|ayoutManager)|A(ssertionHandler|nimation|ctionCell|ttributedString|utoreleasePool|TSTypesetter|ppl(ication|e(Script|Event(Manager|Descriptor)))|ffineTransform|lert|r(chiver|ray(Controller)?)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.cocoa.objc\\\"},\\\"anonymous_pattern_22\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(oundingMode|ule(Editor(RowType|NestingMode)|rOrientation)|e(questUserAttentionType|lativePosition))|G(lyphInscription|radientDrawingOptions)|XML(NodeKind|D(ocumentContentKind|TDNodeKind)|ParserError)|M(ultibyteGlyphPacking|apTableOptions)|B(itmapFormat|oxType|ezierPathElement|ackgroundStyle|rowserDropOperation)|S(tr(ing(CompareOptions|DrawingOptions|EncodingConversionOptions)|eam(Status|Event))|p(eechBoundary|litViewDividerStyle)|e(archPathD(irectory|omainMask)|gmentS(tyle|witchTracking))|liderType|aveOptions)|H(TTPCookieAcceptPolicy|ashTableOptions)|N(otification(SuspensionBehavior|Coalescing)|umberFormatter(RoundingMode|Behavior|Style|PadPosition)|etService(sError|Options))|C(haracterCollection|o(lor(RenderingIntent|SpaceModel|PanelMode)|mp(oundPredicateType|arisonPredicateModifier))|ellStateValue|al(culationError|endarUnit))|T(ypesetterControlCharacterAction|imeZoneNameStyle|e(stComparisonOperation|xt(Block(Dimension|V(erticalAlignment|alueType)|Layer)|TableLayoutAlgorithm|FieldBezelStyle))|ableView(SelectionHighlightStyle|ColumnAutoresizingStyle)|rackingAreaOptions)|I(n(sertionPosition|te(rfaceStyle|ger))|mage(RepLoadStatus|Scaling|CacheMode|FrameStyle|LoadStatus|Alignment))|Ope(nGLPixelFormatAttribute|rationQueuePriority)|Date(Picker(Mode|Style)|Formatter(Behavior|Style))|U(RL(RequestCachePolicy|HandleStatus|C(acheStoragePolicy|redentialPersistence))|Integer)|P(o(stingStyle|int(ingDeviceType|erFunctionsOptions)|pUpArrowPosition)|athStyle|r(int(ing(Orientation|PaginationMode)|erTableStatus|PanelOptions)|opertyList(MutabilityOptions|Format)|edicateOperatorType))|ExpressionType|KeyValue(SetMutationKind|Change)|QTMovieLoopMode|F(indPanel(SubstringMatchType|Action)|o(nt(RenderingMode|FamilyClass)|cusRingPlacement))|W(hoseSubelementIdentifier|ind(ingRule|ow(B(utton|ackingLocation)|SharingType|CollectionBehavior)))|L(ine(MovementDirection|SweepDirection|CapStyle|JoinStyle)|evelIndicatorStyle)|Animation(BlockingMode|Curve))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_23\\\":{\\\"match\\\":\\\"\\\\\\\\bC(I(Sampler|Co(ntext|lor)|Image(Accumulator)?|PlugIn(Registration)?|Vector|Kernel|Filter(Generator|Shape)?)|A(Renderer|MediaTiming(Function)?|BasicAnimation|ScrollLayer|Constraint(LayoutManager)?|T(iledLayer|extLayer|rans(ition|action))|OpenGLLayer|PropertyAnimation|KeyframeAnimation|Layer|A(nimation(Group)?|ction)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.class.quartz.objc\\\"},\\\"anonymous_pattern_24\\\":{\\\"match\\\":\\\"\\\\\\\\bC(G(Float|Point|Size|Rect)|IFormat|AConstraintAttribute)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.quartz.objc\\\"},\\\"anonymous_pattern_25\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(ect(Edge)?|ange)|G(lyph(Relation|LayoutMode)?|radientType)|M(odalSession|a(trixMode|p(Table|Enumerator)))|B(itmapImageFileType|orderType|uttonType|ezelStyle|ackingStoreType|rowserColumnResizingType)|S(cr(oll(er(Part|Arrow)|ArrowPosition)|eenAuxiliaryOpaque)|tringEncoding|ize|ocketNativeHandle|election(Granularity|Direction|Affinity)|wapped(Double|Float)|aveOperationType)|Ha(sh(Table|Enumerator)|ndler(2)?)|C(o(ntrol(Size|Tint)|mp(ositingOperation|arisonResult))|ell(State|Type|ImagePosition|Attribute))|T(hreadPrivate|ypesetterGlyphInfo|i(ckMarkPosition|tlePosition|meInterval)|o(ol(TipTag|bar(SizeMode|DisplayMode))|kenStyle)|IFFCompression|ext(TabType|Alignment)|ab(State|leViewDropOperation|ViewType)|rackingRectTag)|ImageInterpolation|Zone|OpenGL(ContextAuxiliary|PixelFormatAuxiliary)|D(ocumentChangeType|atePickerElementFlags|ra(werState|gOperation))|UsableScrollerParts|P(oint|r(intingPageOrder|ogressIndicator(Style|Th(ickness|readInfo))))|EventType|KeyValueObservingOptions|Fo(nt(SymbolicTraits|TraitMask|Action)|cusRingType)|W(indow(OrderingMode|Depth)|orkspace(IconCreationOptions|LaunchOptions)|ritingDirection)|L(ineBreakMode|ayout(Status|Direction))|A(nimation(Progress|Effect)|ppl(ication(TerminateReply|DelegateReply|PrintReply)|eEventManagerSuspensionID)|ffineTransformStruct|lertStyle))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.cocoa.objc\\\"},\\\"anonymous_pattern_26\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(NotFound|Ordered(Ascending|Descending|Same))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.objc\\\"},\\\"anonymous_pattern_27\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(MenuDidBeginTracking|ViewDidUpdateTrackingAreas)?Notification\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.notification.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_28\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(Menu(Did(RemoveItem|SendAction|ChangeItem|EndTracking|AddItem)|WillSendAction)|S(ystemColorsDidChange|plitView(DidResizeSubviews|WillResizeSubviews))|C(o(nt(extHelpModeDid(Deactivate|Activate)|rolT(intDidChange|extDid(BeginEditing|Change|EndEditing)))|lor(PanelColorDidChange|ListDidChange)|mboBox(Selection(IsChanging|DidChange)|Will(Dismiss|PopUp)))|lassDescriptionNeededForClass)|T(oolbar(DidRemoveItem|WillAddItem)|ext(Storage(DidProcessEditing|WillProcessEditing)|Did(BeginEditing|Change|EndEditing)|View(DidChange(Selection|TypingAttributes)|WillChangeNotifyingTextView))|ableView(Selection(IsChanging|DidChange)|ColumnDid(Resize|Move)))|ImageRepRegistryDidChange|OutlineView(Selection(IsChanging|DidChange)|ColumnDid(Resize|Move)|Item(Did(Collapse|Expand)|Will(Collapse|Expand)))|Drawer(Did(Close|Open)|Will(Close|Open))|PopUpButton(CellWillPopUp|WillPopUp)|View(GlobalFrameDidChange|BoundsDidChange|F(ocusDidChange|rameDidChange))|FontSetChanged|W(indow(Did(Resi(ze|gn(Main|Key))|M(iniaturize|ove)|Become(Main|Key)|ChangeScreen(|Profile)|Deminiaturize|Update|E(ndSheet|xpose))|Will(M(iniaturize|ove)|BeginSheet|Close))|orkspace(SessionDid(ResignActive|BecomeActive)|Did(Mount|TerminateApplication|Unmount|PerformFileOperation|Wake|LaunchApplication)|Will(Sleep|Unmount|PowerOff|LaunchApplication)))|A(ntialiasThresholdChanged|ppl(ication(Did(ResignActive|BecomeActive|Hide|ChangeScreenParameters|U(nhide|pdate)|FinishLaunching)|Will(ResignActive|BecomeActive|Hide|Terminate|U(nhide|pdate)|FinishLaunching))|eEventManagerWillProcessFirstEvent)))Notification\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.notification.cocoa.objc\\\"},\\\"anonymous_pattern_29\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(RuleEditor(RowType(Simple|Compound)|NestingMode(Si(ngle|mple)|Compound|List))|GradientDraws(BeforeStartingLocation|AfterEndingLocation)|M(inusSetExpressionType|a(chPortDeallocate(ReceiveRight|SendRight|None)|pTable(StrongMemory|CopyIn|ZeroingWeakMemory|ObjectPointerPersonality)))|B(oxCustom|undleExecutableArchitecture(X86|I386|PPC(64)?)|etweenPredicateOperatorType|ackgroundStyle(Raised|Dark|L(ight|owered)))|S(tring(DrawingTruncatesLastVisibleLine|EncodingConversion(ExternalRepresentation|AllowLossy))|ubqueryExpressionType|p(e(ech(SentenceBoundary|ImmediateBoundary|WordBoundary)|llingState(GrammarFlag|SpellingFlag))|litViewDividerStyleThi(n|ck))|e(rvice(RequestTimedOutError|M(iscellaneousError|alformedServiceDictionaryError)|InvalidPasteboardDataError|ErrorM(inimum|aximum)|Application(NotFoundError|LaunchFailedError))|gmentStyle(Round(Rect|ed)|SmallSquare|Capsule|Textured(Rounded|Square)|Automatic)))|H(UDWindowMask|ashTable(StrongMemory|CopyIn|ZeroingWeakMemory|ObjectPointerPersonality))|N(oModeColorPanel|etServiceNoAutoRename)|C(hangeRedone|o(ntainsPredicateOperatorType|l(orRenderingIntent(RelativeColorimetric|Saturation|Default|Perceptual|AbsoluteColorimetric)|lectorDisabledOption))|ellHit(None|ContentArea|TrackableArea|EditableTextArea))|T(imeZoneNameStyle(S(hort(Standard|DaylightSaving)|tandard)|DaylightSaving)|extFieldDatePickerStyle|ableViewSelectionHighlightStyle(Regular|SourceList)|racking(Mouse(Moved|EnteredAndExited)|CursorUpdate|InVisibleRect|EnabledDuringMouseDrag|A(ssumeInside|ctive(In(KeyWindow|ActiveApp)|WhenFirstResponder|Always))))|I(n(tersectSetExpressionType|dexedColorSpaceModel)|mageScale(None|Proportionally(Down|UpOrDown)|AxesIndependently))|Ope(nGLPFAAllowOfflineRenderers|rationQueue(DefaultMaxConcurrentOperationCount|Priority(High|Normal|Very(High|Low)|Low)))|D(iacriticInsensitiveSearch|ownloadsDirectory)|U(nionSetExpressionType|TF(16(BigEndianStringEncoding|StringEncoding|LittleEndianStringEncoding)|32(BigEndianStringEncoding|StringEncoding|LittleEndianStringEncoding)))|P(ointerFunctions(Ma(chVirtualMemory|llocMemory)|Str(ongMemory|uctPersonality)|C(StringPersonality|opyIn)|IntegerPersonality|ZeroingWeakMemory|O(paque(Memory|Personality)|bjectP(ointerPersonality|ersonality)))|at(hStyle(Standard|NavigationBar|PopUp)|ternColorSpaceModel)|rintPanelShows(Scaling|Copies|Orientation|P(a(perSize|ge(Range|SetupAccessory))|review)))|Executable(RuntimeMismatchError|NotLoadableError|ErrorM(inimum|aximum)|L(inkError|oadError)|ArchitectureMismatchError)|KeyValueObservingOption(Initial|Prior)|F(i(ndPanelSubstringMatchType(StartsWith|Contains|EndsWith|FullWord)|leRead(TooLargeError|UnknownStringEncodingError))|orcedOrderingSearch)|Wi(ndow(BackingLocation(MainMemory|Default|VideoMemory)|Sharing(Read(Only|Write)|None)|CollectionBehavior(MoveToActiveSpace|CanJoinAllSpaces|Default))|dthInsensitiveSearch)|AggregateExpressionType)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.leopard.objc\\\"},\\\"anonymous_pattern_3\\\":{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?[@]\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"}]},\\\"anonymous_pattern_30\\\":{\\\"match\\\":\\\"\\\\\\\\bNS(R(GB(ModeColorPanel|ColorSpaceModel)|ight(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|T(ext(Movement|Alignment)|ab(sBezelBorder|StopType))|ArrowFunctionKey)|ound(RectBezelStyle|Bankers|ed(BezelStyle|TokenStyle|DisclosureBezelStyle)|Down|Up|Plain|Line(CapStyle|JoinStyle))|un(StoppedResponse|ContinuesResponse|AbortedResponse)|e(s(izableWindowMask|et(CursorRectsRunLoopOrdering|FunctionKey))|ce(ssedBezelStyle|iver(sCantHandleCommandScriptError|EvaluationScriptError))|turnTextMovement|doFunctionKey|quiredArgumentsMissingScriptError|l(evancyLevelIndicatorStyle|ative(Before|After))|gular(SquareBezelStyle|ControlSize)|moveTraitFontAction)|a(n(domSubelement|geDateMode)|tingLevelIndicatorStyle|dio(ModeMatrix|Button)))|G(IFFileType|lyph(Below|Inscribe(B(elow|ase)|Over(strike|Below)|Above)|Layout(WithPrevious|A(tAPoint|gainstAPoint))|A(ttribute(BidiLevel|Soft|Inscribe|Elastic)|bove))|r(ooveBorder|eaterThan(Comparison|OrEqualTo(Comparison|PredicateOperatorType)|PredicateOperatorType)|a(y(ModeColorPanel|ColorSpaceModel)|dient(None|Con(cave(Strong|Weak)|vex(Strong|Weak)))|phiteControlTint)))|XML(N(o(tationDeclarationKind|de(CompactEmptyElement|IsCDATA|OptionsNone|Use(SingleQuotes|DoubleQuotes)|Pre(serve(NamespaceOrder|C(haracterReferences|DATA)|DTD|Prefixes|E(ntities|mptyElements)|Quotes|Whitespace|A(ttributeOrder|ll))|ttyPrint)|ExpandEmptyElement))|amespaceKind)|CommentKind|TextKind|InvalidKind|D(ocument(X(MLKind|HTMLKind|Include)|HTMLKind|T(idy(XML|HTML)|extKind)|IncludeContentTypeDeclaration|Validate|Kind)|TDKind)|P(arser(GTRequiredError|XMLDeclNot(StartedError|FinishedError)|Mi(splaced(XMLDeclarationError|CDATAEndStringError)|xedContentDeclNot(StartedError|FinishedError))|S(t(andaloneValueError|ringNot(StartedError|ClosedError))|paceRequiredError|eparatorRequiredError)|N(MTOKENRequiredError|o(t(ationNot(StartedError|FinishedError)|WellBalancedError)|DTDError)|amespaceDeclarationError|AMERequiredError)|C(haracterRef(In(DTDError|PrologError|EpilogError)|AtEOFError)|o(nditionalSectionNot(StartedError|FinishedError)|mment(NotFinishedError|ContainsDoubleHyphenError))|DATANotFinishedError)|TagNameMismatchError|In(ternalError|valid(HexCharacterRefError|C(haracter(RefError|InEntityError|Error)|onditionalSectionError)|DecimalCharacterRefError|URIError|Encoding(NameError|Error)))|OutOfMemoryError|D(ocumentStartError|elegateAbortedParseError|OCTYPEDeclNotFinishedError)|U(RI(RequiredError|FragmentError)|n(declaredEntityError|parsedEntityError|knownEncodingError|finishedTagError))|P(CDATARequiredError|ublicIdentifierRequiredError|arsedEntityRef(MissingSemiError|NoNameError|In(Internal(SubsetError|Error)|PrologError|EpilogError)|AtEOFError)|r(ocessingInstructionNot(StartedError|FinishedError)|ematureDocumentEndError))|E(n(codingNotSupportedError|tity(Ref(In(DTDError|PrologError|EpilogError)|erence(MissingSemiError|WithoutNameError)|LoopError|AtEOFError)|BoundaryError|Not(StartedError|FinishedError)|Is(ParameterError|ExternalError)|ValueRequiredError))|qualExpectedError|lementContentDeclNot(StartedError|FinishedError)|xt(ernalS(tandaloneEntityError|ubsetNotFinishedError)|raContentError)|mptyDocumentError)|L(iteralNot(StartedError|FinishedError)|T(RequiredError|SlashRequiredError)|essThanSymbolInAttributeError)|Attribute(RedefinedError|HasNoValueError|Not(StartedError|FinishedError)|ListNot(StartedError|FinishedError)))|rocessingInstructionKind)|E(ntity(GeneralKind|DeclarationKind|UnparsedKind|P(ar(sedKind|ameterKind)|redefined))|lement(Declaration(MixedKind|UndefinedKind|E(lementKind|mptyKind)|Kind|AnyKind)|Kind))|Attribute(N(MToken(sKind|Kind)|otationKind)|CDATAKind|ID(Ref(sKind|Kind)|Kind)|DeclarationKind|En(tit(yKind|iesKind)|umerationKind)|Kind))|M(i(n(XEdge|iaturizableWindowMask|YEdge|uteCalendarUnit)|terLineJoinStyle|ddleSubelement|xedState)|o(nthCalendarUnit|deSwitchFunctionKey|use(Moved(Mask)?|E(ntered(Mask)?|ventSubtype|xited(Mask)?))|veToBezierPathElement|mentary(ChangeButton|Push(Button|InButton)|Light(Button)?))|enuFunctionKey|a(c(intoshInterfaceStyle|OSRomanStringEncoding)|tchesPredicateOperatorType|ppedRead|x(XEdge|YEdge))|ACHOperatingSystem)|B(MPFileType|o(ttomTabsBezelBorder|ldFontMask|rderlessWindowMask|x(Se(condary|parator)|OldStyle|Primary))|uttLineCapStyle|e(zelBorder|velLineJoinStyle|low(Bottom|Top)|gin(sWith(Comparison|PredicateOperatorType)|FunctionKey))|lueControlTint|ack(spaceCharacter|tabTextMovement|ingStore(Retained|Buffered|Nonretained)|TabCharacter|wardsSearch|groundTab)|r(owser(NoColumnResizing|UserColumnResizing|AutoColumnResizing)|eakFunctionKey))|S(h(ift(JISStringEncoding|KeyMask)|ow(ControlGlyphs|InvisibleGlyphs)|adowlessSquareBezelStyle)|y(s(ReqFunctionKey|tem(D(omainMask|efined(Mask)?)|FunctionKey))|mbolStringEncoding)|c(a(nnedOption|le(None|ToFit|Proportionally))|r(oll(er(NoPart|Increment(Page|Line|Arrow)|Decrement(Page|Line|Arrow)|Knob(Slot)?|Arrows(M(inEnd|axEnd)|None|DefaultSetting))|Wheel(Mask)?|LockFunctionKey)|eenChangedEventType))|t(opFunctionKey|r(ingDrawing(OneShot|DisableScreenFontSubstitution|Uses(DeviceMetrics|FontLeading|LineFragmentOrigin))|eam(Status(Reading|NotOpen|Closed|Open(ing)?|Error|Writing|AtEnd)|Event(Has(BytesAvailable|SpaceAvailable)|None|OpenCompleted|E(ndEncountered|rrorOccurred)))))|i(ngle(DateMode|UnderlineStyle)|ze(DownFontAction|UpFontAction))|olarisOperatingSystem|unOSOperatingSystem|pecialPageOrder|e(condCalendarUnit|lect(By(Character|Paragraph|Word)|i(ng(Next|Previous)|onAffinity(Downstream|Upstream))|edTab|FunctionKey)|gmentSwitchTracking(Momentary|Select(One|Any)))|quareLineCapStyle|witchButton|ave(ToOperation|Op(tions(Yes|No|Ask)|eration)|AsOperation)|mall(SquareBezelStyle|C(ontrolSize|apsFontMask)|IconButtonBezelStyle))|H(ighlightModeMatrix|SBModeColorPanel|o(ur(Minute(SecondDatePickerElementFlag|DatePickerElementFlag)|CalendarUnit)|rizontalRuler|meFunctionKey)|TTPCookieAcceptPolicy(Never|OnlyFromMainDocumentDomain|Always)|e(lp(ButtonBezelStyle|KeyMask|FunctionKey)|avierFontAction)|PUXOperatingSystem)|Year(MonthDa(yDatePickerElementFlag|tePickerElementFlag)|CalendarUnit)|N(o(n(StandardCharacterSetFontMask|ZeroWindingRule|activatingPanelMask|LossyASCIIStringEncoding)|Border|t(ification(SuspensionBehavior(Hold|Coalesce|D(eliverImmediately|rop))|NoCoalescing|CoalescingOn(Sender|Name)|DeliverImmediately|PostToAllSessions)|PredicateType|EqualToPredicateOperatorType)|S(cr(iptError|ollerParts)|ubelement|pecifierError)|CellMask|T(itle|opLevelContainersSpecifierError|abs(BezelBorder|NoBorder|LineBorder))|I(nterfaceStyle|mage)|UnderlineStyle|FontChangeAction)|u(ll(Glyph|CellType)|m(eric(Search|PadKeyMask)|berFormatter(Round(Half(Down|Up|Even)|Ceiling|Down|Up|Floor)|Behavior(10|Default)|S(cientificStyle|pellOutStyle)|NoStyle|CurrencyStyle|DecimalStyle|P(ercentStyle|ad(Before(Suffix|Prefix)|After(Suffix|Prefix))))))|e(t(Services(BadArgumentError|NotFoundError|C(ollisionError|ancelledError)|TimeoutError|InvalidError|UnknownError|ActivityInProgress)|workDomainMask)|wlineCharacter|xt(StepInterfaceStyle|FunctionKey))|EXTSTEPStringEncoding|a(t(iveShortGlyphPacking|uralTextAlignment)|rrowFontMask))|C(hange(ReadOtherContents|GrayCell(Mask)?|BackgroundCell(Mask)?|Cleared|Done|Undone|Autosaved)|MYK(ModeColorPanel|ColorSpaceModel)|ircular(BezelStyle|Slider)|o(n(stantValueExpressionType|t(inuousCapacityLevelIndicatorStyle|entsCellMask|ain(sComparison|erSpecifierError)|rol(Glyph|KeyMask))|densedFontMask)|lor(Panel(RGBModeMask|GrayModeMask|HSBModeMask|C(MYKModeMask|olorListModeMask|ustomPaletteModeMask|rayonModeMask)|WheelModeMask|AllModesMask)|ListModeColorPanel)|reServiceDirectory|m(p(osite(XOR|Source(In|O(ut|ver)|Atop)|Highlight|C(opy|lear)|Destination(In|O(ut|ver)|Atop)|Plus(Darker|Lighter))|ressedFontMask)|mandKeyMask))|u(stom(SelectorPredicateOperatorType|PaletteModeColorPanel)|r(sor(Update(Mask)?|PointingDevice)|veToBezierPathElement))|e(nterT(extAlignment|abStopType)|ll(State|H(ighlighted|as(Image(Horizontal|OnLeftOrBottom)|OverlappingImage))|ChangesContents|Is(Bordered|InsetButton)|Disabled|Editable|LightsBy(Gray|Background|Contents)|AllowsMixedState))|l(ipPagination|o(s(ePathBezierPathElement|ableWindowMask)|ckAndCalendarDatePickerStyle)|ear(ControlTint|DisplayFunctionKey|LineFunctionKey))|a(seInsensitive(Search|PredicateOption)|n(notCreateScriptCommandError|cel(Button|TextMovement))|chesDirectory|lculation(NoError|Overflow|DivideByZero|Underflow|LossOfPrecision)|rriageReturnCharacter)|r(itical(Request|AlertStyle)|ayonModeColorPanel))|T(hick(SquareBezelStyle|erSquareBezelStyle)|ypesetter(Behavior|HorizontalTabAction|ContainerBreakAction|ZeroAdvancementAction|OriginalBehavior|ParagraphBreakAction|WhitespaceAction|L(ineBreakAction|atestBehavior))|i(ckMark(Right|Below|Left|Above)|tledWindowMask|meZoneDatePickerElementFlag)|o(olbarItemVisibilityPriority(Standard|High|User|Low)|pTabsBezelBorder|ggleButton)|IFF(Compression(N(one|EXT)|CCITTFAX(3|4)|OldJPEG|JPEG|PackBits|LZW)|FileType)|e(rminate(Now|Cancel|Later)|xt(Read(InapplicableDocumentTypeError|WriteErrorM(inimum|aximum))|Block(M(i(nimum(Height|Width)|ddleAlignment)|a(rgin|ximum(Height|Width)))|B(o(ttomAlignment|rder)|aselineAlignment)|Height|TopAlignment|P(ercentageValueType|adding)|Width|AbsoluteValueType)|StorageEdited(Characters|Attributes)|CellType|ured(RoundedBezelStyle|BackgroundWindowMask|SquareBezelStyle)|Table(FixedLayoutAlgorithm|AutomaticLayoutAlgorithm)|Field(RoundedBezel|SquareBezel|AndStepperDatePickerStyle)|WriteInapplicableDocumentTypeError|ListPrependEnclosingMarker))|woByteGlyphPacking|ab(Character|TextMovement|le(tP(oint(Mask|EventSubtype)?|roximity(Mask|EventSubtype)?)|Column(NoResizing|UserResizingMask|AutoresizingMask)|View(ReverseSequentialColumnAutoresizingStyle|GridNone|S(olid(HorizontalGridLineMask|VerticalGridLineMask)|equentialColumnAutoresizingStyle)|NoColumnAutoresizing|UniformColumnAutoresizingStyle|FirstColumnOnlyAutoresizingStyle|LastColumnOnlyAutoresizingStyle)))|rackModeMatrix)|I(n(sert(CharFunctionKey|FunctionKey|LineFunctionKey)|t(Type|ernalS(criptError|pecifierError))|dexSubelement|validIndexSpecifierError|formational(Request|AlertStyle)|PredicateOperatorType)|talicFontMask|SO(2022JPStringEncoding|Latin(1StringEncoding|2StringEncoding))|dentityMappingCharacterCollection|llegalTextMovement|mage(R(ight|ep(MatchesDevice|LoadStatus(ReadingHeader|Completed|InvalidData|Un(expectedEOF|knownType)|WillNeedAllData)))|Below|C(ellType|ache(BySize|Never|Default|Always))|Interpolation(High|None|Default|Low)|O(nly|verlaps)|Frame(Gr(oove|ayBezel)|Button|None|Photo)|L(oadStatus(ReadError|C(ompleted|ancelled)|InvalidData|UnexpectedEOF)|eft)|A(lign(Right|Bottom(Right|Left)?|Center|Top(Right|Left)?|Left)|bove)))|O(n(State|eByteGlyphPacking|OffButton|lyScrollerArrows)|ther(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|TextMovement)|SF1OperatingSystem|pe(n(GL(GO(Re(setLibrary|tainRenderers)|ClearFormatCache|FormatCacheSize)|PFA(R(obust|endererID)|M(inimumPolicy|ulti(sample|Screen)|PSafe|aximumPolicy)|BackingStore|S(creenMask|te(ncilSize|reo)|ingleRenderer|upersample|ample(s|Buffers|Alpha))|NoRecovery|C(o(lor(Size|Float)|mpliant)|losestPolicy)|OffScreen|D(oubleBuffer|epthSize)|PixelBuffer|VirtualScreenCount|FullScreen|Window|A(cc(umSize|elerated)|ux(Buffers|DepthStencil)|l(phaSize|lRenderers))))|StepUnicodeReservedBase)|rationNotSupportedForKeyS(criptError|pecifierError))|ffState|KButton|rPredicateType|bjC(B(itfield|oolType)|S(hortType|tr(ingType|uctType)|electorType)|NoType|CharType|ObjectType|DoubleType|UnionType|PointerType|VoidType|FloatType|Long(Type|longType)|ArrayType))|D(i(s(c(losureBezelStyle|reteCapacityLevelIndicatorStyle)|playWindowRunLoopOrdering)|acriticInsensitivePredicateOption|rect(Selection|PredicateModifier))|o(c(ModalWindowMask|ument(Directory|ationDirectory))|ubleType|wn(TextMovement|ArrowFunctionKey))|e(s(cendingPageOrder|ktopDirectory)|cimalTabStopType|v(ice(NColorSpaceModel|IndependentModifierFlagsMask)|eloper(Directory|ApplicationDirectory))|fault(ControlTint|TokenStyle)|lete(Char(acter|FunctionKey)|FunctionKey|LineFunctionKey)|moApplicationDirectory)|a(yCalendarUnit|teFormatter(MediumStyle|Behavior(10|Default)|ShortStyle|NoStyle|FullStyle|LongStyle))|ra(wer(Clos(ingState|edState)|Open(ingState|State))|gOperation(Generic|Move|None|Copy|Delete|Private|Every|Link|All)))|U(ser(CancelledError|D(irectory|omainMask)|FunctionKey)|RL(Handle(NotLoaded|Load(Succeeded|InProgress|Failed))|CredentialPersistence(None|Permanent|ForSession))|n(scaledWindowMask|cachedRead|i(codeStringEncoding|talicFontMask|fiedTitleAndToolbarWindowMask)|d(o(CloseGroupingRunLoopOrdering|FunctionKey)|e(finedDateComponent|rline(Style(Single|None|Thick|Double)|Pattern(Solid|D(ot|ash(Dot(Dot)?)?)))))|known(ColorSpaceModel|P(ointingDevice|ageOrder)|KeyS(criptError|pecifierError))|boldFontMask)|tilityWindowMask|TF8StringEncoding|p(dateWindowsRunLoopOrdering|TextMovement|ArrowFunctionKey))|J(ustifiedTextAlignment|PEG(2000FileType|FileType)|apaneseEUC(GlyphPacking|StringEncoding))|P(o(s(t(Now|erFontMask|WhenIdle|ASAP)|iti(on(Replace|Be(fore|ginning)|End|After)|ve(IntType|DoubleType|FloatType)))|pUp(NoArrow|ArrowAt(Bottom|Center))|werOffEventType|rtraitOrientation)|NGFileType|ush(InCell(Mask)?|OnPushOffButton)|e(n(TipMask|UpperSideMask|PointingDevice|LowerSideMask)|riodic(Mask)?)|P(S(caleField|tatus(Title|Field)|aveButton)|N(ote(Title|Field)|ame(Title|Field))|CopiesField|TitleField|ImageButton|OptionsButton|P(a(perFeedButton|ge(Range(To|From)|ChoiceMatrix))|reviewButton)|LayoutButton)|lainTextTokenStyle|a(useFunctionKey|ragraphSeparatorCharacter|ge(DownFunctionKey|UpFunctionKey))|r(int(ing(ReplyLater|Success|Cancelled|Failure)|ScreenFunctionKey|erTable(NotFound|OK|Error)|FunctionKey)|o(p(ertyList(XMLFormat|MutableContainers(AndLeaves)?|BinaryFormat|Immutable|OpenStepFormat)|rietaryStringEncoding)|gressIndicator(BarStyle|SpinningStyle|Preferred(SmallThickness|Thickness|LargeThickness|AquaThickness)))|e(ssedTab|vFunctionKey))|L(HeightForm|CancelButton|TitleField|ImageButton|O(KButton|rientationMatrix)|UnitsButton|PaperNameButton|WidthForm))|E(n(terCharacter|d(sWith(Comparison|PredicateOperatorType)|FunctionKey))|v(e(nOddWindingRule|rySubelement)|aluatedObjectExpressionType)|qualTo(Comparison|PredicateOperatorType)|ra(serPointingDevice|CalendarUnit|DatePickerElementFlag)|x(clude(10|QuickDrawElementsIconCreationOption)|pandedFontMask|ecuteFunctionKey))|V(i(ew(M(in(XMargin|YMargin)|ax(XMargin|YMargin))|HeightSizable|NotSizable|WidthSizable)|aPanelFontAction)|erticalRuler|a(lidationErrorM(inimum|aximum)|riableExpressionType))|Key(SpecifierEvaluationScriptError|Down(Mask)?|Up(Mask)?|PathExpressionType|Value(MinusSetMutation|SetSetMutation|Change(Re(placement|moval)|Setting|Insertion)|IntersectSetMutation|ObservingOption(New|Old)|UnionSetMutation|ValidationError))|QTMovie(NormalPlayback|Looping(BackAndForthPlayback|Playback))|F(1(1FunctionKey|7FunctionKey|2FunctionKey|8FunctionKey|3FunctionKey|9FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey|6FunctionKey)|7FunctionKey|i(nd(PanelAction(Replace(A(ndFind|ll(InSelection)?))?|S(howFindPanel|e(tFindString|lectAll(InSelection)?))|Next|Previous)|FunctionKey)|tPagination|le(Read(No(SuchFileError|PermissionError)|CorruptFileError|In(validFileNameError|applicableStringEncodingError)|Un(supportedSchemeError|knownError))|HandlingPanel(CancelButton|OKButton)|NoSuchFileError|ErrorM(inimum|aximum)|Write(NoPermissionError|In(validFileNameError|applicableStringEncodingError)|OutOfSpaceError|Un(supportedSchemeError|knownError))|LockingError)|xedPitchFontMask)|2(1FunctionKey|7FunctionKey|2FunctionKey|8FunctionKey|3FunctionKey|9FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey|6FunctionKey)|o(nt(Mo(noSpaceTrait|dernSerifsClass)|BoldTrait|S(ymbolicClass|criptsClass|labSerifsClass|ansSerifClass)|C(o(ndensedTrait|llectionApplicationOnlyMask)|larendonSerifsClass)|TransitionalSerifsClass|I(ntegerAdvancementsRenderingMode|talicTrait)|O(ldStyleSerifsClass|rnamentalsClass)|DefaultRenderingMode|U(nknownClass|IOptimizedTrait)|Panel(S(hadowEffectModeMask|t(andardModesMask|rikethroughEffectModeMask)|izeModeMask)|CollectionModeMask|TextColorEffectModeMask|DocumentColorEffectModeMask|UnderlineEffectModeMask|FaceModeMask|All(ModesMask|EffectsModeMask))|ExpandedTrait|VerticalTrait|F(amilyClassMask|reeformSerifsClass)|Antialiased(RenderingMode|IntegerAdvancementsRenderingMode))|cusRing(Below|Type(None|Default|Exterior)|Only|Above)|urByteGlyphPacking|rm(attingError(M(inimum|aximum))?|FeedCharacter))|8FunctionKey|unction(ExpressionType|KeyMask)|3(1FunctionKey|2FunctionKey|3FunctionKey|4FunctionKey|5FunctionKey|FunctionKey|0FunctionKey)|9FunctionKey|4FunctionKey|P(RevertButton|S(ize(Title|Field)|etButton)|CurrentField|Preview(Button|Field))|l(oat(ingPointSamplesBitmapFormat|Type)|agsChanged(Mask)?)|axButton|5FunctionKey|6FunctionKey)|W(heelModeColorPanel|indow(s(NTOperatingSystem|CP125(1StringEncoding|2StringEncoding|3StringEncoding|4StringEncoding|0StringEncoding)|95(InterfaceStyle|OperatingSystem))|M(iniaturizeButton|ovedEventType)|Below|CloseButton|ToolbarButton|ZoomButton|Out|DocumentIconButton|ExposedEventType|Above)|orkspaceLaunch(NewInstance|InhibitingBackgroundOnly|Default|PreferringClassic|WithoutA(ctivation|ddingToRecents)|A(sync|nd(Hide(Others)?|Print)|llowingClassicStartup))|eek(day(CalendarUnit|OrdinalCalendarUnit)|CalendarUnit)|a(ntsBidiLevels|rningAlertStyle)|r(itingDirection(RightToLeft|Natural|LeftToRight)|apCalendarComponents))|L(i(stModeMatrix|ne(Moves(Right|Down|Up|Left)|B(order|reakBy(C(harWrapping|lipping)|Truncating(Middle|Head|Tail)|WordWrapping))|S(eparatorCharacter|weep(Right|Down|Up|Left))|ToBezierPathElement|DoesntMove|arSlider)|teralSearch|kePredicateOperatorType|ghterFontAction|braryDirectory)|ocalDomainMask|e(ssThan(Comparison|OrEqualTo(Comparison|PredicateOperatorType)|PredicateOperatorType)|ft(Mouse(D(own(Mask)?|ragged(Mask)?)|Up(Mask)?)|T(ext(Movement|Alignment)|ab(sBezelBorder|StopType))|ArrowFunctionKey))|a(yout(RightToLeft|NotDone|CantFit|OutOfGlyphs|Done|LeftToRight)|ndscapeOrientation)|ABColorSpaceModel)|A(sc(iiWithDoubleByteEUCGlyphPacking|endingPageOrder)|n(y(Type|PredicateModifier|EventMask)|choredSearch|imation(Blocking|Nonblocking(Threaded)?|E(ffect(DisappearingItemDefault|Poof)|ase(In(Out)?|Out))|Linear)|dPredicateType)|t(Bottom|tachmentCharacter|omicWrite|Top)|SCIIStringEncoding|d(obe(GB1CharacterCollection|CNS1CharacterCollection|Japan(1CharacterCollection|2CharacterCollection)|Korea1CharacterCollection)|dTraitFontAction|minApplicationDirectory)|uto(saveOperation|Pagination)|pp(lication(SupportDirectory|D(irectory|e(fined(Mask)?|legateReply(Success|Cancel|Failure)|activatedEventType))|ActivatedEventType)|KitDefined(Mask)?)|l(ternateKeyMask|pha(ShiftKeyMask|NonpremultipliedBitmapFormat|FirstBitmapFormat)|ert(SecondButtonReturn|ThirdButtonReturn|OtherReturn|DefaultReturn|ErrorReturn|FirstButtonReturn|AlternateReturn)|l(ScrollerParts|DomainsMask|PredicateModifier|LibrariesDirectory|ApplicationsDirectory))|rgument(sWrongScriptError|EvaluationScriptError)|bove(Bottom|Top)|WTEventType))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.cocoa.objc\\\"},\\\"anonymous_pattern_4\\\":{\\\"begin\\\":\\\"\\\\\\\\b(id)\\\\\\\\s*(?=<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.objc\\\"}},\\\"end\\\":\\\"(?<=>)\\\",\\\"name\\\":\\\"meta.id-with-protocol.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"}]},\\\"anonymous_pattern_5\\\":{\\\"match\\\":\\\"\\\\\\\\b(NS_DURING|NS_HANDLER|NS_ENDHANDLER)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.macro.objc\\\"},\\\"anonymous_pattern_7\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(try|catch|finally|throw)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.exception.objc\\\"},\\\"anonymous_pattern_8\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(synchronized)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.synchronize.objc\\\"},\\\"anonymous_pattern_9\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(required|optional)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.protocol-specification.objc\\\"},\\\"apple_foundation_functional_macros\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:API_AVAILABLE|API_DEPRECATED|API_UNAVAILABLE|NS_AVAILABLE|NS_AVAILABLE_MAC|NS_AVAILABLE_IOS|NS_DEPRECATED|NS_DEPRECATED_MAC|NS_DEPRECATED_IOS|NS_SWIFT_NAME))(?:(?:\\\\\\\\s)+)?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.apple-foundation.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.macro.arguments.begin.bracket.round.apple-foundation.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.macro.arguments.end.bracket.round.apple-foundation.objc\\\"}},\\\"name\\\":\\\"meta.preprocessor.macro.callable.apple-foundation.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#c_lang\\\"}]},\\\"bracketed_content\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.bracketed.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=predicateWithFormat:)(?<=NSPredicate )(predicateWithFormat:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\])\\\",\\\"name\\\":\\\"meta.function-call.predicate.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\bargument(Array|s)(:)\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(:)\\\",\\\"name\\\":\\\"invalid.illegal.unknown-method.objc\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(AND|OR|NOT|IN)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logical.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ALL|ANY|SOME|NONE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|NIL|SELF|TRUE|YES|FALSE|NO|FIRST|LAST|SIZE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(MATCHES|CONTAINS|BEGINSWITH|ENDSWITH|BETWEEN)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.comparison.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bC(ASEINSENSITIVE|I)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.modifier.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ANYKEY|SUBQUERY|CAST|TRUEPREDICATE|FALSEPREDICATE)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.predicate.cocoa.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnrtv'\\\\\\\"?]|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-zA-Z0-9]+)\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?=\\\\\\\\w)(?<=[\\\\\\\\w\\\\\\\\])\\\\\\\"] )(\\\\\\\\w+(?:(:)|(?=\\\\\\\\])))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\])\\\",\\\"name\\\":\\\"meta.function-call.objc\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"}},\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(:)\\\",\\\"name\\\":\\\"support.function.any-method.name-of-parameter.objc\\\"},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"include\\\":\\\"#c_functions\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"c_functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.support.function.leading.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.C99.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\s*)\\\\\\\\b(hypot(f|l)?|s(scanf|ystem|nprintf|ca(nf|lb(n(f|l)?|ln(f|l)?))|i(n(h(f|l)?|f|l)?|gn(al|bit))|tr(s(tr|pn)|nc(py|at|mp)|c(spn|hr|oll|py|at|mp)|to(imax|d|u(l(l)?|max)|k|f|l(d|l)?)|error|pbrk|ftime|len|rchr|xfrm)|printf|et(jmp|vbuf|locale|buf)|qrt(f|l)?|w(scanf|printf)|rand)|n(e(arbyint(f|l)?|xt(toward(f|l)?|after(f|l)?))|an(f|l)?)|c(s(in(h(f|l)?|f|l)?|qrt(f|l)?)|cos(h(f)?|f|l)?|imag(f|l)?|t(ime|an(h(f|l)?|f|l)?)|o(s(h(f|l)?|f|l)?|nj(f|l)?|pysign(f|l)?)|p(ow(f|l)?|roj(f|l)?)|e(il(f|l)?|xp(f|l)?)|l(o(ck|g(f|l)?)|earerr)|a(sin(h(f|l)?|f|l)?|cos(h(f|l)?|f|l)?|tan(h(f|l)?|f|l)?|lloc|rg(f|l)?|bs(f|l)?)|real(f|l)?|brt(f|l)?)|t(ime|o(upper|lower)|an(h(f|l)?|f|l)?|runc(f|l)?|gamma(f|l)?|mp(nam|file))|i(s(space|n(ormal|an)|cntrl|inf|digit|u(nordered|pper)|p(unct|rint)|finite|w(space|c(ntrl|type)|digit|upper|p(unct|rint)|lower|al(num|pha)|graph|xdigit|blank)|l(ower|ess(equal|greater)?)|al(num|pha)|gr(eater(equal)?|aph)|xdigit|blank)|logb(f|l)?|max(div|abs))|di(v|fftime)|_Exit|unget(c|wc)|p(ow(f|l)?|ut(s|c(har)?|wc(har)?)|error|rintf)|e(rf(c(f|l)?|f|l)?|x(it|p(2(f|l)?|f|l|m1(f|l)?)?))|v(s(scanf|nprintf|canf|printf|w(scanf|printf))|printf|f(scanf|printf|w(scanf|printf))|w(scanf|printf)|a_(start|copy|end|arg))|qsort|f(s(canf|e(tpos|ek))|close|tell|open|dim(f|l)?|p(classify|ut(s|c|w(s|c))|rintf)|e(holdexcept|set(e(nv|xceptflag)|round)|clearexcept|testexcept|of|updateenv|r(aiseexcept|ror)|get(e(nv|xceptflag)|round))|flush|w(scanf|ide|printf|rite)|loor(f|l)?|abs(f|l)?|get(s|c|pos|w(s|c))|re(open|e|ad|xp(f|l)?)|m(in(f|l)?|od(f|l)?|a(f|l|x(f|l)?)?))|l(d(iv|exp(f|l)?)|o(ngjmp|cal(time|econv)|g(1(p(f|l)?|0(f|l)?)|2(f|l)?|f|l|b(f|l)?)?)|abs|l(div|abs|r(int(f|l)?|ound(f|l)?))|r(int(f|l)?|ound(f|l)?)|gamma(f|l)?)|w(scanf|c(s(s(tr|pn)|nc(py|at|mp)|c(spn|hr|oll|py|at|mp)|to(imax|d|u(l(l)?|max)|k|f|l(d|l)?|mbs)|pbrk|ftime|len|r(chr|tombs)|xfrm)|to(b|mb)|rtomb)|printf|mem(set|c(hr|py|mp)|move))|a(s(sert|ctime|in(h(f|l)?|f|l)?)|cos(h(f|l)?|f|l)?|t(o(i|f|l(l)?)|exit|an(h(f|l)?|2(f|l)?|f|l)?)|b(s|ort))|g(et(s|c(har)?|env|wc(har)?)|mtime)|r(int(f|l)?|ound(f|l)?|e(name|alloc|wind|m(ove|quo(f|l)?|ainder(f|l)?))|a(nd|ise))|b(search|towc)|m(odf(f|l)?|em(set|c(hr|py|mp)|move)|ktime|alloc|b(s(init|towcs|rtowcs)|towc|len|r(towc|len))))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.function-call.leading.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.any-method.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.objc\\\"}},\\\"match\\\":\\\"(?:(?=\\\\\\\\s)(?:(?<=else|new|return)|(?<!\\\\\\\\w))(\\\\\\\\s+))?(\\\\\\\\b(?!(while|for|do|if|else|switch|catch|enumerate|return|r?iterate)\\\\\\\\s*\\\\\\\\()(?:(?!NS)[A-Za-z_][A-Za-z0-9_]*+\\\\\\\\b|::)++)\\\\\\\\s*(\\\\\\\\()\\\",\\\"name\\\":\\\"meta.function-call.objc\\\"}]},\\\"c_lang\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#switch_statement\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|continue|do|else|for|goto|if|_Pragma|return|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.objc\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"match\\\":\\\"typedef\\\",\\\"name\\\":\\\"keyword.other.typedef.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.in.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(const|extern|register|restrict|static|volatile|inline|__block)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bk[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.variable.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bg[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.global.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bs[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.static.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#special_variables\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((\\\\\\\\#)\\\\\\\\s*define)\\\\\\\\s+((?<id>[a-zA-Z_$][\\\\\\\\w$]*))(?:(\\\\\\\\()(\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*((,)\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*)*(?:\\\\\\\\.\\\\\\\\.\\\\\\\\.)?)(\\\\\\\\)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.define.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.preprocessor.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.objc\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(error|warning))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.diagnostic.$3.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.diagnostic.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"[^'\\\\\\\"]\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"string.unquoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(include(?:_next)?|import))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.$3.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.include.objc\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.objc\\\"}]},{\\\"include\\\":\\\"#pragma-mark\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*line)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.line.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*undef))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.undef.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*pragma))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w\\\\\\\\-$]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.pragma.preprocessor.objc\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sys-types.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.pthread.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stdint.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(noErr|kNilOptions|kInvalidID|kVariableLengthArray)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AbsoluteTime|Boolean|Byte|ByteCount|ByteOffset|BytePtr|CompTimeValue|ConstLogicalAddress|ConstStrFileNameParam|ConstStringPtr|Duration|Fixed|FixedPtr|Float32|Float32Point|Float64|Float80|Float96|FourCharCode|Fract|FractPtr|Handle|ItemCount|LogicalAddress|OptionBits|OSErr|OSStatus|OSType|OSTypePtr|PhysicalAddress|ProcessSerialNumber|ProcessSerialNumberPtr|ProcHandle|Ptr|ResType|ResTypePtr|ShortFixed|ShortFixedPtr|SignedByte|SInt16|SInt32|SInt64|SInt8|Size|StrFileName|StringHandle|StringPtr|TimeBase|TimeRecord|TimeScale|TimeValue|TimeValue64|UInt16|UInt32|UInt64|UInt8|UniChar|UniCharCount|UniCharCountPtr|UniCharPtr|UnicodeScalarValue|UniversalProcHandle|UniversalProcPtr|UnsignedFixed|UnsignedFixedPtr|UnsignedWide|UTF16Char|UTF32Char|UTF8Char)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.mac-classic.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Za-z0-9_]+_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.posix-reserved.objc\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?!\\\\\\\\s*(?:not|compl|sizeof|not_eq|bitand|xor|bitor|and|or|and_eq|xor_eq|or_eq|alignof|alignas|_Alignof|_Alignas|while|for|do|if|else|goto|switch|return|break|case|continue|default|void|char|short|int|signed|unsigned|long|float|double|bool|_Bool|_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|NULL|true|false|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t|struct|union|enum|typedef|auto|register|static|extern|thread_local|inline|_Noreturn|const|volatile|restrict|_Atomic)\\\\\\\\s*\\\\\\\\()(?=[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))?(\\\\\\\\[)(?!\\\\\\\\])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.objc\\\"}},\\\"name\\\":\\\"meta.bracket.square.access.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\s*\\\\\\\\]\\\",\\\"name\\\":\\\"storage.modifier.array.bracket.square.objc\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.objc\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.objc\\\"}],\\\"repository\\\":{\\\"access-method\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*(?:(\\\\\\\\.)|(->))((?:(?:[a-zA-Z_][a-zA-Z_0-9]*)\\\\\\\\s*(?:(?:\\\\\\\\.)|(?:->)))*)\\\\\\\\s*([a-zA-Z_][a-zA-Z_0-9]*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},{\\\"match\\\":\\\"[a-zA-Z_][a-zA-Z_0-9]*\\\",\\\"name\\\":\\\"variable.object.objc\\\"},{\\\"match\\\":\\\".+\\\",\\\"name\\\":\\\"everything.else.objc\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.objc\\\"}},\\\"name\\\":\\\"meta.function-call.member.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"name\\\":\\\"meta.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"block_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional-block\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#c_function_call\\\"},{\\\"begin\\\":\\\"(?:(?:(?=\\\\\\\\s)(?<!else|new|return)(?<=\\\\\\\\w)\\\\\\\\s+(and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)))((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.initialization.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.initialization.objc\\\"}},\\\"name\\\":\\\"meta.initialization.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"include\\\":\\\"#parens-block\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"c_function_call\\\":{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.function-call.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"case_statement\\\":{\\\"begin\\\":\\\"((?<!\\\\\\\\w)case(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.case.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.case.objc\\\"}},\\\"name\\\":\\\"meta.conditional.case.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.block.objc\\\"}},\\\"match\\\":\\\"^/\\\\\\\\* =(\\\\\\\\s*.*?)\\\\\\\\s*= \\\\\\\\*/$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.objc\\\"}},\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.line.objc\\\"}},\\\"match\\\":\\\"^// =(\\\\\\\\s*.*?)\\\\\\\\s*=\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.banner.objc\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.objc\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]}]},\\\"conditional_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"default_statement\\\":{\\\"begin\\\":\\\"((?<!\\\\\\\\w)default(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.default.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.case.default.objc\\\"}},\\\"name\\\":\\\"meta.conditional.case.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"function-call-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"function-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parameters.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.function.definition.parameters.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#probably_a_parameter\\\"},{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},\\\"line_continuation_character\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\n\\\"}]},\\\"member_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.member.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?-mix:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*(\\\\\\\\b(?!(?:void|char|short|int|signed|unsigned|long|float|double|bool|_Bool|_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t))[a-zA-Z_]\\\\\\\\w*\\\\\\\\b(?!\\\\\\\\())\\\"},\\\"method_access\\\":{\\\"begin\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?-mix:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special_variables\\\"},{\\\"match\\\":\\\"(.+)\\\",\\\"name\\\":\\\"variable.other.object.access.objc\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.objc\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.objc\\\"}},\\\"contentName\\\":\\\"meta.function-call.member.objc\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"numbers\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?=\\\\\\\\d|\\\\\\\\.\\\\\\\\d)\\\",\\\"end\\\":\\\"(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.objc\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.objc\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])(?:([0-9a-fA-F](?:(?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*))?((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))(?:([0-9a-fA-F](?:(?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*))?(?:((?<!')([pP])(\\\\\\\\+)?(\\\\\\\\-)?((?-mix:(?:[0-9](?:(?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)))))?(?:([lLfF](?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.objc\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.objc\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.objc\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))(?:([0-9](?:(?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*))?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))(?:([0-9](?:(?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*))?(?:((?<!')([eE])(\\\\\\\\+)?(\\\\\\\\-)?((?-mix:(?:[0-9](?:(?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)))))?(?:([lLfF](?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:(?:[01]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)(?:((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:(?:[0-7]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))+)(?:((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:(?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)(?:((?<!')([pP])(\\\\\\\\+)?(\\\\\\\\-)?((?-mix:(?:[0-9](?:(?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)))))?(?:((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.objc\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.objc\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.objc\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric.objc\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.objc\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:(?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)(?:((?<!')([eE])(\\\\\\\\+)?(\\\\\\\\-)?((?-mix:(?:[0-9](?:(?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F]))))*)))))?(?:((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w)))?(?!(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))\\\"},{\\\"match\\\":\\\"(?:(?:['0-9a-zA-Z_\\\\\\\\.']|(?<=[eEpP])[+-]))+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric.objc\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\w$])(sizeof)(?![\\\\\\\\w$])\\\",\\\"name\\\":\\\"keyword.operator.sizeof.objc\\\"},{\\\"match\\\":\\\"--\\\",\\\"name\\\":\\\"keyword.operator.decrement.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.objc\\\"},{\\\"match\\\":\\\"%=|\\\\\\\\+=|-=|\\\\\\\\*=|(?<!\\\\\\\\()/=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.objc\\\"},{\\\"match\\\":\\\"&=|\\\\\\\\^=|<<=|>>=|\\\\\\\\|=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.bitwise.objc\\\"},{\\\"match\\\":\\\"<<|>>\\\",\\\"name\\\":\\\"keyword.operator.bitwise.shift.objc\\\"},{\\\"match\\\":\\\"!=|<=|>=|==|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.objc\\\"},{\\\"match\\\":\\\"&&|!|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.objc\\\"},{\\\"match\\\":\\\"&|\\\\\\\\||\\\\\\\\^|~\\\",\\\"name\\\":\\\"keyword.operator.objc\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.objc\\\"},{\\\"match\\\":\\\"%|\\\\\\\\*|/|-|\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.objc\\\"},{\\\"begin\\\":\\\"(\\\\\\\\?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.parens.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"parens-block\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"name\\\":\\\"meta.parens.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"},{\\\"match\\\":\\\"(?-mix:(?<!:):(?!:))\\\",\\\"name\\\":\\\"punctuation.range-based.objc\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.pragma.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.pragma-mark.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.pragma-mark.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(((#)\\\\\\\\s*pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.objc\\\"},\\\"preprocessor-rule-conditional\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-line\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:\\\\\\\\bdefined\\\\\\\\b\\\\\\\\s*$)|(?:\\\\\\\\bdefined\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\s*(?:(?!defined\\\\\\\\b)[a-zA-Z_$][\\\\\\\\w$]*\\\\\\\\b)\\\\\\\\s*\\\\\\\\)*\\\\\\\\s*(?:\\\\\\\\n|//|/\\\\\\\\*|\\\\\\\\?|\\\\\\\\:|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\bdefined\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.macro-name.objc\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"end\\\":\\\":\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.objc\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.objc\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]}]},\\\"preprocessor-rule-define-line-blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-define-line-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.objc\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.objc\\\"}},\\\"name\\\":\\\"meta.block.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"}]},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas|asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|long|short|signed|struct|typedef|union|unsigned|void)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-define-line-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-disabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]}]},\\\"preprocessor-rule-enabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]}]},\\\"preprocessor-rule-enabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"$base\\\"}]}]},\\\"preprocessor-rule-enabled-elif-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.in-block.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"preprocessor-rule-enabled-else\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-enabled-else-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"probably_a_parameter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.probably.objc\\\"}},\\\"match\\\":\\\"(?<=(?:[a-zA-Z_0-9] |[&*>\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=(?:\\\\\\\\[\\\\\\\\]\\\\\\\\s*)?(?:,|\\\\\\\\)))\\\"},\\\"static_assert\\\":{\\\"begin\\\":\\\"(static_assert|_Static_assert)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.static_assert.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.objc\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=(?:L|u8|u|U\\\\\\\\s*\\\\\\\\\\\\\\\")?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.delimiter.objc\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.static_assert.message.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_context\\\"},{\\\"include\\\":\\\"#string_context_c\\\"}]},{\\\"include\\\":\\\"#function_call_context\\\"}]},\\\"storage_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:void|char|short|int|signed|unsigned|long|float|double|bool|_Bool)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.primitive.objc\\\"},{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:_Complex|_Imaginary|u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t|pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t|int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t|memory_order|atomic_bool|atomic_char|atomic_schar|atomic_uchar|atomic_short|atomic_ushort|atomic_int|atomic_uint|atomic_long|atomic_ulong|atomic_llong|atomic_ullong|atomic_char16_t|atomic_char32_t|atomic_wchar_t|atomic_int_least8_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_least16_t|atomic_int_least32_t|atomic_uint_least32_t|atomic_int_least64_t|atomic_uint_least64_t|atomic_int_fast8_t|atomic_uint_fast8_t|atomic_int_fast16_t|atomic_uint_fast16_t|atomic_int_fast32_t|atomic_uint_fast32_t|atomic_int_fast64_t|atomic_uint_fast64_t|atomic_intptr_t|atomic_uintptr_t|atomic_size_t|atomic_ptrdiff_t|atomic_intmax_t|atomic_uintmax_t)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.objc\\\"},{\\\"match\\\":\\\"(?-mix:\\\\\\\\b(asm|__asm__|enum|struct|union)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.type.$1.objc\\\"}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|l|j|t|z|q|L|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.objc\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.double.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.objc\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.objc\\\"}},\\\"name\\\":\\\"string.quoted.single.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"switch_conditional_parentheses\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.conditional.switch.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.conditional.switch.objc\\\"}},\\\"name\\\":\\\"meta.conditional.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_context\\\"}]},\\\"switch_statement\\\":{\\\"begin\\\":\\\"(((?<!\\\\\\\\w)switch(?!\\\\\\\\w)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.head.switch.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.switch.objc\\\"}},\\\"end\\\":\\\"(?:(?<=\\\\\\\\})|(?=[;>\\\\\\\\[\\\\\\\\]=]))\\\",\\\"name\\\":\\\"meta.block.switch.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G ?\\\",\\\"end\\\":\\\"((?:\\\\\\\\{|(?=;)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.switch.objc\\\"}},\\\"name\\\":\\\"meta.head.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#switch_conditional_parentheses\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\{)\\\",\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.switch.objc\\\"}},\\\"name\\\":\\\"meta.body.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#default_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"$base\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"(?<=})[\\\\\\\\s\\\\\\\\n]*\\\",\\\"end\\\":\\\"[\\\\\\\\s\\\\\\\\n]*(?=;)\\\",\\\"name\\\":\\\"meta.tail.switch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},\\\"vararg_ellipses\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\.\\\\\\\\.\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"punctuation.vararg-ellipses.objc\\\"}}},\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.objc\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=//)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.objc\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.objc\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?>\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"punctuation.separator.continuation.objc\\\"}]}]}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"comment\\\":\\\"eat nested preprocessor if(def)s\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"implementation_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-implementation\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-implementation\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other-implementation\\\"},{\\\"include\\\":\\\"#property_directive\\\"},{\\\"include\\\":\\\"#method_super\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"interface_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-interface\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-interface\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other-interface\\\"},{\\\"include\\\":\\\"#properties\\\"},{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#method\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"method\\\":{\\\"begin\\\":\\\"^(-|\\\\\\\\+)\\\\\\\\s*\\\",\\\"end\\\":\\\"(?=\\\\\\\\{|#)|;\\\",\\\"name\\\":\\\"meta.function.objc\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*(\\\\\\\\w+\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.end.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.objc\\\"}},\\\"name\\\":\\\"meta.return-type.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#protocol_type_qualifier\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\w+(?=:)\\\",\\\"name\\\":\\\"entity.name.function.name-of-parameter.objc\\\"},{\\\"begin\\\":\\\"((:))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.name-of-parameter.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.arguments.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.type.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\\\\\\s*(\\\\\\\\w+\\\\\\\\b)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.type.end.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.objc\\\"}},\\\"name\\\":\\\"meta.argument-type.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#protocol_list\\\"},{\\\"include\\\":\\\"#protocol_type_qualifier\\\"},{\\\"include\\\":\\\"$base\\\"}]},{\\\"include\\\":\\\"#comment\\\"}]},\\\"method_super\\\":{\\\"begin\\\":\\\"^(?=-|\\\\\\\\+)\\\",\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=#)\\\",\\\"name\\\":\\\"meta.function-with-body.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#method\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.pragma.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.toc-list.pragma-mark.objc\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.objc\\\"},\\\"preprocessor-rule-disabled-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-disabled-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch.objc\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]}]},\\\"preprocessor-rule-enabled-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.objc\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.objc\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]}]},\\\"preprocessor-rule-other-implementation\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(if(n?def)?)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b).*?(?:(?=(?://|/\\\\\\\\*))|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#implementation_innards\\\"}]},\\\"preprocessor-rule-other-interface\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(if(n?def)?)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.objc\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b).*?(?:(?=(?://|/\\\\\\\\*))|$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interface_innards\\\"}]},\\\"properties\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)property)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.property.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.property-with-attributes.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(getter|setter|readonly|readwrite|assign|retain|copy|nonatomic|atomic|strong|weak|nonnull|nullable|null_resettable|null_unspecified|class|direct)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.property.attribute.objc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.property.objc\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"((@)property)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.property.objc\\\"}]},\\\"property_directive\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.objc\\\"}},\\\"match\\\":\\\"(@)(dynamic|synthesize)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.property.directive.objc\\\"},\\\"protocol_list\\\":{\\\"begin\\\":\\\"(<)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.objc\\\"}},\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.objc\\\"}},\\\"name\\\":\\\"meta.protocol-list.objc\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bNS(GlyphStorage|M(utableCopying|enuItem)|C(hangeSpelling|o(ding|pying|lorPicking(Custom|Default)))|T(oolbarItemValidations|ext(Input|AttachmentCell))|I(nputServ(iceProvider|erMouseTracker)|gnoreMisspelledWords)|Obj(CTypeSerializationCallBack|ect)|D(ecimalNumberBehaviors|raggingInfo)|U(serInterfaceValidations|RL(HandleClient|DownloadDelegate|ProtocolClient|AuthenticationChallengeSender))|Validated(ToobarItem|UserInterfaceItem)|Locking)\\\\\\\\b\\\",\\\"name\\\":\\\"support.other.protocol.objc\\\"}]},\\\"protocol_type_qualifier\\\":{\\\"match\\\":\\\"\\\\\\\\b(in|out|inout|oneway|bycopy|byref|nonnull|nullable|_Nonnull|_Nullable|_Null_unspecified)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.protocol.objc\\\"},\\\"special_variables\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b_cmd\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.selector.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\b(self|super)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.objc\\\"}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.objc\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.objc\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|l|j|t|z|q|L|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.objc\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.objc\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]}},\\\"scopeName\\\":\\\"source.objc\\\",\\\"aliases\\\":[\\\"objc\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/objective-c.mjs\n"));

/***/ })

}]);