import { ReactNode } from 'react';
import SetupSidebar from '@/components/setup-sidebar';

export default function SetupLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <div className="w-full flex overflow-hidden  min-h-full max-h-full">
      <div className="flex flex-row gap-4 w-full">
        <SetupSidebar />
        <main className="flex w-full overflow-y-auto scrollbar-hide ">
          {children}
        </main>
      </div>
    </div>
  );
}
