import type { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import SystemNotifications from '@/components/system-notifications';

export const metadata: Metadata = {
  title: 'System Setup | GOODKEY SHOW SERVICES LTD.',
  description:
    'Configure system settings and preferences for GOODKEY SHOW SERVICES LTD. administrative dashboard.',
  openGraph: {
    title: 'System Setup | GOODKEY SHOW SERVICES LTD.',
    description:
      'Configure system settings and preferences for GOODKEY SHOW SERVICES LTD. administrative dashboard.',
  },
};

export default function SetupPage() {
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'System Setup', link: '/dashboard/setup' },
      ]}
    >
      <SystemNotifications />
    </AppLayout>
  );
}
