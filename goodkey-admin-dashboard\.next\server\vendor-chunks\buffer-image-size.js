"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-image-size";
exports.ids = ["vendor-chunks/buffer-image-size"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffer-image-size/lib/detector.js":
/*!********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/detector.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar typeHandlers = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/buffer-image-size/lib/types.js\");\n\nmodule.exports = function (buffer) {\n  var type, result;\n  for (type in typeHandlers) {\n    result = typeHandlers[type].detect(buffer);\n    if (result) {\n      return type;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL2RldGVjdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG1CQUFtQixtQkFBTyxDQUFDLG9FQUFTOztBQUVwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcYnVmZmVyLWltYWdlLXNpemVcXGxpYlxcZGV0ZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgdHlwZUhhbmRsZXJzID0gcmVxdWlyZSgnLi90eXBlcycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChidWZmZXIpIHtcbiAgdmFyIHR5cGUsIHJlc3VsdDtcbiAgZm9yICh0eXBlIGluIHR5cGVIYW5kbGVycykge1xuICAgIHJlc3VsdCA9IHR5cGVIYW5kbGVyc1t0eXBlXS5kZXRlY3QoYnVmZmVyKTtcbiAgICBpZiAocmVzdWx0KSB7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/detector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar typeHandlers = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/buffer-image-size/lib/types.js\");\nvar detector = __webpack_require__(/*! ./detector */ \"(ssr)/./node_modules/buffer-image-size/lib/detector.js\");\n\n/**\n * Return size information based on a buffer\n *\n * @param {Buffer} buffer\n * @returns {Object}\n */\nfunction lookup (buffer) {\n  // detect the file type.. don't rely on the extension\n  var type = detector(buffer);\n\n  // find an appropriate handler for this file type\n  if (type in typeHandlers) {\n    var size = typeHandlers[type].calculate(buffer);\n    if (size !== false) {\n      size.type = type;\n      return size;\n    }\n  }\n\n  // throw up, if we don't understand the file\n  throw new TypeError('unsupported file type: ' + type);\n}\n\n/**\n * @param {Buffer|string} input - buffer or relative/absolute path of the image file\n */\nmodule.exports = function (input) {\n\n  // Handle buffer input\n  if (Buffer.isBuffer(input)) {\n    return lookup(input);\n  }\n\n  // input should be a buffer at this point\n  throw new TypeError('expecting only a buffer as input');\n};\n\nmodule.exports.types = Object.keys(typeHandlers);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types.js":
/*!*****************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// load all available handlers for browserify support\nvar typeHandlers = {\n  bmp: __webpack_require__(/*! ./types/bmp */ \"(ssr)/./node_modules/buffer-image-size/lib/types/bmp.js\"),\n  cur: __webpack_require__(/*! ./types/cur */ \"(ssr)/./node_modules/buffer-image-size/lib/types/cur.js\"),\n  dds: __webpack_require__(/*! ./types/dds */ \"(ssr)/./node_modules/buffer-image-size/lib/types/dds.js\"),\n  gif: __webpack_require__(/*! ./types/gif */ \"(ssr)/./node_modules/buffer-image-size/lib/types/gif.js\"),\n  ico: __webpack_require__(/*! ./types/ico */ \"(ssr)/./node_modules/buffer-image-size/lib/types/ico.js\"),\n  jpg: __webpack_require__(/*! ./types/jpg */ \"(ssr)/./node_modules/buffer-image-size/lib/types/jpg.js\"),\n  png: __webpack_require__(/*! ./types/png */ \"(ssr)/./node_modules/buffer-image-size/lib/types/png.js\"),\n  psd: __webpack_require__(/*! ./types/psd */ \"(ssr)/./node_modules/buffer-image-size/lib/types/psd.js\"),\n  svg: __webpack_require__(/*! ./types/svg */ \"(ssr)/./node_modules/buffer-image-size/lib/types/svg.js\"),\n  webp: __webpack_require__(/*! ./types/webp */ \"(ssr)/./node_modules/buffer-image-size/lib/types/webp.js\"),\n};\n\nmodule.exports = typeHandlers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQSxPQUFPLG1CQUFPLENBQUMsNEVBQWE7QUFDNUIsT0FBTyxtQkFBTyxDQUFDLDRFQUFhO0FBQzVCLE9BQU8sbUJBQU8sQ0FBQyw0RUFBYTtBQUM1QixPQUFPLG1CQUFPLENBQUMsNEVBQWE7QUFDNUIsT0FBTyxtQkFBTyxDQUFDLDRFQUFhO0FBQzVCLE9BQU8sbUJBQU8sQ0FBQyw0RUFBYTtBQUM1QixPQUFPLG1CQUFPLENBQUMsNEVBQWE7QUFDNUIsT0FBTyxtQkFBTyxDQUFDLDRFQUFhO0FBQzVCLE9BQU8sbUJBQU8sQ0FBQyw0RUFBYTtBQUM1QixRQUFRLG1CQUFPLENBQUMsOEVBQWM7QUFDOUI7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcYnVmZmVyLWltYWdlLXNpemVcXGxpYlxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vLyBsb2FkIGFsbCBhdmFpbGFibGUgaGFuZGxlcnMgZm9yIGJyb3dzZXJpZnkgc3VwcG9ydFxudmFyIHR5cGVIYW5kbGVycyA9IHtcbiAgYm1wOiByZXF1aXJlKCcuL3R5cGVzL2JtcCcpLFxuICBjdXI6IHJlcXVpcmUoJy4vdHlwZXMvY3VyJyksXG4gIGRkczogcmVxdWlyZSgnLi90eXBlcy9kZHMnKSxcbiAgZ2lmOiByZXF1aXJlKCcuL3R5cGVzL2dpZicpLFxuICBpY286IHJlcXVpcmUoJy4vdHlwZXMvaWNvJyksXG4gIGpwZzogcmVxdWlyZSgnLi90eXBlcy9qcGcnKSxcbiAgcG5nOiByZXF1aXJlKCcuL3R5cGVzL3BuZycpLFxuICBwc2Q6IHJlcXVpcmUoJy4vdHlwZXMvcHNkJyksXG4gIHN2ZzogcmVxdWlyZSgnLi90eXBlcy9zdmcnKSxcbiAgd2VicDogcmVxdWlyZSgnLi90eXBlcy93ZWJwJyksXG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IHR5cGVIYW5kbGVycztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/bmp.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/bmp.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nfunction isBMP (buffer) {\n  return ('BM' === buffer.toString('ascii', 0, 2));\n}\n\nfunction calculate (buffer) {\n  return {\n    'width': buffer.readUInt32LE(18),\n    'height': Math.abs(buffer.readInt32LE(22))\n  };\n}\n\nmodule.exports = {\n  'detect': isBMP,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL2JtcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGJ1ZmZlci1pbWFnZS1zaXplXFxsaWJcXHR5cGVzXFxibXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5mdW5jdGlvbiBpc0JNUCAoYnVmZmVyKSB7XG4gIHJldHVybiAoJ0JNJyA9PT0gYnVmZmVyLnRvU3RyaW5nKCdhc2NpaScsIDAsIDIpKTtcbn1cblxuZnVuY3Rpb24gY2FsY3VsYXRlIChidWZmZXIpIHtcbiAgcmV0dXJuIHtcbiAgICAnd2lkdGgnOiBidWZmZXIucmVhZFVJbnQzMkxFKDE4KSxcbiAgICAnaGVpZ2h0JzogTWF0aC5hYnMoYnVmZmVyLnJlYWRJbnQzMkxFKDIyKSlcbiAgfTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICdkZXRlY3QnOiBpc0JNUCxcbiAgJ2NhbGN1bGF0ZSc6IGNhbGN1bGF0ZVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/bmp.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/cur.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/cur.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar TYPE_CURSOR = 2;\n\nfunction isCUR (buffer) {\n  var type;\n  if (buffer.readUInt16LE(0) !== 0) {\n    return false;\n  }\n  type = buffer.readUInt16LE(2);\n  return type === TYPE_CURSOR;\n}\n\nmodule.exports = {\n  'detect': isCUR,\n  'calculate': (__webpack_require__(/*! ./ico */ \"(ssr)/./node_modules/buffer-image-size/lib/types/ico.js\").calculate)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL2N1ci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxlQUFlLHVHQUEwQjtBQUN6QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxidWZmZXItaW1hZ2Utc2l6ZVxcbGliXFx0eXBlc1xcY3VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIFRZUEVfQ1VSU09SID0gMjtcblxuZnVuY3Rpb24gaXNDVVIgKGJ1ZmZlcikge1xuICB2YXIgdHlwZTtcbiAgaWYgKGJ1ZmZlci5yZWFkVUludDE2TEUoMCkgIT09IDApIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgdHlwZSA9IGJ1ZmZlci5yZWFkVUludDE2TEUoMik7XG4gIHJldHVybiB0eXBlID09PSBUWVBFX0NVUlNPUjtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICdkZXRlY3QnOiBpc0NVUixcbiAgJ2NhbGN1bGF0ZSc6IHJlcXVpcmUoJy4vaWNvJykuY2FsY3VsYXRlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/cur.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/dds.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/dds.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nfunction isDDS(buffer){\n  return buffer.readUInt32LE(0) === 0x20534444;\n}\n\nfunction calculate(buffer){\n  // read file resolution metadata\n  return {\n    'height': buffer.readUInt32LE(12),\n    'width': buffer.readUInt32LE(16)\n  };\n}\n\nmodule.exports = {\n  'detect': isDDS,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL2Rkcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcYnVmZmVyLWltYWdlLXNpemVcXGxpYlxcdHlwZXNcXGRkcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmZ1bmN0aW9uIGlzRERTKGJ1ZmZlcil7XG4gIHJldHVybiBidWZmZXIucmVhZFVJbnQzMkxFKDApID09PSAweDIwNTM0NDQ0O1xufVxuXG5mdW5jdGlvbiBjYWxjdWxhdGUoYnVmZmVyKXtcbiAgLy8gcmVhZCBmaWxlIHJlc29sdXRpb24gbWV0YWRhdGFcbiAgcmV0dXJuIHtcbiAgICAnaGVpZ2h0JzogYnVmZmVyLnJlYWRVSW50MzJMRSgxMiksXG4gICAgJ3dpZHRoJzogYnVmZmVyLnJlYWRVSW50MzJMRSgxNilcbiAgfTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICdkZXRlY3QnOiBpc0REUyxcbiAgJ2NhbGN1bGF0ZSc6IGNhbGN1bGF0ZVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/dds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/gif.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/gif.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nvar gifRegexp = /^GIF8[79]a/;\nfunction isGIF (buffer) {\n  var signature = buffer.toString('ascii', 0, 6);\n  return (gifRegexp.test(signature));\n}\n\nfunction calculate(buffer) {\n  return {\n    'width': buffer.readUInt16LE(6),\n    'height': buffer.readUInt16LE(8)\n  };\n}\n\nmodule.exports = {\n  'detect': isGIF,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL2dpZi5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxidWZmZXItaW1hZ2Utc2l6ZVxcbGliXFx0eXBlc1xcZ2lmLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIGdpZlJlZ2V4cCA9IC9eR0lGOFs3OV1hLztcbmZ1bmN0aW9uIGlzR0lGIChidWZmZXIpIHtcbiAgdmFyIHNpZ25hdHVyZSA9IGJ1ZmZlci50b1N0cmluZygnYXNjaWknLCAwLCA2KTtcbiAgcmV0dXJuIChnaWZSZWdleHAudGVzdChzaWduYXR1cmUpKTtcbn1cblxuZnVuY3Rpb24gY2FsY3VsYXRlKGJ1ZmZlcikge1xuICByZXR1cm4ge1xuICAgICd3aWR0aCc6IGJ1ZmZlci5yZWFkVUludDE2TEUoNiksXG4gICAgJ2hlaWdodCc6IGJ1ZmZlci5yZWFkVUludDE2TEUoOClcbiAgfTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICdkZXRlY3QnOiBpc0dJRixcbiAgJ2NhbGN1bGF0ZSc6IGNhbGN1bGF0ZVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/gif.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/ico.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/ico.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nvar TYPE_ICON = 1;\n\n/**\n * ICON Header\n *\n * | Offset | Size | Purpose                                                                                   |\n * | 0\t    | 2    | Reserved. Must always be 0.                                                               |\n * | 2      | 2    | Image type: 1 for icon (.ICO) image, 2 for cursor (.CUR) image. Other values are invalid. |\n * | 4      | 2    | Number of images in the file.                                                             |\n *\n **/\nvar SIZE_HEADER = 2 + 2 + 2; // 6\n\n/**\n * Image Entry\n *\n * | Offset | Size | Purpose                                                                                          |\n * | 0\t    | 1    | Image width in pixels. Can be any number between 0 and 255. Value 0 means width is 256 pixels.   |\n * | 1      | 1    | Image height in pixels. Can be any number between 0 and 255. Value 0 means height is 256 pixels. |\n * | 2      | 1    | Number of colors in the color palette. Should be 0 if the image does not use a color palette.    |\n * | 3      | 1    | Reserved. Should be 0.                                                                           |\n * | 4      | 2    | ICO format: Color planes. Should be 0 or 1.                                                      |\n * |        |      | CUR format: The horizontal coordinates of the hotspot in number of pixels from the left.         |\n * | 6      | 2    | ICO format: Bits per pixel.                                                                      |\n * |        |      | CUR format: The vertical coordinates of the hotspot in number of pixels from the top.            |\n * | 8      | 4    | The size of the image's data in bytes                                                            |\n * | 12     | 4    | The offset of BMP or PNG data from the beginning of the ICO/CUR file                             |\n *\n **/\nvar SIZE_IMAGE_ENTRY = 1 + 1 + 1 + 1 + 2 + 2 + 4 + 4; // 16\n\nfunction isICO (buffer) {\n  var type;\n  if (buffer.readUInt16LE(0) !== 0) {\n    return false;\n  }\n  type = buffer.readUInt16LE(2);\n  return type === TYPE_ICON;\n}\n\nfunction getSizeFromOffset(buffer, offset) {\n  var value = buffer.readUInt8(offset);\n  return value === 0 ? 256 : value;\n}\n\nfunction getImageSize(buffer, imageIndex) {\n  var offset = SIZE_HEADER + (imageIndex * SIZE_IMAGE_ENTRY);\n  return {\n    'width': getSizeFromOffset(buffer, offset),\n    'height': getSizeFromOffset(buffer, offset + 1)\n  };\n}\n\nfunction calculate (buffer) {\n  var \n    nbImages = buffer.readUInt16LE(4),\n    result = getImageSize(buffer, 0),\n    imageIndex;\n    \n  if (nbImages === 1) {\n    return result;\n  }\n  \n  result.images = [{\n    width: result.width,\n    height: result.height\n  }];\n  \n  for (imageIndex = 1; imageIndex < nbImages; imageIndex += 1) {\n    result.images.push(getImageSize(buffer, imageIndex));\n  }\n  \n  return result;\n}\n\nmodule.exports = {\n  'detect': isICO,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/ico.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/jpg.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/jpg.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\n// NOTE: we only support baseline and progressive JPGs here\n// due to the structure of the loader class, we only get a buffer\n// with a maximum size of 4096 bytes. so if the SOF marker is outside\n// if this range we can't detect the file size correctly.\n\nfunction isJPG (buffer) { //, filepath\n  var SOIMarker = buffer.toString('hex', 0, 2);\n  return ('ffd8' === SOIMarker);\n}\n\nfunction extractSize (buffer, i) {\n  return {\n    'height' : buffer.readUInt16BE(i),\n    'width' : buffer.readUInt16BE(i + 2)\n  };\n}\n\nfunction validateBuffer (buffer, i) {\n  // index should be within buffer limits\n  if (i > buffer.length) {\n    throw new TypeError('Corrupt JPG, exceeded buffer limits');\n  }\n  // Every JPEG block must begin with a 0xFF\n  if (buffer[i] !== 0xFF) {\n    throw new TypeError('Invalid JPG, marker table corrupted');\n  }\n}\n\nfunction calculate (buffer) {\n\n  // Skip 4 chars, they are for signature\n  buffer = buffer.slice(4);\n\n  var i, next;\n  while (buffer.length) {\n    // read length of the next block\n    i = buffer.readUInt16BE(0);\n\n    // ensure correct format\n    validateBuffer(buffer, i);\n\n    // 0xFFC0 is baseline standard(SOF)\n    // 0xFFC1 is baseline optimized(SOF)\n    // 0xFFC2 is progressive(SOF2)\n    next = buffer[i + 1];\n    if (next === 0xC0 || next === 0xC1 || next === 0xC2) {\n      return extractSize(buffer, i + 5);\n    }\n\n    // move to the next block\n    buffer = buffer.slice(i + 2);\n  }\n\n  throw new TypeError('Invalid JPG, no size found');\n}\n\nmodule.exports = {\n  'detect': isJPG,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/jpg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/png.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/png.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nvar pngSignature = 'PNG\\r\\n\\x1a\\n';\nvar pngImageHeaderChunkName = 'IHDR';\n\n// Used to detect \"fried\" png's: http://www.jongware.com/pngdefry.html\nvar pngFriedChunkName = 'CgBI'; \n\nfunction isPNG (buffer) {\n  if (pngSignature === buffer.toString('ascii', 1, 8)) {\n    var chunkName = buffer.toString('ascii', 12, 16);\n    if (chunkName === pngFriedChunkName) {\n      chunkName = buffer.toString('ascii', 28, 32);\n    }\n    if (chunkName !== pngImageHeaderChunkName) {\n      throw new TypeError('invalid png');\n    }\n    return true;\n  }\n}\n\nfunction calculate (buffer) {\n  if (buffer.toString('ascii', 12, 16) === pngFriedChunkName) {\n    return {\n      'width': buffer.readUInt32BE(32),\n      'height': buffer.readUInt32BE(36)\n    };\n  }\n  return {\n    'width': buffer.readUInt32BE(16),\n    'height': buffer.readUInt32BE(20)\n  };\n}\n\nmodule.exports = {\n  'detect': isPNG,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL3BuZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxidWZmZXItaW1hZ2Utc2l6ZVxcbGliXFx0eXBlc1xccG5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIHBuZ1NpZ25hdHVyZSA9ICdQTkdcXHJcXG5cXHgxYVxcbic7XG52YXIgcG5nSW1hZ2VIZWFkZXJDaHVua05hbWUgPSAnSUhEUic7XG5cbi8vIFVzZWQgdG8gZGV0ZWN0IFwiZnJpZWRcIiBwbmcnczogaHR0cDovL3d3dy5qb25nd2FyZS5jb20vcG5nZGVmcnkuaHRtbFxudmFyIHBuZ0ZyaWVkQ2h1bmtOYW1lID0gJ0NnQkknOyBcblxuZnVuY3Rpb24gaXNQTkcgKGJ1ZmZlcikge1xuICBpZiAocG5nU2lnbmF0dXJlID09PSBidWZmZXIudG9TdHJpbmcoJ2FzY2lpJywgMSwgOCkpIHtcbiAgICB2YXIgY2h1bmtOYW1lID0gYnVmZmVyLnRvU3RyaW5nKCdhc2NpaScsIDEyLCAxNik7XG4gICAgaWYgKGNodW5rTmFtZSA9PT0gcG5nRnJpZWRDaHVua05hbWUpIHtcbiAgICAgIGNodW5rTmFtZSA9IGJ1ZmZlci50b1N0cmluZygnYXNjaWknLCAyOCwgMzIpO1xuICAgIH1cbiAgICBpZiAoY2h1bmtOYW1lICE9PSBwbmdJbWFnZUhlYWRlckNodW5rTmFtZSkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignaW52YWxpZCBwbmcnKTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbn1cblxuZnVuY3Rpb24gY2FsY3VsYXRlIChidWZmZXIpIHtcbiAgaWYgKGJ1ZmZlci50b1N0cmluZygnYXNjaWknLCAxMiwgMTYpID09PSBwbmdGcmllZENodW5rTmFtZSkge1xuICAgIHJldHVybiB7XG4gICAgICAnd2lkdGgnOiBidWZmZXIucmVhZFVJbnQzMkJFKDMyKSxcbiAgICAgICdoZWlnaHQnOiBidWZmZXIucmVhZFVJbnQzMkJFKDM2KVxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHtcbiAgICAnd2lkdGgnOiBidWZmZXIucmVhZFVJbnQzMkJFKDE2KSxcbiAgICAnaGVpZ2h0JzogYnVmZmVyLnJlYWRVSW50MzJCRSgyMClcbiAgfTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICdkZXRlY3QnOiBpc1BORyxcbiAgJ2NhbGN1bGF0ZSc6IGNhbGN1bGF0ZVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/psd.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/psd.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nfunction isPSD (buffer) {\n  return ('8BPS' === buffer.toString('ascii', 0, 4));\n}\n\nfunction calculate (buffer) {\n  return {\n    'width': buffer.readUInt32BE(18),\n    'height': buffer.readUInt32BE(14)\n  };\n}\n\nmodule.exports = {\n  'detect': isPSD,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWltYWdlLXNpemUvbGliL3R5cGVzL3BzZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGJ1ZmZlci1pbWFnZS1zaXplXFxsaWJcXHR5cGVzXFxwc2QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5mdW5jdGlvbiBpc1BTRCAoYnVmZmVyKSB7XG4gIHJldHVybiAoJzhCUFMnID09PSBidWZmZXIudG9TdHJpbmcoJ2FzY2lpJywgMCwgNCkpO1xufVxuXG5mdW5jdGlvbiBjYWxjdWxhdGUgKGJ1ZmZlcikge1xuICByZXR1cm4ge1xuICAgICd3aWR0aCc6IGJ1ZmZlci5yZWFkVUludDMyQkUoMTgpLFxuICAgICdoZWlnaHQnOiBidWZmZXIucmVhZFVJbnQzMkJFKDE0KVxuICB9O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgJ2RldGVjdCc6IGlzUFNELFxuICAnY2FsY3VsYXRlJzogY2FsY3VsYXRlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/psd.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/svg.js":
/*!*********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/svg.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nvar svgReg = /<svg[^>]+[^>]*>/;\nfunction isSVG (buffer) {\n  return svgReg.test(buffer);\n}\n\nvar extractorRegExps = {\n  'root': /<svg\\s[^>]+>/,\n  'width': /\\bwidth=(['\"])([^%]+?)\\1/,\n  'height': /\\bheight=(['\"])([^%]+?)\\1/,\n  'viewbox': /\\bviewBox=(['\"])(.+?)\\1/\n};\n\nfunction parseViewbox (viewbox) {\n  var bounds = viewbox.split(' ');\n  return {\n    'width': parseInt(bounds[2], 10),\n    'height': parseInt(bounds[3], 10)\n  };\n}\n\nfunction parseAttributes (root) {\n  var width = root.match(extractorRegExps.width);\n  var height = root.match(extractorRegExps.height);\n  var viewbox = root.match(extractorRegExps.viewbox);\n  return {\n    'width': width && parseInt(width[2], 10),\n    'height': height && parseInt(height[2], 10),\n    'viewbox': viewbox && parseViewbox(viewbox[2])\n  };\n}\n\nfunction calculateByDimensions (attrs) {\n  return {\n    'width': attrs.width,\n    'height': attrs.height\n  };\n}\n\nfunction calculateByViewbox (attrs) {\n  var ratio = attrs.viewbox.width / attrs.viewbox.height;\n  if (attrs.width) {\n    return {\n      'width': attrs.width,\n      'height': Math.floor(attrs.width / ratio)\n    };\n  }\n  if (attrs.height) {\n    return {\n      'width': Math.floor(attrs.height * ratio),\n      'height': attrs.height\n    };\n  }\n  return {\n    'width': attrs.viewbox.width,\n    'height': attrs.viewbox.height\n  };\n}\n\nfunction calculate (buffer) {\n  var root = buffer.toString('utf8').match(extractorRegExps.root);\n  if (root) {\n    var attrs = parseAttributes(root[0]);\n    if (attrs.width && attrs.height) {\n      return calculateByDimensions(attrs);\n    }\n    if (attrs.viewbox) {\n      return calculateByViewbox(attrs);\n    }\n  }\n  throw new TypeError('invalid svg');\n}\n\nmodule.exports = {\n  'detect': isSVG,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-image-size/lib/types/webp.js":
/*!**********************************************************!*\
  !*** ./node_modules/buffer-image-size/lib/types/webp.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\n// based on https://developers.google.com/speed/webp/docs/riff_container\n\nfunction isWebP (buffer) {\n  var riffHeader = 'RIFF' === buffer.toString('ascii', 0, 4);\n  var webpHeader = 'WEBP' === buffer.toString('ascii', 8, 12);\n  var vp8Header  = 'VP8'  === buffer.toString('ascii', 12, 15);\n  return (riffHeader && webpHeader && vp8Header);\n}\n\nfunction calculate (buffer) {\n  var chunkHeader = buffer.toString('ascii', 12, 16);\n  buffer = buffer.slice(20, 30);\n\n  // Extended webp stream signature\n  if (chunkHeader === 'VP8X') {\n    var extendedHeader = buffer[0];\n    var validStart = (extendedHeader & 0xc0) === 0;\n    var validEnd = (extendedHeader & 0x01) === 0;\n    if (validStart && validEnd) {\n      return calculateExtended(buffer);\n    } else {\n      return false;\n    }\n  }\n\n  // Lossless webp stream signature\n  if (chunkHeader === 'VP8 ' && buffer[0] !== 0x2f) {\n    return calculateLossy(buffer);\n  }\n\n  // Lossy webp stream signature\n  var signature = buffer.toString('hex', 3, 6);\n  if (chunkHeader === 'VP8L' && signature !== '9d012a') {\n    return calculateLossless(buffer);\n  }\n\n  return false;\n}\n\nfunction calculateExtended (buffer) {\n  return {\n    'width': 1 + buffer.readUIntLE(4, 3),\n    'height': 1 + buffer.readUIntLE(7, 3)\n  };\n}\n\nfunction calculateLossless (buffer) {\n  return {\n    'width': 1 + (((buffer[2] & 0x3F) << 8) | buffer[1]),\n    'height': 1 + (((buffer[4] & 0xF) << 10) | (buffer[3] << 2) |\n                  ((buffer[2] & 0xC0) >> 6))\n  };\n}\n\nfunction calculateLossy (buffer) {\n  // `& 0x3fff` returns the last 14 bits\n  // TO-DO: include webp scaling in the calculations\n  return {\n    'width': buffer.readInt16LE(6) & 0x3fff,\n    'height': buffer.readInt16LE(8) & 0x3fff\n  };\n}\n\nmodule.exports = {\n  'detect': isWebP,\n  'calculate': calculate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-image-size/lib/types/webp.js\n");

/***/ })

};
;