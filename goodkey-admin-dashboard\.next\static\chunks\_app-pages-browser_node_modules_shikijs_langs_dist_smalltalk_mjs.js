"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_smalltalk_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/smalltalk.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/smalltalk.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Smalltalk\\\",\\\"fileTypes\\\":[\\\"st\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\[\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*\\\\\\\\]|^\\\\\\\\s\\\\\\\\]\\\",\\\"name\\\":\\\"smalltalk\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$.\\\",\\\"name\\\":\\\"constant.character.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b(class)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.$1.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b(extend|super|self)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.$1.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b(yourself|new|Smalltalk)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.$1.smalltalk\\\"},{\\\"match\\\":\\\":=\\\",\\\"name\\\":\\\"keyword.operator.assignment.smalltalk\\\"},{\\\"comment\\\":\\\"Parse the variable declaration like: |a b c|\\\",\\\"match\\\":\\\"/^:\\\\\\\\w*\\\\\\\\s*\\\\\\\\|/\\\",\\\"name\\\":\\\"constant.other.block.smalltalk\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.instance-variables.begin.smalltalk\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"support.type.variable.declaration.smalltalk\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.instance-variables.end.smalltalk\\\"}},\\\"match\\\":\\\"(\\\\\\\\|)(\\\\\\\\s*\\\\\\\\w[\\\\\\\\w ]*)(\\\\\\\\|)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\":\\\\\\\\w+\\\",\\\"name\\\":\\\"entity.name.function.block.smalltalk\\\"}]}},\\\"comment\\\":\\\"Parse the blocks like: [ :a :b | ...... ]\\\",\\\"match\\\":\\\"\\\\\\\\[((\\\\\\\\s+|:\\\\\\\\w+)*)\\\\\\\\|\\\"},{\\\"include\\\":\\\"#numeric\\\"},{\\\"match\\\":\\\"<(?!<|=)|>(?!<|=|>)|<=|>=|=|==|~=|~~|>>|\\\\\\\\^\\\",\\\"name\\\":\\\"keyword.operator.comparison.smalltalk\\\"},{\\\"match\\\":\\\"(\\\\\\\\*|\\\\\\\\+|\\\\\\\\-|/|\\\\\\\\\\\\\\\\)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.smalltalk\\\"},{\\\"match\\\":\\\"(?<=[ \\\\\\\\t])!+|\\\\\\\\bnot\\\\\\\\b|&|\\\\\\\\band\\\\\\\\b|\\\\\\\\||\\\\\\\\bor\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logical.smalltalk\\\"},{\\\"comment\\\":\\\"Fake reserved word -> main Smalltalk messages\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(ensure|resume|retry|signal)\\\\\\\\b(?![?!])\\\",\\\"name\\\":\\\"keyword.control.smalltalk\\\"},{\\\"comment\\\":\\\"Fake conditionals. Smalltalk Methods.\\\",\\\"match\\\":\\\"ifCurtailed:|ifTrue:|ifFalse:|whileFalse:|whileTrue:\\\",\\\"name\\\":\\\"keyword.control.conditionals.smalltalk\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.inherited-class.smalltalk\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.smalltalk\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.type.class.smalltalk\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+)(\\\\\\\\s+(subclass:))\\\\\\\\s*(\\\\\\\\w*)\\\",\\\"name\\\":\\\"meta.class.smalltalk\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.comment.begin.smalltalk\\\"}],\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.comment.end.smalltalk\\\"}],\\\"name\\\":\\\"comment.block.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.boolean.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b(nil)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.nil.smalltalk\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.smalltalk\\\"}},\\\"comment\\\":\\\"messages/methods\\\",\\\"match\\\":\\\"(?>[a-zA-Z_]\\\\\\\\w*(?>[?!])?)(:)(?!:)\\\",\\\"name\\\":\\\"constant.other.messages.smalltalk\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.smalltalk\\\"}},\\\"comment\\\":\\\"symbols\\\",\\\"match\\\":\\\"(#)[a-zA-Z_][a-zA-Z0-9_:]*\\\",\\\"name\\\":\\\"constant.other.symbol.smalltalk\\\"},{\\\"begin\\\":\\\"#\\\\\\\\[\\\",\\\"beginCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.constant.begin.smalltalk\\\"}],\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.constant.end.smalltalk\\\"}],\\\"name\\\":\\\"meta.array.byte.smalltalk\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[0-9]+(r[a-zA-Z0-9]+)?\\\",\\\"name\\\":\\\"constant.numeric.integer.smalltalk\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]+\\\",\\\"name\\\":\\\"invalid.illegal.character-not-allowed-here.smalltalk\\\"}]},{\\\"begin\\\":\\\"#\\\\\\\\(\\\",\\\"beginCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.constant.begin.smalltalk\\\"}],\\\"comment\\\":\\\"Array Constructor\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.constant.end.smalltalk\\\"}],\\\"name\\\":\\\"constant.other.array.smalltalk\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.string.begin.smalltalk\\\"}],\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":[{\\\"name\\\":\\\"punctuation.definition.string.end.smalltalk\\\"}],\\\"name\\\":\\\"string.quoted.single.smalltalk\\\"},{\\\"match\\\":\\\"\\\\\\\\b[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.constant.smalltalk\\\"}],\\\"repository\\\":{\\\"numeric\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\w)[0-9]+\\\\\\\\.[0-9]+s[0-9]*\\\",\\\"name\\\":\\\"constant.numeric.float.scaled.smalltalk\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)[0-9]+\\\\\\\\.[0-9]+([edq]-?[0-9]+)?\\\",\\\"name\\\":\\\"constant.numeric.float.smalltalk\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)-?[0-9]+r[a-zA-Z0-9]+\\\",\\\"name\\\":\\\"constant.numeric.integer.radix.smalltalk\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\w)-?[0-9]+([edq]-?[0-9]+)?\\\",\\\"name\\\":\\\"constant.numeric.integer.smalltalk\\\"}]}},\\\"scopeName\\\":\\\"source.smalltalk\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/smalltalk.mjs\n"));

/***/ })

}]);