"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_services_zustand_ConnectionStore_ts";
exports.ids = ["_rsc_src_services_zustand_ConnectionStore_ts"];
exports.modules = {

/***/ "(rsc)/./src/services/zustand/ConnectionStore.ts":
/*!*************************************************!*\
  !*** ./src/services/zustand/ConnectionStore.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(rsc)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(rsc)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useConnectionStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set)=>({\n        isConnected: false,\n        connectionStatus: 'Disconnected',\n        setConnected: (isConnected, connectionStatus)=>set({\n                isConnected,\n                connectionStatus: connectionStatus ?? (isConnected ? 'Connected' : 'Disconnected')\n            })\n    }), {\n    name: 'ConnectionStore'\n}));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useConnectionStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvenVzdGFuZC9Db25uZWN0aW9uU3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBQ1k7QUFTN0MsTUFBTUUscUJBQXFCRiwrQ0FBTUEsR0FDL0JDLDJEQUFPQSxDQUNMLENBQUNFLE1BQVM7UUFDUkMsYUFBYTtRQUNiQyxrQkFBa0I7UUFDbEJDLGNBQWMsQ0FBQ0YsYUFBYUMsbUJBQzFCRixJQUFJO2dCQUNGQztnQkFDQUMsa0JBQ0VBLG9CQUFxQkQsQ0FBQUEsY0FBYyxjQUFjLGNBQWE7WUFDbEU7SUFDSixJQUNBO0lBQ0VHLE1BQU07QUFDUjtBQUlKLGlFQUFlTCxrQkFBa0JBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXHNyY1xcc2VydmljZXNcXHp1c3RhbmRcXENvbm5lY3Rpb25TdG9yZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcclxuaW1wb3J0IHsgcGVyc2lzdCB9IGZyb20gJ3p1c3RhbmQvbWlkZGxld2FyZSc7XHJcbmludGVyZmFjZSBDb25uZWN0aW9uU3RvcmUge1xyXG4gIGNvbm5lY3Rpb25TdGF0dXM6ICdDb25uZWN0ZWQnIHwgJ0Rpc2Nvbm5lY3RlZCcgfCAnZXhwaXJlZCc7XHJcbiAgaXNDb25uZWN0ZWQ6IGJvb2xlYW47XHJcbiAgc2V0Q29ubmVjdGVkOiAoXHJcbiAgICBpc0Nvbm5lY3RlZDogYm9vbGVhbixcclxuICAgIGNvbm5lY3Rpb25TdGF0dXM/OiAnQ29ubmVjdGVkJyB8ICdEaXNjb25uZWN0ZWQnIHwgJ2V4cGlyZWQnLFxyXG4gICkgPT4gdm9pZDtcclxufVxyXG5jb25zdCB1c2VDb25uZWN0aW9uU3RvcmUgPSBjcmVhdGU8Q29ubmVjdGlvblN0b3JlPigpKFxyXG4gIHBlcnNpc3QoXHJcbiAgICAoc2V0KSA9PiAoe1xyXG4gICAgICBpc0Nvbm5lY3RlZDogZmFsc2UsXHJcbiAgICAgIGNvbm5lY3Rpb25TdGF0dXM6ICdEaXNjb25uZWN0ZWQnLFxyXG4gICAgICBzZXRDb25uZWN0ZWQ6IChpc0Nvbm5lY3RlZCwgY29ubmVjdGlvblN0YXR1cykgPT5cclxuICAgICAgICBzZXQoe1xyXG4gICAgICAgICAgaXNDb25uZWN0ZWQsXHJcbiAgICAgICAgICBjb25uZWN0aW9uU3RhdHVzOlxyXG4gICAgICAgICAgICBjb25uZWN0aW9uU3RhdHVzID8/IChpc0Nvbm5lY3RlZCA/ICdDb25uZWN0ZWQnIDogJ0Rpc2Nvbm5lY3RlZCcpLFxyXG4gICAgICAgIH0pLFxyXG4gICAgfSksXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICdDb25uZWN0aW9uU3RvcmUnLFxyXG4gICAgfSxcclxuICApLFxyXG4pO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgdXNlQ29ubmVjdGlvblN0b3JlO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlIiwicGVyc2lzdCIsInVzZUNvbm5lY3Rpb25TdG9yZSIsInNldCIsImlzQ29ubmVjdGVkIiwiY29ubmVjdGlvblN0YXR1cyIsInNldENvbm5lY3RlZCIsIm5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/services/zustand/ConnectionStore.ts\n");

/***/ })

};
;