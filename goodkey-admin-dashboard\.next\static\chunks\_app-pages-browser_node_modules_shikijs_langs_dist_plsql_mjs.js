"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_plsql_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/plsql.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/plsql.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"PL/SQL\\\",\\\"fileTypes\\\":[\\\"sql\\\",\\\"ddl\\\",\\\"dml\\\",\\\"pkh\\\",\\\"pks\\\",\\\"pkb\\\",\\\"pck\\\",\\\"pls\\\",\\\"plb\\\"],\\\"foldingStartMarker\\\":\\\"(?i)^\\\\\\\\s*(begin|if|loop)\\\\\\\\b\\\",\\\"foldingStopMarker\\\":\\\"(?i)^\\\\\\\\s*(end)\\\\\\\\b\\\",\\\"name\\\":\\\"plsql\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.oracle\\\"},{\\\"match\\\":\\\"--.*$\\\",\\\"name\\\":\\\"comment.line.double-dash.oracle\\\"},{\\\"match\\\":\\\"(?i)(?:^\\\\\\\\s*)rem(?:\\\\\\\\s+.*$)\\\",\\\"name\\\":\\\"comment.line.sqlplus.oracle\\\"},{\\\"match\\\":\\\"(?i)(?:^\\\\\\\\s*)prompt(?:\\\\\\\\s+.*$)\\\",\\\"name\\\":\\\"comment.line.sqlplus-prompt.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"}},\\\"match\\\":\\\"(?i)^\\\\\\\\s*(create)(\\\\\\\\s+or\\\\\\\\s+replace)?\\\\\\\\s+\\\",\\\"name\\\":\\\"meta.create.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.type.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(package)(\\\\\\\\s+body)?\\\\\\\\s+(\\\\\\\\S+)\\\",\\\"name\\\":\\\"meta.package.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(type)\\\\\\\\s+\\\\\\\"([^\\\\\\\"]+)\\\\\\\"\\\",\\\"name\\\":\\\"meta.type.oracle\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.oracle\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.oracle\\\"}},\\\"match\\\":\\\"(?i)^\\\\\\\\s*(function|procedure)\\\\\\\\s+\\\\\\\"?([-a-z0-9_]+)\\\\\\\"?\\\",\\\"name\\\":\\\"meta.procedure.oracle\\\"},{\\\"match\\\":\\\"[!<>:]?=|<>|<|>|\\\\\\\\+|(?<!\\\\\\\\.)\\\\\\\\*|-|(?<!^)/|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(true|false|null|is\\\\\\\\s+(not\\\\\\\\s+)?null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.oracle\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+(\\\\\\\\.\\\\\\\\d+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(if|elsif|else|end\\\\\\\\s+if|loop|end\\\\\\\\s+loop|for|while|case|end\\\\\\\\s+case|continue|return|goto)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(or|and|not|like)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(%(isopen|found|notfound|rowcount)|commit|rollback|sqlerrm)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(sql|sqlcode)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|asciistr|chr|compose|concat|convert|decompose|dump|initcap|instr|instrb|instrc|instr2|instr4|unistr|length|lengthb|lengthc|length2|length4|lower|lpad|ltrim|nchr|replace|rpad|rtrim|soundex|substr|translate|trim|upper|vsize)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.char.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(add_months|current_date|current_timestamp|dbtimezone|last_day|localtimestamp|months_between|new_time|next_day|round|sessiontimezone|sysdate|tz_offset|systimestamp)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.date.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(avg|count|sum|max|min|median|corr|corr_\\\\\\\\w+|covar_(pop|samp)|cume_dist|dense_rank|first|group_id|grouping|grouping_id|last|percentile_cont|percentile_disc|percent_rank|rank|regr_\\\\\\\\w+|row_number|stats_binomial_test|stats_crosstab|stats_f_test|stats_ks_test|stats_mode|stats_mw_test|stats_one_way_anova|stats_t_test_\\\\\\\\w+|stats_wsr_test|stddev|stddev_pop|stddev_samp|var_pop|var_samp|variance)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.aggregate.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(bfilename|cardinality|coalesce|decode|empty_(blob|clob)|lag|lead|listagg|lnnvl|nanvl|nullif|nvl|nvl2|sys_(context|guid|typeid|connect_by_path|extract_utc)|uid|(current\\\\\\\\s+)?user|userenv|cardinality|(bulk\\\\\\\\s+)?collect|powermultiset(_by_cardinality)?|ora_hash|standard_hash|execute\\\\\\\\s+immediate|alter\\\\\\\\s+session)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.advanced.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(bin_to_num|cast|chartorowid|from_tz|hextoraw|numtodsinterval|numtoyminterval|rawtohex|rawtonhex|to_char|to_clob|to_date|to_dsinterval|to_lob|to_multi_byte|to_nclob|to_number|to_single_byte|to_timestamp|to_timestamp_tz|to_yminterval|scn_to_timestamp|timestamp_to_scn|rowidtochar|rowidtonchar|to_binary_double|to_binary_float|to_blob|to_nchar|con_dbid_to_id|con_guid_to_id|con_name_to_id|con_uid_to_id)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.convert.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atan2|bit_(and|or|xor)|ceil|cos|cosh|exp|extract|floor|greatest|least|ln|log|mod|power|remainder|round|sign|sin|sinh|sqrt|tan|tanh|trunc)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.math.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(\\\\\\\\.(count|delete|exists|extend|first|last|limit|next|prior|trim|reverse))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.collection.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(cluster_details|cluster_distance|cluster_id|cluster_probability|cluster_set|feature_details|feature_id|feature_set|feature_value|prediction|prediction_bounds|prediction_cost|prediction_details|prediction_probability|prediction_set)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.data_mining.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(appendchildxml|deletexml|depth|extract|existsnode|extractvalue|insertchildxml|insertxmlbefore|xmlcast|xmldiff|xmlelement|xmlexists|xmlisvalid|insertchildxmlafter|insertchildxmlbefore|path|sys_dburigen|sys_xmlagg|sys_xmlgen|updatexml|xmlagg|xmlcdata|xmlcolattval|xmlcomment|xmlconcat|xmlforest|xmlparse|xmlpi|xmlquery|xmlroot|xmlsequence|xmlserialize|xmltable|xmltransform)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.xml.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(pragma\\\\\\\\s+(autonomous_transaction|serially_reusable|restrict_references|exception_init|inline))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.pragma.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(p(i|o|io)_[-a-z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.parameter.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(l_[-a-z0-9_]+)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.oracle\\\"},{\\\"match\\\":\\\"(?i):\\\\\\\\b(new|old)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.trigger.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(connect\\\\\\\\s+by\\\\\\\\s+(nocycle\\\\\\\\s+)?(prior|level)|connect_by_(root|icycle)|level|start\\\\\\\\s+with)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.hierarchical.sql.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(language|name|java|c)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.wrapper.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(end|then|deterministic|exception|when|declare|begin|in|out|nocopy|is|as|exit|open|fetch|into|close|subtype|type|rowtype|default|exclusive|mode|lock|record|index\\\\\\\\s+by|result_cache|constant|comment|\\\\\\\\.(nextval|currval))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(grant|revoke|alter|drop|force|add|check|constraint|primary\\\\\\\\s+key|foreign\\\\\\\\s+key|references|unique(\\\\\\\\s+index)?|column|sequence|increment\\\\\\\\s+by|cache|(materialized\\\\\\\\s+)?view|trigger|storage|tablespace|pct(free|used)|(init|max)trans|logging)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.ddl.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(with|select|from|where|order\\\\\\\\s+(siblings\\\\\\\\s+)?by|group\\\\\\\\s+by|rollup|cube|((left|right|cross|natural)\\\\\\\\s+(outer\\\\\\\\s+)?)?join|on|asc|desc|update|set|insert|into|values|delete|distinct|union|minus|intersect|having|limit|table|between|like|of|row|(range|rows)\\\\\\\\s+between|nulls\\\\\\\\s+first|nulls\\\\\\\\s+last|before|after|all|any|exists|rownum|cursor|returning|over|partition\\\\\\\\s+by|merge|using|matched|pivot|unpivot)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(define|whenever\\\\\\\\s+sqlerror|exec|timing\\\\\\\\s+start|timing\\\\\\\\s+stop)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sqlplus.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(access_into_null|case_not_found|collection_is_null|cursor_already_open|dup_val_on_index|invalid_cursor|invalid_number|login_denied|no_data_found|not_logged_on|program_error|rowtype_mismatch|self_is_null|storage_error|subscript_beyond_count|subscript_outside_limit|sys_invalid_rowid|timeout_on_resource|too_many_rows|value_error|zero_divide|others)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.exception.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((dbms|utl|owa|apex)_\\\\\\\\w+\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((htf|htp)\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"support.class.user-defined.oracle\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b((\\\\\\\\w+_pkg|pkg_\\\\\\\\w+)\\\\\\\\.(\\\\\\\\w+))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.user-defined.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(raise|raise_application_error)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.oracle\\\"},{\\\"begin\\\":\\\"'\\\",\\\"end\\\":\\\"'\\\",\\\"name\\\":\\\"string.quoted.single.oracle\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.oracle\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(char|varchar|varchar2|nchar|nvarchar2|boolean|date|timestamp(\\\\\\\\s+with(\\\\\\\\s+local)?\\\\\\\\s+time\\\\\\\\s+zone)?|interval\\\\\\\\s*day(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?\\\\\\\\s*to\\\\\\\\s*month|interval\\\\\\\\s*year(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?\\\\\\\\s*to\\\\\\\\s*second(\\\\\\\\(\\\\\\\\d*\\\\\\\\))?|xmltype|blob|clob|nclob|bfile|long|long\\\\\\\\s+raw|raw|number|integer|decimal|smallint|float|binary_(float|double|integer)|pls_(float|double|integer)|rowid|urowid|vararray|natural|naturaln|positive|positiven|signtype|simple_(float|double|integer))\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.oracle\\\"}],\\\"scopeName\\\":\\\"source.plsql.oracle\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L3Bsc3FsLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsd0NBQXdDLGdRQUFnUSwwRUFBMEUsRUFBRSxpRUFBaUUsRUFBRSx1RkFBdUYsRUFBRSxpR0FBaUcsRUFBRSxjQUFjLE9BQU8sa0NBQWtDLFFBQVEsbUNBQW1DLGlHQUFpRyxFQUFFLGNBQWMsT0FBTyxrQ0FBa0MsUUFBUSxrQ0FBa0MsUUFBUSxzQ0FBc0MsOEZBQThGLEVBQUUsY0FBYyxPQUFPLGtDQUFrQyxRQUFRLHNDQUFzQyxxRkFBcUYsRUFBRSxjQUFjLE9BQU8sa0NBQWtDLFFBQVEsMENBQTBDLCtHQUErRyxFQUFFLDhHQUE4RyxFQUFFLDZHQUE2RyxFQUFFLGtGQUFrRixFQUFFLGlLQUFpSyxFQUFFLGdGQUFnRixFQUFFLDZIQUE2SCxFQUFFLGdGQUFnRixFQUFFLG9UQUFvVCxFQUFFLG1QQUFtUCxFQUFFLGllQUFpZSxFQUFFLGtaQUFrWixFQUFFLG1lQUFtZSxFQUFFLDRPQUE0TyxFQUFFLGlLQUFpSyxFQUFFLDhUQUE4VCxFQUFFLG1jQUFtYyxFQUFFLHdLQUF3SyxFQUFFLDJGQUEyRixFQUFFLCtFQUErRSxFQUFFLDRFQUE0RSxFQUFFLDZLQUE2SyxFQUFFLHVGQUF1RixFQUFFLDhSQUE4UixFQUFFLDZUQUE2VCxFQUFFLHllQUF5ZSxFQUFFLDhJQUE4SSxFQUFFLG9hQUFvYSxFQUFFLGNBQWMsT0FBTyxtQ0FBbUMsNEdBQTRHLEVBQUUsY0FBYyxPQUFPLG1DQUFtQywyRkFBMkYsRUFBRSxjQUFjLE9BQU8sZ0RBQWdELHNIQUFzSCxFQUFFLGlHQUFpRyxFQUFFLHVFQUF1RSxFQUFFLDZFQUE2RSxFQUFFLHFpQkFBcWlCLHdDQUF3Qzs7QUFFajBSLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXEBzaGlraWpzXFxsYW5nc1xcZGlzdFxccGxzcWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxhbmcgPSBPYmplY3QuZnJlZXplKEpTT04ucGFyc2UoXCJ7XFxcImRpc3BsYXlOYW1lXFxcIjpcXFwiUEwvU1FMXFxcIixcXFwiZmlsZVR5cGVzXFxcIjpbXFxcInNxbFxcXCIsXFxcImRkbFxcXCIsXFxcImRtbFxcXCIsXFxcInBraFxcXCIsXFxcInBrc1xcXCIsXFxcInBrYlxcXCIsXFxcInBja1xcXCIsXFxcInBsc1xcXCIsXFxcInBsYlxcXCJdLFxcXCJmb2xkaW5nU3RhcnRNYXJrZXJcXFwiOlxcXCIoP2kpXlxcXFxcXFxccyooYmVnaW58aWZ8bG9vcClcXFxcXFxcXGJcXFwiLFxcXCJmb2xkaW5nU3RvcE1hcmtlclxcXCI6XFxcIig/aSleXFxcXFxcXFxzKihlbmQpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInBsc3FsXFxcIixcXFwicGF0dGVybnNcXFwiOlt7XFxcImJlZ2luXFxcIjpcXFwiL1xcXFxcXFxcKlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFxcKi9cXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5ibG9jay5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiLS0uKiRcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLmRvdWJsZS1kYXNoLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpKD86XlxcXFxcXFxccyopcmVtKD86XFxcXFxcXFxzKy4qJClcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29tbWVudC5saW5lLnNxbHBsdXMub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSkoPzpeXFxcXFxcXFxzKilwcm9tcHQoPzpcXFxcXFxcXHMrLiokKVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmxpbmUuc3FscGx1cy1wcm9tcHQub3JhY2xlXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIxXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm9yYWNsZVxcXCJ9LFxcXCIyXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLm9yYWNsZVxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXlxcXFxcXFxccyooY3JlYXRlKShcXFxcXFxcXHMrb3JcXFxcXFxcXHMrcmVwbGFjZSk/XFxcXFxcXFxzK1xcXCIsXFxcIm5hbWVcXFwiOlxcXCJtZXRhLmNyZWF0ZS5vcmFjbGVcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjFcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIub3JhY2xlXFxcIn0sXFxcIjJcXFwiOntcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIub3JhY2xlXFxcIn0sXFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcImVudGl0eS5uYW1lLnR5cGUub3JhY2xlXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIocGFja2FnZSkoXFxcXFxcXFxzK2JvZHkpP1xcXFxcXFxccysoXFxcXFxcXFxTKylcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wYWNrYWdlLm9yYWNsZVxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5vcmFjbGVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUudHlwZS5vcmFjbGVcXFwifX0sXFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYih0eXBlKVxcXFxcXFxccytcXFxcXFxcIihbXlxcXFxcXFwiXSspXFxcXFxcXCJcXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS50eXBlLm9yYWNsZVxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiMVxcXCI6e1xcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5vcmFjbGVcXFwifSxcXFwiMlxcXCI6e1xcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24ub3JhY2xlXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIig/aSleXFxcXFxcXFxzKihmdW5jdGlvbnxwcm9jZWR1cmUpXFxcXFxcXFxzK1xcXFxcXFwiPyhbLWEtejAtOV9dKylcXFxcXFxcIj9cXFwiLFxcXCJuYW1lXFxcIjpcXFwibWV0YS5wcm9jZWR1cmUub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlshPD46XT89fDw+fDx8PnxcXFxcXFxcXCt8KD88IVxcXFxcXFxcLilcXFxcXFxcXCp8LXwoPzwhXikvfFxcXFxcXFxcfFxcXFxcXFxcfFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKHRydWV8ZmFsc2V8bnVsbHxpc1xcXFxcXFxccysobm90XFxcXFxcXFxzKyk/bnVsbClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwiY29uc3RhbnQubGFuZ3VhZ2Uub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIlxcXFxcXFxcYlxcXFxcXFxcZCsoXFxcXFxcXFwuXFxcXFxcXFxkKyk/XFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoaWZ8ZWxzaWZ8ZWxzZXxlbmRcXFxcXFxcXHMraWZ8bG9vcHxlbmRcXFxcXFxcXHMrbG9vcHxmb3J8d2hpbGV8Y2FzZXxlbmRcXFxcXFxcXHMrY2FzZXxjb250aW51ZXxyZXR1cm58Z290bylcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5jb250cm9sLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKG9yfGFuZHxub3R8bGlrZSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYiglKGlzb3Blbnxmb3VuZHxub3Rmb3VuZHxyb3djb3VudCl8Y29tbWl0fHJvbGxiYWNrfHNxbGVycm0pXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24ub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoc3FsfHNxbGNvZGUpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLmxhbmd1YWdlLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGFzY2lpfGFzY2lpc3RyfGNocnxjb21wb3NlfGNvbmNhdHxjb252ZXJ0fGRlY29tcG9zZXxkdW1wfGluaXRjYXB8aW5zdHJ8aW5zdHJifGluc3RyY3xpbnN0cjJ8aW5zdHI0fHVuaXN0cnxsZW5ndGh8bGVuZ3RoYnxsZW5ndGhjfGxlbmd0aDJ8bGVuZ3RoNHxsb3dlcnxscGFkfGx0cmltfG5jaHJ8cmVwbGFjZXxycGFkfHJ0cmltfHNvdW5kZXh8c3Vic3RyfHRyYW5zbGF0ZXx0cmltfHVwcGVyfHZzaXplKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLmJ1aWx0aW4uY2hhci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihhZGRfbW9udGhzfGN1cnJlbnRfZGF0ZXxjdXJyZW50X3RpbWVzdGFtcHxkYnRpbWV6b25lfGxhc3RfZGF5fGxvY2FsdGltZXN0YW1wfG1vbnRoc19iZXR3ZWVufG5ld190aW1lfG5leHRfZGF5fHJvdW5kfHNlc3Npb250aW1lem9uZXxzeXNkYXRlfHR6X29mZnNldHxzeXN0aW1lc3RhbXApXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uYnVpbHRpbi5kYXRlLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGF2Z3xjb3VudHxzdW18bWF4fG1pbnxtZWRpYW58Y29ycnxjb3JyX1xcXFxcXFxcdyt8Y292YXJfKHBvcHxzYW1wKXxjdW1lX2Rpc3R8ZGVuc2VfcmFua3xmaXJzdHxncm91cF9pZHxncm91cGluZ3xncm91cGluZ19pZHxsYXN0fHBlcmNlbnRpbGVfY29udHxwZXJjZW50aWxlX2Rpc2N8cGVyY2VudF9yYW5rfHJhbmt8cmVncl9cXFxcXFxcXHcrfHJvd19udW1iZXJ8c3RhdHNfYmlub21pYWxfdGVzdHxzdGF0c19jcm9zc3RhYnxzdGF0c19mX3Rlc3R8c3RhdHNfa3NfdGVzdHxzdGF0c19tb2RlfHN0YXRzX213X3Rlc3R8c3RhdHNfb25lX3dheV9hbm92YXxzdGF0c190X3Rlc3RfXFxcXFxcXFx3K3xzdGF0c193c3JfdGVzdHxzdGRkZXZ8c3RkZGV2X3BvcHxzdGRkZXZfc2FtcHx2YXJfcG9wfHZhcl9zYW1wfHZhcmlhbmNlKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLmJ1aWx0aW4uYWdncmVnYXRlLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGJmaWxlbmFtZXxjYXJkaW5hbGl0eXxjb2FsZXNjZXxkZWNvZGV8ZW1wdHlfKGJsb2J8Y2xvYil8bGFnfGxlYWR8bGlzdGFnZ3xsbm52bHxuYW52bHxudWxsaWZ8bnZsfG52bDJ8c3lzXyhjb250ZXh0fGd1aWR8dHlwZWlkfGNvbm5lY3RfYnlfcGF0aHxleHRyYWN0X3V0Yyl8dWlkfChjdXJyZW50XFxcXFxcXFxzKyk/dXNlcnx1c2VyZW52fGNhcmRpbmFsaXR5fChidWxrXFxcXFxcXFxzKyk/Y29sbGVjdHxwb3dlcm11bHRpc2V0KF9ieV9jYXJkaW5hbGl0eSk/fG9yYV9oYXNofHN0YW5kYXJkX2hhc2h8ZXhlY3V0ZVxcXFxcXFxccytpbW1lZGlhdGV8YWx0ZXJcXFxcXFxcXHMrc2Vzc2lvbilcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5idWlsdGluLmFkdmFuY2VkLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGJpbl90b19udW18Y2FzdHxjaGFydG9yb3dpZHxmcm9tX3R6fGhleHRvcmF3fG51bXRvZHNpbnRlcnZhbHxudW10b3ltaW50ZXJ2YWx8cmF3dG9oZXh8cmF3dG9uaGV4fHRvX2NoYXJ8dG9fY2xvYnx0b19kYXRlfHRvX2RzaW50ZXJ2YWx8dG9fbG9ifHRvX211bHRpX2J5dGV8dG9fbmNsb2J8dG9fbnVtYmVyfHRvX3NpbmdsZV9ieXRlfHRvX3RpbWVzdGFtcHx0b190aW1lc3RhbXBfdHp8dG9feW1pbnRlcnZhbHxzY25fdG9fdGltZXN0YW1wfHRpbWVzdGFtcF90b19zY258cm93aWR0b2NoYXJ8cm93aWR0b25jaGFyfHRvX2JpbmFyeV9kb3VibGV8dG9fYmluYXJ5X2Zsb2F0fHRvX2Jsb2J8dG9fbmNoYXJ8Y29uX2RiaWRfdG9faWR8Y29uX2d1aWRfdG9faWR8Y29uX25hbWVfdG9faWR8Y29uX3VpZF90b19pZClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5idWlsdGluLmNvbnZlcnQub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoYWJzfGFjb3N8YXNpbnxhdGFufGF0YW4yfGJpdF8oYW5kfG9yfHhvcil8Y2VpbHxjb3N8Y29zaHxleHB8ZXh0cmFjdHxmbG9vcnxncmVhdGVzdHxsZWFzdHxsbnxsb2d8bW9kfHBvd2VyfHJlbWFpbmRlcnxyb3VuZHxzaWdufHNpbnxzaW5ofHNxcnR8dGFufHRhbmh8dHJ1bmMpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uYnVpbHRpbi5tYXRoLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKFxcXFxcXFxcLihjb3VudHxkZWxldGV8ZXhpc3RzfGV4dGVuZHxmaXJzdHxsYXN0fGxpbWl0fG5leHR8cHJpb3J8dHJpbXxyZXZlcnNlKSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5idWlsdGluLmNvbGxlY3Rpb24ub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoY2x1c3Rlcl9kZXRhaWxzfGNsdXN0ZXJfZGlzdGFuY2V8Y2x1c3Rlcl9pZHxjbHVzdGVyX3Byb2JhYmlsaXR5fGNsdXN0ZXJfc2V0fGZlYXR1cmVfZGV0YWlsc3xmZWF0dXJlX2lkfGZlYXR1cmVfc2V0fGZlYXR1cmVfdmFsdWV8cHJlZGljdGlvbnxwcmVkaWN0aW9uX2JvdW5kc3xwcmVkaWN0aW9uX2Nvc3R8cHJlZGljdGlvbl9kZXRhaWxzfHByZWRpY3Rpb25fcHJvYmFiaWxpdHl8cHJlZGljdGlvbl9zZXQpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24uYnVpbHRpbi5kYXRhX21pbmluZy5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihhcHBlbmRjaGlsZHhtbHxkZWxldGV4bWx8ZGVwdGh8ZXh0cmFjdHxleGlzdHNub2RlfGV4dHJhY3R2YWx1ZXxpbnNlcnRjaGlsZHhtbHxpbnNlcnR4bWxiZWZvcmV8eG1sY2FzdHx4bWxkaWZmfHhtbGVsZW1lbnR8eG1sZXhpc3RzfHhtbGlzdmFsaWR8aW5zZXJ0Y2hpbGR4bWxhZnRlcnxpbnNlcnRjaGlsZHhtbGJlZm9yZXxwYXRofHN5c19kYnVyaWdlbnxzeXNfeG1sYWdnfHN5c194bWxnZW58dXBkYXRleG1sfHhtbGFnZ3x4bWxjZGF0YXx4bWxjb2xhdHR2YWx8eG1sY29tbWVudHx4bWxjb25jYXR8eG1sZm9yZXN0fHhtbHBhcnNlfHhtbHBpfHhtbHF1ZXJ5fHhtbHJvb3R8eG1sc2VxdWVuY2V8eG1sc2VyaWFsaXplfHhtbHRhYmxlfHhtbHRyYW5zZm9ybSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5idWlsdGluLnhtbC5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihwcmFnbWFcXFxcXFxcXHMrKGF1dG9ub21vdXNfdHJhbnNhY3Rpb258c2VyaWFsbHlfcmV1c2FibGV8cmVzdHJpY3RfcmVmZXJlbmNlc3xleGNlcHRpb25faW5pdHxpbmxpbmUpKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLnByYWdtYS5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihwKGl8b3xpbylfWy1hLXowLTlfXSspXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInZhcmlhYmxlLnBhcmFtZXRlci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihsX1stYS16MC05X10rKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJ2YXJpYWJsZS5vdGhlci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKTpcXFxcXFxcXGIobmV3fG9sZClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUudHJpZ2dlci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihjb25uZWN0XFxcXFxcXFxzK2J5XFxcXFxcXFxzKyhub2N5Y2xlXFxcXFxcXFxzKyk/KHByaW9yfGxldmVsKXxjb25uZWN0X2J5Xyhyb290fGljeWNsZSl8bGV2ZWx8c3RhcnRcXFxcXFxcXHMrd2l0aClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5oaWVyYXJjaGljYWwuc3FsLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGxhbmd1YWdlfG5hbWV8amF2YXxjKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLndyYXBwZXIub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoZW5kfHRoZW58ZGV0ZXJtaW5pc3RpY3xleGNlcHRpb258d2hlbnxkZWNsYXJlfGJlZ2lufGlufG91dHxub2NvcHl8aXN8YXN8ZXhpdHxvcGVufGZldGNofGludG98Y2xvc2V8c3VidHlwZXx0eXBlfHJvd3R5cGV8ZGVmYXVsdHxleGNsdXNpdmV8bW9kZXxsb2NrfHJlY29yZHxpbmRleFxcXFxcXFxccytieXxyZXN1bHRfY2FjaGV8Y29uc3RhbnR8Y29tbWVudHxcXFxcXFxcXC4obmV4dHZhbHxjdXJydmFsKSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYihncmFudHxyZXZva2V8YWx0ZXJ8ZHJvcHxmb3JjZXxhZGR8Y2hlY2t8Y29uc3RyYWludHxwcmltYXJ5XFxcXFxcXFxzK2tleXxmb3JlaWduXFxcXFxcXFxzK2tleXxyZWZlcmVuY2VzfHVuaXF1ZShcXFxcXFxcXHMraW5kZXgpP3xjb2x1bW58c2VxdWVuY2V8aW5jcmVtZW50XFxcXFxcXFxzK2J5fGNhY2hlfChtYXRlcmlhbGl6ZWRcXFxcXFxcXHMrKT92aWV3fHRyaWdnZXJ8c3RvcmFnZXx0YWJsZXNwYWNlfHBjdChmcmVlfHVzZWQpfChpbml0fG1heCl0cmFuc3xsb2dnaW5nKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm90aGVyLmRkbC5vcmFjbGVcXFwifSx7XFxcIm1hdGNoXFxcIjpcXFwiKD9pKVxcXFxcXFxcYih3aXRofHNlbGVjdHxmcm9tfHdoZXJlfG9yZGVyXFxcXFxcXFxzKyhzaWJsaW5nc1xcXFxcXFxccyspP2J5fGdyb3VwXFxcXFxcXFxzK2J5fHJvbGx1cHxjdWJlfCgobGVmdHxyaWdodHxjcm9zc3xuYXR1cmFsKVxcXFxcXFxccysob3V0ZXJcXFxcXFxcXHMrKT8pP2pvaW58b258YXNjfGRlc2N8dXBkYXRlfHNldHxpbnNlcnR8aW50b3x2YWx1ZXN8ZGVsZXRlfGRpc3RpbmN0fHVuaW9ufG1pbnVzfGludGVyc2VjdHxoYXZpbmd8bGltaXR8dGFibGV8YmV0d2VlbnxsaWtlfG9mfHJvd3wocmFuZ2V8cm93cylcXFxcXFxcXHMrYmV0d2VlbnxudWxsc1xcXFxcXFxccytmaXJzdHxudWxsc1xcXFxcXFxccytsYXN0fGJlZm9yZXxhZnRlcnxhbGx8YW55fGV4aXN0c3xyb3dudW18Y3Vyc29yfHJldHVybmluZ3xvdmVyfHBhcnRpdGlvblxcXFxcXFxccytieXxtZXJnZXx1c2luZ3xtYXRjaGVkfHBpdm90fHVucGl2b3QpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3RoZXIuc3FsLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGRlZmluZXx3aGVuZXZlclxcXFxcXFxccytzcWxlcnJvcnxleGVjfHRpbWluZ1xcXFxcXFxccytzdGFydHx0aW1pbmdcXFxcXFxcXHMrc3RvcClcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwia2V5d29yZC5vdGhlci5zcWxwbHVzLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGFjY2Vzc19pbnRvX251bGx8Y2FzZV9ub3RfZm91bmR8Y29sbGVjdGlvbl9pc19udWxsfGN1cnNvcl9hbHJlYWR5X29wZW58ZHVwX3ZhbF9vbl9pbmRleHxpbnZhbGlkX2N1cnNvcnxpbnZhbGlkX251bWJlcnxsb2dpbl9kZW5pZWR8bm9fZGF0YV9mb3VuZHxub3RfbG9nZ2VkX29ufHByb2dyYW1fZXJyb3J8cm93dHlwZV9taXNtYXRjaHxzZWxmX2lzX251bGx8c3RvcmFnZV9lcnJvcnxzdWJzY3JpcHRfYmV5b25kX2NvdW50fHN1YnNjcmlwdF9vdXRzaWRlX2xpbWl0fHN5c19pbnZhbGlkX3Jvd2lkfHRpbWVvdXRfb25fcmVzb3VyY2V8dG9vX21hbnlfcm93c3x2YWx1ZV9lcnJvcnx6ZXJvX2RpdmlkZXxvdGhlcnMpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQudHlwZS5leGNlcHRpb24ub3JhY2xlXFxcIn0se1xcXCJjYXB0dXJlc1xcXCI6e1xcXCIzXFxcIjp7XFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmNsYXNzLm9yYWNsZVxcXCJ9fSxcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKChkYm1zfHV0bHxvd2F8YXBleClfXFxcXFxcXFx3K1xcXFxcXFxcLihcXFxcXFxcXHcrKSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi5vcmFjbGVcXFwifSx7XFxcImNhcHR1cmVzXFxcIjp7XFxcIjNcXFwiOntcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuY2xhc3Mub3JhY2xlXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoKGh0ZnxodHApXFxcXFxcXFwuKFxcXFxcXFxcdyspKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLm9yYWNsZVxcXCJ9LHtcXFwiY2FwdHVyZXNcXFwiOntcXFwiM1xcXCI6e1xcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5jbGFzcy51c2VyLWRlZmluZWQub3JhY2xlXFxcIn19LFxcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoKFxcXFxcXFxcdytfcGtnfHBrZ19cXFxcXFxcXHcrKVxcXFxcXFxcLihcXFxcXFxcXHcrKSlcXFxcXFxcXGJcXFwiLFxcXCJuYW1lXFxcIjpcXFwic3VwcG9ydC5mdW5jdGlvbi51c2VyLWRlZmluZWQub3JhY2xlXFxcIn0se1xcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIocmFpc2V8cmFpc2VfYXBwbGljYXRpb25fZXJyb3IpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuZnVuY3Rpb24ub3JhY2xlXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIidcXFwiLFxcXCJlbmRcXFwiOlxcXCInXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuc2luZ2xlLm9yYWNsZVxcXCJ9LHtcXFwiYmVnaW5cXFwiOlxcXCJcXFxcXFxcIlxcXCIsXFxcImVuZFxcXCI6XFxcIlxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuZG91YmxlLm9yYWNsZVxcXCJ9LHtcXFwibWF0Y2hcXFwiOlxcXCIoP2kpXFxcXFxcXFxiKGNoYXJ8dmFyY2hhcnx2YXJjaGFyMnxuY2hhcnxudmFyY2hhcjJ8Ym9vbGVhbnxkYXRlfHRpbWVzdGFtcChcXFxcXFxcXHMrd2l0aChcXFxcXFxcXHMrbG9jYWwpP1xcXFxcXFxccyt0aW1lXFxcXFxcXFxzK3pvbmUpP3xpbnRlcnZhbFxcXFxcXFxccypkYXkoXFxcXFxcXFwoXFxcXFxcXFxkKlxcXFxcXFxcKSk/XFxcXFxcXFxzKnRvXFxcXFxcXFxzKm1vbnRofGludGVydmFsXFxcXFxcXFxzKnllYXIoXFxcXFxcXFwoXFxcXFxcXFxkKlxcXFxcXFxcKSk/XFxcXFxcXFxzKnRvXFxcXFxcXFxzKnNlY29uZChcXFxcXFxcXChcXFxcXFxcXGQqXFxcXFxcXFwpKT98eG1sdHlwZXxibG9ifGNsb2J8bmNsb2J8YmZpbGV8bG9uZ3xsb25nXFxcXFxcXFxzK3Jhd3xyYXd8bnVtYmVyfGludGVnZXJ8ZGVjaW1hbHxzbWFsbGludHxmbG9hdHxiaW5hcnlfKGZsb2F0fGRvdWJsZXxpbnRlZ2VyKXxwbHNfKGZsb2F0fGRvdWJsZXxpbnRlZ2VyKXxyb3dpZHx1cm93aWR8dmFyYXJyYXl8bmF0dXJhbHxuYXR1cmFsbnxwb3NpdGl2ZXxwb3NpdGl2ZW58c2lnbnR5cGV8c2ltcGxlXyhmbG9hdHxkb3VibGV8aW50ZWdlcikpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcInN0b3JhZ2UudHlwZS5vcmFjbGVcXFwifV0sXFxcInNjb3BlTmFtZVxcXCI6XFxcInNvdXJjZS5wbHNxbC5vcmFjbGVcXFwifVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/plsql.mjs\n"));

/***/ })

}]);