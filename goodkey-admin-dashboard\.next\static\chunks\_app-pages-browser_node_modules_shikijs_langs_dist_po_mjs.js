"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_po_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/po.mjs":
/*!*************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/po.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Gettext PO\\\",\\\"fileTypes\\\":[\\\"po\\\",\\\"pot\\\",\\\"potx\\\"],\\\"name\\\":\\\"po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^(?=(msgid(_plural)?|msgctxt)\\\\\\\\s*\\\\\\\"[^\\\\\\\"])|^\\\\\\\\s*$\\\",\\\"comment\\\":\\\"Start of body of document, after header\\\",\\\"end\\\":\\\"\\\\\\\\z\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#body\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"^msg(id|str)\\\\\\\\s+\\\\\\\"\\\\\\\"\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.po\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.language.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"string.other.po\\\"}},\\\"match\\\":\\\"^\\\\\\\"(?:([^\\\\\\\\s:]+)(:)\\\\\\\\s+)?([^\\\\\\\"]*)\\\\\\\"\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.header.po\\\"}],\\\"repository\\\":{\\\"body\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(msgid(_plural)?)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgid.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgid.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"begin\\\":\\\"^(msgstr)(?:(\\\\\\\\[)(\\\\\\\\d+)(\\\\\\\\]))?\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.po\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.msgstr.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgstr.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"begin\\\":\\\"^(msgctxt)(?:(\\\\\\\\[)(\\\\\\\\d+)(\\\\\\\\]))?\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.po\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.control.msgctxt.po\\\"}},\\\"end\\\":\\\"^(?!\\\\\\\")\\\",\\\"name\\\":\\\"meta.scope.msgctxt.po\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\G|^)\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[\\\\\\\\\\\\\\\\\\\\\\\"]\\\",\\\"name\\\":\\\"constant.character.escape.po\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"match\\\":\\\"^(#~).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.number-sign.obsolete.po\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"comment\\\":\\\"a line that does not begin with # or \\\\\\\". Could improve this regexp\\\",\\\"match\\\":\\\"^(?!\\\\\\\\s*$)[^#\\\\\\\"].*$\\\\\\\\n?\\\",\\\"name\\\":\\\"invalid.illegal.po\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(?=#)\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(#,)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.flag.po\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.flag.po\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\G|,\\\\\\\\s*)((?:fuzzy)|(?:no-)?(?:c|objc|sh|lisp|elisp|librep|scheme|smalltalk|java|csharp|awk|object-pascal|ycp|tcl|perl|perl-brace|php|gcc-internal|qt|boost)-format)\\\"}]},{\\\"begin\\\":\\\"#\\\\\\\\.\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.extracted.po\\\"},{\\\"begin\\\":\\\"(#:)[ \\\\\\\\t]*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.reference.po\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\S+:)([\\\\\\\\d;]*)\\\",\\\"name\\\":\\\"storage.type.class.po\\\"}]},{\\\"begin\\\":\\\"#\\\\\\\\|\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.previous.po\\\"},{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.po\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.po\\\"}]}]}},\\\"scopeName\\\":\\\"source.po\\\",\\\"aliases\\\":[\\\"pot\\\",\\\"potx\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/po.mjs\n"));

/***/ })

}]);