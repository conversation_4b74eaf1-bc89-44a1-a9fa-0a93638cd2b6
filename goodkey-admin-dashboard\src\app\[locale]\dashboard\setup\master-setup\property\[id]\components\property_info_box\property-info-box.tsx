'use client';

import { PropertyUpdateData } from '@/schema/PropertySchema';
import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { useQuery } from '@tanstack/react-query';

interface IPropertyInfoBox {
  id?: number;
}

function PropertyInfoBox({ id }: IPropertyInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Property', { id }],
    queryFn: () => PropertyQuery.get(id!),
    enabled: !!id,
    select: (res): PropertyUpdateData => ({
      name: res.name ?? '',
      code: res.code ?? '',
      description: res.description ?? '',
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <div className="category-info-box">
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-slate-800 mb-2 border-b border-[#00646C] pb-2">
            {data ? data.name : 'Setup a new property'}
          </h1>
          <p className="text-slate-600 text-sm">
            {data ? (
              'Edit the details below to update the property.'
            ) : (
              <span>
                Please fill out the form below to add a new property. Required
                fields are marked with a{' '}
                <span className="text-red-500 font-semibold">*</span>.
              </span>
            )}
          </p>
        </div>
      </div>
    </Suspense>
  );
}

export default PropertyInfoBox;
