'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  CompanyCreateSchema,
  CompanyUpdateSchema,
  CompanyCreateData,
  CompanyUpdateData,
} from '@/schema/CompanySchema';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Company } from '@/models/Company';
import { ProvinceQuery } from '@/services/queries/ProvinceQuery';
import { CountryQuery } from '@/services/queries/CountryQuery';
import { useEffect } from 'react';

interface CompanyFormProps {
  id?: number;
}

function FormContent({
  defaultValues,
  id,
}: {
  defaultValues?: any;
  id?: number;
}) {
  const { push } = useRouter();
  const { toast } = useToast();
  const isEdit = !!id;

  const { data: provinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ProvinceQuery.tags,
    queryFn: ProvinceQuery.getAll,
  });

  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: CountryQuery.tags,
    queryFn: CountryQuery.getAll,
  });

  const form = useForm<any>({
    resolver: zodResolver(isEdit ? CompanyUpdateSchema : CompanyCreateSchema),
    defaultValues: defaultValues ?? {
      name: '',
      phone: '',
      email: '',
      address1: '',
      address2: '',
      city: '',
      provinceId: '',
      postalCode: '',
      countryId: '',
      websiteUrl: '',
      // accountNumber: '',
      note: '',
      isArchived: false,
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CompanyCreateData | CompanyUpdateData) => {
      const dataWithGroup = { ...data, companyGroup: 'Supplier' };

      if (isEdit) {
        await CompanyQuery.update(id!)(dataWithGroup as CompanyUpdateData);
      } else {
        await CompanyQuery.create(dataWithGroup as CompanyCreateData);
      }
    },
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: [...CompanyQuery.tags],
      });
      toast({
        title: 'Success',
        description: isEdit
          ? 'Company updated successfully.'
          : 'Company created successfully.',
        variant: 'success',
      });
      push('/dashboard/setup/company-contact/supplier-company');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Something went wrong.',
        variant: 'destructive',
      });
    },
  });

  // Watch for country changes and reset province when country changes
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (name === 'countryId') {
        // Reset province when country changes
        form.setValue('provinceId', '');
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Get filtered provinces based on selected country
  const getFilteredProvinces = () => {
    const selectedCountryId = form.watch('countryId');
    if (!selectedCountryId || !provinces) return [];

    return provinces.filter(
      (province) => province.countryId.toString() === selectedCountryId,
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => mutate(data))}
        className="space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-2">
          <Field
            control={form.control}
            name="name"
            label="Company Name"
            placeholder="Enter company name"
            type="text"
            required
            containerClassName="col-span-2"
          />
          <Field
            control={form.control}
            name="email"
            label="Email"
            placeholder="Enter email address"
            type="email"
          />
          <Field
            control={form.control}
            name="phone"
            label="Phone"
            type="phone"
          />
          <Field
            control={form.control}
            name="websiteUrl"
            label="Website URL"
            placeholder="Enter website URL"
            type="text"
          />
          <Field
            control={form.control}
            name="address1"
            label="Address Line 1"
            placeholder="Enter street address"
            type="text"
          />
          <Field
            control={form.control}
            name="address2"
            label="Address Line 2"
            placeholder="Enter address line 2"
            type="text"
          />

          <Field
            control={form.control}
            name="postalCode"
            label="Postal Code"
            placeholder="Enter postal code"
            type="text"
          />
          {isLoadingCountries ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="countryId"
              label="Country"
              type={{
                type: 'select',
                props: {
                  options:
                    countries?.map((country) => ({
                      value: country.id.toString(),
                      label: country.name,
                    })) ?? [],
                  placeholder: 'Select a country',
                },
              }}
            />
          )}
          {isLoadingProvinces ? (
            <Spinner />
          ) : (
            <Field
              control={form.control}
              name="provinceId"
              label="Province"
              type={{
                type: 'select',
                props: {
                  options: getFilteredProvinces().map((province) => ({
                    label: province.name,
                    value: province.id.toString(),
                  })),
                  placeholder: form.watch('countryId')
                    ? 'Select Province'
                    : 'Select a country first',
                },
              }}
            />
          )}
          <Field
            control={form.control}
            name="city"
            label="City"
            placeholder="Enter city"
            type="text"
          />
          {/* <Field
            control={form.control}
            name="accountNumber"
            label="Account Number"
            placeholder="Enter account number"
            type="text"
          /> */}
          <Field
            control={form.control}
            name="note"
            label="Note"
            placeholder="Enter note"
            type="textarea"
            className="md:col-span-2"
          />
          {isEdit && (
            <Field
              control={form.control}
              name="isArchived"
              label="Is Archived"
              type="checkbox"
            />
          )}
        </div>

        <div className="flex justify-end items-center gap-4">
          <Button
            variant={'main'}
            disabled={isPending}
            iconName={
              isPending ? 'LoadingIcon' : isEdit ? 'SaveIcon' : 'AddIcon'
            }
            iconProps={{ className: isPending ? 'animate-spin' : '' }}
          >
            {isPending ? 'Saving...' : isEdit ? 'Save Changes' : 'Add Company'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function CompanyForm({ id }: CompanyFormProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...CompanyQuery.tags, { id }],
    queryFn: () => CompanyQuery.getOne(id!),
    enabled: !!id,
    select: (res: Company) => ({
      name: res.name,
      phone: res.phone || '',
      email: res.email || '',
      address1: res.address1 || '',
      address2: res.address2 || '',
      city: res.city || '',
      provinceId: res.provinceId?.toString() || '',
      postalCode: res.postalCode || '',
      countryId: res.countryId?.toString() || '',
      websiteUrl: res.websiteUrl || '',
      // accountNumber: res.accountNumber || '',
      note: res.note || '',
      isArchived: res.isArchived,
    }),
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} />
    </Suspense>
  );
}
