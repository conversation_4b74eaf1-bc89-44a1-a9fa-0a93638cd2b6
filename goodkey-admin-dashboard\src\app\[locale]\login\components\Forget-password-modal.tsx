'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/inputs/input';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import AuthQuery from '@/services/queries/AuthQuery';
interface ForgetPasswordProps {
  close: () => void;
}
const schema = z.object({
  email: z.string().email(),
});
export default function ForgetPasswordModal({ close }: ForgetPasswordProps) {
  const t = useTranslations('contact');
  const c = useTranslations('Common');

  const { isSuccess, isPending, mutate } = useMutation({
    mutationFn: AuthQuery.requestPasswordReset,
    onSuccess: async () => {
      modal(
        ({ close: cl }) => (
          <ModalContainer
            className="gap-0"
            title={t('passwordResetRequestedSuccess')}
            description={t('emailVerificationLinkText')}
            controls={
              <Button variant="default" onClick={cl}>
                {c('done')}
              </Button>
            }
          />
        ),
        DEFAULT_MODAL,
      ).open({
        previousBehavior: 'close',
      });
    },
  });
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
    },
  });
  const { control, handleSubmit } = form;
  return (
    <ModalContainer
      title={t('forgetPasswordQuestion')}
      description={t('emailPlaceholder')}
      onSubmit={handleSubmit((data) => mutate(data))}
      controls={
        <>
          <Button
            className="w-full"
            type="submit"
            disabled={isPending || isSuccess}
          >
            {isPending ? c('pleaseWait') : t('resetPassword')}
          </Button>
          <Button
            variant="secondary"
            type="submit"
            disabled={isPending || isSuccess}
            onClick={() => close()}
          >
            {c('close')}
          </Button>
        </>
      }
    >
      <Form {...form}>
        <FormField
          control={control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} disabled={isPending || isSuccess} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </Form>
    </ModalContainer>
  );
}
