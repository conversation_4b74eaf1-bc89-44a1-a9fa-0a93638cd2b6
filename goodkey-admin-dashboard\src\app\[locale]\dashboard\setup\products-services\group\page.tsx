import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import GroupQuery from '@/services/queries/GroupQuery';
import GroupTable from './components/group_table';

export const metadata: Metadata = {
  title: 'Goodkey | Group',
};

export default async function Cluster() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: GroupQuery.tags,
    queryFn: () => GroupQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Group',
          link: '/dashboard/setup/master-setup/group',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <GroupTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
