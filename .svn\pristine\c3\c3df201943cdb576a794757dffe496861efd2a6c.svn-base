﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class OfferingRate
    {
        public int Id { get; set; }
        public int? WarehouseId { get; set; }
        public int? OfferingId { get; set; }
        public int? OfferingPropertyId { get; set; }
        public decimal? Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public bool? IsDiscontinued { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int? CreatedById { get; set; }
        public int? UpdatedById { get; set; }

        public virtual AuthUser CreatedBy { get; set; }
        public virtual Offering Offering { get; set; }
        public virtual OfferingProperty OfferingProperty { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual Warehouse Warehouse { get; set; }
    }
}