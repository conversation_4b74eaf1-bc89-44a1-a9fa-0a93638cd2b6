'use client';
import { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { PropertyOptionsDto, OfferingRateUpsertDto } from '@/models/Offering';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';
import { Checkbox } from '@/components/ui/checkbox';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/Badge';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { WarehouseSummaryDto } from '@/models/Warehouse';

interface OptionRowProps {
  option: PropertyOptionsDto;
  offeringId: number;
  warehouseId: number;
}

const OptionRow: React.FC<OptionRowProps> = ({
  option,
  warehouseId,
  offeringId,
}) => {
  const [quantity, setQuantity] = useState<number>(option.quantity ?? 0);
  const [unitPrice, setUnitPrice] = useState<number>(option.unitPrice ?? 0);
  const [isDiscontinued, setIsDiscontinued] = useState<boolean | null>(
    option.isDiscontinued === true,
  );

  const { data: warehouses, isLoading } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
    select: (data: WarehouseSummaryDto[]) =>
      data.filter((warehouse) => warehouse.isActive),
  });

  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (payload: OfferingRateUpsertDto) =>
      OfferingRateQuery.saveOfferingRate(payload),
    onSuccess: () => {
      toast({
        title: 'Saved',
        description: `${option.name} item updated successfully`,
        variant: 'success',
      });
      queryClient.invalidateQueries({
        queryKey: ['products-all'],
      });
      if (warehouses)
        warehouses.forEach((wh) => {
          queryClient.invalidateQueries({
            queryKey: ['products', wh.id],
          });
        });
    },
    onError: (err: any) => {
      toast({
        title: 'Error',
        description: err.message || 'Failed to save',
        variant: 'destructive',
      });
    },
  });

  const handleSave = () => {
    const payload: OfferingRateUpsertDto = {
      offeringId,
      offeringPropertyId: option.id,
      warehouseId,
      quantity,
      unitPrice,
      isDiscontinued: isDiscontinued === true,
    };
    mutation.mutate(payload);
  };

  let isOutOfStock = false;
  let isLowStock = false;
  if (quantity == 0) {
    isOutOfStock = true;
  } else if (quantity < 20) {
    isLowStock = true;
  }

  useEffect(() => {
    setQuantity(option.quantity ?? 0);
    setUnitPrice(option.unitPrice ?? 0);
    setIsDiscontinued(option.isDiscontinued ?? false);
  }, [option.quantity, option.unitPrice, option.isDiscontinued]);

  return (
    <div
      className={`py-2 grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 items-center hover:bg-gray-50 w-full `}
    >
      <div className="cursor-pointer flex items-center gap-1">
        <span
          className={`text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline pl-2 ${
            isDiscontinued ? 'line-through' : ''
          }`}
        >
          {option.name}
        </span>
        <span
          className="font-mono font-normal text-sm"
          style={{ color: '#B10055' }}
        >
          ({option.code})
        </span>
        {isOutOfStock && (
          <Badge variant="outline" className="ml-2 text-xs text-red-600">
            Out of Stock
          </Badge>
        )}
        {isLowStock && !isOutOfStock && (
          <Badge variant="outline" className="ml-2 text-xs text-orange-600">
            Low Stock
          </Badge>
        )}
        {/* </Link> */}
      </div>
      <div className="text-left -ml-[260px]">
        <Input
          type="number"
          min="0"
          placeholder="0.00"
          className={`${isDiscontinued ? 'text-muted-foreground' : ''} ${isLowStock ? 'border-orange-300 bg-orange-50' : isOutOfStock ? 'border-red-300 bg-red-50' : 'bg-gray-100'}  text-right w-20  selection:bg-primary selection:text-primary-foreground flex h-8 min-w-0 rounded-md border text-base transition-[color,box-shadow] outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:ring-ring/30 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`}
          value={quantity}
          onChange={(e) => setQuantity(Number(e.target.value))}
          disabled={isDiscontinued === true}
        />
      </div>
      <div className="text-left -ml-[190px]">
        <div className="relative w-fit">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
            $
          </span>
          <Input
            disabled={isDiscontinued === true}
            type="number"
            placeholder="0.00"
            min="0.00"
            step="0.01"
            value={unitPrice}
            onChange={(e) => setUnitPrice(Number(e.target.value))}
            className={`${isDiscontinued ? 'text-muted-foreground' : ''} text-right w-28 pl-8 bg-gray-100 border-none selection:bg-primary selection:text-primary-foreground flex h-8 min-w-0 rounded-md border text-base transition-[color,box-shadow] outline-none  disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:ring-ring/30 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive`}
          />
        </div>
      </div>
      <div className="-ml-[80px]">
        <Checkbox
          checked={isDiscontinued === true}
          onCheckedChange={() => {
            const nextState = !isDiscontinued;

            modal(
              ({ close }) => (
                <MutationConfirmModal
                  close={close}
                  title={
                    nextState
                      ? 'Discontinue Property?'
                      : 'Re-activate Property?'
                  }
                  description={`Are you sure you want to ${
                    nextState ? 'mark' : 'unmark'
                  } "${option.name}" as ${
                    nextState ? 'discontinued' : 'available'
                  } across all warehouses?`}
                  confirmButtonText={nextState ? 'Discontinue' : 'Continue'}
                  confirmIconName={nextState ? 'PausedIcon' : 'PlayedIcon'}
                  mutationKey={['discontinued']}
                  mutateFn={async () => {
                    setIsDiscontinued(nextState);
                  }}
                  variant={nextState ? 'destructive' : 'default'}
                />
              ),
              DEFAULT_MODAL,
            ).open();
          }}
        />
      </div>
      <div className="-ml-[20px]">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSave}
          className="h-7 px-1 py-0 hover:bg-slate-200 text-[#00646C]"
          title="Update quantity, unit price, and status for this property"
        >
          <span className="text-sm">Save</span>
        </Button>
      </div>
      {/* Property Product Status Column */}
    </div>
  );
};

export default OptionRow;
