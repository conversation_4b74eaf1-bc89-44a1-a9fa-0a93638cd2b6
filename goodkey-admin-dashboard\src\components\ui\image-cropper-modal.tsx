import { ImageIcon, Maximize, Minimize } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import Croppers, { Area, Point } from 'react-easy-crop';

import { Button } from '@/components/ui/button';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { getAspectRatio, getImageFile } from '@/utils/image-helper';

function approximateRational(num: number, maxDenominator = 1000) {
  const a = Math.floor(num); // Integer part
  const b = num - a; // Fractional part

  if (b === 0) {
    // The number is already an integer
    return `${a}/1`;
  }

  let minError = Infinity;
  let bestNumerator = 1;
  let bestDenominator = 1;

  for (let denominator = 1; denominator <= maxDenominator; denominator++) {
    const numerator = Math.round(num * denominator);
    const error = Math.abs(num - numerator / denominator);

    if (error < minError) {
      minError = error;
      bestNumerator = numerator;
      bestDenominator = denominator;

      if (error === 0) break;
    }
  }

  return `${bestNumerator}/${bestDenominator}`;
}

interface CropPictureModalProps {
  image: File;
  onSuccess: (file: File) => void;
  onError?: () => void;
  aspect?: (string | number)[] | boolean;
  targetWidth?: number;
  targetHeight?: number;
}

function getImageDimensions(
  file: File,
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    img.onload = function () {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

export function ImageCropperModal({
  image,
  onSuccess,
  onError,
  aspect,
  targetWidth,
  targetHeight,
}: CropPictureModalProps) {
  console.log('ImageCropperModal aspect:', aspect);

  const parseAspect = useCallback((aspectValue: string | number): number => {
    if (typeof aspectValue === 'number') return aspectValue;
    if (typeof aspectValue === 'string') {
      const [numerator, denominator] = aspectValue.split('/').map(Number);
      return numerator / denominator;
    }
    return 1; // Default to 1 if parsing fails
  }, []);

  const [state, setState] = useState<{
    crop: Point;
    zoom: number;
    aspect: number;
    cropArea?: Area;
  }>({
    crop: { x: 0, y: 0 },
    zoom: 1,
    aspect:
      Array.isArray(aspect) && aspect.length === 1
        ? parseAspect(aspect[0])
        : 4 / 3,
  });
  const onCropChange = (crop: { x: number; y: number }) => {
    setState((prevState) => ({ ...prevState, crop }));
  };

  const [liveCropSize, setLiveCropSize] = useState<{
    width: number;
    height: number;
  } | null>(null);

  const onCropComplete = async (_: Area, croppedAreaPixels: Area) => {
    setState((prevState) => ({ ...prevState, cropArea: croppedAreaPixels }));
    setLiveCropSize({
      width: Math.round(croppedAreaPixels.width),
      height: Math.round(croppedAreaPixels.height),
    });
  };

  const onZoomChange = (zoom: number) => {
    setState((prevState) => ({ ...prevState, zoom }));
  };

  const onAspectChange = (aspectValue: number) => {
    setState((prevState) => ({
      ...prevState,
      aspect: aspectValue,
      crop: { x: 0, y: 0 },
      zoom: 1,
    }));
  };

  const [isProcessing, setIsProcessing] = useState(false);

  const upload = useCallback(() => {
    setIsProcessing(true);
    if (!state.cropArea) return;
    getImageFile(image, state.cropArea)
      .then((blob) => {
        if (!blob) return;
        getImageDimensions(blob).then(({ width, height }) => {
          console.log('Cropped image size:', width, 'x', height);
        });
        onSuccess(blob);
      })
      .catch(() => {
        onError?.();
      });
  }, [onError, onSuccess, image, state.cropArea]);
  useEffect(() => {
    if (aspect === undefined || typeof aspect == 'boolean') {
      getAspectRatio(image).then((ratio) => {
        onAspectChange(ratio);
      });
    }
  }, [aspect, image]);
  return (
    <ModalContainer
      title="Crop Image"
      className="crop-picture-modal h-full bg-white"
      controls={
        <div className="flex flex-wrap items-center gap-2 flex-1 justify-between w-full ">
          <div className="flex gap-4 items-center flex-wrap text-black">
            <span>
              Size:{' '}
              {liveCropSize
                ? `${liveCropSize.width} x ${liveCropSize.height}`
                : '-'}
            </span>
            <span>Aspect ratio: {approximateRational(state.aspect)}</span>
          </div>
          <Button disabled={isProcessing} onClick={upload} className="shrink-0">
            {isProcessing ? 'Uploading...' : 'Upload'}
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 items-stretch h-full overflow-auto">
        <div className="flex flex-row gap-2 justify-center flex-wrap">
          {Array.isArray(aspect) &&
            aspect.length > 1 &&
            aspect.map((value, index) => (
              <Button
                key={index}
                className={cn(
                  'aspect-square w-20 max-w-[80px] truncate text-ellipsis border border-border bg-transparent text-foreground hover:bg-border whitespace-nowrap overflow-hidden',
                  state.aspect === parseAspect(value) &&
                    'border-primary bg-primary text-primary-foreground hover:bg-primary-80',
                )}
                style={{ minWidth: 60 }}
                onClick={() => onAspectChange(parseAspect(value))}
                title={
                  typeof value === 'number' ? value.toFixed(2) : String(value)
                }
              >
                {typeof value === 'number' ? approximateRational(value) : value}
              </Button>
            ))}
          {typeof aspect === 'boolean' && (
            <>
              <Minimize className="w-4 h-4" />
              <Slider
                min={0.5}
                max={3}
                step={0.01}
                value={[state.aspect]}
                onValueChange={([e]) => onAspectChange(Number(e))}
              />
              <Maximize className="w-4 h-4" />
            </>
          )}
        </div>
        <div className="relative w-full h-[500px] self-center">
          <Croppers
            image={URL.createObjectURL(image)}
            crop={state.crop}
            zoom={state.zoom}
            aspect={state.aspect}
            cropShape="rect"
            objectFit="contain"
            showGrid={true}
            onCropChange={onCropChange}
            onCropComplete={onCropComplete}
            onZoomChange={onZoomChange}
          />
        </div>
        <div className="flex flex-row gap-2 items-center">
          <ImageIcon className="w-4 h-4" />
          <Slider
            className="flex-1"
            min={0.1}
            max={3}
            step={0.01}
            value={[state.zoom]}
            onValueChange={([e]) => onZoomChange(Number(e))}
          />
          <ImageIcon className="w-8 h-8" />
        </div>
      </div>
    </ModalContainer>
  );
}
