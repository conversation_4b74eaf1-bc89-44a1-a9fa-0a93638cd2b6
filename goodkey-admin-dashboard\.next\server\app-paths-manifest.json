{"/[locale]/api/[...slug]/route": "app/[locale]/api/[...slug]/route.js", "/[locale]/dashboard/event/[id]/page": "app/[locale]/dashboard/event/[id]/page.js", "/[locale]/dashboard/event/[id]/exhibitors/import/page": "app/[locale]/dashboard/event/[id]/exhibitors/import/page.js", "/[locale]/dashboard/setup/users-roles/role-management/page": "app/[locale]/dashboard/setup/users-roles/role-management/page.js", "/[locale]/dashboard/event/[id]/exhibitors/page": "app/[locale]/dashboard/event/[id]/exhibitors/page.js", "/[locale]/dashboard/setup/page": "app/[locale]/dashboard/setup/page.js"}