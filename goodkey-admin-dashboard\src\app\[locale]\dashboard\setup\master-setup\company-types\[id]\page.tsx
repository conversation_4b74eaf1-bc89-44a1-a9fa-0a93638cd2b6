import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import CompanyTypeQuery from '@/services/queries/CompanyTypeQuery';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import CardGenerator from '@/components/ui/card_generator';

export default async function CompanyTypePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: [...CompanyTypeQuery.tags, { id: Number(id) }],
        queryFn: () => CompanyTypeQuery.getOne(Number(id)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Company Types',
        link: '/dashboard/setup/master-setup/company-types',
      },
      {
        title: isAdd ? 'Add Company Type' : 'Edit Company Type',
        link: `/dashboard/setup/master-setup/company-types/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <CardGenerator
            title={isAdd ? 'Add New Company Type' : 'Edit Company Type'}
            description={
              isAdd
                ? 'Create a new company type'
                : 'Update company type information'
            }
          >
            {/* <CompanyTypeForm id={isAdd ? undefined : Number(id)} /> */}
          </CardGenerator>
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/company-types/add');
  }
}
