using System;

namespace goodkey_common.DTO.Show
{
    public class ShowPromoterDto
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public List<int> BilledToContactIds { get; set; }
        public List<int> ManagerContactIds { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }

        // Navigation properties
        public string CompanyName { get; set; }
        public string ShowName { get; set; }
        public string ShowCode { get; set; }

        // Associated taxes
        public List<ShowPromoterTaxDto> Taxes { get; set; } = new List<ShowPromoterTaxDto>();
    }

    public class SetShowPromoterDto
    {
        public int CompanyId { get; set; }
        public List<int> BilledToContactIds { get; set; }
        public List<int> ManagerContactIds { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }

        // Tax selections - boolean for each available tax
        public Dictionary<int, bool> SelectedTaxes { get; set; } = new Dictionary<int, bool>();
    }

    public class ShowPromoterTaxDto
    {
        public int Id { get; set; }
        public int ShowsPromoterId { get; set; }
        public int TaxId { get; set; }
        public decimal TaxRate { get; set; }

        // Navigation properties
        public string TaxTypeName { get; set; }
        public string TaxTypeAbbreviation { get; set; }
        public string ProvinceName { get; set; }
        public string ProvinceCode { get; set; }
        public bool IsActive { get; set; }
    }

    public class AvailableTaxDto
    {
        public int Id { get; set; }
        public string TaxTypeName { get; set; }
        public string TaxTypeAbbreviation { get; set; }
        public string ProvinceName { get; set; }
        public string ProvinceCode { get; set; }
        public decimal TaxRate { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class ShowManagerCompanyDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string City { get; set; }
        public string ProvinceName { get; set; }
        public string ProvinceCode { get; set; }
    }
}
