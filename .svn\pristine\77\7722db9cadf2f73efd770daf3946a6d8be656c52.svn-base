using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace goodkey_common.Repositories
{
    public interface IExhibitorImportRepository
    {
        // Session Management
        ExhibitorImportSessions CreateSession(int showId, int userId, string fileName, string filePath,
            string originalFileName, string mimeType, long? fileSize);
        ExhibitorImportSessions GetSession(Guid sessionId);
        void UpdateSessionStatus(Guid sessionId, string status, bool? canProceed = null);
        void UpdateSessionCounts(Guid sessionId, int totalRows, int validRows, int errorRows, int warningRows, int unresolvedDuplicates);

        // Row Management
        ExhibitorImportRows CreateRow(Guid sessionId, int rowNumber,
            string companyName, string companyPhone, string companyEmail, string companyAddress1, string companyAddress2,
            string companyCity, string companyProvince, string companyPostalCode, string companyCountry, string companyWebsite,
            string contactFirstName, string contactLastName, string contactEmail, string contactPhone,
            string contactMobile, string contactExt, string contactType, string boothNumbers);
        List<ExhibitorImportRows> GetSessionRows(Guid sessionId);
        ExhibitorImportRows GetRow(int rowId);
        void UpdateRowStatus(int rowId, string status, bool? hasErrors = null, bool? hasWarnings = null,
            int? errorCount = null, int? warningCount = null);
        void UpdateRowFieldValue(int rowId, string fieldName, string value);
        void UpdateRowProcessingResults(int rowId, int? createdCompanyId, int? createdContactId,
            int? createdExhibitorId, int? createdUserId);

        // Validation Messages
        void AddValidationMessage(Guid sessionId, int rowId, int rowNumber, string fieldName,
            string fieldValue, string messageType, string validationRule, string messageCode, string message);
        List<ExhibitorImportValidationMessages> GetSessionValidationMessages(Guid sessionId);
        List<ExhibitorImportValidationMessages> GetRowValidationMessages(int rowId);

        // Duplicate Management
        ExhibitorImportDuplicates CreateDuplicate(Guid sessionId, string duplicateType, string duplicateValue,
            List<int> rowNumbers, string conflictDescription, int? existingRecordId = null, string existingRecordType = null);
        List<ExhibitorImportDuplicates> GetSessionDuplicates(Guid sessionId);
        ExhibitorImportDuplicates GetDuplicate(int duplicateId);
        void ResolveDuplicate(int duplicateId, int resolvedBy);

        // Field Resolution Management
        void SaveFieldResolution(Guid sessionId, int duplicateId, int rowId, string fieldName,
            string selectedSource, string selectedValue, string excelValue, string databaseValue, string customValue = null);
        List<ExhibitorImportFieldResolutions> GetDuplicateResolutions(int duplicateId);

        // Database Lookup Methods
        List<Contact> FindContactsByEmail(string email);
        List<Company> FindCompaniesByName(string companyName);
        Contact GetContact(int contactId);
        Company GetCompany(int companyId);

        // Execution Results
        void SaveExecutionResult(Guid sessionId, int rowId, int rowNumber, string status,
            int? createdCompanyId, int? updatedCompanyId, int? createdContactId, int? updatedContactId,
            int? createdExhibitorId, int? createdUserId, string companyAction, string contactAction,
            string exhibitorAction, string userAction, string companyName, string contactName, string contactEmail,
            string errorMessage = null, string skipReason = null);
        List<ExhibitorImportExecutionResults> GetSessionExecutionResults(Guid sessionId);

        // Additional methods for FixSuggestionService
        List<Company> FindSimilarCompanies(string companyName);
        List<ContactType> GetAllContactTypes();
    }

    public class ExhibitorImportRepository : IExhibitorImportRepository
    {
        private readonly GoodkeyContext _context;

        public ExhibitorImportRepository(GoodkeyContext context)
        {
            _context = context;
        }

        // =====================================================
        // SESSION MANAGEMENT
        // =====================================================

        public ExhibitorImportSessions CreateSession(int showId, int userId, string fileName, string filePath,
            string originalFileName, string mimeType, long? fileSize)
        {
            var session = new ExhibitorImportSessions
            {
                SessionId = Guid.NewGuid(),
                ShowId = showId,
                UserId = userId,
                FileName = fileName,
                FilePath = filePath,
                OriginalFileName = originalFileName,
                MimeType = mimeType,
                FileSize = fileSize,
                Status = "Uploaded",
                CanProceed = false,
                CreatedAt = DateTime.Now,
                ExpiresAt = DateTime.Now.AddHours(24)
            };

            _context.ExhibitorImportSessions.Add(session);
            _context.SaveChanges();
            return session;
        }

        public ExhibitorImportSessions GetSession(Guid sessionId)
        {
            return _context.ExhibitorImportSessions
                .Include(s => s.ExhibitorImportRows)
                .Include(s => s.ExhibitorImportValidationMessages)
                .Include(s => s.ExhibitorImportDuplicates)
                .FirstOrDefault(s => s.SessionId == sessionId);
        }

        public void UpdateSessionStatus(Guid sessionId, string status, bool? canProceed = null)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session != null)
            {
                session.Status = status;
                if (canProceed.HasValue)
                    session.CanProceed = canProceed.Value;

                if (status == "Validated")
                    session.ValidatedAt = DateTime.Now;
                else if (status == "Executing")
                    session.ExecutedAt = DateTime.Now;
                else if (status == "Completed")
                    session.CompletedAt = DateTime.Now;

                _context.SaveChanges();
            }
        }

        public void UpdateSessionCounts(Guid sessionId, int totalRows, int validRows, int errorRows, int warningRows, int unresolvedDuplicates)
        {
            var session = _context.ExhibitorImportSessions.FirstOrDefault(s => s.SessionId == sessionId);
            if (session != null)
            {
                session.TotalRows = totalRows;
                session.ValidRows = validRows;
                session.ErrorRows = errorRows;
                session.WarningRows = warningRows;
                session.UnresolvedDuplicates = unresolvedDuplicates;
                _context.SaveChanges();
            }
        }

        // =====================================================
        // ROW MANAGEMENT
        // =====================================================

        public ExhibitorImportRows CreateRow(Guid sessionId, int rowNumber,
            string companyName, string companyPhone, string companyEmail, string companyAddress1, string companyAddress2,
            string companyCity, string companyProvince, string companyPostalCode, string companyCountry, string companyWebsite,
            string contactFirstName, string contactLastName, string contactEmail, string contactPhone,
            string contactMobile, string contactExt, string contactType, string boothNumbers)
        {
            var row = new ExhibitorImportRows
            {
                SessionId = sessionId,
                RowNumber = rowNumber,
                Status = "Pending",
                CompanyName = companyName,
                CompanyPhone = companyPhone,
                CompanyEmail = companyEmail,
                CompanyAddress1 = companyAddress1,
                CompanyAddress2 = companyAddress2,
                CompanyCity = companyCity,
                CompanyProvince = companyProvince,
                CompanyPostalCode = companyPostalCode,
                CompanyCountry = companyCountry,
                CompanyWebsite = companyWebsite,
                ContactFirstName = contactFirstName,
                ContactLastName = contactLastName,
                ContactEmail = contactEmail,
                ContactPhone = contactPhone,
                ContactMobile = contactMobile,
                ContactExt = contactExt,
                ContactType = contactType,
                BoothNumbers = boothNumbers,
                HasErrors = false,
                HasWarnings = false,
                ErrorCount = 0,
                WarningCount = 0,
                CreatedAt = DateTime.Now
            };

            _context.ExhibitorImportRows.Add(row);
            _context.SaveChanges();
            return row;
        }

        public List<ExhibitorImportRows> GetSessionRows(Guid sessionId)
        {
            return _context.ExhibitorImportRows
                .Where(r => r.SessionId == sessionId)
                .OrderBy(r => r.RowNumber)
                .ToList();
        }

        public ExhibitorImportRows GetRow(int rowId)
        {
            return _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
        }

        public void UpdateRowStatus(int rowId, string status, bool? hasErrors = null, bool? hasWarnings = null,
            int? errorCount = null, int? warningCount = null)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row != null)
            {
                row.Status = status;
                if (hasErrors.HasValue) row.HasErrors = hasErrors.Value;
                if (hasWarnings.HasValue) row.HasWarnings = hasWarnings.Value;
                if (errorCount.HasValue) row.ErrorCount = errorCount.Value;
                if (warningCount.HasValue) row.WarningCount = warningCount.Value;
                _context.SaveChanges();
            }
        }

        public void UpdateRowFieldValue(int rowId, string fieldName, string value)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row != null)
            {
                switch (fieldName.ToLower())
                {
                    case "companyname": row.CompanyName = value; break;
                    case "companyphone": row.CompanyPhone = value; break;
                    case "companyemail": row.CompanyEmail = value; break;
                    case "companyaddress1": row.CompanyAddress1 = value; break;
                    case "companyaddress2": row.CompanyAddress2 = value; break;
                    case "companycity": row.CompanyCity = value; break;
                    case "companyprovince": row.CompanyProvince = value; break;
                    case "companypostalcode": row.CompanyPostalCode = value; break;
                    case "companycountry": row.CompanyCountry = value; break;
                    case "companywebsite": row.CompanyWebsite = value; break;
                    case "contactfirstname": row.ContactFirstName = value; break;
                    case "contactlastname": row.ContactLastName = value; break;
                    case "contactemail": row.ContactEmail = value; break;
                    case "contactphone": row.ContactPhone = value; break;
                    case "contactmobile": row.ContactMobile = value; break;
                    case "contactext": row.ContactExt = value; break;
                    case "contacttype": row.ContactType = value; break;
                    case "boothnumbers": row.BoothNumbers = value; break;
                }
                _context.SaveChanges();
            }
        }

        public void UpdateRowProcessingResults(int rowId, int? createdCompanyId, int? createdContactId,
            int? createdExhibitorId, int? createdUserId)
        {
            var row = _context.ExhibitorImportRows.FirstOrDefault(r => r.Id == rowId);
            if (row != null)
            {
                row.CreatedCompanyId = createdCompanyId;
                row.CreatedContactId = createdContactId;
                row.CreatedExhibitorId = createdExhibitorId;
                row.CreatedUserId = createdUserId;
                row.ProcessedAt = DateTime.Now;
                _context.SaveChanges();
            }
        }

        // =====================================================
        // VALIDATION MESSAGES
        // =====================================================

        public void AddValidationMessage(Guid sessionId, int rowId, int rowNumber, string fieldName,
            string fieldValue, string messageType, string validationRule, string messageCode, string message)
        {
            var validationMessage = new ExhibitorImportValidationMessages
            {
                SessionId = sessionId,
                RowId = rowId,
                RowNumber = rowNumber,
                FieldName = fieldName,
                FieldValue = fieldValue,
                MessageType = messageType,
                ValidationRule = validationRule,
                MessageCode = messageCode,
                Message = message,
                CreatedAt = DateTime.Now
            };

            _context.ExhibitorImportValidationMessages.Add(validationMessage);
            _context.SaveChanges();
        }

        public List<ExhibitorImportValidationMessages> GetSessionValidationMessages(Guid sessionId)
        {
            return _context.ExhibitorImportValidationMessages
                .Where(m => m.SessionId == sessionId)
                .OrderBy(m => m.RowNumber)
                .ThenBy(m => m.FieldName)
                .ToList();
        }

        public List<ExhibitorImportValidationMessages> GetRowValidationMessages(int rowId)
        {
            return _context.ExhibitorImportValidationMessages
                .Where(m => m.RowId == rowId)
                .OrderBy(m => m.FieldName)
                .ToList();
        }

        // =====================================================
        // DUPLICATE MANAGEMENT
        // =====================================================

        public ExhibitorImportDuplicates CreateDuplicate(Guid sessionId, string duplicateType, string duplicateValue,
            List<int> rowNumbers, string conflictDescription, int? existingRecordId = null, string existingRecordType = null)
        {
            var duplicate = new ExhibitorImportDuplicates
            {
                SessionId = sessionId,
                DuplicateType = duplicateType,
                DuplicateValue = duplicateValue,
                RowNumbers = JsonSerializer.Serialize(rowNumbers),
                ConflictResolution = "Manual",
                IsResolved = false,
                RequiresUserDecision = true,
                ConflictDescription = conflictDescription,
                ExistingRecordId = existingRecordId,
                ExistingRecordType = existingRecordType,
                CreatedAt = DateTime.Now
            };

            _context.ExhibitorImportDuplicates.Add(duplicate);
            _context.SaveChanges();
            return duplicate;
        }

        public List<ExhibitorImportDuplicates> GetSessionDuplicates(Guid sessionId)
        {
            return _context.ExhibitorImportDuplicates
                .Include(d => d.ExhibitorImportFieldResolutions)
                .Where(d => d.SessionId == sessionId)
                .OrderBy(d => d.DuplicateType)
                .ThenBy(d => d.DuplicateValue)
                .ToList();
        }

        public ExhibitorImportDuplicates GetDuplicate(int duplicateId)
        {
            return _context.ExhibitorImportDuplicates
                .Include(d => d.ExhibitorImportFieldResolutions)
                .FirstOrDefault(d => d.Id == duplicateId);
        }

        public void ResolveDuplicate(int duplicateId, int resolvedBy)
        {
            var duplicate = _context.ExhibitorImportDuplicates.FirstOrDefault(d => d.Id == duplicateId);
            if (duplicate != null)
            {
                duplicate.IsResolved = true;
                duplicate.ConflictResolution = "Resolved";
                duplicate.ResolvedAt = DateTime.Now;
                duplicate.ResolvedBy = resolvedBy;
                _context.SaveChanges();
            }
        }

        // =====================================================
        // FIELD RESOLUTION MANAGEMENT
        // =====================================================

        public void SaveFieldResolution(Guid sessionId, int duplicateId, int rowId, string fieldName,
            string selectedSource, string selectedValue, string excelValue, string databaseValue, string customValue = null)
        {
            var resolution = new ExhibitorImportFieldResolutions
            {
                SessionId = sessionId,
                DuplicateId = duplicateId,
                RowId = rowId,
                FieldName = fieldName,
                SelectedSource = selectedSource,
                SelectedValue = selectedValue,
                ExcelValue = excelValue,
                DatabaseValue = databaseValue,
                CustomValue = customValue,
                CreatedAt = DateTime.Now
            };

            _context.ExhibitorImportFieldResolutions.Add(resolution);
            _context.SaveChanges();
        }

        public List<ExhibitorImportFieldResolutions> GetDuplicateResolutions(int duplicateId)
        {
            return _context.ExhibitorImportFieldResolutions
                .Where(r => r.DuplicateId == duplicateId)
                .OrderBy(r => r.FieldName)
                .ToList();
        }

        // =====================================================
        // DATABASE LOOKUP METHODS
        // =====================================================

        public List<Contact> FindContactsByEmail(string email)
        {
            return _context.Contact
                .Where(c => c.Email.ToLower() == email.ToLower())
                .ToList();
        }

        public List<Company> FindCompaniesByName(string companyName)
        {
            return _context.Company
                .Where(c => c.CompanyName.ToLower() == companyName.ToLower())
                .ToList();
        }

        public Contact GetContact(int contactId)
        {
            return _context.Contact.FirstOrDefault(c => c.ContactId == contactId);
        }

        public Company GetCompany(int companyId)
        {
            return _context.Company.FirstOrDefault(c => c.CompanyId == companyId);
        }

        // =====================================================
        // EXECUTION RESULTS
        // =====================================================

        public void SaveExecutionResult(Guid sessionId, int rowId, int rowNumber, string status,
            int? createdCompanyId, int? updatedCompanyId, int? createdContactId, int? updatedContactId,
            int? createdExhibitorId, int? createdUserId, string companyAction, string contactAction,
            string exhibitorAction, string userAction, string companyName, string contactName, string contactEmail,
            string errorMessage = null, string skipReason = null)
        {
            var result = new ExhibitorImportExecutionResults
            {
                SessionId = sessionId,
                RowId = rowId,
                RowNumber = rowNumber,
                Status = status,
                CreatedCompanyId = createdCompanyId,
                UpdatedCompanyId = updatedCompanyId,
                CreatedContactId = createdContactId,
                UpdatedContactId = updatedContactId,
                CreatedExhibitorId = createdExhibitorId,
                CreatedUserId = createdUserId,
                CompanyAction = companyAction,
                ContactAction = contactAction,
                ExhibitorAction = exhibitorAction,
                UserAction = userAction,
                CompanyName = companyName,
                ContactName = contactName,
                ContactEmail = contactEmail,
                ErrorMessage = errorMessage,
                SkipReason = skipReason,
                ProcessedAt = DateTime.Now
            };

            _context.ExhibitorImportExecutionResults.Add(result);
            _context.SaveChanges();
        }

        public List<ExhibitorImportExecutionResults> GetSessionExecutionResults(Guid sessionId)
        {
            return _context.ExhibitorImportExecutionResults
                .Where(r => r.SessionId == sessionId)
                .OrderBy(r => r.RowNumber)
                .ToList();
        }

        // =====================================================
        // ADDITIONAL METHODS FOR FIX SUGGESTION SERVICE
        // =====================================================

        public List<Company> FindSimilarCompanies(string companyName)
        {
            return _context.Company
                .Where(c => c.CompanyName.ToLower().Contains(companyName.ToLower()))
                .Take(10)
                .ToList();
        }

        public List<ContactType> GetAllContactTypes()
        {
            return _context.ContactType.ToList();
        }
    }
}