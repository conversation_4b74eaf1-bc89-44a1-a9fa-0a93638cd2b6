import { z } from 'zod';

export const baseSchema = z.object({
  subcategoryId: z
    .string({ required_error: 'Subcategory is required' })
    .min(1, 'Name is required'),
  name: z.string().min(1, 'Name is required'),
  alt: z.string().optional(),
  image: z.any().optional(), // Allow FileList, File[], or undefined
  displayOrder: z.coerce.number().min(1, 'Display order is required'),
});

export const addSchema = baseSchema.refine(
  (data) =>
    data.image &&
    (data.image instanceof FileList
      ? data.image.length > 0
      : data.image.length > 0),
  {
    message: 'Image is required',
    path: ['image'],
  },
);

export type ImageFormValues = z.infer<typeof baseSchema>;
