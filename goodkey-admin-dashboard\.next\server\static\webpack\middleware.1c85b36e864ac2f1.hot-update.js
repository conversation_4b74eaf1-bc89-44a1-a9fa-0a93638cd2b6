"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/services/queries/fetcher.ts":
/*!*****************************************!*\
  !*** ./src/services/queries/fetcher.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   axiosInstance: () => (/* binding */ axiosInstance),\n/* harmony export */   \"default\": () => (/* binding */ fetcher),\n/* harmony export */   proxyFetch: () => (/* binding */ proxyFetch)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(middleware)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(middleware)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(middleware)/./node_modules/next-intl/dist/esm/development/react-server/useLocale.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(middleware)/./src/services/queries/error.ts\");\n\n\n\n\nconst isFront = \"undefined\" !== 'undefined';\n//TODO update this file\nasync function getCookies(name) {\n    if (!isFront) {\n        const { cookies } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(middleware)/./node_modules/next/dist/esm/api/headers.js\"));\n        const cookiesInstance = await cookies();\n        return cookiesInstance.get(name)?.value;\n    } else {\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n    }\n}\nasync function fetcher(url, init, noProxy = false) {\n    const locale = await getCookies('NEXT_LOCALE') || 'fr';\n    const fullUrl = isFront && !noProxy ? `/api/${url}` : `${process.env.API_BASE_URL ?? \"https://api.goodkey.com/admin\"}/${url}`;\n    const auth = JSON.parse(await getCookies('AuthStore') ?? '{}')?.state;\n    const response = await fetch(fullUrl, {\n        ...init,\n        headers: {\n            ...init?.headers,\n            ...(!isFront || noProxy) && auth?.accessToken ? {\n                Authorization: `Bearer ${auth.accessToken}`\n            } : {},\n            'accept-language': locale\n        }\n    });\n    if (!response.ok) {\n        // Keep your existing 401 handling exactly here\n        if (response.status === 401) {\n            if (isFront) {\n                (await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../zustand/ConnectionStore */ \"(middleware)/./src/services/zustand/ConnectionStore.ts\")))?.default?.getState()?.setConnected(false, 'expired');\n            }\n        }\n        // Now, attempt to parse the error response body and determine safety\n        let errorMessage = `HTTP error! Status: ${response.status}`;\n        let isUserSafe = false;\n        try {\n            // Attempt to parse as JSON first\n            const errorData = await response.clone().json();\n            if (errorData && errorData.message) {\n                errorMessage = errorData.message;\n                if (response.status >= 400 && response.status < 500) {\n                    isUserSafe = true;\n                }\n            }\n        } catch (jsonError) {\n            // JSON parsing failed, log the specific JSON error\n            console.error('Failed to parse error response as JSON:', jsonError);\n            try {\n                // Attempt to read the response body as text for more context\n                const textError = await response.clone().text();\n                // Include the raw text (or part of it) in the error message\n                // Avoid making it too long for display purposes if needed\n                errorMessage = `HTTP error! Status: ${response.status}. Response body: ${textError.substring(0, 200)}${textError.length > 200 ? '...' : ''}`;\n            } catch (textError) {\n                // If reading as text also fails, log that too\n                console.error('Failed to read error response as text:', textError);\n                // Stick with the original generic message\n                errorMessage = `HTTP error! Status: ${response.status}. Failed to read response body.`;\n            }\n            // Since parsing failed or we got non-JSON, message is not considered safe\n            isUserSafe = false;\n        }\n        // Throw AppError with the potentially more detailed message and status\n        throw new _error__WEBPACK_IMPORTED_MODULE_1__.AppError(errorMessage, response.status, isUserSafe);\n    }\n    // Check if the response is a Blob (for file downloads)\n    if (response.headers.get('Content-Type')?.includes('application/octet-stream') || response.headers.get('Content-Type')?.includes('application/pdf') || response.headers.get('Content-Type')?.includes('image/')) {\n        return await response.blob();\n    }\n    const data = await response.json();\n    if (data.statusCode !== 200 && data.statusCode !== 201) {\n        // Determine safety based on the statusCode in the response body\n        const statusCode = data.statusCode ?? response.status;\n        // Assume messages for 4xx status codes in the body are also user-safe\n        const isUserSafe = statusCode >= 400 && statusCode < 500;\n        throw new _error__WEBPACK_IMPORTED_MODULE_1__.AppError(data.message ?? 'Something went wrong', statusCode, isUserSafe);\n    }\n    return data.data;\n}\nasync function proxyFetch(url, init) {\n    const response = await fetch(`api/${url}`, init);\n    if (!response.ok) {\n        throw new _error__WEBPACK_IMPORTED_MODULE_1__.AppError(`HTTP error! Status: ${response.status}`, response.status);\n    }\n    const data = await response.json();\n    if (data.statusCode !== 200) {\n        throw new _error__WEBPACK_IMPORTED_MODULE_1__.AppError(`HTTP error! Status: ${response.status}`, response.status);\n    }\n    return data.data;\n}\nconst axiosInstance = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create();\naxiosInstance.interceptors.request.use((config)=>{\n    let locale;\n    if (false) {} else {\n        locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    }\n    config.url = `${process.env.API_BASE_URL}/${locale}/api/${config.url}`;\n    return config;\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/services/queries/fetcher.ts\n");

/***/ })

});