"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_matlab_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/matlab.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/matlab.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"MATLAB\\\",\\\"fileTypes\\\":[\\\"m\\\"],\\\"name\\\":\\\"matlab\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"This and #all_after_command_dual are split out so #command_dual can be excluded in things like (), {}, []\\\",\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"}],\\\"repository\\\":{\\\"all_after_command_dual\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#conjugate_transpose\\\"},{\\\"include\\\":\\\"#transpose\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#operators\\\"}]},\\\"all_before_command_dual\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#classdef\\\"},{\\\"include\\\":\\\"#function\\\"},{\\\"include\\\":\\\"#blocks\\\"},{\\\"include\\\":\\\"#control_statements\\\"},{\\\"include\\\":\\\"#global_persistent\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#square_brackets\\\"},{\\\"include\\\":\\\"#indexing_curly_brackets\\\"},{\\\"include\\\":\\\"#curly_brackets\\\"}]},\\\"blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.for.matlab\\\"}},\\\"name\\\":\\\"meta.for.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(if)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.if.matlab\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"name\\\":\\\"meta.if.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.elseif.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(elseif)\\\\\\\\b(.*)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.elseif.matlab\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.else.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(else)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.else.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(parfor)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.for.matlab\\\"}},\\\"name\\\":\\\"meta.parfor.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!$)\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.parfor-quantity.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(spmd)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.spmd.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.spmd.matlab\\\"}},\\\"name\\\":\\\"meta.spmd.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?!$)\\\",\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.spmd-statement.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(switch)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.switch.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.switch.matlab\\\"}},\\\"name\\\":\\\"meta.switch.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.case.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(case)\\\\\\\\b(.*)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.case.matlab\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.otherwise.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(otherwise)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.otherwise.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(try)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.try.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.try.matlab\\\"}},\\\"name\\\":\\\"meta.try.matlab\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.catch.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"end\\\":\\\"^\\\",\\\"match\\\":\\\"(\\\\\\\\s*)(?:^|[\\\\\\\\s,;])(catch)\\\\\\\\b(.*)?$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.catch.matlab\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(while)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.while.matlab\\\"}},\\\"name\\\":\\\"meta.while.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"braced_validator_list\\\":{\\\"begin\\\":\\\"\\\\\\\\s*({)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"comment\\\":\\\"Validator functions. Treated as a recursive group to permit nested brackets, quotes, etc.\\\",\\\"end\\\":\\\"(})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#braced_validator_list\\\"},{\\\"include\\\":\\\"#validator_strings\\\"},{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.matlab\\\"}},\\\"match\\\":\\\"([^{}}'\\\\\\\"\\\\\\\\.]+)\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"storage.type.matlab\\\"}]},\\\"classdef\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(classdef)\\\\\\\\b\\\\\\\\s*(.*)\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.class.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.class.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"2\\\":{\\\"name\\\":\\\"meta.class-declaration.matlab\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.section.class.matlab\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.other.matlab\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_]*)*\\\",\\\"name\\\":\\\"entity.other.inherited-class.matlab\\\"},{\\\"match\\\":\\\"&\\\",\\\"name\\\":\\\"keyword.operator.other.matlab\\\"}]},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*(([a-zA-Z][a-zA-Z0-9_]*)(?:\\\\\\\\s*(<)\\\\\\\\s*([^%]*))?)\\\\\\\\s*($|(?=(%|...)).*)\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.class.matlab\\\"}},\\\"name\\\":\\\"meta.class.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(properties)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.properties.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.properties.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"public|protected|private\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.properties.matlab\\\"}},\\\"name\\\":\\\"meta.properties.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(methods)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.methods.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.methods.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"public|protected|private\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.methods.matlab\\\"}},\\\"name\\\":\\\"meta.methods.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(events)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.events.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.events.matlab\\\"},{\\\"begin\\\":\\\"=\\\\\\\\s*\\\",\\\"end\\\":\\\",|(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"true|false\\\",\\\"name\\\":\\\"constant.language.boolean.matlab\\\"},{\\\"match\\\":\\\"public|protected|private\\\",\\\"name\\\":\\\"constant.language.access.matlab\\\"}]}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.events.matlab\\\"}},\\\"name\\\":\\\"meta.events.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(enumeration)\\\\\\\\b([^%]*)\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.enumeration.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.enumeration.matlab\\\"}},\\\"name\\\":\\\"meta.enumeration.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"command_dual\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.other.command.matlab\\\"},\\\"28\\\":{\\\"name\\\":\\\"comment.line.percentage.matlab\\\"}},\\\"comment\\\":\\\"  1        2                                  3                                                    4                                                  5                                                    6                                                         7                                                                             8                 9                            10                   11                      12                         13                   14                                                               1516       17                                                             18                                                                                                                               19                      20                                     21                                    22                                      23                                         24                                         25                                                                         26            27                 28\\\",\\\"match\\\":\\\"^\\\\\\\\s*(([b-df-hk-moq-zA-HJ-MO-Z]\\\\\\\\w*|a|an|a([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-rt-z0-9_]\\\\\\\\w*|ns\\\\\\\\w+)|e|ep|e([A-Za-oq-z0-9_]\\\\\\\\w*|p[A-Za-rt-z0-9_]\\\\\\\\w*|ps\\\\\\\\w+)|in|i([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-eg-z0-9_]\\\\\\\\w*|nf\\\\\\\\w+)|I|In|I([A-Za-mo-z0-9_]\\\\\\\\w*|n[A-Za-eg-z0-9_]\\\\\\\\w*|nf\\\\\\\\w+)|j\\\\\\\\w+|N|Na|N([A-Zb-z0-9_]\\\\\\\\w*|a[A-MO-Za-z0-9_]\\\\\\\\w*|aN\\\\\\\\w+)|n|na|nar|narg|nargi|nargo|nargou|n([A-Zb-z0-9_]\\\\\\\\w*|a([A-Za-mo-qs-z0-9_]\\\\\\\\w*|n\\\\\\\\w+|r([A-Za-fh-z0-9_]\\\\\\\\w*|g([A-Za-hj-nq-z0-9_]\\\\\\\\w*|i([A-Za-mo-z0-9_]\\\\\\\\w*|n\\\\\\\\w+)|o([A-Za-tv-z0-9_]\\\\\\\\w*|u([A-Za-su-z]\\\\\\\\w*|t\\\\\\\\w+))))))|p|p[A-Za-hj-z0-9_]\\\\\\\\w*|pi\\\\\\\\w+)\\\\\\\\s+((([^\\\\\\\\s;,%()=.{&|~<>:+\\\\\\\\-*/\\\\\\\\\\\\\\\\@^'\\\\\\\"]|(?=')|(?=\\\\\\\"))|(\\\\\\\\.\\\\\\\\^|\\\\\\\\.\\\\\\\\*|\\\\\\\\./|\\\\\\\\.\\\\\\\\\\\\\\\\|\\\\\\\\.'|\\\\\\\\.\\\\\\\\(|&&|==|\\\\\\\\|\\\\\\\\||&(?=[^&])|\\\\\\\\|(?=[^\\\\\\\\|])|~=|<=|>=|~(?!=)|<(?!=)|>(?!=)|:|\\\\\\\\+|-|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|@|\\\\\\\\^)([^\\\\\\\\s]|\\\\\\\\s*(?=%)|\\\\\\\\s+$|\\\\\\\\s+(,|;|\\\\\\\\)|}|\\\\\\\\]|&|\\\\\\\\||<|>|=|:|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\^|@|(\\\\\\\\.[^\\\\\\\\d.]|\\\\\\\\.\\\\\\\\.[^.])))|(\\\\\\\\.[^^*/\\\\\\\\\\\\\\\\'(\\\\\\\\sA-Za-z]))([^%]|'[^']*'|\\\\\\\"[^\\\\\\\"]*\\\\\\\")*|(\\\\\\\\.(?=\\\\\\\\s)|\\\\\\\\.[A-Za-z]|(?={))([^(=\\\\\\\\'\\\\\\\"%]|==|'[^']*'|\\\\\\\"[^\\\\\\\"]*\\\\\\\"|\\\\\\\\(|\\\\\\\\([^)%]*\\\\\\\\)|\\\\\\\\[|\\\\\\\\[[^\\\\\\\\]%]*\\\\\\\\]|{|{[^}%]*})*(\\\\\\\\.\\\\\\\\.\\\\\\\\.[^%]*)?((?=%)|$)))(%.*)?$\\\"},\\\"comment_block\\\":{\\\"begin\\\":\\\"(^[\\\\\\\\s]*)%\\\\\\\\{[^\\\\\\\\n\\\\\\\\S]*+\\\\\\\\n\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"^[\\\\\\\\s]*%\\\\\\\\}[^\\\\\\\\n\\\\\\\\S]*+(?:\\\\\\\\n|$)\\\",\\\"name\\\":\\\"comment.block.percentage.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_block\\\"},{\\\"match\\\":\\\"^[^\\\\\\\\n]*\\\\\\\\n\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=%%\\\\\\\\s)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.matlab\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%%\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-percentage.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G[^\\\\\\\\S\\\\\\\\n]*(?![\\\\\\\\n\\\\\\\\s])\\\",\\\"contentName\\\":\\\"meta.cell.matlab\\\",\\\"end\\\":\\\"(?=\\\\\\\\n)\\\"}]}]},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=%)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.matlab\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"%\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.percentage.matlab\\\"}]}]},\\\"conjugate_transpose\\\":{\\\"match\\\":\\\"((?<=[^\\\\\\\\s])|(?<=\\\\\\\\])|(?<=\\\\\\\\))|(?<=\\\\\\\\}))'\\\",\\\"name\\\":\\\"keyword.operator.transpose.matlab\\\"},\\\"constants\\\":{\\\"comment\\\":\\\"MATLAB Constants\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(eps|false|Inf|inf|intmax|intmin|namelengthmax|NaN|nan|on|off|realmax|realmin|true|pi)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.matlab\\\"},\\\"control_statements\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.matlab\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(break|continue|return)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.control.matlab\\\"},\\\"curly_brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"comment\\\":\\\"We don't include $self here to avoid matching command syntax inside (), [], {}\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"comment\\\":\\\"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\\\",\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"end_in_parens\\\":{\\\"comment\\\":\\\"end as operator symbol\\\",\\\"match\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^\\\\\\\\s*)(function)\\\\\\\\s+(?:(?:(\\\\\\\\[)([^\\\\\\\\]]*)(\\\\\\\\])|([a-zA-Z][a-zA-Z0-9_]*))\\\\\\\\s*=\\\\\\\\s*)?([a-zA-Z][a-zA-Z0-9_]*(\\\\\\\\.[a-zA-Z][a-zA-Z0-9_]*)*)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.function.matlab\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.matlab\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.output.matlab\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.matlab\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.output.function.matlab\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.matlab\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b(\\\\\\\\s*\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.function.matlab\\\"}},\\\"name\\\":\\\"meta.function.matlab\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.arguments.function.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"variable.parameter.input.matlab\\\"}]},{\\\"begin\\\":\\\"(^\\\\\\\\s*)(arguments)\\\\\\\\b([^%]*)\\\\\\\\s*(\\\\\\\\([^)]*\\\\\\\\))?\\\\\\\\s*($|(?=%))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.control.arguments.matlab\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z][a-zA-Z0-9_]*\\\",\\\"name\\\":\\\"variable.parameter.arguments.matlab\\\"}]}},\\\"end\\\":\\\"\\\\\\\\s*(?:^|[\\\\\\\\s,;])(end)\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.end.arguments.matlab\\\"}},\\\"name\\\":\\\"meta.arguments.matlab\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"global_persistent\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.globalpersistent.matlab\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(global|persistent)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.globalpersistent.matlab\\\"},\\\"indexing_curly_brackets\\\":{\\\"Comment\\\":\\\"Match identifier{idx, idx, } and stop at newline without ... This helps with partially written code like x{idx \\\",\\\"begin\\\":\\\"([a-zA-Z][a-zA-Z0-9_\\\\\\\\.]*\\\\\\\\s*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"comment\\\":\\\"We don't include $self here to avoid matching command syntax inside (), [], {}\\\",\\\"end\\\":\\\"(\\\\\\\\}|(?<!\\\\\\\\.\\\\\\\\.\\\\\\\\.).\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"comment\\\":\\\"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\\\",\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"line_continuation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.continuation.matlab\\\"}},\\\"comment\\\":\\\"Line continuations\\\",\\\"match\\\":\\\"(\\\\\\\\.\\\\\\\\.\\\\\\\\.)(.*)$\\\",\\\"name\\\":\\\"meta.linecontinuation.matlab\\\"},\\\"numbers\\\":{\\\"comment\\\":\\\"Valid numbers: 1, .1, 1.1, .1e1, 1.1e1, 1e1, 1i, 1j, 1e2j\\\",\\\"match\\\":\\\"(?<=[\\\\\\\\s\\\\\\\\-\\\\\\\\+\\\\\\\\*\\\\\\\\/\\\\\\\\\\\\\\\\=:\\\\\\\\[\\\\\\\\(\\\\\\\\{,]|^)\\\\\\\\d*\\\\\\\\.?\\\\\\\\d+([eE][+-]?\\\\\\\\d)?([0-9&&[^\\\\\\\\.]])*(i|j)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.matlab\\\"},\\\"operators\\\":{\\\"comment\\\":\\\"Operator symbols\\\",\\\"match\\\":\\\"(?<=\\\\\\\\s)(==|~=|>|>=|<|<=|&|&&|:|\\\\\\\\||\\\\\\\\|\\\\\\\\||\\\\\\\\+|-|\\\\\\\\*|\\\\\\\\.\\\\\\\\*|/|\\\\\\\\./|\\\\\\\\\\\\\\\\|\\\\\\\\.\\\\\\\\\\\\\\\\|\\\\\\\\^|\\\\\\\\.\\\\\\\\^)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.symbols.matlab\\\"},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"comment\\\":\\\"We don't include $self here to avoid matching command syntax inside (), [], {}\\\",\\\"end\\\":\\\"(\\\\\\\\)|(?<!\\\\\\\\.\\\\\\\\.\\\\\\\\.).\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#end_in_parens\\\"},{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"comment\\\":\\\"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\\\",\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"square_brackets\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"comment\\\":\\\"We don't include $self here to avoid matching command syntax inside (), [], {}\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#all_before_command_dual\\\"},{\\\"include\\\":\\\"#all_after_command_dual\\\"},{\\\"comment\\\":\\\"These block keywords pick up any such missed keywords when the block matching for things like (), if-end, etc. don't work. Useful for when someone has partially written\\\",\\\"include\\\":\\\"#block_keywords\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.matlab\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"comment\\\":\\\"Shell command\\\",\\\"match\\\":\\\"^\\\\\\\\s*((!).*$\\\\\\\\n?)\\\"},{\\\"begin\\\":\\\"((?<=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|=|\\\\\\\\s|;|:|,|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^))|^)'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"comment\\\":\\\"Character vector literal (single-quoted)\\\",\\\"end\\\":\\\"'(?=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|\\\\\\\\]|\\\\\\\\)|\\\\\\\\}|=|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^|\\\\\\\\s|;|:|,))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.matlab\\\"}},\\\"name\\\":\\\"string.quoted.single.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"},{\\\"match\\\":\\\"'(?=.)\\\",\\\"name\\\":\\\"invalid.illegal.unescaped-quote.matlab\\\"},{\\\"comment\\\":\\\"Operator symbols\\\",\\\"match\\\":\\\"((\\\\\\\\%([\\\\\\\\+\\\\\\\\-0]?\\\\\\\\d{0,3}(\\\\\\\\.\\\\\\\\d{1,3})?)(c|d|e|E|f|g|G|s|((b|t)?(o|u|x|X))))|\\\\\\\\%\\\\\\\\%|\\\\\\\\\\\\\\\\(b|f|n|r|t|\\\\\\\\\\\\\\\\))\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"}]},{\\\"begin\\\":\\\"((?<=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|=|\\\\\\\\s|;|:|,|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^))|^)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.matlab\\\"}},\\\"comment\\\":\\\"String literal (double-quoted)\\\",\\\"end\\\":\\\"\\\\\\\"(?=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|\\\\\\\\]|\\\\\\\\)|\\\\\\\\}|=|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^|\\\\\\\\||\\\\\\\\s|;|:|,))\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.matlab\\\"}},\\\"name\\\":\\\"string.quoted.double.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.matlab\\\"},{\\\"match\\\":\\\"\\\\\\\"(?=.)\\\",\\\"name\\\":\\\"invalid.illegal.unescaped-quote.matlab\\\"}]}]},\\\"transpose\\\":{\\\"match\\\":\\\"\\\\\\\\.'\\\",\\\"name\\\":\\\"keyword.operator.transpose.matlab\\\"},\\\"validator_strings\\\":{\\\"comment\\\":\\\"Simplified string patterns nested inside validator functions which don't change scopes of matches.\\\",\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"begin\\\":\\\"((?<=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|=|\\\\\\\\s|;|:|,|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^))|^)'\\\",\\\"comment\\\":\\\"Character vector literal (single-quoted)\\\",\\\"end\\\":\\\"'(?=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|\\\\\\\\]|\\\\\\\\)|\\\\\\\\}|=|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^|\\\\\\\\s|;|:|,))\\\",\\\"name\\\":\\\"storage.type.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"''\\\"},{\\\"match\\\":\\\"'(?=.)\\\"},{\\\"match\\\":\\\"([^']+)\\\"}]},{\\\"begin\\\":\\\"((?<=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|=|\\\\\\\\s|;|:|,|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^))|^)\\\\\\\"\\\",\\\"comment\\\":\\\"String literal (double-quoted)\\\",\\\"end\\\":\\\"\\\\\\\"(?=(\\\\\\\\[|\\\\\\\\(|\\\\\\\\{|\\\\\\\\]|\\\\\\\\)|\\\\\\\\}|=|~|<|>|&|\\\\\\\\||-|\\\\\\\\+|\\\\\\\\*|/|\\\\\\\\\\\\\\\\|\\\\\\\\.|\\\\\\\\^|\\\\\\\\||\\\\\\\\s|;|:|,))\\\",\\\"name\\\":\\\"storage.type.matlab\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\"},{\\\"match\\\":\\\"\\\\\\\"(?=.)\\\"},{\\\"match\\\":\\\"[^\\\\\\\"]+\\\"}]}]}]},\\\"validators\\\":{\\\"begin\\\":\\\"\\\\\\\\s*[;]?\\\\\\\\s*([a-zA-Z][a-zA-Z0-9_\\\\\\\\.\\\\\\\\?]*)\\\",\\\"comment\\\":\\\"Property and argument validation. Match an identifier allowing . and ?.\\\",\\\"end\\\":\\\"([;\\\\\\\\n%=].*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"comment\\\":\\\"Match comments\\\",\\\"match\\\":\\\"([%].*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}},\\\"comment\\\":\\\"Handle things like arg = val; nextArg\\\",\\\"match\\\":\\\"(=[^;]*)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#validators\\\"}]}},\\\"comment\\\":\\\"End of property/argument patterns which start a new property/argument. Look for beginning of identifier after semicolon. Otherwise treat as regular code.\\\",\\\"match\\\":\\\"([\\\\\\\\n;]\\\\\\\\s*[a-zA-Z].*)\\\"},{\\\"include\\\":\\\"$self\\\"}]}},\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation\\\"},{\\\"comment\\\":\\\"Size declaration\\\",\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\([^\\\\\\\\)]*\\\\\\\\))\\\",\\\"name\\\":\\\"storage.type.matlab\\\"},{\\\"comment\\\":\\\"Type declaration\\\",\\\"match\\\":\\\"([a-zA-Z][a-zA-Z0-9_\\\\\\\\.]*)\\\",\\\"name\\\":\\\"storage.type.matlab\\\"},{\\\"include\\\":\\\"#braced_validator_list\\\"}]},\\\"variables\\\":{\\\"comment\\\":\\\"MATLAB variables\\\",\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(nargin|nargout|varargin|varargout)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.function.matlab\\\"}},\\\"scopeName\\\":\\\"source.matlab\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/matlab.mjs\n"));

/***/ })

}]);