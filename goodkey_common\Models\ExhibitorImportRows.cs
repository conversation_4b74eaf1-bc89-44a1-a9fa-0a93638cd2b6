// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class ExhibitorImportRows
    {
        public ExhibitorImportRows()
        {
            ExhibitorImportExecutionResults = new HashSet<ExhibitorImportExecutionResults>();
            ExhibitorImportFieldResolutions = new HashSet<ExhibitorImportFieldResolutions>();
            ExhibitorImportValidationMessages = new HashSet<ExhibitorImportValidationMessages>();
        }

        public int Id { get; set; }
        public Guid SessionId { get; set; }
        public int RowNumber { get; set; }
        public string Status { get; set; }
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress1 { get; set; }
        public string CompanyAddress2 { get; set; }
        public string CompanyCity { get; set; }
        public string CompanyProvince { get; set; }
        public string CompanyPostalCode { get; set; }
        public string CompanyCountry { get; set; }
        public string CompanyWebsite { get; set; }
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string ContactType { get; set; }
        public string BoothNumbers { get; set; }
        public bool? HasErrors { get; set; }
        public bool? HasWarnings { get; set; }
        public int? ErrorCount { get; set; }
        public int? WarningCount { get; set; }
        public int? CreatedCompanyId { get; set; }
        public int? CreatedContactId { get; set; }
        public int? CreatedExhibitorId { get; set; }
        public int? CreatedUserId { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public DateTime? CreatedAt { get; set; }

        public virtual ExhibitorImportSessions Session { get; set; }
        public virtual ICollection<ExhibitorImportExecutionResults> ExhibitorImportExecutionResults { get; set; }
        public virtual ICollection<ExhibitorImportFieldResolutions> ExhibitorImportFieldResolutions { get; set; }
        public virtual ICollection<ExhibitorImportValidationMessages> ExhibitorImportValidationMessages { get; set; }
    }
}