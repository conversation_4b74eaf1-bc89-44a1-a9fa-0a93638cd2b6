"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/dashboard/event/[id]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/event-sidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/event-sidebar.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSidebar: () => (/* binding */ EventSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square-quote.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/queries/MenuQuery */ \"(app-pages-browser)/./src/services/queries/MenuQuery.ts\");\n/* __next_internal_client_entry_do_not_use__ EventSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Icon mapping for dynamic menu items\nconst iconMap = {\n    MessageSquareQuote: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    ShoppingCart: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    FileText: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    Package: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    Users: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    BarChart: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Settings: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    ImageIcon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    CreditCard: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n};\nfunction EventSidebar(param) {\n    let { eventId, activeItem } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Fetch dynamic menu items from API\n    const { data: menuItems, isLoading, isError } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            ..._services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"].tags,\n            'event_sidebar'\n        ],\n        queryFn: {\n            \"EventSidebar.useQuery\": ()=>_services_queries_MenuQuery__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getBySection('event_sidebar')\n        }[\"EventSidebar.useQuery\"]\n    });\n    // Transform API data to component format\n    const transformedMenuItems = (menuItems === null || menuItems === void 0 ? void 0 : menuItems.map((item)=>{\n        var _item_url;\n        return {\n            name: item.name || 'Menu Item',\n            icon: iconMap[item.icon || ''] || _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: ((_item_url = item.url) === null || _item_url === void 0 ? void 0 : _item_url.replace('{eventId}', eventId)) || '#',\n            displayOrder: item.displayOrder,\n            isVisible: item.isVisible\n        };\n    })) || [];\n    // Fallback to static menu items if API fails or is loading\n    const fallbackMenuItems = [\n        {\n            name: 'RFQs',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/rfqs\"),\n            displayOrder: 1,\n            isVisible: true\n        },\n        {\n            name: 'ORDERS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/orders\"),\n            displayOrder: 2,\n            isVisible: true\n        },\n        {\n            name: 'ORDER FORMS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/order-forms\"),\n            displayOrder: 3,\n            isVisible: true\n        },\n        {\n            name: 'SHOW PACKAGES',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/show-packages\"),\n            displayOrder: 4,\n            isVisible: true\n        },\n        {\n            name: 'EXHIBITORS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/exhibitors\"),\n            displayOrder: 5,\n            isVisible: true\n        },\n        {\n            name: 'LISTS & REPORTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/reports\"),\n            displayOrder: 6,\n            isVisible: true\n        },\n        {\n            name: 'SHOW MANAGEMENT',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId),\n            displayOrder: 7,\n            isVisible: true\n        },\n        {\n            name: 'GRAPHICS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/graphics\"),\n            displayOrder: 8,\n            isVisible: true\n        },\n        {\n            name: 'PAYMENTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/payments\"),\n            displayOrder: 9,\n            isVisible: true\n        }\n    ];\n    // Use dynamic menu items if available, otherwise fallback\n    const finalMenuItems = isLoading || isError || !(menuItems === null || menuItems === void 0 ? void 0 : menuItems.length) ? fallbackMenuItems : transformedMenuItems.filter((item)=>item.isVisible);\n    const handleItemClick = (url)=>{\n        router.push(url);\n    };\n    const isActive = (name)=>{\n        return name === activeItem;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full md:w-64 bg-white rounded-md border border-slate-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full flex items-center p-2 rounded-md text-left \".concat(isActive(item.name) ? 'bg-slate-50 text-[#00646C]' : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'),\n                        onClick: ()=>handleItemClick(item.url),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, item.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(EventSidebar, \"Cq6nMVoWK52dAqismHSo1JWzXGM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery\n    ];\n});\n_c = EventSidebar;\nvar _c;\n$RefreshReg$(_c, \"EventSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/event-sidebar.tsx\n"));

/***/ })

});