'use client';

import { useQuery } from '@tanstack/react-query';
import ShowQuery from '@/services/queries/ShowQuery';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import AppLayout from '@/components/ui/app_layout';
import ExhibitorForm from './ExhibitorForm';

interface ExhibitorPageClientProps {
  showId: number;
  exhibitorId?: number;
  isAdd: boolean;
}

export default function ExhibitorPageClient({
  showId,
  exhibitorId,
  isAdd,
}: ExhibitorPageClientProps) {
  // Fetch show data
  const { data: show, isLoading: isLoadingShow } = useQuery<
    ShowGeneralInfoData,
    Error
  >({
    queryKey: [ShowQuery.tags, showId],
    queryFn: () => ShowQuery.getOne(showId),
  });

  // Loading state
  if (isLoadingShow) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading event details...</p>
        </div>
      </div>
    );
  }

  if (!show) {
    return <div>Show not found</div>;
  }

  return (
    <AppLayout
      items={[
        { title: 'Events', link: '/dashboard' },
        { title: show.name, link: `/dashboard/event/${showId}` },
        { title: 'Exhibitors', link: `/dashboard/event/${showId}/exhibitors` },
        {
          title: isAdd ? 'Add New Exhibitor' : 'Edit Exhibitor',
          link: `/dashboard/event/${showId}/exhibitors/${exhibitorId || 'add'}`,
        },
      ]}
      childrenClassName="bg-transparent border-none shadow-none px-0 py-0"
    >
      <ExhibitorForm showId={showId} exhibitorId={exhibitorId} isAdd={isAdd} />
    </AppLayout>
  );
}
