using Microsoft.AspNetCore.Http;

namespace goodkey_common.DTO.ExhibitorImport
{
    // =====================================================
    // Phase 1: Validation DTOs
    // =====================================================

    public class ExhibitorImportUploadDto
    {
        public IFormFile ExcelFile { get; set; }
        public int ShowId { get; set; }
    }

    public class ExhibitorImportValidationResponseDto
    {
        public string SessionId { get; set; }
        public int ShowId { get; set; }
        public string FileName { get; set; }
        public ExhibitorImportSummaryDto Summary { get; set; }
        public List<ExhibitorImportRowDto> Rows { get; set; } = new();
        public List<ExhibitorImportValidationMessageDto> ValidationMessages { get; set; } = new();
        public List<ExhibitorImportDuplicateDto> Duplicates { get; set; } = new();
        public bool CanProceed { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class ExhibitorImportSummaryDto
    {
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public int WarningRows { get; set; }
        public int NewCompanies { get; set; }
        public int ExistingCompanies { get; set; }
        public int NewContacts { get; set; }
        public int DuplicateEmails { get; set; }
        public int DuplicateBooths { get; set; }
        public int UnresolvedDuplicates { get; set; }
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public bool HasDuplicates { get; set; }
    }

    public class ExhibitorImportRowDto
    {
        public int RowNumber { get; set; }
        public string Status { get; set; } // Pending, Valid, Error, Warning

        // Company Data
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress1 { get; set; }
        public string CompanyAddress2 { get; set; }
        public string CompanyCity { get; set; }
        public string CompanyProvince { get; set; }
        public string CompanyPostalCode { get; set; }
        public string CompanyCountry { get; set; }
        public string CompanyWebsite { get; set; }

        // Contact Data
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string BoothNumbers { get; set; }
        public string ContactType { get; set; }

        // Resolved Values
        public int? ResolvedCompanyId { get; set; }
        public string ResolvedCompanyName { get; set; }
        public int? ResolvedContactTypeId { get; set; }
        public string ResolvedContactTypeName { get; set; }
        public List<string> ResolvedBoothNumbers { get; set; } = new();

        // Flags
        public bool IsNewCompany { get; set; }
        public bool IsNewContact { get; set; }
        public bool IsDuplicate { get; set; }
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
    }

    public class ExhibitorImportValidationMessageDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
        public string MessageType { get; set; } // Error, Warning, Info
        public string ValidationRule { get; set; } // Required, Format, Duplicate, Lookup, Business
        public string MessageCode { get; set; }
        public string Message { get; set; }
    }

    public class ExhibitorImportDuplicateDto
    {
        public int DuplicateId { get; set; }
        public string DuplicateType { get; set; } // Company, Contact, Email, Phone, Booth
        public string DuplicateValue { get; set; }
        public List<int> RowNumbers { get; set; } = new();
        public string ConflictResolution { get; set; } // Skip, Merge, CreateNew, Manual, SelectExisting
        public List<DuplicateConflictDetail> ConflictDetails { get; set; } = new();
        public List<DuplicateResolutionOption> ResolutionOptions { get; set; } = new();
        public bool RequiresUserDecision { get; set; }
        public string ConflictDescription { get; set; } = string.Empty;
        public int? ExistingRecordId { get; set; }
        public string ExistingRecordType { get; set; } = string.Empty;
        public List<ExhibitorImportFieldConflictDto> FieldConflicts { get; set; } = new();
    }

    public class DuplicateConflictDetail
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public string CurrentValue { get; set; } = string.Empty;
        public string ConflictingValue { get; set; } = string.Empty;
        public string ExistingRecordInfo { get; set; } = string.Empty; // For existing DB records
        public int? ExistingRecordId { get; set; }
    }

    public class DuplicateResolutionOption
    {
        public string OptionType { get; set; } = string.Empty; // UseExisting, CreateNew, Merge, Skip
        public string OptionLabel { get; set; } = string.Empty;
        public string OptionDescription { get; set; } = string.Empty;
        public int? ExistingRecordId { get; set; }
        public Dictionary<string, object> OptionData { get; set; } = new();
    }

    // =====================================================
    // Phase 2: Execution DTOs
    // =====================================================

    public class ExhibitorImportExecuteDto
    {
        public string SessionId { get; set; }
        public bool SendEmailInvites { get; set; } = false;
        public List<ExhibitorImportRowExecutionDto> RowOverrides { get; set; } = new();
    }

    public class ExhibitorImportRowExecutionDto
    {
        public int RowNumber { get; set; }
        public string Action { get; set; } // Process, Skip, Manual
        public int? OverrideCompanyId { get; set; } // Manual company selection
        public int? OverrideContactTypeId { get; set; } // Manual contact type selection
        public string ConflictResolution { get; set; } // For duplicates: Skip, CreateNew, Merge
    }

    public class ExhibitorImportExecutionResponseDto
    {
        public string SessionId { get; set; }
        public string Status { get; set; } // Executing, Completed, Failed
        public ExhibitorImportExecutionSummaryDto Summary { get; set; }
        public List<ExhibitorImportExecutionResultDto> Results { get; set; } = new();
        public DateTime? CompletedAt { get; set; }
    }

    public class ExhibitorImportExecutionSummaryDto
    {
        public int TotalRows { get; set; }
        public int ProcessedRows { get; set; }
        public int SkippedRows { get; set; }
        public int FailedRows { get; set; }
        public int CreatedCompanies { get; set; }
        public int UpdatedCompanies { get; set; }
        public int CreatedContacts { get; set; }
        public int UpdatedContacts { get; set; }
        public int CreatedExhibitors { get; set; }
        public int CreatedUsers { get; set; }
        public int EmailInvitesSent { get; set; }
    }

    public class ExhibitorImportExecutionResultDto
    {
        public int RowNumber { get; set; }
        public string Status { get; set; } // Processed, Skipped, Failed
        public string CompanyName { get; set; }
        public string ContactName { get; set; }
        public string ContactEmail { get; set; }

        // Created IDs
        public int? CreatedCompanyId { get; set; }
        public int? UpdatedCompanyId { get; set; }
        public int? CreatedContactId { get; set; }
        public int? UpdatedContactId { get; set; }
        public int? CreatedExhibitorId { get; set; }
        public int? CreatedUserId { get; set; }

        // Action Details
        public string CompanyAction { get; set; } = string.Empty; // Created, Updated, Used Existing
        public string ContactAction { get; set; } = string.Empty; // Created, Updated, Used Existing
        public string ExhibitorAction { get; set; } = string.Empty; // Created, Updated, Used Existing
        public string UserAction { get; set; } = string.Empty; // Created, Updated, Used Existing

        // Results
        public bool EmailInviteSent { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime? ProcessedAt { get; set; }

        // Creation Details
        public List<CreationAttemptResult> CreationAttempts { get; set; } = new();
        public string SkipReason { get; set; } = string.Empty;
    }

    public class CreationAttemptResult
    {
        public string RecordType { get; set; } = string.Empty; // Company, Contact, Exhibitor, User
        public bool WasCreated { get; set; }
        public bool WasExisting { get; set; }
        public int? RecordId { get; set; }
        public string FailureReason { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty; // Created, Used Existing, Skipped, Failed
        public Dictionary<string, object> Details { get; set; } = new();
    }

    // =====================================================
    // Progress and Status DTOs
    // =====================================================

    public class ExhibitorImportProgressDto
    {
        public string SessionId { get; set; }
        public string Status { get; set; }
        public int TotalRows { get; set; }
        public int ProcessedRows { get; set; }
        public decimal CompletionPercentage { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? EstimatedCompletion { get; set; }
        public string CurrentOperation { get; set; }
    }

    public class ExhibitorImportSessionDto
    {
        public string SessionId { get; set; }
        public int ShowId { get; set; }
        public string ShowName { get; set; }
        public string FileName { get; set; }
        public string Status { get; set; }
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public bool CanProceed { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string CreatedByUsername { get; set; }
    }

    // =====================================================
    // Excel Template and Validation DTOs
    // =====================================================

    public class ExcelRowData
    {
        public int RowNumber { get; set; }
        public string CompanyName { get; set; }
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhone { get; set; }
        public string ContactMobile { get; set; }
        public string ContactExt { get; set; }
        public string BoothNumbers { get; set; }
        public string ContactType { get; set; }
    }

    public class ExcelTemplateInfoDto
    {
        public List<ExcelColumnInfo> Columns { get; set; } = new();
        public List<string> RequiredColumns { get; set; } = new();
        public List<string> OptionalColumns { get; set; } = new();
        public Dictionary<string, List<string>> ValidValues { get; set; } = new();
        public string SampleDataUrl { get; set; }
    }

    public class ExcelColumnInfo
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsRequired { get; set; }
        public string DataType { get; set; }
        public string Format { get; set; }
        public string Example { get; set; }
    }

    // =====================================================
    // Duplicate Resolution DTOs
    // =====================================================

    public class DuplicateResolutionRequestDto
    {
        public Guid SessionId { get; set; }
        public string DuplicateType { get; set; } = string.Empty;
        public string DuplicateValue { get; set; } = string.Empty;
        public List<int> AffectedRowNumbers { get; set; } = new();
        public string ResolutionType { get; set; } = string.Empty; // UseExisting, CreateNew, Merge, Skip, FieldByField
        public int? SelectedExistingId { get; set; }
        public Dictionary<string, object> ResolutionData { get; set; } = new();
        public Dictionary<string, FieldResolutionChoice> FieldResolutions { get; set; } = new();
    }

    public class FieldResolutionChoice
    {
        public string FieldName { get; set; } = string.Empty;
        public string SelectedValue { get; set; } = string.Empty;
        public string SelectedSource { get; set; } = string.Empty; // "Import", "Existing", "Custom"
        public int? SourceRowNumber { get; set; } // For import source
        public int? SourceRecordId { get; set; } // For existing record source
    }

    public class DuplicateResolutionResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<int> UpdatedRowNumbers { get; set; } = new();
        public int? ResolvedToRecordId { get; set; }
        public string ResolutionAction { get; set; } = string.Empty;
    }

    // =====================================================
    // Additional DTOs for Controller Support
    // =====================================================

    public class ExhibitorImportFieldConflictDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string ExcelValue { get; set; } = string.Empty;
        public string DatabaseValue { get; set; } = string.Empty;
        public bool HasConflict { get; set; }
    }

    public class ExhibitorImportResolveDto
    {
        public string SessionId { get; set; } = string.Empty;
        public List<ExhibitorImportDuplicateResolutionDto> DuplicateResolutions { get; set; } = new();
    }

    public class ExhibitorImportDuplicateResolutionDto
    {
        public int DuplicateId { get; set; }
        public List<ExhibitorImportFieldResolutionDto> FieldResolutions { get; set; } = new();
    }

    public class ExhibitorImportFieldResolutionDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string SelectedSource { get; set; } = string.Empty; // Excel, Database, Custom
        public string SelectedValue { get; set; } = string.Empty;
        public string ExcelValue { get; set; } = string.Empty;
        public string DatabaseValue { get; set; } = string.Empty;
        public string CustomValue { get; set; } = string.Empty;
    }
}
