"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_scss_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/css.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/css.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CSS\\\",\\\"name\\\":\\\"css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#combinators\\\"},{\\\"include\\\":\\\"#selector\\\"},{\\\"include\\\":\\\"#at-rules\\\"},{\\\"include\\\":\\\"#rule-list\\\"}],\\\"repository\\\":{\\\"at-rules\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\A(?:\\\\\\\\xEF\\\\\\\\xBB\\\\\\\\xBF)?(?i:(?=\\\\\\\\s*@charset\\\\\\\\b))\\\",\\\"end\\\":\\\";|(?=$)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}},\\\"name\\\":\\\"meta.at-rule.charset.css\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.not-lowercase.charset.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.leading-whitespace.charset.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"invalid.illegal.no-whitespace.charset.css\\\"},\\\"4\\\":{\\\"name\\\":\\\"invalid.illegal.whitespace.charset.css\\\"},\\\"5\\\":{\\\"name\\\":\\\"invalid.illegal.not-double-quoted.charset.css\\\"},\\\"6\\\":{\\\"name\\\":\\\"invalid.illegal.unclosed-string.charset.css\\\"},\\\"7\\\":{\\\"name\\\":\\\"invalid.illegal.unexpected-characters.charset.css\\\"}},\\\"match\\\":\\\"\\\\\\\\G((?!@charset)@\\\\\\\\w+)|\\\\\\\\G(\\\\\\\\s+)|(@charset\\\\\\\\S[^;]*)|(?<=@charset)(\\\\\\\\x20{2,}|\\\\\\\\t+)|(?<=@charset\\\\\\\\x20)([^\\\\\\\";]+)|(\\\\\\\"[^\\\\\\\"]+$)|(?<=\\\\\\\")([^;]+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.charset.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"match\\\":\\\"((@)charset)(?=\\\\\\\\s)\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.css\\\"}},\\\"end\\\":\\\"\\\\\\\"|$\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.css\\\"}},\\\"name\\\":\\\"string.quoted.double.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\G|^)(?=(?:[^\\\\\\\"])+$)\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"invalid.illegal.unclosed.string.css\\\"}]}]},{\\\"begin\\\":\\\"(?i)((@)import)(?:\\\\\\\\s+|$|(?=['\\\\\\\"]|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.import.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}},\\\"name\\\":\\\"meta.at-rule.import.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(?=/\\\\\\\\*)\\\",\\\"end\\\":\\\"(?<=\\\\\\\\*/)\\\\\\\\s*\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#url\\\"},{\\\"include\\\":\\\"#media-query-list\\\"}]},{\\\"begin\\\":\\\"(?i)((@)font-face)(?=\\\\\\\\s*|{|/\\\\\\\\*|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.font-face.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.at-rule.font-face.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},{\\\"begin\\\":\\\"(?i)(@)page(?=[\\\\\\\\s:{]|/\\\\\\\\*|$)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.page.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*($|[:{;]))\\\",\\\"name\\\":\\\"meta.at-rule.page.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list\\\"}]},{\\\"begin\\\":\\\"(?i)(?=@media(\\\\\\\\s|\\\\\\\\(|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)media\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.media.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"name\\\":\\\"meta.at-rule.media.header.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query-list\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.media.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.media.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.media.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},{\\\"begin\\\":\\\"(?i)(?=@counter-style([\\\\\\\\s'\\\\\\\"{;]|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)counter-style\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.counter-style.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*{)\\\",\\\"name\\\":\\\"meta.at-rule.counter-style.header.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\\\",\\\"name\\\":\\\"variable.parameter.style-name.css\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.counter-style.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#rule-list-innards\\\"}]}]},{\\\"begin\\\":\\\"(?i)(?=@document([\\\\\\\\s'\\\\\\\"{;]|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)document\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.document.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"name\\\":\\\"meta.at-rule.document.header.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(url-prefix|domain|regexp)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.document-rule.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.document-rule.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"match\\\":\\\"[^'\\\\\\\")\\\\\\\\s]+\\\",\\\"name\\\":\\\"variable.parameter.document-rule.css\\\"}]},{\\\"include\\\":\\\"#url\\\"},{\\\"include\\\":\\\"#commas\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.document.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.document.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.document.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},{\\\"begin\\\":\\\"(?i)(?=@(?:-(?:webkit|moz|o|ms)-)?keyframes([\\\\\\\\s'\\\\\\\"{;]|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)(?:-(?:webkit|moz|o|ms)-)?keyframes\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.keyframes.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*{)\\\",\\\"name\\\":\\\"meta.at-rule.keyframes.header.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\\\",\\\"name\\\":\\\"variable.parameter.keyframe-list.css\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.keyframes.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.keyframes.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.keyframes.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.keyframe-offset.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.keyframe-offset.percentage.css\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<![\\\\\\\\w-]) (from|to) (?![\\\\\\\\w-])         # Keywords for 0% | 100%\\\\n|\\\\n([-+]?(?:\\\\\\\\d+(?:\\\\\\\\.\\\\\\\\d+)?|\\\\\\\\.\\\\\\\\d+)%)     # Percentile value\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]}]},{\\\"begin\\\":\\\"(?i)(?=@supports(\\\\\\\\s|\\\\\\\\(|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)|(?=;)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)supports\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.supports.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"name\\\":\\\"meta.at-rule.supports.header.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#feature-query-operators\\\"},{\\\"include\\\":\\\"#feature-query\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.supports.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.supports.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.supports.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},{\\\"begin\\\":\\\"(?i)((@)(-(ms|o)-)?viewport)(?=[\\\\\\\\s'\\\\\\\"{;]|/\\\\\\\\*|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.viewport.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*[@{;])\\\",\\\"name\\\":\\\"meta.at-rule.viewport.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"begin\\\":\\\"(?i)((@)font-feature-values)(?=[\\\\\\\\s'\\\\\\\"{;]|/\\\\\\\\*|$)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.font-feature-values.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"contentName\\\":\\\"variable.parameter.font-name.css\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*[@{;])\\\",\\\"name\\\":\\\"meta.at-rule.font-features.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"include\\\":\\\"#font-features\\\"},{\\\"begin\\\":\\\"(?i)((@)namespace)(?=[\\\\\\\\s'\\\\\\\";]|/\\\\\\\\*|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.namespace.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\";|(?=[@{])\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}},\\\"name\\\":\\\"meta.at-rule.namespace.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#url\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.namespace-prefix.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?xi)\\\\n(?:\\\\\\\\G|^|(?<=\\\\\\\\s))\\\\n(?=\\\\n  (?<=\\\\\\\\s|^)                             # Starts with whitespace\\\\n  (?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])          # Then a valid identifier character\\\\n  |\\\\n  \\\\\\\\s*                                   # Possible adjoining whitespace\\\\n  /\\\\\\\\*(?:[^*]|\\\\\\\\*[^/])*\\\\\\\\*/              # Injected comment\\\\n)\\\\n(.*?)                                    # Grouped to embed #comment-block\\\\n(\\\\n  (?:[-a-zA-Z_]    | [^\\\\\\\\x00-\\\\\\\\x7F])     # First letter\\\\n  (?:[-a-zA-Z0-9_] | [^\\\\\\\\x00-\\\\\\\\x7F]      # Remainder of identifier\\\\n    |\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)\\\\n  )*\\\\n)\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#string\\\"}]},{\\\"begin\\\":\\\"(?i)(?=@[\\\\\\\\w-]+[^;]+;s*$)\\\",\\\"end\\\":\\\"(?<=;)(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)[\\\\\\\\w-]+\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}},\\\"name\\\":\\\"meta.at-rule.header.css\\\"}]},{\\\"begin\\\":\\\"(?i)(?=@[\\\\\\\\w-]+(\\\\\\\\s|\\\\\\\\(|{|/\\\\\\\\*|$))\\\",\\\"end\\\":\\\"(?<=})(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)\\\\\\\\G(@)[\\\\\\\\w-]+\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"name\\\":\\\"meta.at-rule.header.css\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.at-rule.body.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]}]},\\\"color-keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<![\\\\\\\\w-])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.color.w3c-standard-color-name.css\\\"},{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood\\\\n|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan\\\\n|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange\\\\n|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise\\\\n|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen\\\\n|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki\\\\n|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow\\\\n|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray\\\\n|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue\\\\n|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise\\\\n|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered\\\\n|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum\\\\n|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell\\\\n|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato\\\\n|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)\\\\n(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.color.w3c-extended-color-name.css\\\"},{\\\"match\\\":\\\"(?i)(?<![\\\\\\\\w-])currentColor(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.color.current.css\\\"},{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow\\\\n|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption\\\\n|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow\\\\n|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText)\\\\n(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"invalid.deprecated.color.system.css\\\"}]},\\\"combinators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"/deep/|>>>\\\",\\\"name\\\":\\\"invalid.deprecated.combinator.css\\\"},{\\\"match\\\":\\\">>|>|\\\\\\\\+|~\\\",\\\"name\\\":\\\"keyword.operator.combinator.css\\\"}]},\\\"commas\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.list.comma.css\\\"},\\\"comment-block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.css\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.css\\\"}},\\\"name\\\":\\\"comment.block.css\\\"},\\\"escapes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[0-9a-fA-F]{1,6}\\\",\\\"name\\\":\\\"constant.character.escape.codepoint.css\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\$\\\\\\\\s*\\\",\\\"end\\\":\\\"^(?<!\\\\\\\\G)\\\",\\\"name\\\":\\\"constant.character.escape.newline.css\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.css\\\"}]},\\\"feature-query\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.condition.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.condition.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.feature-query.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#feature-query-operators\\\"},{\\\"include\\\":\\\"#feature-query\\\"}]},\\\"feature-query-operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<=[\\\\\\\\s()]|^|\\\\\\\\*/)(and|not|or)(?=[\\\\\\\\s()]|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.feature.$1.css\\\"},{\\\"include\\\":\\\"#rule-list-innards\\\"}]},\\\"font-features\\\":{\\\"begin\\\":\\\"(?xi)\\\\n((@)(annotation|character-variant|ornaments|styleset|stylistic|swash))\\\\n(?=[\\\\\\\\s@'\\\\\\\"{;]|/\\\\\\\\*|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.${3:/downcase}.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.css\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.at-rule.${3:/downcase}.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.property-list.font-feature.css\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\\\",\\\"name\\\":\\\"variable.font-feature.css\\\"},{\\\"include\\\":\\\"#rule-list-innards\\\"}]}]},\\\"functional-pseudo-classes\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)((:)dir)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"match\\\":\\\"(?i)(?<![\\\\\\\\w-])(ltr|rtl)(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.text-direction.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?i)((:)lang)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[(,\\\\\\\\s])[a-zA-Z]+(-[a-zA-Z0-9]*|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?=[),\\\\\\\\s])\\\",\\\"name\\\":\\\"support.constant.language-range.css\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.css\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.css\\\"}},\\\"name\\\":\\\"string.quoted.double.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\"\\\\\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=[\\\\\\\"\\\\\\\\s])\\\",\\\"name\\\":\\\"support.constant.language-range.css\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.css\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.css\\\"}},\\\"name\\\":\\\"string.quoted.single.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"},{\\\"match\\\":\\\"(?<=['\\\\\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=['\\\\\\\\s])\\\",\\\"name\\\":\\\"support.constant.language-range.css\\\"}]},{\\\"include\\\":\\\"#commas\\\"}]},{\\\"begin\\\":\\\"(?i)((:)(?:not|has|matches|where|is))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#selector-innards\\\"}]},{\\\"begin\\\":\\\"(?i)((:)nth-(?:last-)?(?:child|of-type))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)[+-]?(\\\\\\\\d+n?|n)(\\\\\\\\s*[+-]\\\\\\\\s*\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.numeric.css\\\"},{\\\"match\\\":\\\"(?i)even|odd\\\",\\\"name\\\":\\\"support.constant.parity.css\\\"}]}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(calc)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.calc.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.calc.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[*/]|(?<=\\\\\\\\s|^)[-+](?=\\\\\\\\s|$)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(rgba?|rgb|hsla?|hsl|hwb|lab|oklab|lch|oklch|color)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.misc.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.color.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(\\\\n  (?:-webkit-|-moz-|-o-)?    # Accept prefixed/historical variants\\\\n  (?:repeating-)?            # \\\\\\\"Repeating\\\\\\\"-type gradient\\\\n  (?:linear|radial|conic)    # Shape\\\\n  -gradient\\\\n)\\\\n(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.gradient.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.gradient.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<![\\\\\\\\w-])(from|to|at|in|hue)(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"keyword.operator.gradient.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(-webkit-gradient)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.deprecated.gradient.function.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.gradient.invalid.deprecated.gradient.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(from|to|color-stop)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.deprecated.function.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(annotation|attr|blur|brightness|character-variant|clamp|contrast|counters?\\\\n|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate|color-mix\\\\n|image-set|invert|local|max|min|minmax|opacity|ornaments|repeat|saturate|sepia\\\\n|styleset|stylistic|swash|symbols\\\\n|cos|sin|tan|acos|asin|atan|atan2|hypot|sqrt|pow|log|exp|abs|sign)\\\\n(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.misc.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.misc.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<=[,\\\\\\\\s\\\\\\\"]|\\\\\\\\*/|^)\\\\\\\\d+x(?=[\\\\\\\\s,\\\\\\\"')]|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"constant.numeric.other.density.css\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"match\\\":\\\"[^'\\\\\\\"),\\\\\\\\s]+\\\",\\\"name\\\":\\\"variable.parameter.misc.css\\\"}]},{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(circle|ellipse|inset|polygon|rect)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.shape.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.shape.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<=\\\\\\\\s|^|\\\\\\\\*/)(at|round)(?=\\\\\\\\s|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"keyword.operator.shape.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(cubic-bezier|steps)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.timing-function.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.timing-function.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?i)(?<![\\\\\\\\w-])(start|end)(?=\\\\\\\\s*\\\\\\\\)|$)\\\",\\\"name\\\":\\\"support.constant.step-direction.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"begin\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n( (?:translate|scale|rotate)(?:[XYZ]|3D)?\\\\n| matrix(?:3D)?\\\\n| skew[XY]?\\\\n| perspective\\\\n)\\\\n(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.transform.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"include\\\":\\\"#url\\\"},{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w-])(var)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.misc.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.variable.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"--(?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\\\",\\\"name\\\":\\\"variable.argument.css\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},\\\"media-feature-keywords\\\":{\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|:|\\\\\\\\*/)\\\\n(?: portrait                  # Orientation\\\\n  | landscape\\\\n  | progressive               # Scan types\\\\n  | interlace\\\\n  | fullscreen                # Display modes\\\\n  | standalone\\\\n  | minimal-ui\\\\n  | browser\\\\n  | hover\\\\n)\\\\n(?=\\\\\\\\s|\\\\\\\\)|$)\\\",\\\"name\\\":\\\"support.constant.property-value.css\\\"},\\\"media-features\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.property-name.media.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.property-name.media.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.type.vendored.property-name.media.css\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|\\\\\\\\(|\\\\\\\\*/)           # Preceded by whitespace, bracket or comment\\\\n(?:\\\\n  # Standardised features\\\\n  (\\\\n    (?:min-|max-)?            # Range features\\\\n    (?: height\\\\n      | width\\\\n      | aspect-ratio\\\\n      | color\\\\n      | color-index\\\\n      | monochrome\\\\n      | resolution\\\\n    )\\\\n    | grid                    # Discrete features\\\\n    | scan\\\\n    | orientation\\\\n    | display-mode\\\\n    | hover\\\\n  )\\\\n  |\\\\n  # Deprecated features\\\\n  (\\\\n    (?:min-|max-)?            # Deprecated in Media Queries 4\\\\n    device-\\\\n    (?: height\\\\n      | width\\\\n      | aspect-ratio\\\\n    )\\\\n  )\\\\n  |\\\\n  # Vendor extensions\\\\n  (\\\\n    (?:\\\\n      # Spec-compliant syntax\\\\n      [-_]\\\\n      (?: webkit              # Webkit/Blink\\\\n        | apple|khtml         # Webkit aliases\\\\n        | epub                # ePub3\\\\n        | moz                 # Gecko\\\\n        | ms                  # Microsoft\\\\n        | o                   # Presto (pre-Opera 15)\\\\n        | xv|ah|rim|atsc|     # Less common vendors\\\\n          hp|tc|wap|ro\\\\n      )\\\\n      |\\\\n      # Non-standard prefixes\\\\n      (?: mso                 # Microsoft Office\\\\n        | prince              # YesLogic\\\\n      )\\\\n    )\\\\n    -\\\\n    [\\\\\\\\w-]+                   # Feature name\\\\n    (?=                       # Terminates correctly\\\\n      \\\\\\\\s*                    # Possible whitespace\\\\n      (?:                     # Possible injected comment\\\\n        /\\\\\\\\*\\\\n        (?:[^*]|\\\\\\\\*[^/])*\\\\n        \\\\\\\\*/\\\\n      )?\\\\n      \\\\\\\\s*\\\\n      [:)]                    # Ends with a colon or closed bracket\\\\n    )\\\\n  )\\\\n)\\\\n(?=\\\\\\\\s|$|[><:=]|\\\\\\\\)|/\\\\\\\\*)     # Terminates cleanly\\\"},\\\"media-query\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#media-types\\\"},{\\\"match\\\":\\\"(?i)(?<=\\\\\\\\s|^|,|\\\\\\\\*/)(only|not)(?=\\\\\\\\s|{|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.$1.media.css\\\"},{\\\"match\\\":\\\"(?i)(?<=\\\\\\\\s|^|\\\\\\\\*/|\\\\\\\\))and(?=\\\\\\\\s|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.and.media.css\\\"},{\\\"match\\\":\\\",(?:(?:\\\\\\\\s*,)+|(?=\\\\\\\\s*[;){]))\\\",\\\"name\\\":\\\"invalid.illegal.comma.css\\\"},{\\\"include\\\":\\\"#commas\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#media-features\\\"},{\\\"include\\\":\\\"#media-feature-keywords\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.css\\\"},{\\\"match\\\":\\\">=|<=|=|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.css\\\"}},\\\"match\\\":\\\"(\\\\\\\\d+)\\\\\\\\s*(/)\\\\\\\\s*(\\\\\\\\d+)\\\",\\\"name\\\":\\\"meta.ratio.css\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#comment-block\\\"}]}]},\\\"media-query-list\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*[^{;])\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query\\\"}]},\\\"media-types\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.constant.media.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.constant.media.css\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?<=^|\\\\\\\\s|,|\\\\\\\\*/)\\\\n(?:\\\\n  # Valid media types\\\\n  (all|print|screen|speech)\\\\n  |\\\\n  # Deprecated in Media Queries 4: http://dev.w3.org/csswg/mediaqueries/#media-types\\\\n  (aural|braille|embossed|handheld|projection|tty|tv)\\\\n)\\\\n(?=$|[{,\\\\\\\\s;]|/\\\\\\\\*)\\\"},\\\"numeric-values\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.css\\\"}},\\\"match\\\":\\\"(#)(?:[0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.color.rgb-value.hex.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.percentage.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.unit.${2:/downcase}.css\\\"}},\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n[-+]?                               # Sign indicator\\\\n\\\\n(?:                                 # Numerals\\\\n    [0-9]+ (?:\\\\\\\\.[0-9]+)?           # Integer/float with leading digits\\\\n  | \\\\\\\\.[0-9]+                       # Float without leading digits\\\\n)\\\\n\\\\n(?:                                 # Scientific notation\\\\n  (?<=[0-9])                        # Exponent must follow a digit\\\\n  E                                 # Exponent indicator\\\\n  [-+]?                             # Possible sign indicator\\\\n  [0-9]+                            # Exponent value\\\\n)?\\\\n\\\\n(?:                                 # Possible unit for data-type:\\\\n  (%)                               # - Percentage\\\\n  | ( deg|grad|rad|turn             # - Angle\\\\n    | Hz|kHz                        # - Frequency\\\\n    | ch|cm|em|ex|fr|in|mm|mozmm|   # - Length\\\\n      pc|pt|px|q|rem|rch|rex|rlh|\\\\n      ic|ric|rcap|vh|vw|vb|vi|svh|\\\\n      svw|svb|svi|dvh|dvw|dvb|dvi|\\\\n      lvh|lvw|lvb|lvi|vmax|vmin|\\\\n      cqw|cqi|cqh|cqb|cqmin|cqmax\\\\n    | dpi|dpcm|dppx                 # - Resolution\\\\n    | s|ms                          # - Time\\\\n    )\\\\n  \\\\\\\\b                               # Boundary checking intentionally lax to\\\\n)?                                  # facilitate embedding in CSS-like grammars\\\",\\\"name\\\":\\\"constant.numeric.css\\\"}]},\\\"property-keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(above|absolute|active|add|additive|after-edge|alias|all|all-petite-caps|all-scroll|all-small-caps|alpha|alphabetic|alternate|alternate-reverse\\\\n|always|antialiased|auto|auto-fill|auto-fit|auto-pos|available|avoid|avoid-column|avoid-page|avoid-region|backwards|balance|baseline|before-edge|below|bevel\\\\n|bidi-override|blink|block|block-axis|block-start|block-end|bold|bolder|border|border-box|both|bottom|bottom-outside|break-all|break-word|bullets\\\\n|butt|capitalize|caption|cell|center|central|char|circle|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color|color-burn\\\\n|color-dodge|column|column-reverse|common-ligatures|compact|condensed|contain|content|content-box|contents|context-menu|contextual|copy|cover\\\\n|crisp-edges|crispEdges|crosshair|cyclic|dark|darken|dashed|decimal|default|dense|diagonal-fractions|difference|digits|disabled|disc|discretionary-ligatures\\\\n|distribute|distribute-all-lines|distribute-letter|distribute-space|dot|dotted|double|double-circle|downleft|downright|e-resize|each-line|ease|ease-in\\\\n|ease-in-out|ease-out|economy|ellipse|ellipsis|embed|end|evenodd|ew-resize|exact|exclude|exclusion|expanded|extends|extra-condensed|extra-expanded\\\\n|fallback|farthest-corner|farthest-side|fill|fill-available|fill-box|filled|fit-content|fixed|flat|flex|flex-end|flex-start|flip|flow-root|forwards|freeze\\\\n|from-image|full-width|geometricPrecision|georgian|grab|grabbing|grayscale|grid|groove|hand|hanging|hard-light|help|hidden|hide\\\\n|historical-forms|historical-ligatures|horizontal|horizontal-tb|hue|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space\\\\n|ideographic|inactive|infinite|inherit|initial|inline|inline-axis|inline-block|inline-end|inline-flex|inline-grid|inline-list-item|inline-start\\\\n|inline-table|inset|inside|inter-character|inter-ideograph|inter-word|intersect|invert|isolate|isolate-override|italic|jis04|jis78|jis83\\\\n|jis90|justify|justify-all|kannada|keep-all|landscape|large|larger|left|light|lighten|lighter|line|line-edge|line-through|linear|linearRGB\\\\n|lining-nums|list-item|local|loose|lowercase|lr|lr-tb|ltr|luminance|luminosity|main-size|mandatory|manipulation|manual|margin-box|match-parent\\\\n|match-source|mathematical|max-content|medium|menu|message-box|middle|min-content|miter|mixed|move|multiply|n-resize|narrower|ne-resize\\\\n|nearest-neighbor|nesw-resize|newspaper|no-change|no-clip|no-close-quote|no-common-ligatures|no-contextual|no-discretionary-ligatures\\\\n|no-drop|no-historical-ligatures|no-open-quote|no-repeat|none|nonzero|normal|not-allowed|nowrap|ns-resize|numbers|numeric|nw-resize|nwse-resize\\\\n|oblique|oldstyle-nums|open|open-quote|optimizeLegibility|optimizeQuality|optimizeSpeed|optional|ordinal|outset|outside|over|overlay|overline|padding\\\\n|padding-box|page|painted|pan-down|pan-left|pan-right|pan-up|pan-x|pan-y|paused|petite-caps|pixelated|plaintext|pointer|portrait|pre|pre-line\\\\n|pre-wrap|preserve-3d|progress|progressive|proportional-nums|proportional-width|proximity|radial|recto|region|relative|remove|repeat|repeat-[xy]\\\\n|reset-size|reverse|revert|ridge|right|rl|rl-tb|round|row|row-resize|row-reverse|row-severse|rtl|ruby|ruby-base|ruby-base-container|ruby-text\\\\n|ruby-text-container|run-in|running|s-resize|saturation|scale-down|screen|scroll|scroll-position|se-resize|semi-condensed|semi-expanded|separate\\\\n|sesame|show|sideways|sideways-left|sideways-lr|sideways-right|sideways-rl|simplified|slashed-zero|slice|small|small-caps|small-caption|smaller\\\\n|smooth|soft-light|solid|space|space-around|space-between|space-evenly|spell-out|square|sRGB|stacked-fractions|start|static|status-bar|swap\\\\n|step-end|step-start|sticky|stretch|strict|stroke|stroke-box|style|sub|subgrid|subpixel-antialiased|subtract|super|sw-resize|symbolic|table\\\\n|table-caption|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|tabular-nums|tb|tb-rl\\\\n|text|text-after-edge|text-before-edge|text-bottom|text-top|thick|thin|titling-caps|top|top-outside|touch|traditional|transparent|triangle\\\\n|ultra-condensed|ultra-expanded|under|underline|unicase|unset|upleft|uppercase|upright|use-glyph-orientation|use-script|verso|vertical\\\\n|vertical-ideographic|vertical-lr|vertical-rl|vertical-text|view-box|visible|visibleFill|visiblePainted|visibleStroke|w-resize|wait|wavy\\\\n|weight|whitespace|wider|words|wrap|wrap-reverse|x|x-large|x-small|xx-large|xx-small|y|zero|zoom-in|zoom-out)\\\\n(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.property-value.css\\\"},{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|cjk-ideographic\\\\n|decimal|decimal-leading-zero|devanagari|disc|disclosure-closed|disclosure-open|ethiopic-halehame-am\\\\n|ethiopic-halehame-ti-e[rt]|ethiopic-numeric|georgian|gujarati|gurmukhi|hangul|hangul-consonant|hebrew\\\\n|hiragana|hiragana-iroha|japanese-formal|japanese-informal|kannada|katakana|katakana-iroha|khmer\\\\n|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek\\\\n|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal\\\\n|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian\\\\n|upper-latin|upper-roman|urdu)\\\\n(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.property-value.list-style-type.css\\\"},{\\\"match\\\":\\\"(?<![\\\\\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\\\",\\\"name\\\":\\\"support.constant.vendored.property-value.css\\\"},{\\\"match\\\":\\\"(?<![\\\\\\\\w-])(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system-ui|system|tahoma|times|trebuchet|ui-monospace|ui-rounded|ui-sans-serif|ui-serif|utopia|verdana|webdings|sans-serif|serif|monospace)(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.constant.font-name.css\\\"}]},\\\"property-names\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w-])\\\\n(?:\\\\n  # Standard CSS\\\\n  accent-color|additive-symbols|align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration\\\\n  | animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|aspect-ratio|backdrop-filter\\\\n  | backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image\\\\n  | background-origin|background-position|background-position-[xy]|background-repeat|background-size|bleed|block-size|border\\\\n  | border-block-end|border-block-end-color|border-block-end-style|border-block-end-width|border-block-start|border-block-start-color\\\\n  | border-block-start-style|border-block-start-width|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius\\\\n  | border-bottom-style|border-bottom-width|border-collapse|border-color|border-end-end-radius|border-end-start-radius|border-image\\\\n  | border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-inline-end\\\\n  | border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-start|border-inline-start-color\\\\n  | border-inline-start-style|border-inline-start-width|border-left|border-left-color|border-left-style|border-left-width\\\\n  | border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-start-end-radius\\\\n  | border-start-start-radius|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style\\\\n  | border-top-width|border-width|bottom|box-decoration-break|box-shadow|box-sizing|break-after|break-before|break-inside|caption-side\\\\n  | caret-color|clear|clip|clip-path|clip-rule|color|color-adjust|color-interpolation-filters|color-scheme|column-count|column-fill|column-gap\\\\n  | column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|contain|container|container-name|container-type|content|counter-increment\\\\n  | counter-reset|cursor|direction|display|empty-cells|enable-background|fallback|fill|fill-opacity|fill-rule|filter|flex|flex-basis\\\\n  | flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|flood-color|flood-opacity|font|font-display|font-family\\\\n  | font-feature-settings|font-kerning|font-language-override|font-optical-sizing|font-size|font-size-adjust|font-stretch\\\\n  | font-style|font-synthesis|font-variant|font-variant-alternates|font-variant-caps|font-variant-east-asian|font-variant-ligatures\\\\n  | font-variant-numeric|font-variant-position|font-variation-settings|font-weight|gap|glyph-orientation-horizontal|glyph-orientation-vertical\\\\n  | grid|grid-area|grid-auto-columns|grid-auto-flow|grid-auto-rows|grid-column|grid-column-end|grid-column-gap|grid-column-start\\\\n  | grid-gap|grid-row|grid-row-end|grid-row-gap|grid-row-start|grid-template|grid-template-areas|grid-template-columns|grid-template-rows\\\\n  | hanging-punctuation|height|hyphens|image-orientation|image-rendering|image-resolution|ime-mode|initial-letter|initial-letter-align\\\\n  | inline-size|inset|inset-block|inset-block-end|inset-block-start|inset-inline|inset-inline-end|inset-inline-start|isolation\\\\n  | justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-break|line-clamp|line-height|list-style\\\\n  | list-style-image|list-style-position|list-style-type|margin|margin-block|margin-block-end|margin-block-start|margin-bottom|margin-inline|margin-inline-end|margin-inline-start\\\\n  | margin-left|margin-right|margin-top|marker-end|marker-mid|marker-start|marks|mask|mask-border|mask-border-mode|mask-border-outset\\\\n  | mask-border-repeat|mask-border-slice|mask-border-source|mask-border-width|mask-clip|mask-composite|mask-image|mask-mode\\\\n  | mask-origin|mask-position|mask-repeat|mask-size|mask-type|max-block-size|max-height|max-inline-size|max-lines|max-width\\\\n  | max-zoom|min-block-size|min-height|min-inline-size|min-width|min-zoom|mix-blend-mode|negative|object-fit|object-position\\\\n  | offset|offset-anchor|offset-distance|offset-path|offset-position|offset-rotation|opacity|order|orientation|orphans\\\\n  | outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-anchor|overflow-block|overflow-inline\\\\n  | overflow-wrap|overflow-[xy]|overscroll-behavior|overscroll-behavior-block|overscroll-behavior-inline|overscroll-behavior-[xy]\\\\n  | pad|padding|padding-block|padding-block-end|padding-block-start|padding-bottom|padding-inline|padding-inline-end|padding-inline-start|padding-left\\\\n  | padding-right|padding-top|page-break-after|page-break-before|page-break-inside|paint-order|perspective|perspective-origin\\\\n  | place-content|place-items|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|row-gap|ruby-align\\\\n  | ruby-merge|ruby-position|scale|scroll-behavior|scroll-margin|scroll-margin-block|scroll-margin-block-end|scroll-margin-block-start\\\\n  | scroll-margin-bottom|scroll-margin-inline|scroll-margin-inline-end|scroll-margin-inline-start|scroll-margin-left|scroll-margin-right\\\\n  | scroll-margin-top|scroll-padding|scroll-padding-block|scroll-padding-block-end|scroll-padding-block-start|scroll-padding-bottom\\\\n  | scroll-padding-inline|scroll-padding-inline-end|scroll-padding-inline-start|scroll-padding-left|scroll-padding-right\\\\n  | scroll-padding-top|scroll-snap-align|scroll-snap-coordinate|scroll-snap-destination|scroll-snap-stop|scroll-snap-type\\\\n  | scrollbar-color|scrollbar-gutter|scrollbar-width|shape-image-threshold|shape-margin|shape-outside|shape-rendering|size\\\\n  | speak-as|src|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit\\\\n  | stroke-opacity|stroke-width|suffix|symbols|system|tab-size|table-layout|text-align|text-align-last|text-anchor|text-combine-upright\\\\n  | text-decoration|text-decoration-color|text-decoration-line|text-decoration-skip|text-decoration-skip-ink|text-decoration-style|text-decoration-thickness\\\\n  | text-emphasis|text-emphasis-color|text-emphasis-position|text-emphasis-style|text-indent|text-justify|text-orientation\\\\n  | text-overflow|text-rendering|text-shadow|text-size-adjust|text-transform|text-underline-offset|text-underline-position|top|touch-action|transform\\\\n  | transform-box|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function\\\\n  | translate|unicode-bidi|unicode-range|user-select|user-zoom|vertical-align|visibility|white-space|widows|width|will-change\\\\n  | word-break|word-spacing|word-wrap|writing-mode|z-index|zoom\\\\n\\\\n  # SVG attributes\\\\n  | alignment-baseline|baseline-shift|clip-rule|color-interpolation|color-interpolation-filters|color-profile\\\\n  | color-rendering|cx|cy|dominant-baseline|enable-background|fill|fill-opacity|fill-rule|flood-color|flood-opacity\\\\n  | glyph-orientation-horizontal|glyph-orientation-vertical|height|kerning|lighting-color|marker-end|marker-mid\\\\n  | marker-start|r|rx|ry|shape-rendering|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap\\\\n  | stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|text-anchor|width|x|y\\\\n\\\\n  # Not listed on MDN; presumably deprecated\\\\n  | adjust|after|align|align-last|alignment|alignment-adjust|appearance|attachment|azimuth|background-break\\\\n  | balance|baseline|before|bidi|binding|bookmark|bookmark-label|bookmark-level|bookmark-target|border-length\\\\n  | bottom-color|bottom-left-radius|bottom-right-radius|bottom-style|bottom-width|box|box-align|box-direction\\\\n  | box-flex|box-flex-group|box-lines|box-ordinal-group|box-orient|box-pack|break|character|collapse|column\\\\n  | column-break-after|column-break-before|count|counter|crop|cue|cue-after|cue-before|decoration|decoration-break\\\\n  | delay|display-model|display-role|down|drop|drop-initial-after-adjust|drop-initial-after-align|drop-initial-before-adjust\\\\n  | drop-initial-before-align|drop-initial-size|drop-initial-value|duration|elevation|emphasis|family|fit|fit-position\\\\n  | flex-group|float-offset|gap|grid-columns|grid-rows|hanging-punctuation|header|hyphenate|hyphenate-after|hyphenate-before\\\\n  | hyphenate-character|hyphenate-lines|hyphenate-resource|icon|image|increment|indent|index|initial-after-adjust\\\\n  | initial-after-align|initial-before-adjust|initial-before-align|initial-size|initial-value|inline-box-align|iteration-count\\\\n  | justify|label|left-color|left-style|left-width|length|level|line|line-stacking|line-stacking-ruby|line-stacking-shift\\\\n  | line-stacking-strategy|lines|list|mark|mark-after|mark-before|marks|marquee|marquee-direction|marquee-play-count|marquee-speed\\\\n  | marquee-style|max|min|model|move-to|name|nav|nav-down|nav-index|nav-left|nav-right|nav-up|new|numeral|offset|ordinal-group\\\\n  | orient|origin|overflow-style|overhang|pack|page|page-policy|pause|pause-after|pause-before|phonemes|pitch|pitch-range\\\\n  | play-count|play-during|play-state|point|presentation|presentation-level|profile|property|punctuation|punctuation-trim\\\\n  | radius|rate|rendering-intent|repeat|replace|reset|resolution|resource|respond-to|rest|rest-after|rest-before|richness\\\\n  | right-color|right-style|right-width|role|rotation|rotation-point|rows|ruby|ruby-overhang|ruby-span|rule|rule-color\\\\n  | rule-style|rule-width|shadow|size|size-adjust|sizing|space|space-collapse|spacing|span|speak|speak-header|speak-numeral\\\\n  | speak-punctuation|speech|speech-rate|speed|stacking|stacking-ruby|stacking-shift|stacking-strategy|stress|stretch\\\\n  | string-set|style|style-image|style-position|style-type|target|target-name|target-new|target-position|text|text-height\\\\n  | text-justify|text-outline|text-replace|text-wrap|timing-function|top-color|top-left-radius|top-right-radius|top-style\\\\n  | top-width|trim|unicode|up|user-select|variant|voice|voice-balance|voice-duration|voice-family|voice-pitch|voice-pitch-range\\\\n  | voice-rate|voice-stress|voice-volume|volume|weight|white|white-space-collapse|word|wrap\\\\n)\\\\n(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"support.type.property-name.css\\\"},{\\\"match\\\":\\\"(?<![\\\\\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\\\",\\\"name\\\":\\\"support.type.vendored.property-name.css\\\"}]},\\\"property-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#commas\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#property-keywords\\\"},{\\\"include\\\":\\\"#unicode-range\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#color-keywords\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"match\\\":\\\"!\\\\\\\\s*important(?![\\\\\\\\w-])\\\",\\\"name\\\":\\\"keyword.other.important.css\\\"}]},\\\"pseudo-classes\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.colon.css\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(:)(:*)\\\\n(?: active|any-link|checked|default|disabled|empty|enabled|first\\\\n  | (?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within|fullscreen|host|hover\\\\n  | in-range|indeterminate|invalid|left|link|optional|out-of-range\\\\n  | read-only|read-write|required|right|root|scope|target|unresolved\\\\n  | valid|visited\\\\n)(?![\\\\\\\\w-]|\\\\\\\\s*[;}])\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"pseudo-elements\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(?:\\\\n  (::?)                       # Elements using both : and :: notation\\\\n  (?: after\\\\n    | before\\\\n    | first-letter\\\\n    | first-line\\\\n    | (?:-(?:ah|apple|atsc|epub|hp|khtml|moz\\\\n            |ms|o|rim|ro|tc|wap|webkit|xv)\\\\n        | (?:mso|prince))\\\\n      -[a-z-]+\\\\n  )\\\\n  |\\\\n  (::)                        # Double-colon only\\\\n  (?: backdrop\\\\n    | content\\\\n    | grammar-error\\\\n    | marker\\\\n    | placeholder\\\\n    | selection\\\\n    | shadow\\\\n    | spelling-error\\\\n  )\\\\n)\\\\n(?![\\\\\\\\w-]|\\\\\\\\s*[;}])\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.css\\\"},\\\"rule-list\\\":{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.bracket.curly.css\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.end.bracket.curly.css\\\"}},\\\"name\\\":\\\"meta.property-list.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-innards\\\"}]},\\\"rule-list-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#font-features\\\"},{\\\"match\\\":\\\"(?<![\\\\\\\\w-])--(?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\\\",\\\"name\\\":\\\"variable.css\\\"},{\\\"begin\\\":\\\"(?<![-a-zA-Z])(?=[-a-zA-Z])\\\",\\\"end\\\":\\\"$|(?![-a-zA-Z])\\\",\\\"name\\\":\\\"meta.property-name.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-names\\\"}]},{\\\"begin\\\":\\\"(:)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.css\\\"}},\\\"contentName\\\":\\\"meta.property-value.css\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|\\\\\\\\s*(?=}|\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}]},\\\"selector\\\":{\\\"begin\\\":\\\"(?=(?:\\\\\\\\|)?(?:[-\\\\\\\\[:.*\\\\\\\\#a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)))\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*[/@{)])\\\",\\\"name\\\":\\\"meta.selector.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selector-innards\\\"}]},\\\"selector-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#commas\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"include\\\":\\\"#combinators\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.namespace-prefix.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.css\\\"}},\\\"match\\\":\\\"(?:^|(?<=[\\\\\\\\s,(};]))(?![-\\\\\\\\w*]+\\\\\\\\|(?![-\\\\\\\\[:.*\\\\\\\\#a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F]))((?:[-a-zA-Z_]|[^\\\\\\\\x00-\\\\\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*|\\\\\\\\*)?(\\\\\\\\|)\\\"},{\\\"include\\\":\\\"#tag-names\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"entity.name.tag.wildcard.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?<![@\\\\\\\\w-])([.\\\\\\\\#])((?:-?[0-9]|-(?=$|[\\\\\\\\s,.\\\\\\\\#)\\\\\\\\[:{>+~|]|/\\\\\\\\*)|(?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?:[!\\\\\\\"'%&(*;<?@^`|\\\\\\\\]}]|/(?!\\\\\\\\*))+)(?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*)\\\",\\\"name\\\":\\\"invalid.illegal.bad-identifier.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\.)((?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\\\\\s,.\\\\\\\\#)\\\\\\\\[:{>+~|]|/\\\\\\\\*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.class.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\#)(-?(?![0-9])(?:[-a-zA-Z0-9_]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\\\\\s,.\\\\\\\\#)\\\\\\\\[:{>+~|]|/\\\\\\\\*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.id.css\\\"},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.entity.begin.bracket.square.css\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.entity.end.bracket.square.css\\\"}},\\\"name\\\":\\\"meta.attribute-selector.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.modifier.ignore-case.css\\\"}},\\\"match\\\":\\\"(?<=[\\\\\\\"'\\\\\\\\s]|^|\\\\\\\\*/)\\\\\\\\s*([iI])\\\\\\\\s*(?=[\\\\\\\\s\\\\\\\\]]|/\\\\\\\\*|$)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(?<==)\\\\\\\\s*((?!/\\\\\\\\*)(?:[^\\\\\\\\\\\\\\\\\\\\\\\"'\\\\\\\\s\\\\\\\\]]|\\\\\\\\\\\\\\\\.)+)\\\"},{\\\"include\\\":\\\"#escapes\\\"},{\\\"match\\\":\\\"[~|^$*]?=\\\",\\\"name\\\":\\\"keyword.operator.pattern.css\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"punctuation.separator.css\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.namespace-prefix.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(-?(?!\\\\\\\\d)(?:[\\\\\\\\w-]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+|\\\\\\\\*)(?=\\\\\\\\|(?!\\\\\\\\s|=|$|\\\\\\\\])(?:-?(?!\\\\\\\\d)|[\\\\\\\\\\\\\\\\\\\\\\\\w-]|[^\\\\\\\\x00-\\\\\\\\x7F]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"match\\\":\\\"(-?(?!\\\\\\\\d)(?>[\\\\\\\\w-]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)\\\\\\\\s*(?=[~|^\\\\\\\\]$*=]|/\\\\\\\\*)\\\"}]},{\\\"include\\\":\\\"#pseudo-classes\\\"},{\\\"include\\\":\\\"#pseudo-elements\\\"},{\\\"include\\\":\\\"#functional-pseudo-classes\\\"},{\\\"match\\\":\\\"(?<![@\\\\\\\\w-])(?=[a-z]\\\\\\\\w*-)(?:(?![A-Z])[\\\\\\\\w-])+(?![(\\\\\\\\w-])\\\",\\\"name\\\":\\\"entity.name.tag.custom.css\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.css\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=$|\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.css\\\"}},\\\"name\\\":\\\"string.quoted.double.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\G|^)(?=(?:[^\\\\\\\\\\\\\\\\\\\\\\\"]|\\\\\\\\\\\\\\\\.)+$)\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"invalid.illegal.unclosed.string.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.css\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=$|\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.css\\\"}},\\\"name\\\":\\\"string.quoted.single.css\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\G|^)(?=(?:[^\\\\\\\\\\\\\\\\']|\\\\\\\\\\\\\\\\.)+$)\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"invalid.illegal.unclosed.string.css\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escapes\\\"}]},{\\\"include\\\":\\\"#escapes\\\"}]}]},\\\"tag-names\\\":{\\\"match\\\":\\\"(?xi) (?<![\\\\\\\\w:-])\\\\n(?:\\\\n    # HTML\\\\n    a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|bgsound\\\\n  | big|blink|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command\\\\n  | content|data|datalist|dd|del|details|dfn|dialog|dir|div|dl|dt|element|em|embed|fieldset\\\\n  | figcaption|figure|font|footer|form|frame|frameset|h[1-6]|head|header|hgroup|hr|html|i\\\\n  | iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|link|listing|main|map|mark\\\\n  | marquee|math|menu|menuitem|meta|meter|multicol|nav|nextid|nobr|noembed|noframes|noscript\\\\n  | object|ol|optgroup|option|output|p|param|picture|plaintext|pre|progress|q|rb|rp|rt|rtc\\\\n  | ruby|s|samp|script|section|select|shadow|slot|small|source|spacer|span|strike|strong\\\\n  | style|sub|summary|sup|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr\\\\n  | track|tt|u|ul|var|video|wbr|xmp\\\\n\\\\n  # SVG\\\\n  | altGlyph|altGlyphDef|altGlyphItem|animate|animateColor|animateMotion|animateTransform\\\\n  | circle|clipPath|color-profile|cursor|defs|desc|discard|ellipse|feBlend|feColorMatrix\\\\n  | feComponentTransfer|feComposite|feConvolveMatrix|feDiffuseLighting|feDisplacementMap\\\\n  | feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur\\\\n  | feImage|feMerge|feMergeNode|feMorphology|feOffset|fePointLight|feSpecularLighting\\\\n  | feSpotLight|feTile|feTurbulence|filter|font-face|font-face-format|font-face-name\\\\n  | font-face-src|font-face-uri|foreignObject|g|glyph|glyphRef|hatch|hatchpath|hkern\\\\n  | line|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|metadata\\\\n  | missing-glyph|mpath|path|pattern|polygon|polyline|radialGradient|rect|set|solidcolor\\\\n  | stop|svg|switch|symbol|text|textPath|tref|tspan|use|view|vkern\\\\n\\\\n  # MathML\\\\n  | annotation|annotation-xml|maction|maligngroup|malignmark|math|menclose|merror|mfenced\\\\n  | mfrac|mglyph|mi|mlabeledtr|mlongdiv|mmultiscripts|mn|mo|mover|mpadded|mphantom|mroot\\\\n  | mrow|ms|mscarries|mscarry|msgroup|msline|mspace|msqrt|msrow|mstack|mstyle|msub|msubsup\\\\n  | msup|mtable|mtd|mtext|mtr|munder|munderover|semantics\\\\n)\\\\n(?=[+~>\\\\\\\\s,.\\\\\\\\#|){:\\\\\\\\[]|/\\\\\\\\*|$)\\\",\\\"name\\\":\\\"entity.name.tag.css\\\"},\\\"unicode-range\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"constant.other.unicode-range.css\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dash.unicode-range.css\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w-])[Uu]\\\\\\\\+[0-9A-Fa-f?]{1,6}(?:(-)[0-9A-Fa-f]{1,6})?(?![\\\\\\\\w-])\\\"},\\\"url\\\":{\\\"begin\\\":\\\"(?i)(?<![\\\\\\\\w@-])(url)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.url.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.function.end.bracket.round.css\\\"}},\\\"name\\\":\\\"meta.function.url.css\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[^'\\\\\\\")\\\\\\\\s]+\\\",\\\"name\\\":\\\"variable.parameter.url.css\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#escapes\\\"}]}},\\\"scopeName\\\":\\\"source.css\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/css.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/scss.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/scss.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _css_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./css.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/css.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SCSS\\\",\\\"name\\\":\\\"scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable_setting\\\"},{\\\"include\\\":\\\"#at_rule_forward\\\"},{\\\"include\\\":\\\"#at_rule_use\\\"},{\\\"include\\\":\\\"#at_rule_include\\\"},{\\\"include\\\":\\\"#at_rule_import\\\"},{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#flow_control\\\"},{\\\"include\\\":\\\"#rules\\\"},{\\\"include\\\":\\\"#property_list\\\"},{\\\"include\\\":\\\"#at_rule_mixin\\\"},{\\\"include\\\":\\\"#at_rule_media\\\"},{\\\"include\\\":\\\"#at_rule_function\\\"},{\\\"include\\\":\\\"#at_rule_charset\\\"},{\\\"include\\\":\\\"#at_rule_option\\\"},{\\\"include\\\":\\\"#at_rule_namespace\\\"},{\\\"include\\\":\\\"#at_rule_fontface\\\"},{\\\"include\\\":\\\"#at_rule_page\\\"},{\\\"include\\\":\\\"#at_rule_keyframes\\\"},{\\\"include\\\":\\\"#at_rule_at_root\\\"},{\\\"include\\\":\\\"#at_rule_supports\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.rule.css\\\"}],\\\"repository\\\":{\\\"at_rule_at_root\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)(at-root))(\\\\\\\\s+|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.at-root.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.at-root.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#selectors\\\"}]},\\\"at_rule_charset\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)charset\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.charset.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;|$))\\\",\\\"name\\\":\\\"meta.at-rule.charset.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"}]},\\\"at_rule_content\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)content\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.content.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;))\\\",\\\"name\\\":\\\"meta.content.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"at_rule_each\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)each\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.each.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=}))\\\",\\\"name\\\":\\\"meta.at-rule.each.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(in|,)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.operator\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"at_rule_else\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)else(\\\\\\\\s*(if)?))\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.else.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.else.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_operators\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"at_rule_extend\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)extend\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.extend.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.extend.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"at_rule_fontface\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((@)font-face\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.fontface.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.fontface.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]}]},\\\"at_rule_for\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)for\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.for.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.for.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(==|!=|<=|>=|<|>|from|to|through)\\\",\\\"name\\\":\\\"keyword.control.operator\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"at_rule_forward\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)forward\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.forward.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.forward.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(as|hide|show)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.operator\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.module.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.wildcard.scss\\\"}},\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\w-]+)(\\\\\\\\*)\\\"},{\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\w-]+\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.scss\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"},{\\\"include\\\":\\\"#comment_line\\\"},{\\\"include\\\":\\\"#comment_block\\\"}]},\\\"at_rule_function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*((@)function\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.function.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.function.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.function.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((@)function\\\\\\\\b)\\\\\\\\s*\\\",\\\"name\\\":\\\"meta.at-rule.function.scss\\\"}]},\\\"at_rule_if\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)if\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.if.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.if.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_operators\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"at_rule_import\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)import\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.import.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;)|(?=}))\\\",\\\"name\\\":\\\"meta.at-rule.import.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"},{\\\"include\\\":\\\"#functions\\\"},{\\\"include\\\":\\\"#comment_line\\\"}]},\\\"at_rule_include\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=@include)\\\\\\\\s+(?:([\\\\\\\\w-]+)\\\\\\\\s*(\\\\\\\\.))?([\\\\\\\\w-]+)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.access.module.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.bracket.round.scss\\\"}},\\\"name\\\":\\\"meta.at-rule.include.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.at-rule.include.scss\\\"},\\\"1\\\":{\\\"name\\\":\\\"variable.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.access.module.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"match\\\":\\\"(?<=@include)\\\\\\\\s+(?:([\\\\\\\\w-]+)\\\\\\\\s*(\\\\\\\\.))?([\\\\\\\\w-]+)\\\"},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.at-rule.include.scss\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.include.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"match\\\":\\\"((@)include)\\\\\\\\b\\\"}]},\\\"at_rule_keyframes\\\":{\\\"begin\\\":\\\"(?<=^|\\\\\\\\s)(@)(?:-(?:webkit|moz)-)?keyframes\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.keyframes.scss\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"(?<=})\\\",\\\"name\\\":\\\"meta.at-rule.keyframes.scss\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"match\\\":\\\"(?<=@keyframes)\\\\\\\\s+((?:[_A-Za-z][-\\\\\\\\w]|-[_A-Za-z])[-\\\\\\\\w]*)\\\"},{\\\"begin\\\":\\\"(?<=@keyframes)\\\\\\\\s+(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"}},\\\"contentName\\\":\\\"entity.name.function.scss\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"}},\\\"name\\\":\\\"string.quoted.double.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},{\\\"begin\\\":\\\"(?<=@keyframes)\\\\\\\\s+(')\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"}},\\\"contentName\\\":\\\"entity.name.function.scss\\\",\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"}},\\\"name\\\":\\\"string.quoted.single.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.keyframes.begin.scss\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.keyframes.end.scss\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?:(?:100|[1-9]\\\\\\\\d|\\\\\\\\d)%|from|to)(?=\\\\\\\\s*{)\\\",\\\"name\\\":\\\"entity.other.attribute-name.scss\\\"},{\\\"include\\\":\\\"#flow_control\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#property_list\\\"},{\\\"include\\\":\\\"#rules\\\"}]}]},\\\"at_rule_media\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((@)media)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.media.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.media.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_docblock\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"include\\\":\\\"#comment_line\\\"},{\\\"match\\\":\\\"\\\\\\\\b(only)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.operator.css.scss\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.media-query.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.media-query.end.bracket.round.scss\\\"}},\\\"name\\\":\\\"meta.property-list.media-query.scss\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![-a-z])(?=[-a-z])\\\",\\\"end\\\":\\\"$|(?![-a-z])\\\",\\\"name\\\":\\\"meta.property-name.media-query.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css#media-features\\\"},{\\\"include\\\":\\\"source.css#property-names\\\"}]},{\\\"begin\\\":\\\"(:)\\\\\\\\s*(?!(\\\\\\\\s*{))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.scss\\\"}},\\\"contentName\\\":\\\"meta.property-value.media-query.scss\\\",\\\"end\\\":\\\"\\\\\\\\s*(;|(?=}|\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#property_values\\\"}]}]},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#conditional_operators\\\"},{\\\"include\\\":\\\"source.css#media-types\\\"}]}]},\\\"at_rule_mixin\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=@mixin)\\\\\\\\s+([\\\\\\\\w-]+)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.bracket.round.scss\\\"}},\\\"name\\\":\\\"meta.at-rule.mixin.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"match\\\":\\\"(?<=@mixin)\\\\\\\\s+([\\\\\\\\w-]+)\\\",\\\"name\\\":\\\"meta.at-rule.mixin.scss\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.mixin.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"match\\\":\\\"((@)mixin)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.at-rule.mixin.scss\\\"}]},\\\"at_rule_namespace\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=@namespace)\\\\\\\\s+(?=url)\\\",\\\"end\\\":\\\"(?=;|$)\\\",\\\"name\\\":\\\"meta.at-rule.namespace.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"}]},{\\\"begin\\\":\\\"(?<=@namespace)\\\\\\\\s+([\\\\\\\\w-]*)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.namespace-prefix.scss\\\"}},\\\"end\\\":\\\"(?=;|$)\\\",\\\"name\\\":\\\"meta.at-rule.namespace.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.namespace.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"match\\\":\\\"((@)namespace)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.at-rule.namespace.scss\\\"}]},\\\"at_rule_option\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.charset.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*((@)option\\\\\\\\b)\\\\\\\\s*\\\",\\\"name\\\":\\\"meta.at-rule.option.scss\\\"},\\\"at_rule_page\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((@)page)(?=:|\\\\\\\\s)\\\\\\\\s*([-:\\\\\\\\w]*)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.page.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?={)\\\",\\\"name\\\":\\\"meta.at-rule.page.scss\\\"}]},\\\"at_rule_return\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)(return)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.return.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;))\\\",\\\"name\\\":\\\"meta.at-rule.return.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"at_rule_supports\\\":{\\\"begin\\\":\\\"(?<=^|\\\\\\\\s)(@)supports\\\\\\\\b\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.at-rule.supports.scss\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"(?={)|$\\\",\\\"name\\\":\\\"meta.at-rule.supports.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#logical_operators\\\"},{\\\"include\\\":\\\"#properties\\\"},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.definition.condition.begin.bracket.round.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.definition.condition.end.bracket.round.scss\\\"}]},\\\"at_rule_use\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)use\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.use.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.use.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(as|with)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.operator\\\"},{\\\"match\\\":\\\"\\\\\\\\b[\\\\\\\\w-]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"variable.language.expanded-namespace.scss\\\"},{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"},{\\\"include\\\":\\\"#comment_line\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.bracket.round.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]}]},\\\"at_rule_warn\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)(warn|debug|error)\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.warn.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.warn.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#string_double\\\"},{\\\"include\\\":\\\"#string_single\\\"}]},\\\"at_rule_while\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)while\\\\\\\\b)\\\\\\\\s*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.while.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=})\\\",\\\"name\\\":\\\"meta.at-rule.while.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#conditional_operators\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"comment_block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scss\\\"}},\\\"name\\\":\\\"comment.block.scss\\\"},\\\"comment_docblock\\\":{\\\"begin\\\":\\\"///\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scss\\\"}},\\\"end\\\":\\\"(?=$)\\\",\\\"name\\\":\\\"comment.block.documentation.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sassdoc\\\"}]},\\\"comment_line\\\":{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.scss\\\"},\\\"comparison_operators\\\":{\\\"match\\\":\\\"==|!=|<=|>=|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.scss\\\"},\\\"conditional_operators\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comparison_operators\\\"},{\\\"include\\\":\\\"#logical_operators\\\"}]},\\\"constant_default\\\":{\\\"match\\\":\\\"!default\\\",\\\"name\\\":\\\"keyword.other.default.scss\\\"},\\\"constant_functions\\\":{\\\"begin\\\":\\\"(?:([\\\\\\\\w-]+)(\\\\\\\\.))?([\\\\\\\\w-]+)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.access.module.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.function.misc.scss\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"}]},\\\"constant_important\\\":{\\\"match\\\":\\\"!important\\\",\\\"name\\\":\\\"keyword.other.important.scss\\\"},\\\"constant_mathematical_symbols\\\":{\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\+|-|\\\\\\\\*|/)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.mathematical-symbols.scss\\\"},\\\"constant_optional\\\":{\\\"match\\\":\\\"!optional\\\",\\\"name\\\":\\\"keyword.other.optional.scss\\\"},\\\"constant_sass_functions\\\":{\\\"begin\\\":\\\"(headings|stylesheet-url|rgba?|hsla?|ie-hex-str|red|green|blue|alpha|opacity|hue|saturation|lightness|prefixed|prefix|-moz|-svg|-css2|-pie|-webkit|-ms|font-(?:files|url)|grid-image|image-(?:width|height|url|color)|sprites?|sprite-(?:map|map-name|file|url|position)|inline-(?:font-files|image)|opposite-position|grad-point|grad-end-position|color-stops|color-stops-in-percentages|grad-color-stops|(?:radial|linear)-(?:gradient|svg-gradient)|opacify|fade-?in|transparentize|fade-?out|lighten|darken|saturate|desaturate|grayscale|adjust-(?:hue|lightness|saturation|color)|scale-(?:lightness|saturation|color)|change-color|spin|complement|invert|mix|-compass-(?:list|space-list|slice|nth|list-size)|blank|compact|nth|first-value-of|join|length|append|nest|append-selector|headers|enumerate|range|percentage|unitless|unit|if|type-of|comparable|elements-of-type|quote|unquote|escape|e|sin|cos|tan|abs|round|ceil|floor|pi|translate(?:X|Y))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.misc.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"}]},\\\"flow_control\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#at_rule_if\\\"},{\\\"include\\\":\\\"#at_rule_else\\\"},{\\\"include\\\":\\\"#at_rule_warn\\\"},{\\\"include\\\":\\\"#at_rule_for\\\"},{\\\"include\\\":\\\"#at_rule_while\\\"},{\\\"include\\\":\\\"#at_rule_each\\\"},{\\\"include\\\":\\\"#at_rule_return\\\"}]},\\\"function_attributes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.scss\\\"},{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"match\\\":\\\"[={}\\\\\\\\?;@]\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},\\\"functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"([\\\\\\\\w-]{1,})(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.misc.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#parameters\\\"}]},{\\\"match\\\":\\\"([\\\\\\\\w-]{1,})\\\",\\\"name\\\":\\\"support.function.misc.scss\\\"}]},\\\"general\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#comment_docblock\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"include\\\":\\\"#comment_line\\\"}]},\\\"interpolation\\\":{\\\"begin\\\":\\\"#{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.interpolation.begin.bracket.curly.scss\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.interpolation.end.bracket.curly.scss\\\"}},\\\"name\\\":\\\"variable.interpolation.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"logical_operators\\\":{\\\"match\\\":\\\"\\\\\\\\b(not|or|and)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logical.scss\\\"},\\\"map\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.map.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.map.end.bracket.round.scss\\\"}},\\\"name\\\":\\\"meta.definition.variable.map.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_docblock\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"include\\\":\\\"#comment_line\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.map.key.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.scss\\\"}},\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\w-]+)\\\\\\\\s*(:)\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.scss\\\"},{\\\"include\\\":\\\"#map\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#property_values\\\"}]},\\\"operators\\\":{\\\"match\\\":\\\"[-+*/](?!\\\\\\\\s*[-+*/])\\\",\\\"name\\\":\\\"keyword.operator.css\\\"},\\\"parameters\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function_attributes\\\"}]},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"match\\\":\\\"[^'\\\\\\\",) \\\\\\\\t]+\\\",\\\"name\\\":\\\"variable.parameter.url.scss\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.scss\\\"}]},\\\"parent_selector_suffix\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.identifier.scss\\\"}]}},\\\"match\\\":\\\"(?<=&)((?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\\\\\#\\\\\\\\{|\\\\\\\\$|})+)(?=$|[\\\\\\\\s,.\\\\\\\\#)\\\\\\\\[:{>+~|]|/\\\\\\\\*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.parent-selector-suffix.css\\\"},\\\"properties\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<![-a-z])(?=[-a-z])\\\",\\\"end\\\":\\\"$|(?![-a-z])\\\",\\\"name\\\":\\\"meta.property-name.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css#property-names\\\"},{\\\"include\\\":\\\"#at_rule_include\\\"}]},{\\\"begin\\\":\\\"(:)\\\\\\\\s*(?!(\\\\\\\\s*{))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.scss\\\"}},\\\"contentName\\\":\\\"meta.property-value.scss\\\",\\\"end\\\":\\\"\\\\\\\\s*(;|(?=}|\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#property_values\\\"}]}]},\\\"property_list\\\":{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.bracket.curly.scss\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.end.bracket.curly.scss\\\"}},\\\"name\\\":\\\"meta.property-list.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#flow_control\\\"},{\\\"include\\\":\\\"#rules\\\"},{\\\"include\\\":\\\"#properties\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"property_values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string_single\\\"},{\\\"include\\\":\\\"#string_double\\\"},{\\\"include\\\":\\\"#constant_functions\\\"},{\\\"include\\\":\\\"#constant_sass_functions\\\"},{\\\"include\\\":\\\"#constant_important\\\"},{\\\"include\\\":\\\"#constant_default\\\"},{\\\"include\\\":\\\"#constant_optional\\\"},{\\\"include\\\":\\\"source.css#numeric-values\\\"},{\\\"include\\\":\\\"source.css#property-keywords\\\"},{\\\"include\\\":\\\"source.css#color-keywords\\\"},{\\\"include\\\":\\\"source.css#property-names\\\"},{\\\"include\\\":\\\"#constant_mathematical_symbols\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.round.scss\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.round.scss\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#property_values\\\"}]}]},\\\"rules\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#general\\\"},{\\\"include\\\":\\\"#at_rule_extend\\\"},{\\\"include\\\":\\\"#at_rule_content\\\"},{\\\"include\\\":\\\"#at_rule_include\\\"},{\\\"include\\\":\\\"#at_rule_media\\\"},{\\\"include\\\":\\\"#selectors\\\"}]},\\\"selector_attribute\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.attribute-selector.begin.bracket.square.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.attribute.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.scss\\\"},\\\"4\\\":{\\\"name\\\":\\\"string.unquoted.attribute-value.scss\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"string.quoted.double.attribute-value.scss\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"},\\\"7\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"},\\\"9\\\":{\\\"name\\\":\\\"string.quoted.single.attribute-value.scss\\\"},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"},\\\"11\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"},\\\"13\\\":{\\\"name\\\":\\\"punctuation.definition.attribute-selector.end.bracket.square.scss\\\"}},\\\"match\\\":\\\"(?xi)\\\\n(\\\\\\\\[)\\\\n\\\\\\\\s*\\\\n(\\\\n  (?:\\\\n    [-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]       # Valid identifier characters\\\\n    | \\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)       # Escape sequence\\\\n    | \\\\\\\\#\\\\\\\\{                           # Interpolation (escaped to avoid Coffeelint errors)\\\\n    | \\\\\\\\.?\\\\\\\\$                          # Possible start of interpolation variable\\\\n    | }                                # Possible end of interpolation\\\\n  )+?\\\\n)\\\\n(?:\\\\n  \\\\\\\\s*([~|^$*]?=)\\\\\\\\s*\\\\n  (?:\\\\n    (\\\\n      (?:\\\\n        [-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]       # Valid identifier characters\\\\n        | \\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)       # Escape sequence\\\\n        | \\\\\\\\#\\\\\\\\{                           # Interpolation (escaped to avoid Coffeelint errors)\\\\n        | \\\\\\\\.?\\\\\\\\$                          # Possible start of interpolation variable\\\\n        | }                                # Possible end of interpolation\\\\n      )+\\\\n    )\\\\n    |\\\\n    ((\\\\\\\")(.*?)(\\\\\\\"))\\\\n    |\\\\n    ((')(.*?)('))\\\\n  )\\\\n)?\\\\n\\\\\\\\s*\\\\n(\\\\\\\\])\\\",\\\"name\\\":\\\"meta.attribute-selector.scss\\\"},\\\"selector_class\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\.)((?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\\\\\#\\\\\\\\{|\\\\\\\\.?\\\\\\\\$|})+)(?=$|[\\\\\\\\s,\\\\\\\\#)\\\\\\\\[:{>+~|]|\\\\\\\\.[^$]|/\\\\\\\\*|;)\\\",\\\"name\\\":\\\"entity.other.attribute-name.class.css\\\"},\\\"selector_custom\\\":{\\\"match\\\":\\\"\\\\\\\\b([a-zA-Z0-9]+(-[a-zA-Z0-9]+)+)(?=\\\\\\\\.|\\\\\\\\s++[^:]|\\\\\\\\s*[,\\\\\\\\[{]|:(link|visited|hover|active|focus|target|lang|disabled|enabled|checked|indeterminate|root|nth-(child|last-child|of-type|last-of-type)|first-child|last-child|first-of-type|last-of-type|only-child|only-of-type|empty|not|valid|invalid)(\\\\\\\\([0-9A-Za-z]*\\\\\\\\))?)\\\",\\\"name\\\":\\\"entity.name.tag.custom.scss\\\"},\\\"selector_id\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.identifier.scss\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\#)((?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\\\\\#\\\\\\\\{|\\\\\\\\.?\\\\\\\\$|})+)(?=$|[\\\\\\\\s,\\\\\\\\#)\\\\\\\\[:{>+~|]|\\\\\\\\.[^$]|/\\\\\\\\*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.id.css\\\"},\\\"selector_placeholder\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-9a-fA-F]{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\$|}\\\",\\\"name\\\":\\\"invalid.illegal.identifier.scss\\\"}]}},\\\"match\\\":\\\"(%)((?:[-a-zA-Z_0-9]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)|\\\\\\\\#\\\\\\\\{|\\\\\\\\.\\\\\\\\$|\\\\\\\\$|})+)(?=;|$|[\\\\\\\\s,\\\\\\\\#)\\\\\\\\[:{>+~|]|\\\\\\\\.[^$]|/\\\\\\\\*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.placeholder.css\\\"},\\\"selector_pseudo_class\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((:)\\\\\\\\bnth-(?:child|last-child|of-type|last-of-type))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.css\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.entity.css\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.pseudo-class.begin.bracket.round.css\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.pseudo-class.end.bracket.round.css\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\d+\\\",\\\"name\\\":\\\"constant.numeric.css\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\d)n\\\\\\\\b|\\\\\\\\b(n|even|odd)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.scss\\\"},{\\\"match\\\":\\\"\\\\\\\\w+\\\",\\\"name\\\":\\\"invalid.illegal.scss\\\"}]},{\\\"include\\\":\\\"source.css#pseudo-classes\\\"},{\\\"include\\\":\\\"source.css#pseudo-elements\\\"},{\\\"include\\\":\\\"source.css#functional-pseudo-classes\\\"}]},\\\"selectors\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.css#tag-names\\\"},{\\\"include\\\":\\\"#selector_custom\\\"},{\\\"include\\\":\\\"#selector_class\\\"},{\\\"include\\\":\\\"#selector_id\\\"},{\\\"include\\\":\\\"#selector_pseudo_class\\\"},{\\\"include\\\":\\\"#tag_wildcard\\\"},{\\\"include\\\":\\\"#tag_parent_reference\\\"},{\\\"include\\\":\\\"source.css#pseudo-elements\\\"},{\\\"include\\\":\\\"#selector_attribute\\\"},{\\\"include\\\":\\\"#selector_placeholder\\\"},{\\\"include\\\":\\\"#parent_selector_suffix\\\"}]},\\\"string_double\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"}},\\\"name\\\":\\\"string.quoted.double.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"string_single\\\":{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.scss\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.scss\\\"}},\\\"name\\\":\\\"string.quoted.single.scss\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.scss\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"tag_parent_reference\\\":{\\\"match\\\":\\\"&\\\",\\\"name\\\":\\\"entity.name.tag.reference.scss\\\"},\\\"tag_wildcard\\\":{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"entity.name.tag.wildcard.scss\\\"},\\\"variable\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variables\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]},\\\"variable_setting\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\$[\\\\\\\\w-]+\\\\\\\\s*:)\\\",\\\"contentName\\\":\\\"meta.definition.variable.scss\\\",\\\"end\\\":\\\";\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.scss\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\w-]+(?=\\\\\\\\s*:)\\\",\\\"name\\\":\\\"variable.scss\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.scss\\\"}},\\\"end\\\":\\\"(?=;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_docblock\\\"},{\\\"include\\\":\\\"#comment_block\\\"},{\\\"include\\\":\\\"#comment_line\\\"},{\\\"include\\\":\\\"#map\\\"},{\\\"include\\\":\\\"#property_values\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.scss\\\"}]}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.scss\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.access.module.scss\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.scss\\\"}},\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\w-]+)(\\\\\\\\.)(\\\\\\\\$[\\\\\\\\w-]+)\\\\\\\\b\\\"},{\\\"match\\\":\\\"(\\\\\\\\$|\\\\\\\\-\\\\\\\\-)[A-Za-z0-9_-]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.scss\\\"}]}},\\\"scopeName\\\":\\\"source.css.scss\\\",\\\"embeddedLangs\\\":[\\\"css\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._css_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/scss.mjs\n"));

/***/ })

}]);