"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_tcl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/tcl.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/tcl.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Tcl\\\",\\\"fileTypes\\\":[\\\"tcl\\\"],\\\"foldingStartMarker\\\":\\\"\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"tcl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=^|;)\\\\\\\\s*((#))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.number-sign.tcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.comment.tcl\\\"}},\\\"contentName\\\":\\\"comment.line.number-sign.tcl\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\|\\\\\\\\\\\\\\\\\\\\\\\\n)\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tcl\\\"}},\\\"match\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(if|while|for|catch|default|return|break|continue|switch|exit|foreach|try|throw)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.tcl\\\"}},\\\"match\\\":\\\"(?<=^|})\\\\\\\\s*(then|elseif|else)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.tcl\\\"}},\\\"match\\\":\\\"(?<=^|{)\\\\\\\\s*(proc)\\\\\\\\s+([^\\\\\\\\s]+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"}},\\\"match\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(after|append|array|auto_execok|auto_import|auto_load|auto_mkindex|auto_mkindex_old|auto_qualify|auto_reset|bgerror|binary|cd|clock|close|concat|dde|encoding|eof|error|eval|exec|expr|fblocked|fconfigure|fcopy|file|fileevent|filename|flush|format|gets|glob|global|history|http|incr|info|interp|join|lappend|library|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort|memory|msgcat|namespace|open|package|parray|pid|pkg::create|pkg_mkIndex|proc|puts|pwd|re_syntax|read|registry|rename|resource|scan|seek|set|socket|SafeBase|source|split|string|subst|Tcl|tcl_endOfWord|tcl_findLibrary|tcl_startOfNextWord|tcl_startOfPreviousWord|tcl_wordBreakAfter|tcl_wordBreakBefore|tcltest|tclvars|tell|time|trace|unknown|unset|update|uplevel|upvar|variable|vwait)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"(?<=^|[\\\\\\\\[{;])\\\\\\\\s*(regexp|regsub)\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.tcl\\\"}},\\\"comment\\\":\\\"special-case regexp/regsub keyword in order to handle the expression\\\",\\\"end\\\":\\\"[\\\\\\\\n;\\\\\\\\]]\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"comment\\\":\\\"switch for regexp\\\",\\\"match\\\":\\\"-\\\\\\\\w+\\\\\\\\s*\\\"},{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"--\\\\\\\\s*\\\",\\\"comment\\\":\\\"end of switches\\\",\\\"end\\\":\\\"\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"include\\\":\\\"#regexp\\\"}]},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#numeric\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.tcl\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.tcl\\\"}},\\\"name\\\":\\\"string.quoted.double.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#embedded\\\"}]}],\\\"repository\\\":{\\\"bare-string\\\":{\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))\\\\\\\"\\\",\\\"comment\\\":\\\"matches a single quote-enclosed word without scoping\\\",\\\"end\\\":\\\"\\\\\\\"([^\\\\\\\\s\\\\\\\\]]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.tcl\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"}]},\\\"braces\\\":{\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))\\\\\\\\{\\\",\\\"comment\\\":\\\"matches a single brace-enclosed word\\\",\\\"end\\\":\\\"\\\\\\\\}([^\\\\\\\\s\\\\\\\\]]*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.tcl\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[{}\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"include\\\":\\\"#inner-braces\\\"}]},\\\"embedded\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.tcl\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.tcl\\\"}},\\\"name\\\":\\\"source.tcl.embedded\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tcl\\\"}]},\\\"escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\d{1,3}|x[a-fA-F0-9]+|u[a-fA-F0-9]{1,4}|.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},\\\"inner-braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"comment\\\":\\\"matches a nested brace in a brace-enclosed word\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[{}\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.escape.tcl\\\"},{\\\"include\\\":\\\"#inner-braces\\\"}]},\\\"numeric\\\":{\\\"match\\\":\\\"(?<![a-zA-Z])([+-]?([0-9]*[.])?[0-9]+f?)(?![\\\\\\\\.a-zA-Z])\\\",\\\"name\\\":\\\"constant.numeric.tcl\\\"},\\\"operator\\\":{\\\"match\\\":\\\"(?<= |\\\\\\\\d)(-|\\\\\\\\+|~|&{1,2}|\\\\\\\\|{1,2}|<{1,2}|>{1,2}|\\\\\\\\*{1,2}|!|%|\\\\\\\\/|<=|>=|={1,2}|!=|\\\\\\\\^)(?= |\\\\\\\\d)\\\",\\\"name\\\":\\\"keyword.operator.tcl\\\"},\\\"regexp\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\S)(?![\\\\\\\\n;\\\\\\\\]])\\\",\\\"comment\\\":\\\"matches a single word, named as a regexp, then swallows the rest of the command\\\",\\\"end\\\":\\\"(?=[\\\\\\\\n;\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^ \\\\\\\\t\\\\\\\\n;])\\\",\\\"end\\\":\\\"(?=[ \\\\\\\\t\\\\\\\\n;])\\\",\\\"name\\\":\\\"string.regexp.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#bare-string\\\"},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#variable\\\"}]},{\\\"begin\\\":\\\"[ \\\\\\\\t]\\\",\\\"comment\\\":\\\"swallow the rest of the command\\\",\\\"end\\\":\\\"(?=[\\\\\\\\n;\\\\\\\\]])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#variable\\\"},{\\\"include\\\":\\\"#embedded\\\"},{\\\"include\\\":\\\"#escape\\\"},{\\\"include\\\":\\\"#braces\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"string\\\":{\\\"applyEndPatternLast\\\":1,\\\"begin\\\":\\\"(?:^|(?<=\\\\\\\\s))(?=\\\\\\\")\\\",\\\"comment\\\":\\\"matches a single quote-enclosed word with scoping\\\",\\\"end\\\":\\\"\\\",\\\"name\\\":\\\"string.quoted.double.tcl\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#bare-string\\\"}]},\\\"variable\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.tcl\\\"}},\\\"match\\\":\\\"(\\\\\\\\$)((?:[a-zA-Z0-9_]|::)+(\\\\\\\\([^\\\\\\\\)]+\\\\\\\\))?|\\\\\\\\{[^\\\\\\\\}]*\\\\\\\\})\\\",\\\"name\\\":\\\"support.function.tcl\\\"}},\\\"scopeName\\\":\\\"source.tcl\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/tcl.mjs\n"));

/***/ })

}]);