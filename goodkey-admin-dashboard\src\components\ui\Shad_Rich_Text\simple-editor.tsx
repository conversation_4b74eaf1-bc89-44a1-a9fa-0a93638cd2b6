'use client';
import { useState } from 'react';
import RichTextEditor, {
  BaseKit,
  Blockquote,
  Bold,
  BulletList,
  Clear,
  Color,
  ColumnActionButton,
  Emoji,
  FontFamily,
  FontSize,
  FormatPainter,
  Heading,
  Highlight,
  History,
  HorizontalRule,
  Indent,
  Italic,
  LineHeight,
  Link,
  MoreMark,
  OrderedList,
  SlashCommand,
  Strike,
  Table,
  TaskList,
  TextAlign,
  Underline,
  TextDirection,
  Mention,
} from 'reactjs-tiptap-editor';
import 'reactjs-tiptap-editor/style.css';
// import { Button } from '../button';
// import { Icons } from './components/icons';

interface SimpleEditorProps {
  value: string;
  onChange: (value: string) => void;
  // uploadImageMutation?: (data: AddRichTextImageData) => Promise<any>;
}
function SimpleEditor({
  value,
  onChange,
  // uploadImageMutation,
}: SimpleEditorProps) {
  const [content, setContent] = useState(value);

  const onChangeContent = (val: any) => {
    setContent(val);
    onChange(val);
  };
  return (
    <div className="relative">
      <RichTextEditor
        dark={false}
        output="html"
        content={content}
        onChangeContent={onChangeContent}
        extensions={[
          BaseKit.configure({
            multiColumn: true,
            placeholder: {
              showOnlyCurrent: true,
            },
            characterCount: {
              limit: 50_000,
            },
          }),
          History,
          TextDirection,
          FormatPainter.configure({ spacer: true }),
          Clear,
          FontFamily.configure({
            fontFamilyList: [
              'Inter',
              'Arial',
              'Roboto',
              'Helvetica',
              'Times New Roman',
              'Comic Neue',
              'IBM Plex Sans',
              'Lora',
              'Edu AU VIC WA NT Hand',
            ],
          }),
          Heading.configure({ spacer: true }),
          FontSize,
          Bold,
          Italic,
          Underline,
          Strike,
          MoreMark,
          Emoji,
          Color.configure({ spacer: true }),
          Highlight,
          BulletList,
          OrderedList,
          TextAlign.configure({
            types: ['heading', 'paragraph'],
            spacer: true,
          }),
          Indent,
          LineHeight,
          TaskList.configure({
            spacer: true,
            taskItem: {
              nested: true,
            },
          }),
          Link.configure({
            autolink: true,
            linkOnPaste: true,
          }),
          Blockquote.configure({ spacer: true }),
          SlashCommand,
          HorizontalRule,
          ColumnActionButton,
          Table,
          Mention,
          // Image.configure({
          //   button: ({ editor }) => ({
          //     componentProps: {
          //       type: 'button',
          //       children: <Icons.image className="size-5" />,
          //       onClick: (e: any) => {
          //         e.preventDefault();
          //         // modal(
          //         //   <AddImageModal
          //         //     insertImage={(url, alt) => {
          //         //       editor.commands.setImage({
          //         //         src: url,
          //         //         alt: alt,
          //         //         title: alt,
          //         //       });
          //         //     }}
          //         //     selectedLanguage="en"
          //         //     // mutationFn={uploadImageMutation}
          //         //   />,
          //         //   DEFAULT_MODAL,
          //         // ).open();
          //       },
          //     },
          //     component: Button,
          //   }),
          // }),
          // CodeBlock.configure({ defaultTheme: 'dracula' }),
        ]}
      />
    </div>
  );
}

export default SimpleEditor;
