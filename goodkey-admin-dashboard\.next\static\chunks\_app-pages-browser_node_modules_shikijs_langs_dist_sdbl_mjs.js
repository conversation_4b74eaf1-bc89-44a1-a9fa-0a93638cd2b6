"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_sdbl_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/sdbl.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Query)\\\",\\\"fileTypes\\\":[\\\"sdbl\\\",\\\"query\\\"],\\\"firstLineMatch\\\":\\\"(?i)Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?.*\\\",\\\"name\\\":\\\"sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\\\\\\\\\"(?![\\\\\\\\\\\\\\\"])\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.sdbl\\\"},{\\\"match\\\":\\\"(^\\\\\\\\s*//.*$)\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"}]},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"constant.language.sdbl\\\"},{\\\"match\\\":\\\"(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"constant.numeric.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.control.conditional.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<!КАК\\\\\\\\s|AS\\\\\\\\s)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\\\\\s+ИЕРАРХИИ|IN\\\\\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\\\\\s+НЕ)?\\\\\\\\s+NULL|Is(\\\\\\\\s+NOT)?\\\\\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.sdbl\\\"},{\\\"match\\\":\\\"<=|>=|=|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.sdbl\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|-|\\\\\\\\*|/|%)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sdbl\\\"},{\\\"match\\\":\\\"(,|;)\\\",\\\"name\\\":\\\"keyword.operator.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\\\\\s+(Внешнее\\\\\\\\s+|Outer\\\\\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\\\\\s+По(\\\\\\\\s+Группирующим\\\\\\\\s+Наборам)?)|(Group\\\\\\\\s+By(\\\\\\\\s+Grouping\\\\\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\\\\\s+Все)?|Union(\\\\\\\\s+All)?|(Упорядочить\\\\\\\\s+По)|(Order\\\\\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\\\\\s+Общие)?|By(\\\\\\\\s+Overall)?|(Только\\\\\\\\s+)?Иерархия|(Only\\\\\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\\\\\s+Изменения|(For\\\\\\\\s+Update(\\\\\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"keyword.control.sdbl\\\"},{\\\"comment\\\":\\\"Функции языка запросов\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы со строками\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы с датами\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Функции работы с числами\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Агрегатные функции\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"comment\\\":\\\"Прочие функции\\\",\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^\\\\\\\\wа-яё\\\\\\\\.])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^\\\\\\\\wа-яё\\\\\\\\.]|$)\\\",\\\"name\\\":\\\"support.type.sdbl\\\"},{\\\"match\\\":\\\"(&[\\\\\\\\wа-яё]+)\\\",\\\"name\\\":\\\"variable.parameter.sdbl\\\"}],\\\"scopeName\\\":\\\"source.sdbl\\\",\\\"aliases\\\":[\\\"1c-query\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/sdbl.mjs\n"));

/***/ })

}]);