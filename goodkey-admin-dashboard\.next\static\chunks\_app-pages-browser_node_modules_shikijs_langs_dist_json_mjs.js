"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_json_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/json.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/json.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSON\\\",\\\"name\\\":\\\"json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.array.end.json\\\"}},\\\"name\\\":\\\"meta.structure.array.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.array.json\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]]\\\",\\\"name\\\":\\\"invalid.illegal.expected-array-separator.json\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!/)\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.documentation.json\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.json\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.json\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"}]},\\\"constant\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:true|false|null)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.json\\\"},\\\"number\\\":{\\\"match\\\":\\\"-?(?:0|[1-9]\\\\\\\\d*)(?:(?:\\\\\\\\.\\\\\\\\d+)?(?:[eE][+-]?\\\\\\\\d+)?)?\\\",\\\"name\\\":\\\"constant.numeric.json\\\"},\\\"object\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dictionary.end.json\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.json\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"the JSON object key\\\",\\\"include\\\":\\\"#objectkey\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\":\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.key-value.json\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.dictionary.pair.json\\\"}},\\\"name\\\":\\\"meta.structure.dictionary.value.json\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"the JSON object value\\\",\\\"include\\\":\\\"#value\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s,]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json\\\"}]},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\}]\\\",\\\"name\\\":\\\"invalid.illegal.expected-dictionary-separator.json\\\"}]},\\\"objectkey\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.support.type.property-name.end.json\\\"}},\\\"name\\\":\\\"string.json support.type.property-name.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"string\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.json\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.json\\\"}},\\\"name\\\":\\\"string.quoted.double.json\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(?:[\\\\\\\"\\\\\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})\\\",\\\"name\\\":\\\"constant.character.escape.json\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.json\\\"}]},\\\"value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#constant\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#object\\\"},{\\\"include\\\":\\\"#comments\\\"}]}},\\\"scopeName\\\":\\\"source.json\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/json.mjs\n"));

/***/ })

}]);