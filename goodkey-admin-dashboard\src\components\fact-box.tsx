'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronRight, ExternalLink, FileText, Info } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface FactBoxProps {
  currentRole: string;
}

export function FactBox({ currentRole }: FactBoxProps) {
  const factBoxContent = {
    finance: {
      title: 'Financial Insights',
      stats: [
        { label: 'Fiscal Year', value: '2023-2024' },
        { label: 'Current Period', value: 'April 2024' },
        { label: 'Budget Status', value: 'On Track' },
        { label: 'Last Reconciliation', value: 'Apr 15, 2024' },
      ],
      reports: [
        { name: 'Profit & Loss Statement', date: 'Apr 1, 2024' },
        { name: 'Balance Sheet', date: 'Apr 1, 2024' },
        { name: 'Cash Flow Statement', date: 'Apr 1, 2024' },
        { name: 'Budget vs. Actual', date: 'Apr 1, 2024' },
      ],
    },
    sales: {
      title: 'Sales Insights',
      stats: [
        { label: 'Sales Period', value: 'Q2 2024' },
        { label: 'Target Achievement', value: '78%' },
        { label: 'Top Product', value: 'Product X' },
        { label: 'Top Salesperson', value: 'Lisa Brown' },
      ],
      reports: [
        { name: 'Sales by Region', date: 'Apr 1, 2024' },
        { name: 'Sales by Product', date: 'Apr 1, 2024' },
        { name: 'Customer Analysis', date: 'Apr 1, 2024' },
        { name: 'Pipeline Forecast', date: 'Apr 1, 2024' },
      ],
    },
    inventory: {
      title: 'Inventory Insights',
      stats: [
        { label: 'Total SKUs', value: '2,583' },
        { label: 'Avg. Turnover', value: '45 days' },
        { label: 'Stock Value', value: '$1.2M' },
        { label: 'Pending Receipts', value: '12' },
      ],
      reports: [
        { name: 'Inventory Valuation', date: 'Apr 1, 2024' },
        { name: 'Stock Movement', date: 'Apr 1, 2024' },
        { name: 'Aging Analysis', date: 'Apr 1, 2024' },
        { name: 'Reorder Report', date: 'Apr 1, 2024' },
      ],
    },
    admin: {
      title: 'System Insights',
      stats: [
        { label: 'System Version', value: 'v4.2.1' },
        { label: 'Last Update', value: 'Apr 10, 2024' },
        { label: 'Active Sessions', value: '24' },
        { label: 'Database Size', value: '45.8 GB' },
      ],
      reports: [
        { name: 'User Activity Log', date: 'Apr 1, 2024' },
        { name: 'System Performance', date: 'Apr 1, 2024' },
        { name: 'Security Audit', date: 'Apr 1, 2024' },
        { name: 'License Usage', date: 'Apr 1, 2024' },
      ],
    },
  };

  const content = factBoxContent[currentRole as keyof typeof factBoxContent];

  return (
    <TooltipProvider>
      <div className="hidden md:block border-l border-slate-200 w-80 shrink-0 bg-white">
        <Card className="h-full rounded-none border-0 shadow-none">
          <CardHeader className="px-6 pt-6 pb-4">
            <CardTitle className="flex items-center text-lg text-slate-800">
              <Info className="mr-2 h-5 w-5 text-slate-400" />
              {content.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="px-6">
            <ScrollArea className="h-[calc(100vh-10rem)]">
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-3 text-slate-700">
                    Key Information
                  </h3>
                  <div className="space-y-2">
                    {content.stats.map((stat, index) => (
                      <div key={index} className="flex justify-between">
                        <span className="text-sm text-slate-500">
                          {stat.label}
                        </span>
                        <span className="text-sm font-medium text-slate-800">
                          {stat.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="bg-slate-200" />

                <div>
                  <h3 className="text-sm font-medium mb-3 text-slate-700">
                    Recent Reports
                  </h3>
                  <div className="space-y-1">
                    {content.reports.map((report, index) => (
                      <Tooltip key={index}>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            className="w-full justify-start px-2 h-auto py-2 text-slate-700 hover:bg-slate-100"
                          >
                            <FileText className="mr-2 h-4 w-4 text-slate-400" />
                            <div className="flex flex-col items-start">
                              <span className="text-sm">{report.name}</span>
                              <span className="text-xs text-slate-500">
                                {report.date}
                              </span>
                            </div>
                            <ChevronRight className="ml-auto h-4 w-4 text-slate-400" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                          <p>Open {report.name}</p>
                        </TooltipContent>
                      </Tooltip>
                    ))}
                  </div>
                </div>

                <Separator className="bg-slate-200" />

                <div>
                  <h3 className="text-sm font-medium mb-3 text-slate-700">
                    Resources
                  </h3>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start border-slate-200 text-slate-700"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Help Documentation
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start border-slate-200 text-slate-700"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Training Videos
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start border-slate-200 text-slate-700"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Support Portal
                    </Button>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
