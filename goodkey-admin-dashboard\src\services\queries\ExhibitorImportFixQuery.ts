import fetcher from './fetcher';
import type {
  ExhibitorImportFieldEditRequestDto,
  ExhibitorImportFieldEditResponseDto,
  ExhibitorImportBulkFixRequestDto,
  ExhibitorImportBulkFixResponseDto,
  ExhibitorImportRowComparisonRequestDto,
  ExhibitorImportRowComparisonResponseDto,
  ExhibitorImportRevalidateRequestDto,
  ExhibitorImportRevalidateResponseDto,
  ExhibitorImportFieldSuggestionsRequestDto,
  ExhibitorImportFieldSuggestionsResponseDto,
} from '@/models/ExhibitorImportFix';

const ExhibitorImportFixQuery = {
  tags: ['ExhibitorImportFix'] as const,

  // =====================================================
  // FIELD EDITING AND ERROR FIXING
  // =====================================================

  /**
   * Edit individual field values to fix validation errors
   */
  editFields: async (
    request: ExhibitorImportFieldEditRequestDto,
  ): Promise<ExhibitorImportFieldEditResponseDto> => {
    return fetcher<ExhibitorImportFieldEditResponseDto>(
      'ExhibitorImport/edit-fields',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Bulk fix multiple errors and duplicates
   */
  bulkFix: async (
    request: ExhibitorImportBulkFixRequestDto,
  ): Promise<ExhibitorImportBulkFixResponseDto> => {
    return fetcher<ExhibitorImportBulkFixResponseDto>(
      'ExhibitorImport/bulk-fix',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Compare a row with existing database records
   */
  compareRow: async (
    request: ExhibitorImportRowComparisonRequestDto,
  ): Promise<ExhibitorImportRowComparisonResponseDto> => {
    return fetcher<ExhibitorImportRowComparisonResponseDto>(
      'ExhibitorImport/compare-row',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Revalidate rows after fixes
   */
  revalidate: async (
    request: ExhibitorImportRevalidateRequestDto,
  ): Promise<ExhibitorImportRevalidateResponseDto> => {
    return fetcher<ExhibitorImportRevalidateResponseDto>(
      'ExhibitorImport/revalidate',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  // =====================================================
  // FIELD SUGGESTIONS AND AUTO-FIXING
  // =====================================================

  /**
   * Get field suggestions for auto-fixing
   */
  getFieldSuggestions: async (
    request: ExhibitorImportFieldSuggestionsRequestDto,
  ): Promise<ExhibitorImportFieldSuggestionsResponseDto> => {
    return fetcher<ExhibitorImportFieldSuggestionsResponseDto>(
      'ExhibitorImport/field-suggestions',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Get detailed validation errors for a specific row
   */
  getRowErrors: async (sessionId: string, rowNumber: number): Promise<any> => {
    return fetcher<any>(
      `ExhibitorImport/row-errors/${sessionId}/${rowNumber}`,
      {
        method: 'GET',
      },
    );
  },

  /**
   * Auto-fix common validation errors
   */
  autoFix: async (
    sessionId: string,
    fixTypes: string[] = ['email', 'phone', 'postalCode'],
  ): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/auto-fix',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, fixTypes }),
      },
    );
  },

  // =====================================================
  // PREVIEW AND HISTORY
  // =====================================================

  /**
   * Preview changes before applying fixes
   */
  previewFixes: async (
    request: ExhibitorImportFieldEditRequestDto,
  ): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/preview-fixes',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      },
    );
  },

  /**
   * Undo recent field edits
   */
  undoEdits: async (sessionId: string, editIds: number[]): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/undo-edits',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, editIds }),
      },
    );
  },

  /**
   * Get edit history for a session
   */
  getEditHistory: async (sessionId: string): Promise<any> => {
    return fetcher<any>(
      `ExhibitorImport/edit-history/${sessionId}`,
      {
        method: 'GET',
      },
    );
  },

  // =====================================================
  // EXPORT AND STATISTICS
  // =====================================================

  /**
   * Export fixed data for review
   */
  exportFixedData: async (
    sessionId: string,
    format: 'excel' | 'csv' = 'excel',
  ): Promise<Blob> => {
    return fetcher<Blob>(
      `ExhibitorImport/export-fixed/${sessionId}?format=${format}`,
      {
        method: 'GET',
      },
      true, // Use blob response
    );
  },

  /**
   * Get statistics about fixes applied
   */
  getFixStatistics: async (sessionId: string): Promise<any> => {
    return fetcher<any>(
      `ExhibitorImport/fix-statistics/${sessionId}`,
      {
        method: 'GET',
      },
    );
  },

  /**
   * Validate a single field value
   */
  validateField: async (
    fieldName: string,
    value: string,
    context?: any,
  ): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/validate-field',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fieldName, value, context }),
      },
    );
  },

  // =====================================================
  // DUPLICATE MANAGEMENT
  // =====================================================

  /**
   * Get similar records from database for comparison
   */
  findSimilarRecords: async (
    sessionId: string,
    rowNumber: number,
    searchType: 'company' | 'contact' = 'company',
  ): Promise<any> => {
    return fetcher<any>(
      `ExhibitorImport/similar-records/${sessionId}/${rowNumber}?type=${searchType}`,
      {
        method: 'GET',
      },
    );
  },

  /**
   * Merge duplicate records with custom field selection
   */
  mergeDuplicates: async (
    sessionId: string,
    duplicateId: number,
    fieldSelections: Record<string, string>,
  ): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/merge-duplicates',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, duplicateId, fieldSelections }),
      },
    );
  },

  /**
   * Split merged records back to separate entries
   */
  splitMergedRecord: async (
    sessionId: string,
    recordId: number,
  ): Promise<any> => {
    return fetcher<any>(
      'ExhibitorImport/split-merged',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, recordId }),
      },
    );
  },

  /**
   * Get field-level change history
   */
  static async getFieldHistory(sessionId: string, rowNumber: number, fieldName: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/field-history/${sessionId}/${rowNumber}/${fieldName}`
    );
    return response.data;
  }

  /**
   * Batch update multiple fields across multiple rows
   */
  static async batchUpdate(sessionId: string, updates: Array<{
    rowNumber: number;
    fieldName: string;
    newValue: string;
  }>): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/batch-update`,
      { sessionId, updates }
    );
    return response.data;
  }

  /**
   * Apply smart fixes based on patterns and rules
   */
  static async applySmartFixes(sessionId: string, rules: string[] = ['standardizeNames', 'formatPhones', 'fixEmails']): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/smart-fixes`,
      { sessionId, rules }
    );
    return response.data;
  }

  /**
   * Get recommendations for fixing specific error types
   */
  static async getFixRecommendations(sessionId: string, errorType: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/fix-recommendations/${sessionId}?errorType=${errorType}`
    );
    return response.data;
  }

  /**
   * Create a backup before applying major fixes
   */
  static async createBackup(sessionId: string, description?: string): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/create-backup`,
      { sessionId, description }
    );
    return response.data;
  }

  /**
   * Restore from a previous backup
   */
  static async restoreBackup(sessionId: string, backupId: number): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/restore-backup`,
      { sessionId, backupId }
    );
    return response.data;
  }

  /**
   * Get list of available backups
   */
  static async getBackups(sessionId: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/backups/${sessionId}`
    );
    return response.data;
  }
}

export default ExhibitorImportFixQuery;
