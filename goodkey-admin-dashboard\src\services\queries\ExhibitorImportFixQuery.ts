import { ApiClient } from '../ApiClient';
import type {
  ExhibitorImportFieldEditRequestDto,
  ExhibitorImportFieldEditResponseDto,
  ExhibitorImportBulkFixRequestDto,
  ExhibitorImportBulkFixResponseDto,
  ExhibitorImportRowComparisonRequestDto,
  ExhibitorImportRowComparisonResponseDto,
  ExhibitorImportRevalidateRequestDto,
  ExhibitorImportRevalidateResponseDto,
  ExhibitorImportFieldSuggestionsRequestDto,
  ExhibitorImportFieldSuggestionsResponseDto,
} from '@/models/ExhibitorImportFix';

class ExhibitorImportFixQuery {
  private static readonly BASE_URL = '/api/exhibitor-import';

  /**
   * Edit individual field values to fix validation errors
   */
  static async editFields(request: ExhibitorImportFieldEditRequestDto): Promise<ExhibitorImportFieldEditResponseDto> {
    const response = await ApiClient.post<ExhibitorImportFieldEditResponseDto>(
      `${this.BASE_URL}/edit-fields`,
      request
    );
    return response.data;
  }

  /**
   * Bulk fix multiple errors and duplicates
   */
  static async bulkFix(request: ExhibitorImportBulkFixRequestDto): Promise<ExhibitorImportBulkFixResponseDto> {
    const response = await ApiClient.post<ExhibitorImportBulkFixResponseDto>(
      `${this.BASE_URL}/bulk-fix`,
      request
    );
    return response.data;
  }

  /**
   * Compare a row with existing database records
   */
  static async compareRow(request: ExhibitorImportRowComparisonRequestDto): Promise<ExhibitorImportRowComparisonResponseDto> {
    const response = await ApiClient.post<ExhibitorImportRowComparisonResponseDto>(
      `${this.BASE_URL}/compare-row`,
      request
    );
    return response.data;
  }

  /**
   * Revalidate rows after fixes
   */
  static async revalidate(request: ExhibitorImportRevalidateRequestDto): Promise<ExhibitorImportRevalidateResponseDto> {
    const response = await ApiClient.post<ExhibitorImportRevalidateResponseDto>(
      `${this.BASE_URL}/revalidate`,
      request
    );
    return response.data;
  }

  /**
   * Get field suggestions for auto-fixing
   */
  static async getFieldSuggestions(request: ExhibitorImportFieldSuggestionsRequestDto): Promise<ExhibitorImportFieldSuggestionsResponseDto> {
    const response = await ApiClient.post<ExhibitorImportFieldSuggestionsResponseDto>(
      `${this.BASE_URL}/field-suggestions`,
      request
    );
    return response.data;
  }

  /**
   * Get detailed validation errors for a specific row
   */
  static async getRowErrors(sessionId: string, rowNumber: number): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/row-errors/${sessionId}/${rowNumber}`
    );
    return response.data;
  }

  /**
   * Auto-fix common validation errors
   */
  static async autoFix(sessionId: string, fixTypes: string[] = ['email', 'phone', 'postalCode']): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/auto-fix`,
      { sessionId, fixTypes }
    );
    return response.data;
  }

  /**
   * Preview changes before applying fixes
   */
  static async previewFixes(request: ExhibitorImportFieldEditRequestDto): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/preview-fixes`,
      request
    );
    return response.data;
  }

  /**
   * Undo recent field edits
   */
  static async undoEdits(sessionId: string, editIds: number[]): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/undo-edits`,
      { sessionId, editIds }
    );
    return response.data;
  }

  /**
   * Get edit history for a session
   */
  static async getEditHistory(sessionId: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/edit-history/${sessionId}`
    );
    return response.data;
  }

  /**
   * Export fixed data for review
   */
  static async exportFixedData(sessionId: string, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/export-fixed/${sessionId}?format=${format}`,
      { responseType: 'blob' }
    );
    return response.data;
  }

  /**
   * Get statistics about fixes applied
   */
  static async getFixStatistics(sessionId: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/fix-statistics/${sessionId}`
    );
    return response.data;
  }

  /**
   * Validate a single field value
   */
  static async validateField(fieldName: string, value: string, context?: any): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/validate-field`,
      { fieldName, value, context }
    );
    return response.data;
  }

  /**
   * Get similar records from database for comparison
   */
  static async findSimilarRecords(sessionId: string, rowNumber: number, searchType: 'company' | 'contact' = 'company'): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/similar-records/${sessionId}/${rowNumber}?type=${searchType}`
    );
    return response.data;
  }

  /**
   * Merge duplicate records with custom field selection
   */
  static async mergeDuplicates(sessionId: string, duplicateId: number, fieldSelections: Record<string, string>): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/merge-duplicates`,
      { sessionId, duplicateId, fieldSelections }
    );
    return response.data;
  }

  /**
   * Split merged records back to separate entries
   */
  static async splitMergedRecord(sessionId: string, recordId: number): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/split-merged`,
      { sessionId, recordId }
    );
    return response.data;
  }

  /**
   * Get field-level change history
   */
  static async getFieldHistory(sessionId: string, rowNumber: number, fieldName: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/field-history/${sessionId}/${rowNumber}/${fieldName}`
    );
    return response.data;
  }

  /**
   * Batch update multiple fields across multiple rows
   */
  static async batchUpdate(sessionId: string, updates: Array<{
    rowNumber: number;
    fieldName: string;
    newValue: string;
  }>): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/batch-update`,
      { sessionId, updates }
    );
    return response.data;
  }

  /**
   * Apply smart fixes based on patterns and rules
   */
  static async applySmartFixes(sessionId: string, rules: string[] = ['standardizeNames', 'formatPhones', 'fixEmails']): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/smart-fixes`,
      { sessionId, rules }
    );
    return response.data;
  }

  /**
   * Get recommendations for fixing specific error types
   */
  static async getFixRecommendations(sessionId: string, errorType: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/fix-recommendations/${sessionId}?errorType=${errorType}`
    );
    return response.data;
  }

  /**
   * Create a backup before applying major fixes
   */
  static async createBackup(sessionId: string, description?: string): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/create-backup`,
      { sessionId, description }
    );
    return response.data;
  }

  /**
   * Restore from a previous backup
   */
  static async restoreBackup(sessionId: string, backupId: number): Promise<any> {
    const response = await ApiClient.post(
      `${this.BASE_URL}/restore-backup`,
      { sessionId, backupId }
    );
    return response.data;
  }

  /**
   * Get list of available backups
   */
  static async getBackups(sessionId: string): Promise<any> {
    const response = await ApiClient.get(
      `${this.BASE_URL}/backups/${sessionId}`
    );
    return response.data;
  }
}

export default ExhibitorImportFixQuery;
