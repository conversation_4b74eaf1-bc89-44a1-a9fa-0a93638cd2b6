import { format } from 'date-fns';
import React from 'react';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { useFormField } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from '@/assets/Icons';

interface IMonthPickerInput {
  onValueChange: (value: Date | undefined) => void;
  date?: Date;
}

function MonthPickerInput({ onValueChange, date }: IMonthPickerInput) {
  const [month, setMonth] = React.useState<Date | undefined>(
    date || new Date(),
  );

  const disabledDays = [
    { from: new Date(2022, 12, 1), to: new Date(2046, 1, 31) },
  ];
  const { error } = useFormField();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'flex  w-full  bg-transparent text-foreground  text-sm shadow-sm transition-colors  placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-[#1a7efb] disabled:cursor-not-allowed disabled:opacity-50',
            'p-2 border border-gray-300 rounded-sm',
            error && 'border-red-500 ',
          )}
        >
          {month ? format(month, 'MMMM, yyyy') : <span>Pick a date</span>}
          <CalendarIcon className="ml-auto h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          month={month}
          fromYear={2023}
          toYear={2045}
          disabled={disabledDays}
          onMonthChange={(m) => {
            onValueChange(m);
            setMonth(m);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

export default MonthPickerInput;
