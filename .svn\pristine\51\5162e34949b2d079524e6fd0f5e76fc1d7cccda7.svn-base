using goodkey_cms.DTO.ShowExhibitor;
using goodkey_common.DTO;
using goodkey_common.Models;
using goodkey_common.Repositories;
using goodkey_cms.Services;
using goodkey_cms.Repositories;
using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Utils;
using Microsoft.AspNetCore.Mvc;
using goodkey_common.Context;

namespace goodkey_cms.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ShowExhibitorsController : ControllerBase
    {
        private readonly IShowExhibitorRepository _repository;
        private readonly ICompanyRepository _companyRepository;
        private readonly IUserRepository _userRepository;
        private readonly AuthService _authService;
        private readonly GoodkeyContext _context;

        public ShowExhibitorsController(
            IShowExhibitorRepository repository,
            ICompanyRepository companyRepository,
            IUserRepository userRepository,
            AuthService authService,
            GoodkeyContext context)
        {
            _repository = repository;
            _companyRepository = companyRepository;
            _userRepository = userRepository;
            _authService = authService;
            _context = context;
        }

        /// <summary>
        /// Generates a unique username with sequence numbers to handle duplicates
        /// </summary>
        private string GenerateUniqueUsername(string baseUsername)
        {
            // Start with the base username (email or generated)
            string candidateUsername = baseUsername;

            // Check if it already exists
            if (!_context.AuthUser.Any(u => u.Username == candidateUsername))
            {
                return candidateUsername;
            }

            // If it exists, try with sequence numbers
            for (int sequence = 1; sequence <= 999; sequence++)
            {
                candidateUsername = $"{baseUsername}_{sequence}";

                if (!_context.AuthUser.Any(u => u.Username == candidateUsername))
                {
                    return candidateUsername;
                }
            }

            // If all sequences are taken, use GUID as fallback
            return $"{baseUsername}_{Guid.NewGuid().ToString().Substring(0, 8)}";
        }

        // GET: /ShowExhibitors/show/{showId}
        [HttpGet("show/{showId}")]
        public GenericRespond<IEnumerable<ShowExhibitorListDto>> GetExhibitorsByShow(
            int showId,
            [FromQuery] int? companyId = null,
            [FromQuery] string? companyName = null,
            [FromQuery] string? contactName = null,
            [FromQuery] string? boothNumber = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool? isArchived = null,
            [FromQuery] string? searchTerm = null)
        {
            var exhibitors = _repository.SearchExhibitors(showId, companyName, contactName,
                boothNumber, isActive, isArchived, searchTerm);

            if (companyId.HasValue)
            {
                exhibitors = exhibitors.Where(e => e.CompanyId == companyId.Value);
            }

            var result = exhibitors.Select(e => new ShowExhibitorListDto
            {
                Id = e.Id,
                BoothNumber = e.BoothNumber,
                CompanyName = e.Company?.CompanyName,
                ContactName = e.Contact != null ? $"{e.Contact.FirstName} {e.Contact.LastName}".Trim() : null,
                ContactEmail = e.Contact?.Email,
                ContactTelephone = e.Contact?.Telephone,
                ContactIsRegistered = e.Contact?.Authuser?.IsVerified ?? false,
                IsActive = e.IsActive,
                IsArchived = e.IsArchived,
                CreatedAt = e.CreatedAt
            });

            return new GenericRespond<IEnumerable<ShowExhibitorListDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Exhibitors retrieved successfully"
            };
        }

        // GET: /ShowExhibitors/{id}
        [HttpGet("{id}")]
        public GenericRespond<ShowExhibitorResponseDto> GetExhibitorById(int id)
        {
            var exhibitor = _repository.GetById(id);
            if (exhibitor == null)
            {
                return new GenericRespond<ShowExhibitorResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Exhibitor not found"
                };
            }

            var result = new ShowExhibitorResponseDto
            {
                Id = exhibitor.Id,
                ShowId = exhibitor.ShowId,
                CompanyId = exhibitor.CompanyId,
                ContactId = exhibitor.ContactId,
                BoothNumber = exhibitor.BoothNumber,
                IsActive = exhibitor.IsActive,
                IsArchived = exhibitor.IsArchived,
                CreatedAt = exhibitor.CreatedAt,
                UpdatedAt = exhibitor.UpdatedAt,
                ArchivedAt = exhibitor.ArchivedAt,
                ShowName = exhibitor.Show?.Name,
                ShowCode = exhibitor.Show?.Code,
                CompanyName = exhibitor.Company?.CompanyName,
                ContactFirstName = exhibitor.Contact?.FirstName,
                ContactLastName = exhibitor.Contact?.LastName,
                ContactEmail = exhibitor.Contact?.Email,
                ContactTelephone = exhibitor.Contact?.Telephone,
                ContactCellphone = exhibitor.Contact?.Cellphone,
                ContactExt = exhibitor.Contact?.Ext,
                ContactIsRegistered = exhibitor.Contact?.Authuser?.IsVerified ?? false,
                CreatedByName = exhibitor.CreatedBy?.Username,
                UpdatedByName = exhibitor.UpdatedBy?.Username,
                ArchivedByName = exhibitor.ArchivedBy?.Username
            };

            return new GenericRespond<ShowExhibitorResponseDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Exhibitor retrieved successfully"
            };
        }

        // POST: /ShowExhibitors
        [HttpPost]
        public GenericRespond<ShowExhibitorResponseDto> CreateExhibitor([FromBody] CreateShowExhibitorDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<ShowExhibitorResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Check if exhibitor already exists for this show and company
            var existingExhibitor = _repository.GetExhibitorByShowAndCompany(dto.ShowId, dto.CompanyId);
            if (existingExhibitor != null)
            {
                return new GenericRespond<ShowExhibitorResponseDto>
                {
                    Data = null,
                    StatusCode = 409,
                    Message = "Exhibitor already exists for this show and company"
                };
            }

            int? contactId = dto.ContactId;

            // If no contact provided, create a new one with user account
            if (!contactId.HasValue && !string.IsNullOrEmpty(dto.FirstName))
            {
                // Step 1: Create the contact
                var newContactId = _companyRepository.AddContact(
                    dto.ContactTypeId ?? 1, // Default contact type
                    dto.CompanyId,
                    dto.FirstName ?? "",
                    dto.LastName ?? "",
                    dto.Email ?? "",
                    dto.Telephone ?? "",
                    dto.Ext ?? "",
                    dto.Cellphone ?? "",
                    false, // isArchived
                    user.Username
                );

                if (newContactId.HasValue)
                {
                    // Step 2: Generate unique username following standard pattern
                    var baseUsername = !string.IsNullOrEmpty(dto.Email) ? dto.Email : $"user_{Guid.NewGuid().ToString().Substring(0, 8)}";
                    var uniqueUsername = GenerateUniqueUsername(baseUsername);

                    // Step 3: Create user account for the contact
                    var userDto = new CreateUserDto
                    {
                        FirstName = dto.FirstName ?? "Contact",
                        LastName = dto.LastName ?? "User",
                        Email = dto.Email ?? string.Empty,
                        WorkEmail = dto.Email ?? string.Empty,
                        VerificationEmail = uniqueUsername, // Use unique username as verification email
                        WorkPhoneNumber = dto.Telephone ?? string.Empty,
                        MobileNumber = dto.Cellphone ?? string.Empty,
                        StatusId = 1, // Active status
                        RoleId = 4, // Default role for Exhibitor users
                        SalutationId = 1, // Default salutation
                        DepartmentId = 1 // Default department
                    };

                    var userId = _userRepository.CreateUser(user.Username, userDto);

                    if (userId.HasValue)
                    {
                        // Step 3: Set default password "blue"
                        var password = "blue";
                        var hashedPassword = HashUtility.HashPassword(password);
                        _userRepository.SetPassword(userId.Value, hashedPassword);

                        // Step 4: Link contact to user account
                        var contact = _companyRepository.GetContact(newContactId.Value);
                        if (contact != null)
                        {
                            contact.Authuserid = userId.Value;
                            _companyRepository.UpdateContact(
                                newContactId.Value,
                                contact.ContactTypeId,
                                contact.CompanyId,
                                contact.FirstName,
                                contact.LastName,
                                contact.Email,
                                contact.Telephone,
                                contact.Ext,
                                contact.Cellphone,
                                contact.IsArchived ?? false,
                                user.Username
                            );
                        }

                        // Step 5: Send invitation email (optional)
                        if (dto.SendEmailInvite && !string.IsNullOrEmpty(dto.Email))
                        {
                            var verificationToken = _userRepository.SetInvitationTokenForUser(userId.Value);
                            if (!string.IsNullOrEmpty(verificationToken))
                            {
                                _authService.SendInviteEmail(dto.Email, verificationToken);
                            }
                        }
                    }
                    else
                    {
                        // ROLLBACK: If user creation failed, delete the contact to avoid orphaned records
                        _companyRepository.DeleteContact(newContactId.Value, user.Username);

                        return new GenericRespond<ShowExhibitorResponseDto>
                        {
                            Data = null,
                            StatusCode = 400,
                            Message = "Failed to create user account for contact. Contact creation rolled back."
                        };
                    }
                }
                else
                {
                    return new GenericRespond<ShowExhibitorResponseDto>
                    {
                        Data = null,
                        StatusCode = 400,
                        Message = "Failed to create contact record."
                    };
                }

                contactId = newContactId;
            }

            var exhibitor = new ShowExhibitors
            {
                ShowId = dto.ShowId,
                CompanyId = dto.CompanyId,
                ContactId = contactId,
                BoothNumber = dto.BoothNumber,
                CreatedById = user.UserId
            };

            var result = _repository.CreateShowExhibitor(exhibitor);

            // Get the created exhibitor with navigation properties
            var createdExhibitor = _repository.GetById(result.Id);

            var responseDto = new ShowExhibitorResponseDto
            {
                Id = createdExhibitor.Id,
                ShowId = createdExhibitor.ShowId,
                CompanyId = createdExhibitor.CompanyId,
                ContactId = createdExhibitor.ContactId,
                BoothNumber = createdExhibitor.BoothNumber,
                IsActive = createdExhibitor.IsActive,
                IsArchived = createdExhibitor.IsArchived,
                CreatedAt = createdExhibitor.CreatedAt,
                ShowName = createdExhibitor.Show?.Name,
                ShowCode = createdExhibitor.Show?.Code,
                CompanyName = createdExhibitor.Company?.CompanyName,
                ContactFirstName = createdExhibitor.Contact?.FirstName,
                ContactLastName = createdExhibitor.Contact?.LastName,
                ContactEmail = createdExhibitor.Contact?.Email,
                ContactTelephone = createdExhibitor.Contact?.Telephone,
                ContactCellphone = createdExhibitor.Contact?.Cellphone,
                ContactIsRegistered = createdExhibitor.Contact?.Authuser?.IsVerified ?? false,
                CreatedByName = createdExhibitor.CreatedBy?.Username
            };

            return new GenericRespond<ShowExhibitorResponseDto>
            {
                Data = responseDto,
                StatusCode = 201,
                Message = "Exhibitor created successfully"
            };
        }

        // PUT: /ShowExhibitors/{id}
        [HttpPut("{id}")]
        public GenericRespond<ShowExhibitorResponseDto> UpdateExhibitor(int id, [FromBody] UpdateShowExhibitorDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<ShowExhibitorResponseDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var exhibitor = _repository.GetById(id);
            if (exhibitor == null)
            {
                return new GenericRespond<ShowExhibitorResponseDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Exhibitor not found"
                };
            }

            // Update company if provided
            if (dto.CompanyId.HasValue)
                exhibitor.CompanyId = dto.CompanyId.Value;

            // Update exhibitor properties
            if (dto.BoothNumber != null)
                exhibitor.BoothNumber = dto.BoothNumber;

            if (dto.IsActive.HasValue)
                exhibitor.IsActive = dto.IsActive.Value;

            exhibitor.UpdatedById = user.UserId;

            // Handle contact updates/creation
            var contactId = exhibitor.ContactId;

            // If ContactId is provided in DTO, use it (change contact)
            if (dto.ContactId.HasValue)
            {
                contactId = dto.ContactId.Value;
                exhibitor.ContactId = contactId;
            }
            // If no ContactId provided but contact fields are provided, create new contact
            else if (!contactId.HasValue && !string.IsNullOrEmpty(dto.FirstName))
            {
                // Create new contact (similar to CreateExhibitor logic)
                var newContactId = _companyRepository.AddContact(
                    dto.ContactTypeId ?? 1,
                    exhibitor.CompanyId,
                    dto.FirstName ?? "",
                    dto.LastName ?? "",
                    dto.Email ?? "",
                    dto.Telephone ?? "",
                    dto.Ext ?? "",
                    dto.Cellphone ?? "",
                    false,
                    user.Username
                );

                if (newContactId.HasValue)
                {
                    // Generate unique username and create user account
                    var baseUsername = !string.IsNullOrEmpty(dto.Email) ? dto.Email : $"user_{Guid.NewGuid().ToString().Substring(0, 8)}";
                    var uniqueUsername = GenerateUniqueUsername(baseUsername);

                    var userDto = new CreateUserDto
                    {
                        FirstName = dto.FirstName ?? "Contact",
                        LastName = dto.LastName ?? "User",
                        Email = dto.Email ?? string.Empty,
                        WorkEmail = dto.Email ?? string.Empty,
                        VerificationEmail = uniqueUsername,
                        WorkPhoneNumber = dto.Telephone ?? string.Empty,
                        MobileNumber = dto.Cellphone ?? string.Empty,
                        StatusId = 1,
                        RoleId = 4, // Exhibitor role
                        SalutationId = 1,
                        DepartmentId = 1
                    };

                    var userId = _userRepository.CreateUser(user.Username, userDto);

                    if (userId.HasValue)
                    {
                        // Set password and link contact
                        var password = "blue";
                        var hashedPassword = HashUtility.HashPassword(password);
                        _userRepository.SetPassword(userId.Value, hashedPassword);

                        var contact = _companyRepository.GetContact(newContactId.Value);
                        if (contact != null)
                        {
                            contact.Authuserid = userId.Value;
                            _companyRepository.UpdateContact(
                                newContactId.Value,
                                contact.ContactTypeId,
                                contact.CompanyId,
                                contact.FirstName,
                                contact.LastName,
                                contact.Email,
                                contact.Telephone,
                                contact.Ext,
                                contact.Cellphone,
                                contact.IsArchived ?? false,
                                user.Username
                            );
                        }

                        // Send invitation email if requested
                        if (dto.SendEmailInvite && !string.IsNullOrEmpty(dto.Email))
                        {
                            var verificationToken = _userRepository.SetInvitationTokenForUser(userId.Value);
                            if (!string.IsNullOrEmpty(verificationToken))
                            {
                                _authService.SendInviteEmail(dto.Email, verificationToken);
                            }
                        }
                    }
                    else
                    {
                        // Rollback: Delete contact if user creation failed
                        _companyRepository.DeleteContact(newContactId.Value, user.Username);

                        return new GenericRespond<ShowExhibitorResponseDto>
                        {
                            Data = null,
                            StatusCode = 400,
                            Message = "Failed to create user account for new contact. Contact creation rolled back."
                        };
                    }

                    contactId = newContactId;
                    exhibitor.ContactId = contactId;
                }
            }
            // If contact exists and contact fields are provided, update existing contact
            else if (contactId.HasValue &&
                (!string.IsNullOrEmpty(dto.FirstName) || !string.IsNullOrEmpty(dto.LastName) ||
                 !string.IsNullOrEmpty(dto.Email) || !string.IsNullOrEmpty(dto.Telephone) ||
                 !string.IsNullOrEmpty(dto.Ext) || !string.IsNullOrEmpty(dto.Cellphone)))
            {
                var contact = _companyRepository.GetContact(contactId.Value);
                if (contact != null)
                {
                    _companyRepository.UpdateContact(
                        contactId.Value,
                        dto.ContactTypeId ?? contact.ContactTypeId,
                        contact.CompanyId,
                        dto.FirstName ?? contact.FirstName,
                        dto.LastName ?? contact.LastName,
                        dto.Email ?? contact.Email,
                        dto.Telephone ?? contact.Telephone,
                        dto.Ext ?? contact.Ext,
                        dto.Cellphone ?? contact.Cellphone,
                        contact.IsArchived ?? false,
                        user.Username
                    );
                }
            }

            var result = _repository.UpdateShowExhibitor(exhibitor);

            // Get updated exhibitor with navigation properties
            var updatedExhibitor = _repository.GetById(result.Id);

            var responseDto = new ShowExhibitorResponseDto
            {
                Id = updatedExhibitor.Id,
                ShowId = updatedExhibitor.ShowId,
                CompanyId = updatedExhibitor.CompanyId,
                ContactId = updatedExhibitor.ContactId,
                BoothNumber = updatedExhibitor.BoothNumber,
                IsActive = updatedExhibitor.IsActive,
                IsArchived = updatedExhibitor.IsArchived,
                CreatedAt = updatedExhibitor.CreatedAt,
                UpdatedAt = updatedExhibitor.UpdatedAt,
                ShowName = updatedExhibitor.Show?.Name,
                ShowCode = updatedExhibitor.Show?.Code,
                CompanyName = updatedExhibitor.Company?.CompanyName,
                ContactFirstName = updatedExhibitor.Contact?.FirstName,
                ContactLastName = updatedExhibitor.Contact?.LastName,
                ContactEmail = updatedExhibitor.Contact?.Email,
                ContactTelephone = updatedExhibitor.Contact?.Telephone,
                ContactCellphone = updatedExhibitor.Contact?.Cellphone,
                ContactIsRegistered = updatedExhibitor.Contact?.Authuser?.IsVerified ?? false,
                CreatedByName = updatedExhibitor.CreatedBy?.Username,
                UpdatedByName = updatedExhibitor.UpdatedBy?.Username
            };

            return new GenericRespond<ShowExhibitorResponseDto>
            {
                Data = responseDto,
                StatusCode = 200,
                Message = "Exhibitor updated successfully"
            };
        }

        // DELETE: /ShowExhibitors/{id}
        [HttpDelete("{id}")]
        public GenericRespond<bool> DeleteExhibitor(int id)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            var result = _repository.DeleteShowExhibitor(id);

            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 404,
                Message = result ? "Exhibitor deleted successfully" : "Exhibitor not found"
            };
        }

        // POST: /ShowExhibitors/{id}/archive
        [HttpPost("{id}/archive")]
        public GenericRespond<bool> ToggleArchiveExhibitor(int id, [FromBody] ArchiveShowExhibitorDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get current exhibitor to check archive status
            var exhibitor = _repository.GetById(id);
            if (exhibitor == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Exhibitor not found"
                };
            }

            bool result;
            string message;

            if (exhibitor.IsArchived == true)
            {
                // Unarchive the exhibitor
                result = _repository.UnarchiveShowExhibitor(id, user.UserId);
                message = result ? "Exhibitor unarchived successfully" : "Failed to unarchive exhibitor";
            }
            else
            {
                // Archive the exhibitor
                result = _repository.ArchiveShowExhibitor(id, user.UserId, dto.ArchiveReason);
                message = result ? "Exhibitor archived successfully" : "Failed to archive exhibitor";
            }

            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 500,
                Message = message
            };
        }

        // GET: /ShowExhibitors/show/{showId}/stats
        [HttpGet("show/{showId}/stats")]
        public GenericRespond<ShowExhibitorStatsDto> GetExhibitorStats(int showId)
        {
            var totalExhibitors = _repository.GetExhibitorCount(showId);
            var activeExhibitors = _repository.GetExhibitorCount(showId, isActive: true);
            var archivedExhibitors = _repository.GetExhibitorCount(showId, isArchived: true);

            var exhibitors = _repository.GetExhibitorsByShowId(showId);
            var exhibitorsWithBooths = exhibitors.Count(e => e.BoothNumber != null && e.BoothNumber.Any());
            var exhibitorsWithoutBooths = totalExhibitors - exhibitorsWithBooths;

            var exhibitorsByCompany = _repository.GetExhibitorStatsByCompany(showId);
            var allBoothNumbers = _repository.GetUsedBoothNumbers(showId).ToList();

            var stats = new ShowExhibitorStatsDto
            {
                TotalExhibitors = totalExhibitors,
                ActiveExhibitors = activeExhibitors,
                ArchivedExhibitors = archivedExhibitors,
                ExhibitorsWithBooths = exhibitorsWithBooths,
                ExhibitorsWithoutBooths = exhibitorsWithoutBooths,
                ExhibitorsByCompany = exhibitorsByCompany,
                AllBoothNumbers = allBoothNumbers
            };

            return new GenericRespond<ShowExhibitorStatsDto>
            {
                Data = stats,
                StatusCode = 200,
                Message = "Exhibitor statistics retrieved successfully"
            };
        }

        // GET: /ShowExhibitors/show/{showId}/booths
        [HttpGet("show/{showId}/booths")]
        public GenericRespond<IEnumerable<string>> GetUsedBoothNumbers(int showId)
        {
            var boothNumbers = _repository.GetUsedBoothNumbers(showId);

            return new GenericRespond<IEnumerable<string>>
            {
                Data = boothNumbers,
                StatusCode = 200,
                Message = "Used booth numbers retrieved successfully"
            };
        }

        // GET: /ShowExhibitors/show/{showId}/booths/available
        [HttpGet("show/{showId}/booths/available")]
        public GenericRespond<bool> CheckBoothAvailability(int showId, [FromQuery] string boothNumber, [FromQuery] int? excludeExhibitorId = null)
        {
            if (string.IsNullOrEmpty(boothNumber))
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 400,
                    Message = "Booth number is required"
                };
            }

            var isAvailable = _repository.IsBoothNumberAvailable(showId, boothNumber, excludeExhibitorId);

            return new GenericRespond<bool>
            {
                Data = isAvailable,
                StatusCode = 200,
                Message = isAvailable ? "Booth number is available" : "Booth number is already taken"
            };
        }
    }
}
