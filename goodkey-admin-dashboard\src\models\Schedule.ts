export interface Schedule {
  id: number;
  code: string;
  name: string;
  description: string;
  notes?: string;
  isActive: boolean;
  createdBy?: {
    id: number;
    name: string;
  };
  updatedBy?: {
    id: number;
    name: string;
  };
  createdAt: string;
  updatedAt?: string;
}

export interface CreateScheduleRequest {
  name: string;
  description: string;
  notes?: string;
  isActive: boolean;
}

export interface UpdateScheduleRequest {
  name?: string;
  description?: string;
  notes?: string;
  isActive?: boolean;
}
