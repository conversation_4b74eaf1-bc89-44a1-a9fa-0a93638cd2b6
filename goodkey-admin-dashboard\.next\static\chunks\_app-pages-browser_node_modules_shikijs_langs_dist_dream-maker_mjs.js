"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_dream-maker_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/dream-maker.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/dream-maker.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Dream Maker\\\",\\\"fileTypes\\\":[\\\"dm\\\",\\\"dme\\\"],\\\"foldingStartMarker\\\":\\\"/\\\\\\\\*\\\\\\\\*(?!\\\\\\\\*)|^(?![^{]*?//|[^{]*?/\\\\\\\\*(?!.*?\\\\\\\\*/.*?\\\\\\\\{)).*?\\\\\\\\{\\\\\\\\s*($|//|/\\\\\\\\*(?!.*?\\\\\\\\*/.*\\\\\\\\S))\\\",\\\"foldingStopMarker\\\":\\\"(?<!\\\\\\\\*)\\\\\\\\*\\\\\\\\*/|^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"dream-maker\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.dm\\\"},\\\"5\\\":{\\\"name\\\":\\\"variable.other.dm\\\"}},\\\"match\\\":\\\"(var)[\\\\\\\\/ ](?:(static|global|tmp|const)\\\\\\\\/)?(?:(datum|atom(?:\\\\\\\\/movable)?|obj|mob|turf|area|savefile|list|client|sound|image|database|matrix|regex|exception)\\\\\\\\/)?(?:([a-zA-Z0-9_\\\\\\\\-$]*)\\\\\\\\/)*([A-Za-z0-9_$]*)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.initialization.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\\\\\.?[0-9]*)|(\\\\\\\\.[0-9]+))((e|E)(\\\\\\\\+|-)?[0-9]+)?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sleep|spawn|break|continue|do|else|for|goto|if|return|switch|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b(del|new)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b(proc|verb|datum|atom(/movable)?|obj|mob|turf|area|savefile|list|client|sound|image|database|matrix|regex|exception)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b(as|const|global|set|static|tmp)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b(usr|world|src|args)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.language.dm\\\"},{\\\"match\\\":\\\"(\\\\\\\\?|(>|<)(=)?|\\\\\\\\.|:|/(=)?|~|\\\\\\\\+(\\\\\\\\+|=)?|-(-|=)?|\\\\\\\\*(\\\\\\\\*|=)?|%|>>|<<|=(=)?|!(=)?|<>|&|&&|\\\\\\\\^|\\\\\\\\||\\\\\\\\|\\\\\\\\||\\\\\\\\bto\\\\\\\\b|\\\\\\\\bin\\\\\\\\b|\\\\\\\\bstep\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.operator.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z_][A-Z_0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\bnull\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.dm\\\"},{\\\"begin\\\":\\\"{\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dm\\\"}},\\\"end\\\":\\\"\\\\\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dm\\\"}},\\\"name\\\":\\\"string.quoted.triple.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_embedded_expression\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dm\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dm\\\"}},\\\"name\\\":\\\"string.quoted.double.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_embedded_expression\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dm\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dm\\\"}},\\\"name\\\":\\\"string.quoted.single.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((\\\\\\\\#)\\\\\\\\s*define)\\\\\\\\s+((?<id>[a-zA-Z_][a-zA-Z0-9_]*))(?:(\\\\\\\\()(\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*((,)\\\\\\\\s*\\\\\\\\g<id>\\\\\\\\s*)*(?:\\\\\\\\.\\\\\\\\.\\\\\\\\.)?)(\\\\\\\\)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.define.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.dm\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.dm\\\"},\\\"6\\\":{\\\"name\\\":\\\"variable.parameter.preprocessor.dm\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.dm\\\"},\\\"9\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.dm\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((\\\\\\\\#)\\\\\\\\s*define)\\\\\\\\s+((?<id>[a-zA-Z_][a-zA-Z0-9_]*))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.define.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.other.preprocessor.dm\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(error|warn))\\\\\\\\b\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.import.error.dm\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"meta.preprocessor.diagnostic.dm\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?>\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"punctuation.separator.continuation.dm\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*(?:elif|else|if|ifdef|ifndef))|((#)\\\\\\\\s*(undef|include)))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.directive.$5.dm\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.directive.dm\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.dm\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?>\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"punctuation.separator.continuation.dm\\\"}]},{\\\"include\\\":\\\"#block\\\"},{\\\"begin\\\":\\\"(?:^|(?:(?=\\\\\\\\s)(?<!else|new|return)(?<=\\\\\\\\w)|(?=\\\\\\\\s*[A-Za-z_])(?<!&&)(?<=[*&>])))(\\\\\\\\s*)(?!(while|for|do|if|else|switch|catch|enumerate|return|r?iterate)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.function.leading.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.dm\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.dm\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\})|(?=#)|(;)?\\\",\\\"name\\\":\\\"meta.function.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"match\\\":\\\"\\\\\\\\bconst\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.dm\\\"},{\\\"include\\\":\\\"#block\\\"}]}],\\\"repository\\\":{\\\"access\\\":{\\\"match\\\":\\\"\\\\\\\\.[a-zA-Z_][a-zA-Z_0-9]*\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"variable.other.dot-access.dm\\\"},\\\"block\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"name\\\":\\\"meta.block.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"block_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-other-block\\\"},{\\\"include\\\":\\\"#access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.function-call.leading.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.any-method.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.dm\\\"}},\\\"match\\\":\\\"(?:(?=\\\\\\\\s)(?:(?<=else|new|return)|(?<!\\\\\\\\w))(\\\\\\\\s+))?(\\\\\\\\b(?!(while|for|do|if|else|switch|catch|enumerate|return|r?iterate)\\\\\\\\s*\\\\\\\\()(?:(?!NS)[A-Za-z_][A-Za-z0-9_]*+\\\\\\\\b|::)++)\\\\\\\\s*(\\\\\\\\()\\\",\\\"name\\\":\\\"meta.function-call.dm\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"$base\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.block.dm\\\"}},\\\"match\\\":\\\"^/\\\\\\\\* =(\\\\\\\\s*.*?)\\\\\\\\s*= \\\\\\\\*/$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.block.dm\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.dm\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"}]},{\\\"match\\\":\\\"\\\\\\\\*/.*\\\\\\\\n\\\",\\\"name\\\":\\\"invalid.illegal.stray-comment-end.dm\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.line.dm\\\"}},\\\"match\\\":\\\"^// =(\\\\\\\\s*.*?)\\\\\\\\s*=\\\\\\\\s*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.banner.dm\\\"},{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.dm\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.dm\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?>\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"punctuation.separator.continuation.dm\\\"}]}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"comment\\\":\\\"eat nested preprocessor if(def)s\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"meta.parens.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.dm\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*$)\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"}]}]},\\\"preprocessor-rule-disabled-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.dm\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*$)\\\",\\\"name\\\":\\\"comment.block.preprocessor.if-branch.in-block\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"}]}]},\\\"preprocessor-rule-enabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.dm\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]}]},\\\"preprocessor-rule-enabled-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#(if)\\\\\\\\s+(0*1)\\\\\\\\b)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.if.dm\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(else)\\\\\\\\b).*\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.else.dm\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.in-block\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"}]},{\\\"begin\\\":\\\"\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*#\\\\\\\\s*(else|endif)\\\\\\\\b.*$)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"preprocessor-rule-other\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#\\\\\\\\s*(if(n?def)?))\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#\\\\\\\\s*(endif))\\\\\\\\b).*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$base\\\"}]},\\\"preprocessor-rule-other-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(if(n?def)?)\\\\\\\\b.*?(?:(?=(?://|/\\\\\\\\*))|$))\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.dm\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.dm\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*(#\\\\\\\\s*(endif)\\\\\\\\b).*$\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"string_embedded_expression\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\\[\\\",\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"string.interpolated.dm\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(h(?:(?:er|im)self|ers|im)|([tTsS]?he)|He|[Hh]is|[aA]n?|(?:im)?proper|\\\\\\\\.\\\\\\\\.\\\\\\\\.|(?:icon|ref|[Rr]oman)(?=\\\\\\\\[)|[s<>\\\\\\\"n\\\\\\\\n \\\\\\\\[])\\\",\\\"name\\\":\\\"constant.character.escape.dm\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.dm\\\"}]}},\\\"scopeName\\\":\\\"source.dm\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/dream-maker.mjs\n"));

/***/ })

}]);