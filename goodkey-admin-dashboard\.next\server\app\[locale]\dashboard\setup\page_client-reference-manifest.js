globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/dashboard/setup/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_provider/providers/query-provider.tsx":{"*":{"id":"(ssr)/./src/components/app_provider/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_provider/providers/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/app_provider/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/overlay/OverlayContainer.tsx":{"*":{"id":"(ssr)/./src/components/ui/overlay/OverlayContainer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/tooltip.tsx":{"*":{"id":"(ssr)/./src/components/ui/tooltip.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/components/login-form.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/login/components/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_header/app-header.tsx":{"*":{"id":"(ssr)/./src/components/app_header/app-header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/events-table.tsx":{"*":{"id":"(ssr)/./src/components/ui/events-table.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/components/EventSidebarWrapper.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/dashboard/event/[id]/components/EventSidebarWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/EventPageClient.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/dashboard/event/[id]/EventPageClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/ExhibitorsClient.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/dashboard/event/[id]/exhibitors/ExhibitorsClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/setup-sidebar.tsx":{"*":{"id":"(ssr)/./src/components/setup-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/heading.tsx":{"*":{"id":"(ssr)/./src/components/ui/heading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/setup/users-roles/role-management/components/role_group_table/index.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/dashboard/setup/users-roles/role-management/components/role_group_table/index.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ExhibitorImportClient.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ExhibitorImportClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\globals.scss":{"id":"(app-pages-browser)/./src/app/globals.scss","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/dashboard/setup/page","static/chunks/app/%5Blocale%5D/dashboard/setup/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/[locale]/dashboard/setup/page","static/chunks/app/%5Blocale%5D/dashboard/setup/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\not-found.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\not-found.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\HydrationBoundary.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\isRestoring.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryClientProvider.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryErrorResetBoundary.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useInfiniteQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useIsFetching.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutation.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutationState.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useQueries.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseInfiniteQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQueries.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQuery.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\app_provider\\providers\\query-provider.tsx":{"id":"(app-pages-browser)/./src/components/app_provider/providers/query-provider.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\app_provider\\providers\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/app_provider/providers/theme-provider.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\ui\\overlay\\OverlayContainer.tsx":{"id":"(app-pages-browser)/./src/components/ui/overlay/OverlayContainer.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\ui\\tooltip.tsx":{"id":"(app-pages-browser)/./src/components/ui/tooltip.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\login\\components\\login-form.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/login/components/login-form.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\app_header\\app-header.tsx":{"id":"(app-pages-browser)/./src/components/app_header/app-header.tsx","name":"*","chunks":["app/[locale]/dashboard/layout","static/chunks/app/%5Blocale%5D/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\ui\\events-table.tsx":{"id":"(app-pages-browser)/./src/components/ui/events-table.tsx","name":"*","chunks":["app/[locale]/dashboard/page","static/chunks/app/%5Blocale%5D/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\event\\[id]\\components\\EventSidebarWrapper.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/components/EventSidebarWrapper.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\event\\[id]\\EventPageClient.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/EventPageClient.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\event\\[id]\\exhibitors\\ExhibitorsClient.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/ExhibitorsClient.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\setup-sidebar.tsx":{"id":"(app-pages-browser)/./src/components/setup-sidebar.tsx","name":"*","chunks":["app/[locale]/dashboard/setup/layout","static/chunks/app/%5Blocale%5D/dashboard/setup/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\components\\ui\\heading.tsx":{"id":"(app-pages-browser)/./src/components/ui/heading.tsx","name":"*","chunks":["app/[locale]/dashboard/setup/page","static/chunks/app/%5Blocale%5D/dashboard/setup/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\setup\\users-roles\\role-management\\components\\role_group_table\\index.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/dashboard/setup/users-roles/role-management/components/role_group_table/index.tsx","name":"*","chunks":[],"async":true},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\event\\[id]\\exhibitors\\import\\components\\ExhibitorImportClient.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ExhibitorImportClient.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/page.tsx","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\":[],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\not-found":[{"inlined":false,"path":"static/css/app/not-found.css"}],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\layout":[{"inlined":false,"path":"static/css/app/[locale]/layout.css"}],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\page":[],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\layout":[{"inlined":false,"path":"static/css/app/[locale]/dashboard/layout.css"}],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\loading":[],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\page":[],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\setup\\layout":[],"C:\\Users\\<USER>\\source\\repos\\Project\\goodkey-admin-dashboard\\src\\app\\[locale]\\dashboard\\setup\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.scss":{"*":{"id":"(rsc)/./src/app/globals.scss","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useIsFetching.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useMutation.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useMutationState.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQueries.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js":{"*":{"id":"(rsc)/./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":{"*":{"id":"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_provider/providers/query-provider.tsx":{"*":{"id":"(rsc)/./src/components/app_provider/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_provider/providers/theme-provider.tsx":{"*":{"id":"(rsc)/./src/components/app_provider/providers/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/overlay/OverlayContainer.tsx":{"*":{"id":"(rsc)/./src/components/ui/overlay/OverlayContainer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/tooltip.tsx":{"*":{"id":"(rsc)/./src/components/ui/tooltip.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/login/components/login-form.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/login/components/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/app_header/app-header.tsx":{"*":{"id":"(rsc)/./src/components/app_header/app-header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/events-table.tsx":{"*":{"id":"(rsc)/./src/components/ui/events-table.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/components/EventSidebarWrapper.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/dashboard/event/[id]/components/EventSidebarWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/EventPageClient.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/dashboard/event/[id]/EventPageClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/ExhibitorsClient.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/dashboard/event/[id]/exhibitors/ExhibitorsClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/setup-sidebar.tsx":{"*":{"id":"(rsc)/./src/components/setup-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/heading.tsx":{"*":{"id":"(rsc)/./src/components/ui/heading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/setup/users-roles/role-management/components/role_group_table/index.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/dashboard/setup/users-roles/role-management/components/role_group_table/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ExhibitorImportClient.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/dashboard/event/[id]/exhibitors/import/components/ExhibitorImportClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}