'use client';

import './style/index.scss';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { formatDateTime } from '@/utils/date-format';

interface IPropertyInfoBox {
  id?: number;
}

function PropertyDetailBox({ id }: IPropertyInfoBox) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Property Detail', { id }],
    queryFn: () => PropertyQuery.getDetail(id!),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      {data && (
        <div className="category-info-box">
          <div className="text-sm text-slate-700">
            {data?.code && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600">Code:</span>
                <span className="whitespace-nowrap">{data.code}</span>
              </div>
            )}
            {data?.createdAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Created At:
                </span>
                <span className="whitespace-nowrap ">
                  {formatDateTime(data.createdAt)}
                </span>
              </div>
            )}
            {data?.updatedAt && (
              <div className="flex justify-between">
                <span className="font-medium text-slate-600 whitespace-nowrap">
                  Last Updated:&nbsp;
                </span>
                <span className="whitespace-nowrap">
                  {formatDateTime(data.updatedAt)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </Suspense>
  );
}

export default PropertyDetailBox;
