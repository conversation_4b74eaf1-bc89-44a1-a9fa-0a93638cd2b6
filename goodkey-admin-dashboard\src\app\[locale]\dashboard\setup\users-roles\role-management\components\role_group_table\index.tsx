'use client';

import { useQuery } from '@tanstack/react-query';
import { Users, Settings } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FaPlus } from 'react-icons/fa6';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/Badge';

import RoleGroupQuery from '@/services/queries/RoleGroupQuery';
import { BasicRoleGroupDetail } from '@/models/RoleGroup';
import AddRoleGroupModal from './AddRoleGroupModal';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/Collapsible';
import EditRoleGroupModal from './EditRoleGroupModal';
import { ChevronDownIcon, ChevronRightIcon } from '@/assets/Icons';

export default function RoleGroupTable() {
  const router = useRouter();
  const { data: roleGroups, isLoading } = useQuery({
    queryKey: RoleGroupQuery.tags,
    queryFn: () => RoleGroupQuery.getAll(),
  });

  const [openGroups, setOpenGroups] = useState<Set<number>>(new Set());
  const [editingGroup, setEditingGroup] = useState<BasicRoleGroupDetail | null>(
    null,
  );
  const [showAddModal, setShowAddModal] = useState(false);

  const toggleGroup = (groupId: number) => {
    const newOpenGroups = new Set(openGroups);
    if (newOpenGroups.has(groupId)) {
      newOpenGroups.delete(groupId);
    } else {
      newOpenGroups.add(groupId);
    }
    setOpenGroups(newOpenGroups);
  };

  const getTotalUserCount = (group: BasicRoleGroupDetail) => {
    return group.roles.reduce((total, role) => total + role.userCount, 0);
  };

  const handleEditGroup = (group: BasicRoleGroupDetail) => {
    setEditingGroup(group);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading role groups...</div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-slate-600">
              Manage role groups and their associated roles
            </p>
          </div>
          <Button
            variant="main"
            className="shadow-sm"
            onClick={() => setShowAddModal(true)}
            iconName="AddIcon"
          >
            Add Role Group
          </Button>
        </div>

        <div className="space-y-4">
          {roleGroups?.map((group) => (
            <Card
              key={group.id}
              className="border border-slate-200 hover:border-main/30 hover:shadow-sm transition-all duration-200"
            >
              <Collapsible
                open={openGroups.has(group.id)}
                onOpenChange={() => toggleGroup(group.id)}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="pb-4 cursor-pointer hover:bg-main/5 transition-colors duration-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0">
                          {openGroups.has(group.id) ? (
                            <ChevronDownIcon className="h-4 w-4 text-main" />
                          ) : (
                            <ChevronRightIcon className="h-4 w-4 text-slate-600" />
                          )}
                        </div>
                        <div>
                          <CardTitle className="text-lg text-slate-900">
                            {group.name}
                          </CardTitle>
                          <div className="flex items-center gap-4 mt-2">
                            <Badge
                              variant="secondary"
                              className="text-xs bg-brand-brown/10 text-brand-brown border-brand-brown/20"
                            >
                              Level {group.minLevel}-{group.maxLevel}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-slate-600">
                              <Users className="h-4 w-4 text-brand-lime" />
                              {getTotalUserCount(group)} users
                            </div>
                            <div className="text-sm text-slate-600">
                              {group.roles.length} roles
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-main/20 text-main hover:bg-main hover:text-white transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditGroup(group);
                          }}
                        >
                          <Settings className="h-4 w-4 mr-1" />
                          Edit Group
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <div className="bg-slate-50/50 rounded-lg p-4 border-t border-slate-200">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-sm text-slate-900">
                          Roles in this group
                        </h4>
                        <Button
                          variant="main"
                          // size="sm"
                          // className="text-xs"
                          iconName="AddIcon"
                          onClick={() =>
                            router.push(
                              `/dashboard/setup/users-roles/role-management/add?groupId=${group.id}&minLevel=${group.minLevel}&maxLevel=${group.maxLevel}&groupName=${encodeURIComponent(group.name)}`,
                            )
                          }
                        >
                          Add Role
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {group.roles.map((role) => (
                          <div
                            key={role.id}
                            className="bg-white rounded-lg p-4 border border-slate-200 hover:border-brand-magenta/30 hover:shadow-sm transition-all duration-200"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="font-medium text-sm text-slate-900 mb-1">
                                  {role.name}
                                </div>
                                <div className="flex items-center gap-1 text-xs text-slate-500">
                                  <Users className="h-3 w-3 text-brand-lime" />
                                  {role.userCount} users
                                </div>
                              </div>
                              <Link
                                href={`/dashboard/setup/users-roles/role-management/${role.id}`}
                              >
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-brand-magenta/10 hover:text-brand-magenta transition-colors"
                                  iconName="EditIcon"
                                  iconProps={{
                                    className: 'text-brand-magenta',
                                    size: 16,
                                  }}
                                ></Button>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>

        {roleGroups?.length === 0 && (
          <Card className="text-center py-12 border-slate-200">
            <CardContent>
              <div className="text-slate-500 mb-4">No role groups found</div>
              <Button
                variant="main"
                className="shadow-sm"
                onClick={() => setShowAddModal(true)}
              >
                <FaPlus className="mr-2 h-4 w-4" />
                Create your first role group
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      <AddRoleGroupModal
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
      />

      {editingGroup && (
        <EditRoleGroupModal
          roleGroup={editingGroup}
          open={!!editingGroup}
          onClose={() => setEditingGroup(null)}
        />
      )}
    </>
  );
}
