using goodkey_common.DTO;
using goodkey_common.DTO.Show;
using goodkey_common.Models;
using goodkey_common.Repositories;
using goodkey_cms.Services;
using Microsoft.AspNetCore.Mvc;
using FileType = goodkey_cms.Services.FileType;
using StorageService = goodkey_cms.Services.StorageService;
using Visibility = goodkey_cms.Services.Visibility;

namespace GoodkeyAPI.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class ShowDocumentsController : ControllerBase
    {
        private readonly IShowDocumentRepository _repository;
        private readonly StorageService _storageService;
        private readonly AuthService _authService;

        public ShowDocumentsController(
            IShowDocumentRepository repository,
            StorageService storageService,
            AuthService authService)
        {
            _repository = repository;
            _storageService = storageService;
            _authService = authService;
        }

        // GET: api/ShowDocuments/show/{showId}
        [HttpGet("show/{showId}")]
        public GenericRespond<IEnumerable<ShowDocumentListDto>> GetShowDocuments(
            int showId,
            [FromQuery] ShowDocumentFilterDto? filter = null)
        {

            var documents = _repository.GetShowDocuments(showId, filter);

            var result = documents.Select(d => new ShowDocumentListDto
            {
                Id = d.Id,
                Name = d.Name,
                Description = d.Description,
                OriginalFileName = d.OriginalFileName,
                FileSize = d.FileSize,
                MimeType = d.MimeType,
                IsRequired = d.IsRequired,
                IsPublic = d.IsPublic,
                Version = d.Version,
                Status = d.Status,
                UploadedAt = d.UploadedAt,
                DocumentTypeName = d.DocumentType?.Name,
                UploadedByName = $"{d.UploadedBy?.FirstName} {d.UploadedBy?.LastName}".Trim(),
                IsArchived = d.IsArchived
            });

            return new GenericRespond<IEnumerable<ShowDocumentListDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Show documents retrieved successfully"
            };


        }

        // GET: api/ShowDocuments/{id}
        [HttpGet("{id}")]
        public GenericRespond<ShowDocumentDto> GetShowDocument(int id)
        {

            var document = _repository.GetShowDocumentById(id);
            if (document == null)
            {
                return new GenericRespond<ShowDocumentDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Document not found"
                };
            }

            var result = new ShowDocumentDto
            {
                Id = document.Id,
                ShowId = document.ShowId,
                DocumentTypeId = document.DocumentTypeId,
                Name = document.Name,
                Description = document.Description,
                FileName = document.FileName,
                OriginalFileName = document.OriginalFileName,
                FilePath = document.FilePath,
                FileSize = document.FileSize,
                MimeType = document.MimeType,
                IsRequired = document.IsRequired,
                IsPublic = document.IsPublic,
                Version = document.Version,
                Status = document.Status,
                UploadedAt = document.UploadedAt,
                UploadedById = document.UploadedById,
                UpdatedAt = document.UpdatedAt,
                UpdatedById = document.UpdatedById,
                ArchivedAt = document.ArchivedAt,
                ArchivedById = document.ArchivedById,
                IsArchived = document.IsArchived,
                ShowName = document.Show?.Name,
                ShowCode = document.Show?.Code,
                DocumentTypeName = document.DocumentType?.Name,
                UploadedByName = $"{document.UploadedBy?.FirstName} {document.UploadedBy?.LastName}".Trim(),
                UpdatedByName = $"{document.UpdatedBy?.FirstName} {document.UpdatedBy?.LastName}".Trim(),
                ArchivedByName = $"{document.ArchivedBy?.FirstName} {document.ArchivedBy?.LastName}".Trim()
            };

            return new GenericRespond<ShowDocumentDto>
            {
                Data = result,
                StatusCode = 200,
                Message = "Document retrieved successfully"
            };

        }

        // POST: api/ShowDocuments
        [HttpPost]
        public GenericRespond<ShowDocumentDto> CreateShowDocument([FromForm] CreateShowDocumentDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<ShowDocumentDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }



            // Validate show exists
            var show = _repository.GetShow(dto.ShowId);
            if (show == null)
            {
                return new GenericRespond<ShowDocumentDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Show not found"
                };
            }

            // Upload file using StorageService
            var uploadResult = _storageService.UploadFile(
                dto.File,
                FileType.Document,
                $"shows/{dto.ShowId}",
                Visibility.Protected,
                true,
                $"{dto.Name}_{DateTime.Now:yyyyMMddHHmmss}"
            );

            // Create document entity
            var document = new ShowDocuments
            {
                ShowId = dto.ShowId,
                DocumentTypeId = dto.DocumentTypeId,
                Name = dto.Name,
                Description = dto.Description,
                FileName = uploadResult.Name,
                OriginalFileName = dto.File.FileName,
                FilePath = uploadResult.RelativePath,
                FileSize = dto.File.Length,
                MimeType = dto.File.ContentType,
                IsRequired = dto.IsRequired ?? false,
                IsPublic = dto.IsPublic ?? false,
                UploadedById = user.UserId
            };

            var createdDocument = _repository.CreateShowDocument(document);

            // Get the created document with navigation properties
            var result = _repository.GetShowDocumentById(createdDocument.Id);

            var responseDto = new ShowDocumentDto
            {
                Id = result!.Id,
                ShowId = result.ShowId,
                DocumentTypeId = result.DocumentTypeId,
                Name = result.Name,
                Description = result.Description,
                FileName = result.FileName,
                OriginalFileName = result.OriginalFileName,
                FilePath = result.FilePath,
                FileSize = result.FileSize,
                MimeType = result.MimeType,
                IsRequired = result.IsRequired,
                IsPublic = result.IsPublic,
                Version = result.Version,
                Status = result.Status,
                UploadedAt = result.UploadedAt,
                UploadedById = result.UploadedById,
                ShowName = result.Show?.Name,
                ShowCode = result.Show?.Code,
                DocumentTypeName = result.DocumentType?.Name,
                UploadedByName = $"{result.UploadedBy?.FirstName} {result.UploadedBy?.LastName}".Trim()
            };

            return new GenericRespond<ShowDocumentDto>
            {
                Data = responseDto,
                StatusCode = 201,
                Message = "Document uploaded successfully"
            };

        }

        // PUT: api/ShowDocuments/{id}
        [HttpPut("{id}")]
        public GenericRespond<ShowDocumentDto> UpdateShowDocument(int id, [FromForm] UpdateShowDocumentDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<ShowDocumentDto>
                {
                    Data = null,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }


            var document = _repository.GetShowDocumentById(id);
            if (document == null)
            {
                return new GenericRespond<ShowDocumentDto>
                {
                    Data = null,
                    StatusCode = 404,
                    Message = "Document not found"
                };
            }

            // Update document properties
            if (!string.IsNullOrEmpty(dto.Name))
                document.Name = dto.Name;

            if (dto.Description != null)
                document.Description = dto.Description;

            if (dto.IsRequired.HasValue)
                document.IsRequired = dto.IsRequired.Value;

            if (dto.IsPublic.HasValue)
                document.IsPublic = dto.IsPublic.Value;

            if (!string.IsNullOrEmpty(dto.Status))
                document.Status = dto.Status;

            document.UpdatedById = user.UserId;

            // Handle file replacement if provided
            if (dto.File != null)
            {
                // Delete old file
                if (!string.IsNullOrEmpty(document.FilePath))
                {
                    _storageService.DeleteFileSpecific(document.FilePath, FileType.Document, Visibility.Protected);
                }

                // Upload new file
                var uploadResult = _storageService.UploadFile(
                    dto.File,
                    FileType.Document,
                    $"shows/{document.ShowId}",
                    Visibility.Protected,
                    true,
                    $"{document.Name}_{DateTime.Now:yyyyMMddHHmmss}"
                );

                _repository.UpdateFilePath(
                    id,
                    uploadResult.RelativePath,
                    uploadResult.Name,
                    dto.File.FileName,
                    dto.File.Length,
                    dto.File.ContentType
                );
            }

            var updatedDocument = _repository.UpdateShowDocument(document);

            // Get updated document with navigation properties
            var result = _repository.GetShowDocumentById(updatedDocument.Id);

            var responseDto = new ShowDocumentDto
            {
                Id = result!.Id,
                ShowId = result.ShowId,
                DocumentTypeId = result.DocumentTypeId,
                Name = result.Name,
                Description = result.Description,
                FileName = result.FileName,
                OriginalFileName = result.OriginalFileName,
                FilePath = result.FilePath,
                FileSize = result.FileSize,
                MimeType = result.MimeType,
                IsRequired = result.IsRequired,
                IsPublic = result.IsPublic,
                Version = result.Version,
                Status = result.Status,
                UploadedAt = result.UploadedAt,
                UploadedById = result.UploadedById,
                UpdatedAt = result.UpdatedAt,
                UpdatedById = result.UpdatedById,
                ShowName = result.Show?.Name,
                ShowCode = result.Show?.Code,
                DocumentTypeName = result.DocumentType?.Name,
                UploadedByName = $"{result.UploadedBy?.FirstName} {result.UploadedBy?.LastName}".Trim(),
                UpdatedByName = $"{result.UpdatedBy?.FirstName} {result.UpdatedBy?.LastName}".Trim()
            };

            return new GenericRespond<ShowDocumentDto>
            {
                Data = responseDto,
                StatusCode = 200,
                Message = "Document updated successfully"
            };

        }

        // GET: api/ShowDocuments/{id}/download
        [HttpGet("{id}/download")]
        public IActionResult DownloadDocument(int id)
        {

            var document = _repository.GetShowDocumentById(id);
            if (document == null)
            {
                return NotFound("Document not found");
            }

            if (string.IsNullOrEmpty(document.FilePath))
            {
                return NotFound("File not found");
            }

            var fileDownload = _storageService.DownloadDocument(document.FilePath, Visibility.Protected);

            return File(
                fileDownload.FileBytes,
                fileDownload.ContentType,
                document.OriginalFileName ?? document.FileName
            );


        }

        // DELETE: api/ShowDocuments/{id}
        [HttpDelete("{id}")]
        public GenericRespond<bool> DeleteShowDocument(int id)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }


            var document = _repository.GetShowDocumentById(id);
            if (document == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Document not found"
                };
            }

            // Delete file from storage
            if (!string.IsNullOrEmpty(document.FilePath))
            {
                _storageService.DeleteFileSpecific(document.FilePath, FileType.Document, Visibility.Protected);
            }

            // Delete document record
            var result = _repository.DeleteShowDocument(id);

            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 500,
                Message = result ? "Document deleted successfully" : "Failed to delete document"
            };

        }

        // POST: api/ShowDocuments/{id}/archive
        [HttpPost("{id}/archive")]
        public GenericRespond<bool> ToggleArchiveDocument(int id, [FromBody] ArchiveShowDocumentDto dto)
        {
            var user = _authService.Current;
            if (user == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 401,
                    Message = "Unauthorized"
                };
            }

            // Get current document to check archive status
            var document = _repository.GetShowDocumentById(id);
            if (document == null)
            {
                return new GenericRespond<bool>
                {
                    Data = false,
                    StatusCode = 404,
                    Message = "Document not found"
                };
            }

            bool result;
            string message;

            if (document.IsArchived == true)
            {
                // Unarchive the document
                result = _repository.UnarchiveShowDocument(id, user.UserId);
                message = result ? "Document unarchived successfully" : "Failed to unarchive document";
            }
            else
            {
                // Archive the document
                result = _repository.ArchiveShowDocument(id, user.UserId, dto.ArchiveReason);
                message = result ? "Document archived successfully" : "Failed to archive document";
            }

            return new GenericRespond<bool>
            {
                Data = result,
                StatusCode = result ? 200 : 500,
                Message = message
            };
        }





        // GET: api/ShowDocuments/show/{showId}/stats
        [HttpGet("show/{showId}/stats")]
        public GenericRespond<ShowDocumentStatsDto> GetShowDocumentStats(int showId)
        {

            var stats = _repository.GetShowDocumentStats(showId);

            return new GenericRespond<ShowDocumentStatsDto>
            {
                Data = stats,
                StatusCode = 200,
                Message = "Document statistics retrieved successfully"
            };


        }

        // GET: api/ShowDocuments/document-types
        [HttpGet("document-types")]
        public GenericRespond<IEnumerable<DocumentTypeDto>> GetDocumentTypes()
        {

            var documentTypes = _repository.GetAvailableDocumentTypes();

            var result = documentTypes.Select(dt => new DocumentTypeDto
            {
                Id = dt.Id,
                Name = dt.Name,
                ExtensionCode = dt.ExtensionCode,
                Extension = dt.Extension,
                IsAvailable = dt.IsAvailable,
                IsImage = dt.IsImage
            });

            return new GenericRespond<IEnumerable<DocumentTypeDto>>
            {
                Data = result,
                StatusCode = 200,
                Message = "Document types retrieved successfully"
            };


        }


    }
}
