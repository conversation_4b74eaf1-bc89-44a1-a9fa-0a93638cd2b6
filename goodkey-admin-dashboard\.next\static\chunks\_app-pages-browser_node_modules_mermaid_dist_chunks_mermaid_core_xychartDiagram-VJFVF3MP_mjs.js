"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_xychartDiagram-VJFVF3MP_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_C3MQ5ANM_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-C3MQ5ANM.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_7B677QYD_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-7B677QYD.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7B677QYD.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = (0,_chunk_C3MQ5ANM_mjs__WEBPACK_IMPORTED_MODULE_0__.computeDimensionOfText)(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\n\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleBand)().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleBand)().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\n\nvar LinearAxis = class extends BaseAxis {\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleLinear)().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleLinear)().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\n\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = (0,d3__WEBPACK_IMPORTED_MODULE_4__.line)().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = (0,d3__WEBPACK_IMPORTED_MODULE_4__.line)().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getThemeVariables)();\n  const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_1__.cleanAndMerge)(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_1__.cleanAndMerge)(\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(text.trim(), config);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle)();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartConfig, \"getChartConfig\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.clear)();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle,\n  getAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccTitle,\n  setDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setDiagramTitle,\n  getDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle,\n  getAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccDescription,\n  setAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getTextTransformation, \"getTextTransformation\");\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = (0,_chunk_7B677QYD_mjs__WEBPACK_IMPORTED_MODULE_2__.selectSvgElement)(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.configureSvgSize)(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs\n"));

/***/ })

}]);