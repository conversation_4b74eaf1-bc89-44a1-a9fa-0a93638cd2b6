import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import AppLayout from '@/components/ui/app_layout';
import UsersQuery from '@/services/queries/UsersQuery';
import { getQueryClient } from '@/utils/query-client';

import UserTable from './components/user_table';

interface IUserManagement {}

async function UserManagement({}: IUserManagement) {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: UsersQuery.tags,
    queryFn: () => UsersQuery.getAll(),
  });

  const t = await getTranslations('usersPage');
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Users & Roles', link: '/dashboard/setup' },
        {
          title: t('subtitle'),
          link: '/dashboard/setup/users-roles/user-management',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <UserTable />
      </HydrationBoundary>
    </AppLayout>
  );
}

export default UserManagement;
