"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/re-resizable";
exports.ids = ["vendor-chunks/re-resizable"];
exports.modules = {

/***/ "(ssr)/./node_modules/re-resizable/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/re-resizable/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resizable: () => (/* binding */ Resizable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _resizer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resizer */ \"(ssr)/./node_modules/re-resizable/lib/resizer.js\");\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\n\n\nvar DEFAULT_SIZE = {\n    width: 'auto',\n    height: 'auto',\n};\nvar clamp = function (n, min, max) { return Math.max(Math.min(n, max), min); };\nvar snap = function (n, size, gridGap) {\n    var v = Math.round(n / size);\n    return v * size + gridGap * (v - 1);\n};\nvar hasDirection = function (dir, target) {\n    return new RegExp(dir, 'i').test(target);\n};\n// INFO: In case of window is a Proxy and does not porxy Events correctly, use isTouchEvent & isMouseEvent to distinguish event type instead of `instanceof`.\nvar isTouchEvent = function (event) {\n    return Boolean(event.touches && event.touches.length);\n};\nvar isMouseEvent = function (event) {\n    return Boolean((event.clientX || event.clientX === 0) &&\n        (event.clientY || event.clientY === 0));\n};\nvar findClosestSnap = function (n, snapArray, snapGap) {\n    if (snapGap === void 0) { snapGap = 0; }\n    var closestGapIndex = snapArray.reduce(function (prev, curr, index) { return (Math.abs(curr - n) < Math.abs(snapArray[prev] - n) ? index : prev); }, 0);\n    var gap = Math.abs(snapArray[closestGapIndex] - n);\n    return snapGap === 0 || gap < snapGap ? snapArray[closestGapIndex] : n;\n};\nvar getStringSize = function (n) {\n    n = n.toString();\n    if (n === 'auto') {\n        return n;\n    }\n    if (n.endsWith('px')) {\n        return n;\n    }\n    if (n.endsWith('%')) {\n        return n;\n    }\n    if (n.endsWith('vh')) {\n        return n;\n    }\n    if (n.endsWith('vw')) {\n        return n;\n    }\n    if (n.endsWith('vmax')) {\n        return n;\n    }\n    if (n.endsWith('vmin')) {\n        return n;\n    }\n    return \"\".concat(n, \"px\");\n};\nvar getPixelSize = function (size, parentSize, innerWidth, innerHeight) {\n    if (size && typeof size === 'string') {\n        if (size.endsWith('px')) {\n            return Number(size.replace('px', ''));\n        }\n        if (size.endsWith('%')) {\n            var ratio = Number(size.replace('%', '')) / 100;\n            return parentSize * ratio;\n        }\n        if (size.endsWith('vw')) {\n            var ratio = Number(size.replace('vw', '')) / 100;\n            return innerWidth * ratio;\n        }\n        if (size.endsWith('vh')) {\n            var ratio = Number(size.replace('vh', '')) / 100;\n            return innerHeight * ratio;\n        }\n    }\n    return size;\n};\nvar calculateNewMax = function (parentSize, innerWidth, innerHeight, maxWidth, maxHeight, minWidth, minHeight) {\n    maxWidth = getPixelSize(maxWidth, parentSize.width, innerWidth, innerHeight);\n    maxHeight = getPixelSize(maxHeight, parentSize.height, innerWidth, innerHeight);\n    minWidth = getPixelSize(minWidth, parentSize.width, innerWidth, innerHeight);\n    minHeight = getPixelSize(minHeight, parentSize.height, innerWidth, innerHeight);\n    return {\n        maxWidth: typeof maxWidth === 'undefined' ? undefined : Number(maxWidth),\n        maxHeight: typeof maxHeight === 'undefined' ? undefined : Number(maxHeight),\n        minWidth: typeof minWidth === 'undefined' ? undefined : Number(minWidth),\n        minHeight: typeof minHeight === 'undefined' ? undefined : Number(minHeight),\n    };\n};\n/**\n * transform T | [T, T] to [T, T]\n * @param val\n * @returns\n */\n// tslint:disable-next-line\nvar normalizeToPair = function (val) { return (Array.isArray(val) ? val : [val, val]); };\nvar definedProps = [\n    'as',\n    'ref',\n    'style',\n    'className',\n    'grid',\n    'gridGap',\n    'snap',\n    'bounds',\n    'boundsByDirection',\n    'size',\n    'defaultSize',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'lockAspectRatio',\n    'lockAspectRatioExtraWidth',\n    'lockAspectRatioExtraHeight',\n    'enable',\n    'handleStyles',\n    'handleClasses',\n    'handleWrapperStyle',\n    'handleWrapperClass',\n    'children',\n    'onResizeStart',\n    'onResize',\n    'onResizeStop',\n    'handleComponent',\n    'scale',\n    'resizeRatio',\n    'snapGap',\n];\n// HACK: This class is used to calculate % size.\nvar baseClassName = '__resizable_base__';\nvar Resizable = /** @class */ (function (_super) {\n    __extends(Resizable, _super);\n    function Resizable(props) {\n        var _a, _b, _c, _d;\n        var _this = _super.call(this, props) || this;\n        _this.ratio = 1;\n        _this.resizable = null;\n        // For parent boundary\n        _this.parentLeft = 0;\n        _this.parentTop = 0;\n        // For boundary\n        _this.resizableLeft = 0;\n        _this.resizableRight = 0;\n        _this.resizableTop = 0;\n        _this.resizableBottom = 0;\n        // For target boundary\n        _this.targetLeft = 0;\n        _this.targetTop = 0;\n        _this.delta = {\n            width: 0,\n            height: 0,\n        };\n        _this.appendBase = function () {\n            if (!_this.resizable || !_this.window) {\n                return null;\n            }\n            var parent = _this.parentNode;\n            if (!parent) {\n                return null;\n            }\n            var element = _this.window.document.createElement('div');\n            element.style.width = '100%';\n            element.style.height = '100%';\n            element.style.position = 'absolute';\n            element.style.transform = 'scale(0, 0)';\n            element.style.left = '0';\n            element.style.flex = '0 0 100%';\n            if (element.classList) {\n                element.classList.add(baseClassName);\n            }\n            else {\n                element.className += baseClassName;\n            }\n            parent.appendChild(element);\n            return element;\n        };\n        _this.removeBase = function (base) {\n            var parent = _this.parentNode;\n            if (!parent) {\n                return;\n            }\n            parent.removeChild(base);\n        };\n        _this.state = {\n            isResizing: false,\n            width: (_b = (_a = _this.propsSize) === null || _a === void 0 ? void 0 : _a.width) !== null && _b !== void 0 ? _b : 'auto',\n            height: (_d = (_c = _this.propsSize) === null || _c === void 0 ? void 0 : _c.height) !== null && _d !== void 0 ? _d : 'auto',\n            direction: 'right',\n            original: {\n                x: 0,\n                y: 0,\n                width: 0,\n                height: 0,\n            },\n            backgroundStyle: {\n                height: '100%',\n                width: '100%',\n                backgroundColor: 'rgba(0,0,0,0)',\n                cursor: 'auto',\n                opacity: 0,\n                position: 'fixed',\n                zIndex: 9999,\n                top: '0',\n                left: '0',\n                bottom: '0',\n                right: '0',\n            },\n            flexBasis: undefined,\n        };\n        _this.onResizeStart = _this.onResizeStart.bind(_this);\n        _this.onMouseMove = _this.onMouseMove.bind(_this);\n        _this.onMouseUp = _this.onMouseUp.bind(_this);\n        return _this;\n    }\n    Object.defineProperty(Resizable.prototype, \"parentNode\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            return this.resizable.parentNode;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"window\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            if (!this.resizable.ownerDocument) {\n                return null;\n            }\n            return this.resizable.ownerDocument.defaultView;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"propsSize\", {\n        get: function () {\n            return this.props.size || this.props.defaultSize || DEFAULT_SIZE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"size\", {\n        get: function () {\n            var width = 0;\n            var height = 0;\n            if (this.resizable && this.window) {\n                var orgWidth = this.resizable.offsetWidth;\n                var orgHeight = this.resizable.offsetHeight;\n                // HACK: Set position `relative` to get parent size.\n                //       This is because when re-resizable set `absolute`, I can not get base width correctly.\n                var orgPosition = this.resizable.style.position;\n                if (orgPosition !== 'relative') {\n                    this.resizable.style.position = 'relative';\n                }\n                // INFO: Use original width or height if set auto.\n                width = this.resizable.style.width !== 'auto' ? this.resizable.offsetWidth : orgWidth;\n                height = this.resizable.style.height !== 'auto' ? this.resizable.offsetHeight : orgHeight;\n                // Restore original position\n                this.resizable.style.position = orgPosition;\n            }\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"sizeStyle\", {\n        get: function () {\n            var _this = this;\n            var size = this.props.size;\n            var getSize = function (key) {\n                var _a;\n                if (typeof _this.state[key] === 'undefined' || _this.state[key] === 'auto') {\n                    return 'auto';\n                }\n                if (_this.propsSize && _this.propsSize[key] && ((_a = _this.propsSize[key]) === null || _a === void 0 ? void 0 : _a.toString().endsWith('%'))) {\n                    if (_this.state[key].toString().endsWith('%')) {\n                        return _this.state[key].toString();\n                    }\n                    var parentSize = _this.getParentSize();\n                    var value = Number(_this.state[key].toString().replace('px', ''));\n                    var percent = (value / parentSize[key]) * 100;\n                    return \"\".concat(percent, \"%\");\n                }\n                return getStringSize(_this.state[key]);\n            };\n            var width = size && typeof size.width !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.width)\n                : getSize('width');\n            var height = size && typeof size.height !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.height)\n                : getSize('height');\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Resizable.prototype.getParentSize = function () {\n        if (!this.parentNode) {\n            if (!this.window) {\n                return { width: 0, height: 0 };\n            }\n            return { width: this.window.innerWidth, height: this.window.innerHeight };\n        }\n        var base = this.appendBase();\n        if (!base) {\n            return { width: 0, height: 0 };\n        }\n        // INFO: To calculate parent width with flex layout\n        var wrapChanged = false;\n        var wrap = this.parentNode.style.flexWrap;\n        if (wrap !== 'wrap') {\n            wrapChanged = true;\n            this.parentNode.style.flexWrap = 'wrap';\n            // HACK: Use relative to get parent padding size\n        }\n        base.style.position = 'relative';\n        base.style.minWidth = '100%';\n        base.style.minHeight = '100%';\n        var size = {\n            width: base.offsetWidth,\n            height: base.offsetHeight,\n        };\n        if (wrapChanged) {\n            this.parentNode.style.flexWrap = wrap;\n        }\n        this.removeBase(base);\n        return size;\n    };\n    Resizable.prototype.bindEvents = function () {\n        if (this.window) {\n            this.window.addEventListener('mouseup', this.onMouseUp);\n            this.window.addEventListener('mousemove', this.onMouseMove);\n            this.window.addEventListener('mouseleave', this.onMouseUp);\n            this.window.addEventListener('touchmove', this.onMouseMove, {\n                capture: true,\n                passive: false,\n            });\n            this.window.addEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.unbindEvents = function () {\n        if (this.window) {\n            this.window.removeEventListener('mouseup', this.onMouseUp);\n            this.window.removeEventListener('mousemove', this.onMouseMove);\n            this.window.removeEventListener('mouseleave', this.onMouseUp);\n            this.window.removeEventListener('touchmove', this.onMouseMove, true);\n            this.window.removeEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.componentDidMount = function () {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        this.setState({\n            width: this.state.width || this.size.width,\n            height: this.state.height || this.size.height,\n            flexBasis: computedStyle.flexBasis !== 'auto' ? computedStyle.flexBasis : undefined,\n        });\n    };\n    Resizable.prototype.componentWillUnmount = function () {\n        if (this.window) {\n            this.unbindEvents();\n        }\n    };\n    Resizable.prototype.createSizeForCssProperty = function (newSize, kind) {\n        var propsSize = this.propsSize && this.propsSize[kind];\n        return this.state[kind] === 'auto' &&\n            this.state.original[kind] === newSize &&\n            (typeof propsSize === 'undefined' || propsSize === 'auto')\n            ? 'auto'\n            : newSize;\n    };\n    Resizable.prototype.calculateNewMaxFromBoundary = function (maxWidth, maxHeight) {\n        var boundsByDirection = this.props.boundsByDirection;\n        var direction = this.state.direction;\n        var widthByDirection = boundsByDirection && hasDirection('left', direction);\n        var heightByDirection = boundsByDirection && hasDirection('top', direction);\n        var boundWidth;\n        var boundHeight;\n        if (this.props.bounds === 'parent') {\n            var parent_1 = this.parentNode;\n            if (parent_1) {\n                boundWidth = widthByDirection\n                    ? this.resizableRight - this.parentLeft\n                    : parent_1.offsetWidth + (this.parentLeft - this.resizableLeft);\n                boundHeight = heightByDirection\n                    ? this.resizableBottom - this.parentTop\n                    : parent_1.offsetHeight + (this.parentTop - this.resizableTop);\n            }\n        }\n        else if (this.props.bounds === 'window') {\n            if (this.window) {\n                boundWidth = widthByDirection ? this.resizableRight : this.window.innerWidth - this.resizableLeft;\n                boundHeight = heightByDirection ? this.resizableBottom : this.window.innerHeight - this.resizableTop;\n            }\n        }\n        else if (this.props.bounds) {\n            boundWidth = widthByDirection\n                ? this.resizableRight - this.targetLeft\n                : this.props.bounds.offsetWidth + (this.targetLeft - this.resizableLeft);\n            boundHeight = heightByDirection\n                ? this.resizableBottom - this.targetTop\n                : this.props.bounds.offsetHeight + (this.targetTop - this.resizableTop);\n        }\n        if (boundWidth && Number.isFinite(boundWidth)) {\n            maxWidth = maxWidth && maxWidth < boundWidth ? maxWidth : boundWidth;\n        }\n        if (boundHeight && Number.isFinite(boundHeight)) {\n            maxHeight = maxHeight && maxHeight < boundHeight ? maxHeight : boundHeight;\n        }\n        return { maxWidth: maxWidth, maxHeight: maxHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromDirection = function (clientX, clientY) {\n        var scale = this.props.scale || 1;\n        var _a = normalizeToPair(this.props.resizeRatio || 1), resizeRatioX = _a[0], resizeRatioY = _a[1];\n        var _b = this.state, direction = _b.direction, original = _b.original;\n        var _c = this.props, lockAspectRatio = _c.lockAspectRatio, lockAspectRatioExtraHeight = _c.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _c.lockAspectRatioExtraWidth;\n        var newWidth = original.width;\n        var newHeight = original.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (hasDirection('right', direction)) {\n            newWidth = original.width + ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('left', direction)) {\n            newWidth = original.width - ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('bottom', direction)) {\n            newHeight = original.height + ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        if (hasDirection('top', direction)) {\n            newHeight = original.height - ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromAspectRatio = function (newWidth, newHeight, max, min) {\n        var _a = this.props, lockAspectRatio = _a.lockAspectRatio, lockAspectRatioExtraHeight = _a.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _a.lockAspectRatioExtraWidth;\n        var computedMinWidth = typeof min.width === 'undefined' ? 10 : min.width;\n        var computedMaxWidth = typeof max.width === 'undefined' || max.width < 0 ? newWidth : max.width;\n        var computedMinHeight = typeof min.height === 'undefined' ? 10 : min.height;\n        var computedMaxHeight = typeof max.height === 'undefined' || max.height < 0 ? newHeight : max.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (lockAspectRatio) {\n            var extraMinWidth = (computedMinHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMaxWidth = (computedMaxHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMinHeight = (computedMinWidth - extraWidth) / this.ratio + extraHeight;\n            var extraMaxHeight = (computedMaxWidth - extraWidth) / this.ratio + extraHeight;\n            var lockedMinWidth = Math.max(computedMinWidth, extraMinWidth);\n            var lockedMaxWidth = Math.min(computedMaxWidth, extraMaxWidth);\n            var lockedMinHeight = Math.max(computedMinHeight, extraMinHeight);\n            var lockedMaxHeight = Math.min(computedMaxHeight, extraMaxHeight);\n            newWidth = clamp(newWidth, lockedMinWidth, lockedMaxWidth);\n            newHeight = clamp(newHeight, lockedMinHeight, lockedMaxHeight);\n        }\n        else {\n            newWidth = clamp(newWidth, computedMinWidth, computedMaxWidth);\n            newHeight = clamp(newHeight, computedMinHeight, computedMaxHeight);\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.setBoundingClientRect = function () {\n        var adjustedScale = 1 / (this.props.scale || 1);\n        // For parent boundary\n        if (this.props.bounds === 'parent') {\n            var parent_2 = this.parentNode;\n            if (parent_2) {\n                var parentRect = parent_2.getBoundingClientRect();\n                this.parentLeft = parentRect.left * adjustedScale;\n                this.parentTop = parentRect.top * adjustedScale;\n            }\n        }\n        // For target(html element) boundary\n        if (this.props.bounds && typeof this.props.bounds !== 'string') {\n            var targetRect = this.props.bounds.getBoundingClientRect();\n            this.targetLeft = targetRect.left * adjustedScale;\n            this.targetTop = targetRect.top * adjustedScale;\n        }\n        // For boundary\n        if (this.resizable) {\n            var _a = this.resizable.getBoundingClientRect(), left = _a.left, top_1 = _a.top, right = _a.right, bottom = _a.bottom;\n            this.resizableLeft = left * adjustedScale;\n            this.resizableRight = right * adjustedScale;\n            this.resizableTop = top_1 * adjustedScale;\n            this.resizableBottom = bottom * adjustedScale;\n        }\n    };\n    Resizable.prototype.onResizeStart = function (event, direction) {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var clientX = 0;\n        var clientY = 0;\n        if (event.nativeEvent && isMouseEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.clientX;\n            clientY = event.nativeEvent.clientY;\n        }\n        else if (event.nativeEvent && isTouchEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.touches[0].clientX;\n            clientY = event.nativeEvent.touches[0].clientY;\n        }\n        if (this.props.onResizeStart) {\n            if (this.resizable) {\n                var startResize = this.props.onResizeStart(event, direction, this.resizable);\n                if (startResize === false) {\n                    return;\n                }\n            }\n        }\n        // Fix #168\n        if (this.props.size) {\n            if (typeof this.props.size.height !== 'undefined' && this.props.size.height !== this.state.height) {\n                this.setState({ height: this.props.size.height });\n            }\n            if (typeof this.props.size.width !== 'undefined' && this.props.size.width !== this.state.width) {\n                this.setState({ width: this.props.size.width });\n            }\n        }\n        // For lockAspectRatio case\n        this.ratio =\n            typeof this.props.lockAspectRatio === 'number' ? this.props.lockAspectRatio : this.size.width / this.size.height;\n        var flexBasis;\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        if (computedStyle.flexBasis !== 'auto') {\n            var parent_3 = this.parentNode;\n            if (parent_3) {\n                var dir = this.window.getComputedStyle(parent_3).flexDirection;\n                this.flexDir = dir.startsWith('row') ? 'row' : 'column';\n                flexBasis = computedStyle.flexBasis;\n            }\n        }\n        // For boundary\n        this.setBoundingClientRect();\n        this.bindEvents();\n        var state = {\n            original: {\n                x: clientX,\n                y: clientY,\n                width: this.size.width,\n                height: this.size.height,\n            },\n            isResizing: true,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: this.window.getComputedStyle(event.target).cursor || 'auto' }),\n            direction: direction,\n            flexBasis: flexBasis,\n        };\n        this.setState(state);\n    };\n    Resizable.prototype.onMouseMove = function (event) {\n        var _this = this;\n        if (!this.state.isResizing || !this.resizable || !this.window) {\n            return;\n        }\n        if (this.window.TouchEvent && isTouchEvent(event)) {\n            try {\n                event.preventDefault();\n                event.stopPropagation();\n            }\n            catch (e) {\n                // Ignore on fail\n            }\n        }\n        var _a = this.props, maxWidth = _a.maxWidth, maxHeight = _a.maxHeight, minWidth = _a.minWidth, minHeight = _a.minHeight;\n        var clientX = isTouchEvent(event) ? event.touches[0].clientX : event.clientX;\n        var clientY = isTouchEvent(event) ? event.touches[0].clientY : event.clientY;\n        var _b = this.state, direction = _b.direction, original = _b.original, width = _b.width, height = _b.height;\n        var parentSize = this.getParentSize();\n        var max = calculateNewMax(parentSize, this.window.innerWidth, this.window.innerHeight, maxWidth, maxHeight, minWidth, minHeight);\n        maxWidth = max.maxWidth;\n        maxHeight = max.maxHeight;\n        minWidth = max.minWidth;\n        minHeight = max.minHeight;\n        // Calculate new size\n        var _c = this.calculateNewSizeFromDirection(clientX, clientY), newHeight = _c.newHeight, newWidth = _c.newWidth;\n        // Calculate max size from boundary settings\n        var boundaryMax = this.calculateNewMaxFromBoundary(maxWidth, maxHeight);\n        if (this.props.snap && this.props.snap.x) {\n            newWidth = findClosestSnap(newWidth, this.props.snap.x, this.props.snapGap);\n        }\n        if (this.props.snap && this.props.snap.y) {\n            newHeight = findClosestSnap(newHeight, this.props.snap.y, this.props.snapGap);\n        }\n        // Calculate new size from aspect ratio\n        var newSize = this.calculateNewSizeFromAspectRatio(newWidth, newHeight, { width: boundaryMax.maxWidth, height: boundaryMax.maxHeight }, { width: minWidth, height: minHeight });\n        newWidth = newSize.newWidth;\n        newHeight = newSize.newHeight;\n        if (this.props.grid) {\n            var newGridWidth = snap(newWidth, this.props.grid[0], this.props.gridGap ? this.props.gridGap[0] : 0);\n            var newGridHeight = snap(newHeight, this.props.grid[1], this.props.gridGap ? this.props.gridGap[1] : 0);\n            var gap = this.props.snapGap || 0;\n            var w = gap === 0 || Math.abs(newGridWidth - newWidth) <= gap ? newGridWidth : newWidth;\n            var h = gap === 0 || Math.abs(newGridHeight - newHeight) <= gap ? newGridHeight : newHeight;\n            newWidth = w;\n            newHeight = h;\n        }\n        var delta = {\n            width: newWidth - original.width,\n            height: newHeight - original.height,\n        };\n        this.delta = delta;\n        if (width && typeof width === 'string') {\n            if (width.endsWith('%')) {\n                var percent = (newWidth / parentSize.width) * 100;\n                newWidth = \"\".concat(percent, \"%\");\n            }\n            else if (width.endsWith('vw')) {\n                var vw = (newWidth / this.window.innerWidth) * 100;\n                newWidth = \"\".concat(vw, \"vw\");\n            }\n            else if (width.endsWith('vh')) {\n                var vh = (newWidth / this.window.innerHeight) * 100;\n                newWidth = \"\".concat(vh, \"vh\");\n            }\n        }\n        if (height && typeof height === 'string') {\n            if (height.endsWith('%')) {\n                var percent = (newHeight / parentSize.height) * 100;\n                newHeight = \"\".concat(percent, \"%\");\n            }\n            else if (height.endsWith('vw')) {\n                var vw = (newHeight / this.window.innerWidth) * 100;\n                newHeight = \"\".concat(vw, \"vw\");\n            }\n            else if (height.endsWith('vh')) {\n                var vh = (newHeight / this.window.innerHeight) * 100;\n                newHeight = \"\".concat(vh, \"vh\");\n            }\n        }\n        var newState = {\n            width: this.createSizeForCssProperty(newWidth, 'width'),\n            height: this.createSizeForCssProperty(newHeight, 'height'),\n        };\n        if (this.flexDir === 'row') {\n            newState.flexBasis = newState.width;\n        }\n        else if (this.flexDir === 'column') {\n            newState.flexBasis = newState.height;\n        }\n        var widthChanged = this.state.width !== newState.width;\n        var heightChanged = this.state.height !== newState.height;\n        var flexBaseChanged = this.state.flexBasis !== newState.flexBasis;\n        var changed = widthChanged || heightChanged || flexBaseChanged;\n        if (changed) {\n            // For v18, update state sync\n            (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.flushSync)(function () {\n                _this.setState(newState);\n            });\n        }\n        if (this.props.onResize) {\n            if (changed) {\n                this.props.onResize(event, direction, this.resizable, delta);\n            }\n        }\n    };\n    Resizable.prototype.onMouseUp = function (event) {\n        var _a, _b;\n        var _c = this.state, isResizing = _c.isResizing, direction = _c.direction, original = _c.original;\n        if (!isResizing || !this.resizable) {\n            return;\n        }\n        if (this.props.onResizeStop) {\n            this.props.onResizeStop(event, direction, this.resizable, this.delta);\n        }\n        if (this.props.size) {\n            this.setState({ width: (_a = this.props.size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = this.props.size.height) !== null && _b !== void 0 ? _b : 'auto' });\n        }\n        this.unbindEvents();\n        this.setState({\n            isResizing: false,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: 'auto' }),\n        });\n    };\n    Resizable.prototype.updateSize = function (size) {\n        var _a, _b;\n        this.setState({ width: (_a = size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = size.height) !== null && _b !== void 0 ? _b : 'auto' });\n    };\n    Resizable.prototype.renderResizer = function () {\n        var _this = this;\n        var _a = this.props, enable = _a.enable, handleStyles = _a.handleStyles, handleClasses = _a.handleClasses, handleWrapperStyle = _a.handleWrapperStyle, handleWrapperClass = _a.handleWrapperClass, handleComponent = _a.handleComponent;\n        if (!enable) {\n            return null;\n        }\n        var resizers = Object.keys(enable).map(function (dir) {\n            if (enable[dir] !== false) {\n                return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_resizer__WEBPACK_IMPORTED_MODULE_3__.Resizer, { direction: dir, onResizeStart: _this.onResizeStart, replaceStyles: handleStyles && handleStyles[dir], className: handleClasses && handleClasses[dir], children: handleComponent && handleComponent[dir] ? handleComponent[dir] : null }, dir));\n            }\n            return null;\n        });\n        // #93 Wrap the resize box in span (will not break 100% width/height)\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: handleWrapperClass, style: handleWrapperStyle, children: resizers }));\n    };\n    Resizable.prototype.render = function () {\n        var _this = this;\n        var extendsProps = Object.keys(this.props).reduce(function (acc, key) {\n            if (definedProps.indexOf(key) !== -1) {\n                return acc;\n            }\n            acc[key] = _this.props[key];\n            return acc;\n        }, {});\n        var style = __assign(__assign(__assign({ position: 'relative', userSelect: this.state.isResizing ? 'none' : 'auto' }, this.props.style), this.sizeStyle), { maxWidth: this.props.maxWidth, maxHeight: this.props.maxHeight, minWidth: this.props.minWidth, minHeight: this.props.minHeight, boxSizing: 'border-box', flexShrink: 0 });\n        if (this.state.flexBasis) {\n            style.flexBasis = this.state.flexBasis;\n        }\n        var Wrapper = this.props.as || 'div';\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Wrapper, __assign({ style: style, className: this.props.className }, extendsProps, { \n            // `ref` is after `extendsProps` to ensure this one wins over a version\n            // passed in\n            ref: function (c) {\n                if (c) {\n                    _this.resizable = c;\n                }\n            }, children: [this.state.isResizing && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { style: this.state.backgroundStyle }), this.props.children, this.renderResizer()] })));\n    };\n    Resizable.defaultProps = {\n        as: 'div',\n        onResizeStart: function () { },\n        onResize: function () { },\n        onResizeStop: function () { },\n        enable: {\n            top: true,\n            right: true,\n            bottom: true,\n            left: true,\n            topRight: true,\n            bottomRight: true,\n            bottomLeft: true,\n            topLeft: true,\n        },\n        style: {},\n        grid: [1, 1],\n        gridGap: [0, 0],\n        lockAspectRatio: false,\n        lockAspectRatioExtraWidth: 0,\n        lockAspectRatioExtraHeight: 0,\n        scale: 1,\n        resizeRatio: 1,\n        snapGap: 0,\n    };\n    return Resizable;\n}(react__WEBPACK_IMPORTED_MODULE_1__.PureComponent));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/re-resizable/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/re-resizable/lib/resizer.js":
/*!**************************************************!*\
  !*** ./node_modules/re-resizable/lib/resizer.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resizer: () => (/* binding */ Resizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\nvar rowSizeBase = {\n    width: '100%',\n    height: '10px',\n    top: '0px',\n    left: '0px',\n    cursor: 'row-resize',\n};\nvar colSizeBase = {\n    width: '10px',\n    height: '100%',\n    top: '0px',\n    left: '0px',\n    cursor: 'col-resize',\n};\nvar edgeBase = {\n    width: '20px',\n    height: '20px',\n    position: 'absolute',\n    zIndex: 1,\n};\nvar styles = {\n    top: __assign(__assign({}, rowSizeBase), { top: '-5px' }),\n    right: __assign(__assign({}, colSizeBase), { left: undefined, right: '-5px' }),\n    bottom: __assign(__assign({}, rowSizeBase), { top: undefined, bottom: '-5px' }),\n    left: __assign(__assign({}, colSizeBase), { left: '-5px' }),\n    topRight: __assign(__assign({}, edgeBase), { right: '-10px', top: '-10px', cursor: 'ne-resize' }),\n    bottomRight: __assign(__assign({}, edgeBase), { right: '-10px', bottom: '-10px', cursor: 'se-resize' }),\n    bottomLeft: __assign(__assign({}, edgeBase), { left: '-10px', bottom: '-10px', cursor: 'sw-resize' }),\n    topLeft: __assign(__assign({}, edgeBase), { left: '-10px', top: '-10px', cursor: 'nw-resize' }),\n};\nvar Resizer = (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function (props) {\n    var onResizeStart = props.onResizeStart, direction = props.direction, children = props.children, replaceStyles = props.replaceStyles, className = props.className;\n    var onMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var onTouchStart = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var style = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n        return __assign(__assign({ position: 'absolute', userSelect: 'none' }, styles[direction]), (replaceStyles !== null && replaceStyles !== void 0 ? replaceStyles : {}));\n    }, [replaceStyles, direction]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: className || undefined, style: style, onMouseDown: onMouseDown, onTouchStart: onTouchStart, children: children }));\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/re-resizable/lib/resizer.js\n");

/***/ })

};
;