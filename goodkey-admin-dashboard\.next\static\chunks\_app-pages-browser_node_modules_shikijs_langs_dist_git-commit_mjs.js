"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_git-commit_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/diff.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Diff\\\",\\\"name\\\":\\\"diff\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.separator.diff\\\"}},\\\"match\\\":\\\"^((\\\\\\\\*{15})|(={67})|(-{3}))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.separator.diff\\\"},{\\\"match\\\":\\\"^\\\\\\\\d+(,\\\\\\\\d+)*(a|d|c)\\\\\\\\d+(,\\\\\\\\d+)*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.normal\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.line-number.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(@@)\\\\\\\\s*(.+?)\\\\\\\\s*(@@)($\\\\\\\\n?)?\\\",\\\"name\\\":\\\"meta.diff.range.unified\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.range.diff\\\"}},\\\"match\\\":\\\"^(((\\\\\\\\-{3}) .+ (\\\\\\\\-{4}))|((\\\\\\\\*{3}) .+ (\\\\\\\\*{4})))$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.range.context\\\"},{\\\"match\\\":\\\"^diff --git a/.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.git\\\"},{\\\"match\\\":\\\"^diff (-|\\\\\\\\S+\\\\\\\\s+\\\\\\\\S+).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.header.command\\\"},{\\\"captures\\\":{\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"},\\\"7\\\":{\\\"name\\\":\\\"punctuation.definition.from-file.diff\\\"}},\\\"match\\\":\\\"(^(((-{3}) .+)|((\\\\\\\\*{3}) .+))$\\\\\\\\n?|^(={4}) .+(?= - ))\\\",\\\"name\\\":\\\"meta.diff.header.from-file\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.to-file.diff\\\"}},\\\"match\\\":\\\"(^(\\\\\\\\+{3}) .+$\\\\\\\\n?| (-) .* (={4})$\\\\\\\\n?)\\\",\\\"name\\\":\\\"meta.diff.header.to-file\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.inserted.diff\\\"}},\\\"match\\\":\\\"^(((>)( .*)?)|((\\\\\\\\+).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.inserted.diff\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.changed.diff\\\"}},\\\"match\\\":\\\"^(!).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.changed.diff\\\"},{\\\"captures\\\":{\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.deleted.diff\\\"}},\\\"match\\\":\\\"^(((<)( .*)?)|((-).*))$\\\\\\\\n?\\\",\\\"name\\\":\\\"markup.deleted.diff\\\"},{\\\"begin\\\":\\\"^(#)\\\",\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.diff\\\"}},\\\"comment\\\":\\\"Git produces unified diffs with embedded comments\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.diff\\\"},{\\\"match\\\":\\\"^index [0-9a-f]{7,40}\\\\\\\\.\\\\\\\\.[0-9a-f]{7,40}.*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index.git\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.diff\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.toc-list.file-name.diff\\\"}},\\\"match\\\":\\\"^Index(:) (.+)$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.index\\\"},{\\\"match\\\":\\\"^Only in .*: .*$\\\\\\\\n?\\\",\\\"name\\\":\\\"meta.diff.only-in\\\"}],\\\"scopeName\\\":\\\"source.diff\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/git-commit.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/git-commit.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _diff_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./diff.mjs */ \"(app-pages-browser)/./node_modules/@shikijs/langs/dist/diff.mjs\");\n\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Git Commit Message\\\",\\\"name\\\":\\\"git-commit\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=^diff\\\\\\\\ \\\\\\\\-\\\\\\\\-git)\\\",\\\"comment\\\":\\\"diff presented at the end of the commit message when using commit -v.\\\",\\\"contentName\\\":\\\"source.diff\\\",\\\"end\\\":\\\"\\\\\\\\z\\\",\\\"name\\\":\\\"meta.embedded.diff.git-commit\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.diff\\\"}]},{\\\"begin\\\":\\\"^(?!#)\\\",\\\"comment\\\":\\\"User supplied message\\\",\\\"end\\\":\\\"^(?=#)\\\",\\\"name\\\":\\\"meta.scope.message.git-commit\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.deprecated.line-too-long.git-commit\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.line-too-long.git-commit\\\"}},\\\"comment\\\":\\\"Mark > 50 lines as deprecated, > 72 as illegal\\\",\\\"match\\\":\\\"\\\\\\\\G.{0,50}(.{0,22}(.*))$\\\",\\\"name\\\":\\\"meta.scope.subject.git-commit\\\"}]},{\\\"begin\\\":\\\"^(?=#)\\\",\\\"comment\\\":\\\"Git supplied metadata in a number of lines starting with #\\\",\\\"contentName\\\":\\\"comment.line.number-sign.git-commit\\\",\\\"end\\\":\\\"^(?!#)\\\",\\\"name\\\":\\\"meta.scope.metadata.git-commit\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.changed.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t((modified|renamed):.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.inserted.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t(new file:.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"markup.deleted.git-commit\\\"}},\\\"match\\\":\\\"^#\\\\\\\\t(deleted.*)$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.file-type.git-commit\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.unquoted.filename.git-commit\\\"}},\\\"comment\\\":\\\"Fallback for non-English git commit template\\\",\\\"match\\\":\\\"^#\\\\\\\\t([^:]+): *(.*)$\\\"}]}],\\\"scopeName\\\":\\\"text.git-commit\\\",\\\"embeddedLangs\\\":[\\\"diff\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n..._diff_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/git-commit.mjs\n"));

/***/ })

}]);