'use client';
import { Button } from '@/components/ui/button';
import ShowQuery from '@/services/queries/ShowQuery';
import Notices from './components/Notices';
import WorkorderLocations from './components/WorkorderLocations';
import ShowDetails from './components/ShowDetails';
import Documents from './components/Documents';
import Schedule from './components/Schedule';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { ShowSchedule, ShowPromoter } from '@/models/Show';
import { ShowLocationInList } from '@/models/ShowLocation';
import { ShowGeneralInfoData } from '@/app/[locale]/dashboard/setup/list-of-shows/components/show-tabs/GeneralInfoTab';
import PromoterInfo from './components/PromoterInfo';

export default function EventPageClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const showId = Number(params.id);

  const { data: show, isLoading: isLoadingShow } = useQuery<
    ShowGeneralInfoData,
    Error
  >({
    queryKey: [ShowQuery.tags, showId],
    queryFn: () => ShowQuery.getOne(showId),
  });

  const { data: schedules, isLoading: isLoadingSchedules } = useQuery<
    ShowSchedule[],
    Error
  >({
    queryKey: [ShowQuery.tags, 'schedules', showId],
    queryFn: () => ShowQuery.getSchedules(showId),
    enabled: !!showId,
  });

  const { data: promoter, isLoading: isLoadingPromoter } = useQuery<
    ShowPromoter,
    Error
  >({
    queryKey: [ShowQuery.tags, 'promoter', showId],
    queryFn: () => ShowQuery.getPromoter(showId),
    enabled: !!showId,
  });

  const { data: locations, isLoading: isLoadingLocations } = useQuery<
    ShowLocationInList[],
    Error
  >({
    queryKey: [ShowLocationQuery.tags],
    queryFn: () => ShowLocationQuery.getAll(),
  });

  const isLoading =
    isLoadingShow ||
    isLoadingLocations ||
    isLoadingSchedules ||
    isLoadingPromoter;

  const locationName = locations?.find(
    (location) => location.id == Number(show?.locationId),
  )?.name;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-4"></div>
          <p className="text-slate-600">Loading event details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto pb-8 px-4">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.back()}
            iconName="BackIcon"
            iconProps={{ size: 20 }}
          >
            Back to Events
          </Button>
          <Button
            className="bg-[#00646C] hover:bg-[#00646C]/90 text-white"
            onClick={() =>
              router.push(`/dashboard/event/${params.id}/products`)
            }
          >
            Order Online
          </Button>
        </div>
      </div>

      {/* <EventInformation show={show!} locationName={locationName} /> */}

      <div className="flex flex-col gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
          <PromoterInfo promoter={promoter} />
          {show && <Notices show={show} />}
        </div>
        <ShowDetails show={show} showId={showId} />
        <WorkorderLocations show={show} />
        <Documents showId={showId} />
        <Schedule schedules={schedules} />
      </div>
    </div>
  );
}
