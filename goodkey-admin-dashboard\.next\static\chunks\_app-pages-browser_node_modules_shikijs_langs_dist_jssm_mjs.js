"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_jssm_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/jssm.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/jssm.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"JSSM\\\",\\\"fileTypes\\\":[\\\"jssm\\\",\\\"jssm_state\\\"],\\\"name\\\":\\\"jssm\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.mn\\\"}},\\\"comment\\\":\\\"block comment\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block.jssm\\\"},{\\\"begin\\\":\\\"//\\\",\\\"comment\\\":\\\"block comment\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.jssm\\\"},{\\\"begin\\\":\\\"\\\\\\\\${\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"entity.name.function\\\"}},\\\"comment\\\":\\\"js outcalls\\\",\\\"end\\\":\\\"}\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"comment\\\":\\\"semver\\\",\\\"match\\\":\\\"([0-9]*)(\\\\\\\\.)([0-9]*)(\\\\\\\\.)([0-9]*)\\\",\\\"name\\\":\\\"constant.numeric\\\"},{\\\"comment\\\":\\\"jssm language tokens\\\",\\\"match\\\":\\\"graph_layout(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"comment\\\":\\\"jssm language tokens\\\",\\\"match\\\":\\\"machine_name(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"comment\\\":\\\"jssm language tokens\\\",\\\"match\\\":\\\"machine_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"comment\\\":\\\"jssm language tokens\\\",\\\"match\\\":\\\"jssm_version(\\\\\\\\s*)(:)\\\",\\\"name\\\":\\\"constant.language.jssmLanguage\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_legal\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<-\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_none\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_legal\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_main\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_main\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<=\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_none\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_forced\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.none_forced\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<~\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_none\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<-=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_main\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<=->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_legal\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<-~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.legal_forced\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<~->\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_legal\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<=~>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.main_forced\\\"},{\\\"comment\\\":\\\"transitions\\\",\\\"match\\\":\\\"<~=>\\\",\\\"name\\\":\\\"keyword.control.transition.jssmArrow.forced_main\\\"},{\\\"comment\\\":\\\"edge probability annotation\\\",\\\"match\\\":\\\"([0-9]+)%\\\",\\\"name\\\":\\\"constant.numeric.jssmProbability\\\"},{\\\"comment\\\":\\\"action annotation\\\",\\\"match\\\":\\\"\\\\\\\\'[^']*\\\\\\\\'\\\",\\\"name\\\":\\\"constant.character.jssmAction\\\"},{\\\"comment\\\":\\\"jssm label annotation\\\",\\\"match\\\":\\\"\\\\\\\\\\\\\\\"[^\\\\\\\"]*\\\\\\\\\\\\\\\"\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.doublequoted\\\"},{\\\"comment\\\":\\\"jssm label annotation\\\",\\\"match\\\":\\\"([a-zA-Z0-9_.+&()#@!?,])\\\",\\\"name\\\":\\\"entity.name.tag.jssmLabel.atom\\\"}],\\\"scopeName\\\":\\\"source.jssm\\\",\\\"aliases\\\":[\\\"fsl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/jssm.mjs\n"));

/***/ })

}]);