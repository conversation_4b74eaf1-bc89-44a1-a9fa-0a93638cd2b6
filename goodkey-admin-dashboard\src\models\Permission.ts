import { BriefData } from './BriefData';

export enum PermissionKey {
  EditRequestStatus = 'request.edit_status',
  AssignRequest = 'request.assign',
  AddImportantNote = 'request.add_important_note',
  ViewImportantNote = 'request.view_important_note',
  AddRequestItem = 'request.add_item',
  DeleteRequestItem = 'request.delete_item',
  UploadQuotation = 'quotation.upload',
  ViewEstimatedPrice = 'quotation.view_estimated_price',
  NotifyClientAboutDeposit = 'deposit.notify_client',
  AddDeposit = 'deposit.add',
  RemoveDeposit = 'deposit.remove',
  UpdateDeposit = 'deposit.update',
  InternalCommunication = 'communication.internal',
  TransferToOrder = 'request.transfer_to_order',
}
export interface Permission extends BriefData {
  code: PermissionKey;
  description?: string;
}
