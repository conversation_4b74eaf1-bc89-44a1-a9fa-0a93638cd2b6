"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_wenyan_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/wenyan.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/wenyan.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Wenyan\\\",\\\"name\\\":\\\"wenyan\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#symbols\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#comment-blocks\\\"},{\\\"include\\\":\\\"#comment-lines\\\"}],\\\"repository\\\":{\\\"comment-blocks\\\":{\\\"begin\\\":\\\"(注曰|疏曰|批曰)。?(「「|『)\\\",\\\"end\\\":\\\"(」」|』)\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]},\\\"comment-lines\\\":{\\\"begin\\\":\\\"注曰|疏曰|批曰\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"負|·|又|零|〇|一|二|三|四|五|六|七|八|九|十|百|千|萬|億|兆|京|垓|秭|穰|溝|澗|正|載|極|分|釐|毫|絲|忽|微|纖|沙|塵|埃|渺|漠\\\",\\\"name\\\":\\\"constant.numeric\\\"},{\\\"match\\\":\\\"其|陰|陽\\\",\\\"name\\\":\\\"constant.language\\\"},{\\\"begin\\\":\\\"「「|『\\\",\\\"end\\\":\\\"」」|』\\\",\\\"name\\\":\\\"string.quoted\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#variables\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"數|列|言|術|爻|物|元\\\",\\\"name\\\":\\\"storage.type\\\"},{\\\"match\\\":\\\"乃行是術曰|若其不然者|乃歸空無|欲行是術|乃止是遍|若其然者|其物如是|乃得矣|之術也|必先得|是術曰|恆為是|之物也|乃得|是謂|云云|中之|為是|乃止|若非|或若|之長|其餘\\\",\\\"name\\\":\\\"keyword.control\\\"},{\\\"match\\\":\\\"或云|蓋謂\\\",\\\"name\\\":\\\"keyword.control\\\"},{\\\"match\\\":\\\"中有陽乎|中無陰乎|所餘幾何|不等於|不大於|不小於|等於|大於|小於|加|減|乘|除|變|以|於\\\",\\\"name\\\":\\\"keyword.operator\\\"},{\\\"match\\\":\\\"不知何禍歟|不復存矣|姑妄行此|如事不諧|名之曰|吾嘗觀|之禍歟|乃作罷|吾有|今有|物之|書之|以施|昔之|是矣|之書|方悟|之義|嗚呼|之禍|有|施|曰|噫|取|今|夫|中|豈\\\",\\\"name\\\":\\\"keyword.other\\\"},{\\\"match\\\":\\\"也|凡|遍|若|者|之|充|銜\\\",\\\"name\\\":\\\"keyword.control\\\"}]},\\\"symbols\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"。|、\\\",\\\"name\\\":\\\"punctuation.separator\\\"}]},\\\"variables\\\":{\\\"begin\\\":\\\"「\\\",\\\"end\\\":\\\"」\\\",\\\"name\\\":\\\"variable.other\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character\\\"}]}},\\\"scopeName\\\":\\\"source.wenyan\\\",\\\"aliases\\\":[\\\"文言\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/wenyan.mjs\n"));

/***/ })

}]);