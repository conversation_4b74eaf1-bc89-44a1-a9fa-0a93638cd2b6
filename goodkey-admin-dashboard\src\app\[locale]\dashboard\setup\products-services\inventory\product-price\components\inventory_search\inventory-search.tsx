'use client';

import { useQuery } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Label } from '@/components/ui/label';
import { Warehouse, Eye } from 'lucide-react';
import { useState } from 'react';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import { WarehouseSummaryDto } from '@/models/Warehouse';
import ProductTableAccordion from '../product_table_accordion';
import ShowAllProducts from '../show_all_products';

export const InventorySearch = () => {
  const [selectedWarehouseCode, setSelectedWarehouseCode] =
    useState<string>('view-all');

  const { data: warehouses, isLoading } = useQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 2 }],
    queryFn: () => WarehouseQuery.getAll(2),
    select: (data) => data.filter((warehouse) => warehouse.isActive),
  });

  const handleChange = (value: string) => {
    setSelectedWarehouseCode(value);
  };

  const selectedWarehouseObj = warehouses?.find(
    (wh: WarehouseSummaryDto) => wh.code === selectedWarehouseCode,
  );

  const selectedWarehouseId = selectedWarehouseObj?.id ?? null;

  if (isLoading) {
    return (
      <div>
        <Spinner />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="flex items-center gap-3  p-4 w-full">
        <div className="space-y-2">
          <Label htmlFor="warehouse-select" className="text-sm font-medium">
            Select Warehouse
          </Label>
          <Select onValueChange={handleChange} value={selectedWarehouseCode}>
            <SelectTrigger id="warehouse-select" className="w-72">
              <Warehouse className="h-5 w-5 text-primary" />
              <SelectValue placeholder="Choose warehouse..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="view-all">
                <div className="flex items-center gap-2">
                  <span className="text-md font-medium text-primary">
                    All Warehouses
                  </span>
                  <Eye className="h-4 w-4" />
                </div>
              </SelectItem>

              {warehouses?.map((warehouse: WarehouseSummaryDto) => (
                <SelectItem key={warehouse.id} value={warehouse.code}>
                  <div className="flex flex-row items-center gap-2 text-md font-medium text-primary">
                    <span className="font-medium">
                      {warehouse.code} - {warehouse.warehouseName}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedWarehouseCode === 'view-all' ? (
        <div className="w-full border-t border-primary">
          <ShowAllProducts />
        </div>
      ) : selectedWarehouseId ? (
        <div className="w-full border-t border-primary">
          <ProductTableAccordion selectedWarehouseId={selectedWarehouseId} />
        </div>
      ) : null}
    </div>
  );
};

export default InventorySearch;
