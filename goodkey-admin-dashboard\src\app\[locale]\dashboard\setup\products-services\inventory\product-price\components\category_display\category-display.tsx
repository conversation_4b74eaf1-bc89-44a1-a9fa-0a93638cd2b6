'use client';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { CategoryWithOfferingsDto, OfferingDto } from '@/models/Offering';
import OfferingDisplay from '../offering_display';
interface GroupDisplayProps {
  data: CategoryWithOfferingsDto;
  groupId: number;
  categoryId: number;
  warehouseId?: number | null;
}

const GroupDisplay: React.FC<GroupDisplayProps> = ({
  data,
  groupId,
  categoryId,
  warehouseId,
}) => {
  return (
    <Accordion
      key={data.categoryId}
      type="single"
      collapsible
      className="space-y-3"
    >
      <AccordionItem value={data.categoryName + data.categoryId.toString()}>
        <div
          className={`pl-3 pr-1 hover:no-underline hover:bg-slate-50 hover:rounded-lg`}
        >
          <div className="grid grid-cols-[1fr_50px_50px_50px_50px] gap-2 items-center py-1 hover:bg-gray-50 w-full">
            <div>
              <AccordionTrigger className="cursor-pointer hover:text-main hover:underline flex items-center gap-1">
                <span
                  className={`text-md font-medium ${data.offerings && data.offerings.length === 0 ? 'text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1 hover:text-main`}
                >
                  {data.categoryName}
                </span>
                <span
                  className="font-mono font-normal text-sm"
                  style={{ color: '#B10055' }}
                >
                  ({data.code})
                </span>
              </AccordionTrigger>
            </div>
            <div className="text-left -ml-[260px]"></div>
            <div className="text-left -ml-[190px]"></div>
            <div className="-ml-[80px]"></div>
            <div className="-ml-[20px]"></div>
          </div>
        </div>
        <AccordionContent className="pl-4 pb-3">
          {data && data.offerings.length > 0 ? (
            data.offerings.map((offering: OfferingDto) => (
              <OfferingDisplay
                key={offering.id}
                id={offering.id}
                name={offering.name}
                groupId={groupId}
                categoryId={data.categoryId}
                options={offering.options}
                code={offering.code}
                isActive={offering.isActive}
                warehouseId={warehouseId}
                isDiscontinued={offering.isDiscontinued}
                quantity={offering.quantity}
                price={offering.unitPrice}
              />
            ))
          ) : (
            <div className="ml-3 pl-2 text-sm text-gray-500 italic">
              No products in this category.
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default GroupDisplay;
