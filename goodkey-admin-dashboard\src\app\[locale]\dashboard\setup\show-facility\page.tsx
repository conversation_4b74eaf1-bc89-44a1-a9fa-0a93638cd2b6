import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import ShowFacilityTable from './components/show_facility_table';

export const metadata: Metadata = {
  title: 'Goodkey | Show Facility',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: ShowLocationQuery.tags,
    queryFn: () => ShowLocationQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Show Facility',
          link: '/dashboard/setup/show-facility',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ShowFacilityTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
