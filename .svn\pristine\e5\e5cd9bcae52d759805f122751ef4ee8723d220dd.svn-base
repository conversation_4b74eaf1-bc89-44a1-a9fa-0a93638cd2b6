using goodkey_common.Context;
using goodkey_common.Models;
using goodkey_common.DTO.Show;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowDocumentRepository
    {
        // Basic CRUD operations
        IEnumerable<ShowDocuments> GetShowDocuments(int showId, ShowDocumentFilterDto? filter = null);
        ShowDocuments? GetShowDocumentById(int id);
        ShowDocuments CreateShowDocument(ShowDocuments document);
        ShowDocuments UpdateShowDocument(ShowDocuments document);
        bool DeleteShowDocument(int id);

        // Archive operations
        bool ArchiveShowDocument(int id, int archivedById, string? reason = null);
        bool UnarchiveShowDocument(int id, int unarchiveById);

        // File operations
        bool UpdateFilePath(int id, string filePath, string fileName, string originalFileName, long fileSize, string mimeType);
        string? GetFilePath(int id);

        // Statistics and reporting
        ShowDocumentStatsDto GetShowDocumentStats(int showId);
        IEnumerable<DocumentFileTypes> GetAvailableDocumentTypes();

        // Validation
        bool DocumentExists(int id);
        bool ShowHasDocumentType(int showId, int documentTypeId);
        Shows? GetShow(int showId);
    }

    public class ShowDocumentRepository : IShowDocumentRepository
    {
        private readonly GoodkeyContext _context;

        public ShowDocumentRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<ShowDocuments> GetShowDocuments(int showId, ShowDocumentFilterDto? filter = null)
        {
            var query = _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Include(d => d.Show)
                .Include(d => d.UploadedBy)
                .Include(d => d.UpdatedBy)
                .Include(d => d.ArchivedBy)
                .Where(d => d.ShowId == showId);

            if (filter != null)
            {
                if (filter.DocumentTypeId.HasValue)
                    query = query.Where(d => d.DocumentTypeId == filter.DocumentTypeId.Value);

                if (!string.IsNullOrEmpty(filter.Status))
                    query = query.Where(d => d.Status == filter.Status);

                if (filter.IsRequired.HasValue)
                    query = query.Where(d => d.IsRequired == filter.IsRequired.Value);

                if (filter.IsPublic.HasValue)
                    query = query.Where(d => d.IsPublic == filter.IsPublic.Value);

                if (filter.IsArchived.HasValue)
                    query = query.Where(d => d.IsArchived == filter.IsArchived.Value);

                if (filter.UploadedAfter.HasValue)
                    query = query.Where(d => d.UploadedAt >= filter.UploadedAfter.Value);

                if (filter.UploadedBefore.HasValue)
                    query = query.Where(d => d.UploadedAt <= filter.UploadedBefore.Value);

                if (!string.IsNullOrEmpty(filter.SearchTerm))
                {
                    var searchTerm = filter.SearchTerm.ToLower();
                    query = query.Where(d =>
                        d.Name.ToLower().Contains(searchTerm) ||
                        d.Description.ToLower().Contains(searchTerm) ||
                        d.OriginalFileName.ToLower().Contains(searchTerm));
                }
            }

            return query
                .OrderByDescending(d => d.UploadedAt)
                .ToList();
        }

        public ShowDocuments? GetShowDocumentById(int id)
        {
            return _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Include(d => d.Show)
                .Include(d => d.UploadedBy)
                .Include(d => d.UpdatedBy)
                .Include(d => d.ArchivedBy)
                .FirstOrDefault(d => d.Id == id);
        }

        public ShowDocuments CreateShowDocument(ShowDocuments document)
        {
            document.UploadedAt = DateTime.Now;
            document.Version = 1;
            document.Status = "Active";
            document.IsArchived = false;

            _context.ShowDocuments.Add(document);
            _context.SaveChanges();
            return document;
        }

        public ShowDocuments UpdateShowDocument(ShowDocuments document)
        {
            document.UpdatedAt = DateTime.Now;
            _context.ShowDocuments.Update(document);
            _context.SaveChanges();
            return document;
        }

        public bool DeleteShowDocument(int id)
        {
            var document = _context.ShowDocuments.Find(id);
            if (document == null)
                return false;

            _context.ShowDocuments.Remove(document);
            _context.SaveChanges();
            return true;
        }

        public bool ArchiveShowDocument(int id, int archivedById, string? reason = null)
        {
            var document = _context.ShowDocuments.Find(id);
            if (document == null)
                return false;

            document.IsArchived = true;
            document.ArchivedAt = DateTime.Now;
            document.ArchivedById = archivedById;
            document.Status = "Archived";

            _context.SaveChanges();
            return true;
        }

        public bool UnarchiveShowDocument(int id, int unarchiveById)
        {
            var document = _context.ShowDocuments.Find(id);
            if (document == null)
                return false;

            document.IsArchived = false;
            document.ArchivedAt = null;
            document.ArchivedById = null;
            document.Status = "Active";
            document.UpdatedAt = DateTime.Now;
            document.UpdatedById = unarchiveById;

            _context.SaveChanges();
            return true;
        }



        public bool UpdateFilePath(int id, string filePath, string fileName, string originalFileName, long fileSize, string mimeType)
        {
            var document = _context.ShowDocuments.Find(id);
            if (document == null)
                return false;

            // Increment version when file is replaced
            document.Version = (document.Version ?? 1) + 1;
            document.FilePath = filePath;
            document.FileName = fileName;
            document.OriginalFileName = originalFileName;
            document.FileSize = fileSize;
            document.MimeType = mimeType;
            document.UpdatedAt = DateTime.Now;

            _context.SaveChanges();
            return true;
        }

        public string? GetFilePath(int id)
        {
            var document = _context.ShowDocuments.Find(id);
            return document?.FilePath;
        }

        public ShowDocumentStatsDto GetShowDocumentStats(int showId)
        {
            var documents = _context.ShowDocuments
                .Include(d => d.DocumentType)
                .Where(d => d.ShowId == showId)
                .ToList();

            return new ShowDocumentStatsDto
            {
                TotalDocuments = documents.Count,
                ActiveDocuments = documents.Count(d => d.IsArchived != true),
                ArchivedDocuments = documents.Count(d => d.IsArchived == true),
                RequiredDocuments = documents.Count(d => d.IsRequired == true),
                PublicDocuments = documents.Count(d => d.IsPublic == true),
                TotalFileSize = documents.Sum(d => d.FileSize ?? 0),
                DocumentsByType = documents
                    .GroupBy(d => d.DocumentType?.Name ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count()),
                DocumentsByStatus = documents
                    .GroupBy(d => d.Status ?? "Unknown")
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        public IEnumerable<DocumentFileTypes> GetAvailableDocumentTypes()
        {
            return _context.DocumentFileTypes
                .Where(dt => dt.IsAvailable == true)
                .OrderBy(dt => dt.Name)
                .ToList();
        }

        public bool DocumentExists(int id)
        {
            return _context.ShowDocuments.Any(d => d.Id == id);
        }

        public bool ShowHasDocumentType(int showId, int documentTypeId)
        {
            return _context.ShowDocuments
                .Any(d => d.ShowId == showId && d.DocumentTypeId == documentTypeId && d.IsArchived != true);
        }

        public Shows? GetShow(int showId)
        {
            return _context.Shows.Find(showId);
        }
    }
}
