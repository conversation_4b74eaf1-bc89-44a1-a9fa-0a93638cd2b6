"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/dashboard/event/[id]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/event-sidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/event-sidebar.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSidebar: () => (/* binding */ EventSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square-quote.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,CreditCard,FileText,ImageIcon,MessageSquareQuote,Package,Settings,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ EventSidebar auto */ \nvar _s = $RefreshSig$();\n\n\nfunction EventSidebar(param) {\n    let { eventId, activeItem } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const menuItems = [\n        {\n            name: 'RFQs',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/rfqs\")\n        },\n        {\n            name: 'ORDERS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/orders\")\n        },\n        {\n            name: 'ORDER FORMS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/order-forms\")\n        },\n        {\n            name: 'SHOW PACKAGES',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/show-packages\")\n        },\n        {\n            name: 'EXHIBITORS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/exhibitors\")\n        },\n        {\n            name: 'LISTS & REPORTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/reports\")\n        },\n        {\n            name: 'SHOW MANAGEMENT',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId)\n        },\n        {\n            name: 'GRAPHICS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/graphics\")\n        },\n        {\n            name: 'PAYMENTS',\n            icon: _barrel_optimize_names_BarChart_CreditCard_FileText_ImageIcon_MessageSquareQuote_Package_Settings_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            url: \"/dashboard/event/\".concat(eventId, \"/payments\")\n        }\n    ];\n    const handleItemClick = (url)=>{\n        router.push(url);\n    };\n    const isActive = (name)=>{\n        return name === activeItem;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full md:w-64 bg-white rounded-md border border-slate-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: menuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full flex items-center p-2 rounded-md text-left \".concat(isActive(item.name) ? 'bg-slate-50 text-[#00646C]' : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'),\n                        onClick: ()=>handleItemClick(item.url),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 13\n                    }, this)\n                }, item.name, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Project\\\\goodkey-admin-dashboard\\\\src\\\\components\\\\ui\\\\event-sidebar.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(EventSidebar, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = EventSidebar;\nvar _c;\n$RefreshReg$(_c, \"EventSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/event-sidebar.tsx\n"));

/***/ })

});