"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_next_dist_api_headers_js"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/headers.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/api/headers.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __esModule: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.__esModule),\n/* harmony export */   cookies: () => (/* reexport safe */ _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__.cookies),\n/* harmony export */   draftMode: () => (/* reexport safe */ _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__.draftMode),\n/* harmony export */   headers: () => (/* reexport safe */ _server_request_headers__WEBPACK_IMPORTED_MODULE_1__.headers)\n/* harmony export */ });\n/* harmony import */ var _server_request_cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../server/request/cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js\");\n/* harmony import */ var _server_request_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/request/headers */ \"(app-pages-browser)/./node_modules/next/dist/server/request/headers.js\");\n/* harmony import */ var _server_request_draft_mode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/request/draft-mode */ \"(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js\");\n\n\n\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2hlYWRlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQztBQUNBO0FBQ0c7O0FBRTdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcaGVhZGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9zZXJ2ZXIvcmVxdWVzdC9jb29raWVzJztcbmV4cG9ydCAqIGZyb20gJy4uL3NlcnZlci9yZXF1ZXN0L2hlYWRlcnMnO1xuZXhwb3J0ICogZnJvbSAnLi4vc2VydmVyL3JlcXVlc3QvZHJhZnQtbW9kZSc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlYWRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DynamicServerError: function() {\n        return DynamicServerError;\n    },\n    isDynamicServerError: function() {\n        return isDynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE';\nclass DynamicServerError extends Error {\n    constructor(description){\n        super(\"Dynamic server usage: \" + description), this.description = description, this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nfunction isDynamicServerError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err) || typeof err.digest !== 'string') {\n        return false;\n    }\n    return err.digest === DYNAMIC_ERROR_CODE;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaG9va3Mtc2VydmVyLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBRWFBLGtCQUFrQjtlQUFsQkE7O0lBUUdDLG9CQUFvQjtlQUFwQkE7OztBQVZoQixNQUFNQyxxQkFBcUI7QUFFcEIsTUFBTUYsMkJBQTJCRztJQUd0Q0MsWUFBNEJDLFdBQW1CLENBQUU7UUFDL0MsS0FBSyxDQUFFLDJCQUF3QkEsY0FBQUEsSUFBQUEsQ0FETEEsV0FBQUEsR0FBQUEsYUFBQUEsSUFBQUEsQ0FGNUJDLE1BQUFBLEdBQW9DSjtJQUlwQztBQUNGO0FBRU8sU0FBU0QscUJBQXFCTSxHQUFZO0lBQy9DLElBQ0UsT0FBT0EsUUFBUSxZQUNmQSxRQUFRLFFBQ1IsQ0FBRSxhQUFZQSxHQUFBQSxDQUFFLElBQ2hCLE9BQU9BLElBQUlELE1BQU0sS0FBSyxVQUN0QjtRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU9DLElBQUlELE1BQU0sS0FBS0o7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcaG9va3Mtc2VydmVyLWNvbnRleHQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRFlOQU1JQ19FUlJPUl9DT0RFID0gJ0RZTkFNSUNfU0VSVkVSX1VTQUdFJ1xuXG5leHBvcnQgY2xhc3MgRHluYW1pY1NlcnZlckVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBkaWdlc3Q6IHR5cGVvZiBEWU5BTUlDX0VSUk9SX0NPREUgPSBEWU5BTUlDX0VSUk9SX0NPREVcblxuICBjb25zdHJ1Y3RvcihwdWJsaWMgcmVhZG9ubHkgZGVzY3JpcHRpb246IHN0cmluZykge1xuICAgIHN1cGVyKGBEeW5hbWljIHNlcnZlciB1c2FnZTogJHtkZXNjcmlwdGlvbn1gKVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0R5bmFtaWNTZXJ2ZXJFcnJvcihlcnI6IHVua25vd24pOiBlcnIgaXMgRHluYW1pY1NlcnZlckVycm9yIHtcbiAgaWYgKFxuICAgIHR5cGVvZiBlcnIgIT09ICdvYmplY3QnIHx8XG4gICAgZXJyID09PSBudWxsIHx8XG4gICAgISgnZGlnZXN0JyBpbiBlcnIpIHx8XG4gICAgdHlwZW9mIGVyci5kaWdlc3QgIT09ICdzdHJpbmcnXG4gICkge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgcmV0dXJuIGVyci5kaWdlc3QgPT09IERZTkFNSUNfRVJST1JfQ09ERVxufVxuIl0sIm5hbWVzIjpbIkR5bmFtaWNTZXJ2ZXJFcnJvciIsImlzRHluYW1pY1NlcnZlckVycm9yIiwiRFlOQU1JQ19FUlJPUl9DT0RFIiwiRXJyb3IiLCJjb25zdHJ1Y3RvciIsImRlc2NyaXB0aW9uIiwiZGlnZXN0IiwiZXJyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    StaticGenBailoutError: function() {\n        return StaticGenBailoutError;\n    },\n    isStaticGenBailoutError: function() {\n        return isStaticGenBailoutError;\n    }\n});\nconst NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT';\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args), this.code = NEXT_STATIC_GEN_BAILOUT;\n    }\n}\nfunction isStaticGenBailoutError(error) {\n    if (typeof error !== 'object' || error === null || !('code' in error)) {\n        return false;\n    }\n    return error.code === NEXT_STATIC_GEN_BAILOUT;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFFYUEscUJBQXFCO2VBQXJCQTs7SUFJR0MsdUJBQXVCO2VBQXZCQTs7O0FBTmhCLE1BQU1DLDBCQUEwQjtBQUV6QixNQUFNRiw4QkFBOEJHOztRQUFwQyxxQkFDV0MsSUFBQUEsR0FBT0Y7O0FBQ3pCO0FBRU8sU0FBU0Qsd0JBQ2RJLEtBQWM7SUFFZCxJQUFJLE9BQU9BLFVBQVUsWUFBWUEsVUFBVSxRQUFRLENBQUUsV0FBVUEsS0FBQUEsQ0FBSSxFQUFJO1FBQ3JFLE9BQU87SUFDVDtJQUVBLE9BQU9BLE1BQU1ELElBQUksS0FBS0Y7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcc3RhdGljLWdlbmVyYXRpb24tYmFpbG91dC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBORVhUX1NUQVRJQ19HRU5fQkFJTE9VVCA9ICdORVhUX1NUQVRJQ19HRU5fQkFJTE9VVCdcblxuZXhwb3J0IGNsYXNzIFN0YXRpY0dlbkJhaWxvdXRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgcHVibGljIHJlYWRvbmx5IGNvZGUgPSBORVhUX1NUQVRJQ19HRU5fQkFJTE9VVFxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNTdGF0aWNHZW5CYWlsb3V0RXJyb3IoXG4gIGVycm9yOiB1bmtub3duXG4pOiBlcnJvciBpcyBTdGF0aWNHZW5CYWlsb3V0RXJyb3Ige1xuICBpZiAodHlwZW9mIGVycm9yICE9PSAnb2JqZWN0JyB8fCBlcnJvciA9PT0gbnVsbCB8fCAhKCdjb2RlJyBpbiBlcnJvcikpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHJldHVybiBlcnJvci5jb2RlID09PSBORVhUX1NUQVRJQ19HRU5fQkFJTE9VVFxufVxuIl0sIm5hbWVzIjpbIlN0YXRpY0dlbkJhaWxvdXRFcnJvciIsImlzU3RhdGljR2VuQmFpbG91dEVycm9yIiwiTkVYVF9TVEFUSUNfR0VOX0JBSUxPVVQiLCJFcnJvciIsImNvZGUiLCJlcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/lib/metadata/metadata-constants.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    METADATA_BOUNDARY_NAME: function() {\n        return METADATA_BOUNDARY_NAME;\n    },\n    OUTLET_BOUNDARY_NAME: function() {\n        return OUTLET_BOUNDARY_NAME;\n    },\n    VIEWPORT_BOUNDARY_NAME: function() {\n        return VIEWPORT_BOUNDARY_NAME;\n    }\n});\nconst METADATA_BOUNDARY_NAME = '__next_metadata_boundary__';\nconst VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__';\nconst OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__';\n\n//# sourceMappingURL=metadata-constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL21ldGFkYXRhL21ldGFkYXRhLWNvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLE1BQU0sQ0FJTDtBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxsaWJcXG1ldGFkYXRhXFxtZXRhZGF0YS1jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBNRVRBREFUQV9CT1VOREFSWV9OQU1FOiBudWxsLFxuICAgIE9VVExFVF9CT1VOREFSWV9OQU1FOiBudWxsLFxuICAgIFZJRVdQT1JUX0JPVU5EQVJZX05BTUU6IG51bGxcbn0pO1xuZnVuY3Rpb24gX2V4cG9ydCh0YXJnZXQsIGFsbCkge1xuICAgIGZvcih2YXIgbmFtZSBpbiBhbGwpT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgbmFtZSwge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGFsbFtuYW1lXVxuICAgIH0pO1xufVxuX2V4cG9ydChleHBvcnRzLCB7XG4gICAgTUVUQURBVEFfQk9VTkRBUllfTkFNRTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBNRVRBREFUQV9CT1VOREFSWV9OQU1FO1xuICAgIH0sXG4gICAgT1VUTEVUX0JPVU5EQVJZX05BTUU6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gT1VUTEVUX0JPVU5EQVJZX05BTUU7XG4gICAgfSxcbiAgICBWSUVXUE9SVF9CT1VOREFSWV9OQU1FOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIFZJRVdQT1JUX0JPVU5EQVJZX05BTUU7XG4gICAgfVxufSk7XG5jb25zdCBNRVRBREFUQV9CT1VOREFSWV9OQU1FID0gJ19fbmV4dF9tZXRhZGF0YV9ib3VuZGFyeV9fJztcbmNvbnN0IFZJRVdQT1JUX0JPVU5EQVJZX05BTUUgPSAnX19uZXh0X3ZpZXdwb3J0X2JvdW5kYXJ5X18nO1xuY29uc3QgT1VUTEVUX0JPVU5EQVJZX05BTUUgPSAnX19uZXh0X291dGxldF9ib3VuZGFyeV9fJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWV0YWRhdGEtY29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/lib/scheduler.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    atLeastOneTask: function() {\n        return atLeastOneTask;\n    },\n    scheduleImmediate: function() {\n        return scheduleImmediate;\n    },\n    scheduleOnNextTick: function() {\n        return scheduleOnNextTick;\n    },\n    waitAtLeastOneReactRenderTask: function() {\n        return waitAtLeastOneReactRenderTask;\n    }\n});\nconst scheduleOnNextTick = (cb)=>{\n    // We use Promise.resolve().then() here so that the operation is scheduled at\n    // the end of the promise job queue, we then add it to the next process tick\n    // to ensure it's evaluated afterwards.\n    //\n    // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n    //\n    Promise.resolve().then(()=>{\n        if (false) {} else {\n            process.nextTick(cb);\n        }\n    });\n};\nconst scheduleImmediate = (cb)=>{\n    if (false) {} else {\n        setImmediate(cb);\n    }\n};\nfunction atLeastOneTask() {\n    return new Promise((resolve)=>scheduleImmediate(resolve));\n}\nfunction waitAtLeastOneReactRenderTask() {\n    if (false) {} else {\n        return new Promise((r)=>setImmediate(r));\n    }\n}\n\n//# sourceMappingURL=scheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return afterTaskAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst afterTaskAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=after-task-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsaUVBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkJBQTJCLG1CQUFPLENBQUMsb0hBQXVCO0FBQzFEOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlSW5zdGFuY2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FzeW5jbG9jYWxzdG9yYWdlID0gcmVxdWlyZShcIi4vYXN5bmMtbG9jYWwtc3RvcmFnZVwiKTtcbmNvbnN0IGFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage.external.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _aftertaskasyncstorageinstance.afterTaskAsyncStorageInstance;\n    }\n}));\nconst _aftertaskasyncstorageinstance = __webpack_require__(/*! ./after-task-async-storage-instance */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\");\n\n//# sourceMappingURL=after-task-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YseURBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsdUNBQXVDLG1CQUFPLENBQUMsZ0pBQXFDOztBQUVwRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXGFmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImFmdGVyVGFza0FzeW5jU3RvcmFnZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2FmdGVydGFza2FzeW5jc3RvcmFnZWluc3RhbmNlLmFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FmdGVydGFza2FzeW5jc3RvcmFnZWluc3RhbmNlID0gcmVxdWlyZShcIi4vYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlXCIpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/dynamic-rendering.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Postpone: function() {\n        return Postpone;\n    },\n    abortAndThrowOnSynchronousRequestDataAccess: function() {\n        return abortAndThrowOnSynchronousRequestDataAccess;\n    },\n    abortOnSynchronousPlatformIOAccess: function() {\n        return abortOnSynchronousPlatformIOAccess;\n    },\n    accessedDynamicData: function() {\n        return accessedDynamicData;\n    },\n    annotateDynamicAccess: function() {\n        return annotateDynamicAccess;\n    },\n    consumeDynamicAccess: function() {\n        return consumeDynamicAccess;\n    },\n    createDynamicTrackingState: function() {\n        return createDynamicTrackingState;\n    },\n    createDynamicValidationState: function() {\n        return createDynamicValidationState;\n    },\n    createHangingInputAbortSignal: function() {\n        return createHangingInputAbortSignal;\n    },\n    createPostponedAbortSignal: function() {\n        return createPostponedAbortSignal;\n    },\n    formatDynamicAPIAccesses: function() {\n        return formatDynamicAPIAccesses;\n    },\n    getFirstDynamicReason: function() {\n        return getFirstDynamicReason;\n    },\n    isDynamicPostpone: function() {\n        return isDynamicPostpone;\n    },\n    isPrerenderInterruptedError: function() {\n        return isPrerenderInterruptedError;\n    },\n    markCurrentScopeAsDynamic: function() {\n        return markCurrentScopeAsDynamic;\n    },\n    postponeWithTracking: function() {\n        return postponeWithTracking;\n    },\n    throwIfDisallowedDynamic: function() {\n        return throwIfDisallowedDynamic;\n    },\n    throwToInterruptStaticGeneration: function() {\n        return throwToInterruptStaticGeneration;\n    },\n    trackAllowedDynamicAccess: function() {\n        return trackAllowedDynamicAccess;\n    },\n    trackDynamicDataInDynamicRender: function() {\n        return trackDynamicDataInDynamicRender;\n    },\n    trackFallbackParamAccessed: function() {\n        return trackFallbackParamAccessed;\n    },\n    trackSynchronousPlatformIOAccessInDev: function() {\n        return trackSynchronousPlatformIOAccessInDev;\n    },\n    trackSynchronousRequestDataAccessInDev: function() {\n        return trackSynchronousRequestDataAccessInDev;\n    },\n    useDynamicRouteParams: function() {\n        return useDynamicRouteParams;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ./work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _metadataconstants = __webpack_require__(/*! ../../lib/metadata/metadata-constants */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst hasPostpone = typeof _react.default.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicExpression: undefined,\n        syncDynamicErrorWithStack: null\n    };\n}\nfunction createDynamicValidationState() {\n    return {\n        hasSuspendedDynamic: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasSyncDynamicErrors: false,\n        dynamicErrors: []\n    };\n}\nfunction getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\nfunction markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender-ppr') {\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                value: \"E550\",\n                enumerable: false,\n                configurable: true\n            });\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\nfunction trackFallbackParamAccessed(store, expression) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n    postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\nfunction throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\nfunction trackDynamicDataInDynamicRender(_store, workUnitStore) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n        }\n        if ( true && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicExpression = expression;\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n    return abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\nfunction abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicExpression = expression;\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n            if (prerenderStore.validating === true) {\n                // We always log Request Access in dev at the point of calling the function\n                // So we mark the dynamic validation as not requiring it to be printed\n                dynamicTracking.syncDynamicLogged = true;\n            }\n        }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone({ reason, route }) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    _react.default.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nfunction isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nfunction isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n')// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\nfunction createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        _react.default.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\nfunction createHangingInputAbortSignal(workUnitStore) {\n    const controller = new AbortController();\n    if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If the input\n        // we're waiting on is coming from another cache, we do want to wait for it so that\n        // we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(()=>{\n            controller.abort();\n        });\n    } else {\n        // Otherwise we're in the final render and we should already have all our caches\n        // filled. We might still be waiting on some microtasks so we wait one tick before\n        // giving up. When we give up, we still want to render the content of this cache\n        // as deeply as we can so that we can suspend as deeply as possible in the tree\n        // or not at all if we don't end up waiting for the input.\n        (0, _scheduler.scheduleOnNextTick)(()=>controller.abort());\n    }\n    return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction useDynamicRouteParams(expression) {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n        // There are fallback route params, we should track these as dynamic\n        // accesses.\n        const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            // We're prerendering with dynamicIO or PPR or both\n            if (workUnitStore.type === 'prerender') {\n                // We are in a prerender with dynamicIO semantics\n                // We are going to hang here and never resolve. This will cause the currently\n                // rendering component to effectively be a dynamic hole\n                _react.default.use((0, _dynamicrenderingutils.makeHangingPromise)(workUnitStore.renderSignal, expression));\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // We're prerendering with PPR\n                postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n            }\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${_metadataconstants.OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nfunction trackAllowedDynamicAccess(route, componentStack, dynamicValidation, serverDynamic, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        dynamicValidation.hasSuspendedDynamic = true;\n        return;\n    } else if (serverDynamic.syncDynamicErrorWithStack || clientDynamic.syncDynamicErrorWithStack) {\n        dynamicValidation.hasSyncDynamicErrors = true;\n        return;\n    } else {\n        const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\nfunction createErrorWithComponentStack(message, componentStack) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = 'Error: ' + message + componentStack;\n    return error;\n}\nfunction throwIfDisallowedDynamic(route, dynamicValidation, serverDynamic, clientDynamic) {\n    let syncError;\n    let syncExpression;\n    let syncLogged;\n    if (serverDynamic.syncDynamicErrorWithStack) {\n        syncError = serverDynamic.syncDynamicErrorWithStack;\n        syncExpression = serverDynamic.syncDynamicExpression;\n        syncLogged = serverDynamic.syncDynamicLogged === true;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        syncError = clientDynamic.syncDynamicErrorWithStack;\n        syncExpression = clientDynamic.syncDynamicExpression;\n        syncLogged = clientDynamic.syncDynamicLogged === true;\n    } else {\n        syncError = null;\n        syncExpression = undefined;\n        syncLogged = false;\n    }\n    if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n        if (!syncLogged) {\n            // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n            // the offending sync error is logged before we exit the build\n            console.error(syncError);\n        }\n        // The actual error should have been logged when the sync access ocurred\n        throw new _staticgenerationbailout.StaticGenBailoutError();\n    }\n    const dynamicErrors = dynamicValidation.dynamicErrors;\n    if (dynamicErrors.length) {\n        for(let i = 0; i < dynamicErrors.length; i++){\n            console.error(dynamicErrors[i]);\n        }\n        throw new _staticgenerationbailout.StaticGenBailoutError();\n    }\n    if (!dynamicValidation.hasSuspendedDynamic) {\n        if (dynamicValidation.hasDynamicMetadata) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E608\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E534\",\n                enumerable: false,\n                configurable: true\n            });\n        } else if (dynamicValidation.hasDynamicViewport) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E573\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E590\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn =  false ? 0 : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (true) {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {}\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/server/dynamic-rendering-utils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isHangingPromiseRejectionError: function() {\n        return isHangingPromiseRejectionError;\n    },\n    makeHangingPromise: function() {\n        return makeHangingPromise;\n    }\n});\nfunction isHangingPromiseRejectionError(err) {\n    if (typeof err !== 'object' || err === null || !('digest' in err)) {\n        return false;\n    }\n    return err.digest === HANGING_PROMISE_REJECTION;\n}\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION';\nclass HangingPromiseRejectionError extends Error {\n    constructor(expression){\n        super(`During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`), this.expression = expression, this.digest = HANGING_PROMISE_REJECTION;\n    }\n}\nfunction makeHangingPromise(signal, expression) {\n    const hangingPromise = new Promise((_, reject)=>{\n        signal.addEventListener('abort', ()=>{\n            reject(new HangingPromiseRejectionError(expression));\n        }, {\n            once: true\n        });\n    });\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject);\n    return hangingPromise;\n}\nfunction ignoreReject() {}\n\n//# sourceMappingURL=dynamic-rendering-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/server/request/cookies.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cookies\", ({\n    enumerable: true,\n    get: function() {\n        return cookies;\n    }\n}));\nconst _requestcookies = __webpack_require__(/*! ../web/spec-extension/adapters/request-cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\");\nconst _cookies = __webpack_require__(/*! ../web/spec-extension/cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction cookies() {\n    const callingExpression = 'cookies';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(// TODO(after): clarify that this only applies to pages?\n            `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the cookies object.\n                return makeDynamicallyTrackedExoticCookies(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We track dynamic access here so we don't need to wrap the cookies in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)(callingExpression, workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    let underlyingCookies;\n    if ((0, _requestcookies.areCookiesMutableInCurrentPhase)(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return _requestcookies.RequestCookiesAdapter.seal(new _cookies.RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeDynamicallyTrackedExoticCookies(route, prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`cookies()[Symbol.iterator]()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? `'${arg.name}'` : typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`cookies()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n}\n\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js":
/*!*************************************************************!*\
  !*** ./node_modules/next/dist/server/request/draft-mode.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"draftMode\", ({\n    enumerable: true,\n    get: function() {\n        return draftMode;\n    }\n}));\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _hooksservercontext = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nfunction draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache' || workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-ppr' || workUnitStore.type === 'prerender-legacy') {\n            // Return empty draft mode\n            if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n                const route = workStore == null ? void 0 : workStore.route;\n                return createExoticDraftModeWithDevWarnings(null, route);\n            } else {\n                return createExoticDraftMode(null);\n            }\n        }\n    }\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    const cachedDraftMode = CachedDraftModes.get(requestStore.draftMode);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        promise = createExoticDraftModeWithDevWarnings(requestStore.draftMode, route);\n    } else {\n        promise = createExoticDraftMode(requestStore.draftMode);\n    }\n    CachedDraftModes.set(requestStore.draftMode, promise);\n    return promise;\n}\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\nclass DraftMode {\n    constructor(provider){\n        this._provider = provider;\n    }\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`draftMode()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                const error = Object.defineProperty(new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E126\",\n                    enumerable: false,\n                    configurable: true\n                });\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(store.route, expression, error, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender\n                (0, _dynamicrendering.postponeWithTracking)(store.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // legacy Prerender\n                workUnitStore.revalidate = 0;\n                const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E558\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n                workUnitStore.usedDynamic = true;\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=draft-mode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/draft-mode.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/headers.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/server/request/headers.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"headers\", ({\n    enumerable: true,\n    get: function() {\n        return headers;\n    }\n}));\nconst _headers = __webpack_require__(/*! ../web/spec-extension/adapters/headers */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction headers() {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = _headers.HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the headers object.\n                return makeDynamicallyTrackedExoticHeaders(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how headers has worked in PPR without dynamicIO.\n                // TODO consider switching the semantic to throw on property access instead\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, 'headers', workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We are in a legacy static generation mode while prerendering\n                // We track dynamic access here so we don't need to wrap the headers in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)('headers', workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)('headers');\n    if ( true && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeDynamicallyTrackedExoticHeaders(route, prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`headers()[Symbol.iterator]()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`headers()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/utils.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/server/request/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    isRequestAPICallableInsideAfter: function() {\n        return isRequestAPICallableInsideAfter;\n    },\n    throwForSearchParamsAccessInUseCache: function() {\n        return throwForSearchParamsAccessInUseCache;\n    },\n    throwWithStaticGenerationBailoutError: function() {\n        return throwWithStaticGenerationBailoutError;\n    },\n    throwWithStaticGenerationBailoutErrorWithDynamicError: function() {\n        return throwWithStaticGenerationBailoutErrorWithDynamicError;\n    }\n});\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _aftertaskasyncstorageexternal = __webpack_require__(/*! ../app-render/after-task-async-storage.external */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\");\nfunction throwWithStaticGenerationBailoutError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E576\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n        value: \"E543\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction throwForSearchParamsAccessInUseCache(route) {\n    throw Object.defineProperty(new Error(`Route ${route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n        value: \"E634\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction isRequestAPICallableInsideAfter() {\n    const afterTaskStore = _aftertaskasyncstorageexternal.afterTaskAsyncStorage.getStore();\n    return (afterTaskStore == null ? void 0 : afterTaskStore.rootTaskSpawnPhase) === 'action';\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/headers.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    HeadersAdapter: function() {\n        return HeadersAdapter;\n    },\n    ReadonlyHeadersError: function() {\n        return ReadonlyHeadersError;\n    }\n});\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nclass ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nclass HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return _reflect.ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return _reflect.ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return _reflect.ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return _reflect.ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0RBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcd2ViXFxzcGVjLWV4dGVuc2lvblxcYWRhcHRlcnNcXHJlZmxlY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJSZWZsZWN0QWRhcHRlclwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdEFkYXB0ZXI7XG4gICAgfVxufSk7XG5jbGFzcyBSZWZsZWN0QWRhcHRlciB7XG4gICAgc3RhdGljIGdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gUmVmbGVjdC5nZXQodGFyZ2V0LCBwcm9wLCByZWNlaXZlcik7XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5iaW5kKHRhcmdldCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBzdGF0aWMgc2V0KHRhcmdldCwgcHJvcCwgdmFsdWUsIHJlY2VpdmVyKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LnNldCh0YXJnZXQsIHByb3AsIHZhbHVlLCByZWNlaXZlcik7XG4gICAgfVxuICAgIHN0YXRpYyBoYXModGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0Lmhhcyh0YXJnZXQsIHByb3ApO1xuICAgIH1cbiAgICBzdGF0aWMgZGVsZXRlUHJvcGVydHkodGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LmRlbGV0ZVByb3BlcnR5KHRhcmdldCwgcHJvcCk7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWZsZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MutableRequestCookiesAdapter: function() {\n        return MutableRequestCookiesAdapter;\n    },\n    ReadonlyRequestCookiesError: function() {\n        return ReadonlyRequestCookiesError;\n    },\n    RequestCookiesAdapter: function() {\n        return RequestCookiesAdapter;\n    },\n    appendMutableCookies: function() {\n        return appendMutableCookies;\n    },\n    areCookiesMutableInCurrentPhase: function() {\n        return areCookiesMutableInCurrentPhase;\n    },\n    getModifiedCookieValues: function() {\n        return getModifiedCookieValues;\n    },\n    responseCookiesToRequestCookies: function() {\n        return responseCookiesToRequestCookies;\n    },\n    wrapWithMutableAccessCheck: function() {\n        return wrapWithMutableAccessCheck;\n    }\n});\nconst _cookies = __webpack_require__(/*! ../cookies */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\");\nconst _reflect = __webpack_require__(/*! ./reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../../../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nclass ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super('Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options');\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'clear':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies');\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting workStore\n            const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n            if (workStore) {\n                workStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        const wrappedCookies = new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case 'delete':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case 'set':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.set(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n        return wrappedCookies;\n    }\n}\nfunction wrapWithMutableAccessCheck(responseCookies) {\n    const wrappedCookies = new Proxy(responseCookies, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'delete':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().delete');\n                        target.delete(...args);\n                        return wrappedCookies;\n                    };\n                case 'set':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().set');\n                        target.set(...args);\n                        return wrappedCookies;\n                    };\n                default:\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    });\n    return wrappedCookies;\n}\nfunction areCookiesMutableInCurrentPhase(requestStore) {\n    return requestStore.phase === 'action';\n}\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */ function ensureCookiesAreStillMutable(callingExpression) {\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    if (!areCookiesMutableInCurrentPhase(requestStore)) {\n        // TODO: maybe we can give a more precise error message based on callingExpression?\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nfunction responseCookiesToRequestCookies(responseCookies) {\n    const requestCookies = new _cookies.RequestCookies(new Headers());\n    for (const cookie of responseCookies.getAll()){\n        requestCookies.set(cookie);\n    }\n    return requestCookies;\n}\n\n//# sourceMappingURL=request-cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/cookies.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    RequestCookies: function() {\n        return _cookies.RequestCookies;\n    },\n    ResponseCookies: function() {\n        return _cookies.ResponseCookies;\n    },\n    stringifyCookie: function() {\n        return _cookies.stringifyCookie;\n    }\n});\nconst _cookies = __webpack_require__(/*! next/dist/compiled/@edge-runtime/cookies */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@edge-runtime/cookies/index.js\");\n\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9jb29raWVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsTUFBTSxDQUlMO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsaUJBQWlCLG1CQUFPLENBQUMsc0lBQTBDOztBQUVuRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXHdlYlxcc3BlYy1leHRlbnNpb25cXGNvb2tpZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG4wICYmIChtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBSZXF1ZXN0Q29va2llczogbnVsbCxcbiAgICBSZXNwb25zZUNvb2tpZXM6IG51bGwsXG4gICAgc3RyaW5naWZ5Q29va2llOiBudWxsXG59KTtcbmZ1bmN0aW9uIF9leHBvcnQodGFyZ2V0LCBhbGwpIHtcbiAgICBmb3IodmFyIG5hbWUgaW4gYWxsKU9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIG5hbWUsIHtcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiBhbGxbbmFtZV1cbiAgICB9KTtcbn1cbl9leHBvcnQoZXhwb3J0cywge1xuICAgIFJlcXVlc3RDb29raWVzOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9jb29raWVzLlJlcXVlc3RDb29raWVzO1xuICAgIH0sXG4gICAgUmVzcG9uc2VDb29raWVzOiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9jb29raWVzLlJlc3BvbnNlQ29va2llcztcbiAgICB9LFxuICAgIHN0cmluZ2lmeUNvb2tpZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfY29va2llcy5zdHJpbmdpZnlDb29raWU7XG4gICAgfVxufSk7XG5jb25zdCBfY29va2llcyA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvQGVkZ2UtcnVudGltZS9jb29raWVzXCIpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb29raWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/cookies.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage-instance.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDREQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLHlHQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXHdvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndvcmtBc3luY1N0b3JhZ2VJbnN0YW5jZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gd29ya0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FzeW5jbG9jYWxzdG9yYWdlID0gcmVxdWlyZShcIi4vYXN5bmMtbG9jYWwtc3RvcmFnZVwiKTtcbmNvbnN0IHdvcmtBc3luY1N0b3JhZ2VJbnN0YW5jZSA9ICgwLCBfYXN5bmNsb2NhbHN0b3JhZ2UuY3JlYXRlQXN5bmNMb2NhbFN0b3JhZ2UpKCk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage.external.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _workasyncstorageinstance.workAsyncStorageInstance;\n    }\n}));\nconst _workasyncstorageinstance = __webpack_require__(/*! ./work-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\");\n\n//# sourceMappingURL=work-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLGtDQUFrQyxtQkFBTyxDQUFDLHlIQUErQjs7QUFFekUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGV2ZWxvcGVyMlxcc291cmNlXFxyZXBvc1xcUHJvamVjdFxcZ29vZGtleS1hZG1pbi1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2VydmVyXFxhcHAtcmVuZGVyXFx3b3JrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJ3b3JrQXN5bmNTdG9yYWdlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfd29ya2FzeW5jc3RvcmFnZWluc3RhbmNlLndvcmtBc3luY1N0b3JhZ2VJbnN0YW5jZTtcbiAgICB9XG59KTtcbmNvbnN0IF93b3JrYXN5bmNzdG9yYWdlaW5zdGFuY2UgPSByZXF1aXJlKFwiLi93b3JrLWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2VcIik7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workUnitAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workUnitAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workUnitAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-unit-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstdW5pdC1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsZ0VBQStEO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkJBQTJCLG1CQUFPLENBQUMseUdBQXVCO0FBQzFEOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRldmVsb3BlcjJcXHNvdXJjZVxccmVwb3NcXFByb2plY3RcXGdvb2RrZXktYWRtaW4tZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcd29yay11bml0LWFzeW5jLXN0b3JhZ2UtaW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJ3b3JrVW5pdEFzeW5jU3RvcmFnZUluc3RhbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3b3JrVW5pdEFzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FzeW5jbG9jYWxzdG9yYWdlID0gcmVxdWlyZShcIi4vYXN5bmMtbG9jYWwtc3RvcmFnZVwiKTtcbmNvbnN0IHdvcmtVbml0QXN5bmNTdG9yYWdlSW5zdGFuY2UgPSAoMCwgX2FzeW5jbG9jYWxzdG9yYWdlLmNyZWF0ZUFzeW5jTG9jYWxTdG9yYWdlKSgpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD13b3JrLXVuaXQtYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getExpectedRequestStore: function() {\n        return getExpectedRequestStore;\n    },\n    getHmrRefreshHash: function() {\n        return getHmrRefreshHash;\n    },\n    getPrerenderResumeDataCache: function() {\n        return getPrerenderResumeDataCache;\n    },\n    getRenderResumeDataCache: function() {\n        return getRenderResumeDataCache;\n    },\n    workUnitAsyncStorage: function() {\n        return _workunitasyncstorageinstance.workUnitAsyncStorageInstance;\n    }\n});\nconst _workunitasyncstorageinstance = __webpack_require__(/*! ./work-unit-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js\");\nfunction getExpectedRequestStore(callingExpression) {\n    const workUnitStore = _workunitasyncstorageinstance.workUnitAsyncStorageInstance.getStore();\n    if (workUnitStore) {\n        if (workUnitStore.type === 'request') {\n            return workUnitStore;\n        }\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-ppr' || workUnitStore.type === 'prerender-legacy') {\n            // This should not happen because we should have checked it already.\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside a prerender. This is a bug in Next.js.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E401\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore.type === 'cache') {\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside \"use cache\". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                value: \"E37\",\n                enumerable: false,\n                configurable: true\n            });\n        } else if (workUnitStore.type === 'unstable-cache') {\n            throw Object.defineProperty(new Error(`\\`${callingExpression}\\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                value: \"E69\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    throw Object.defineProperty(new Error(`\\`${callingExpression}\\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`), \"__NEXT_ERROR_CODE\", {\n        value: \"E251\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction getPrerenderResumeDataCache(workUnitStore) {\n    if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-ppr') {\n        return workUnitStore.prerenderResumeDataCache;\n    }\n    return null;\n}\nfunction getRenderResumeDataCache(workUnitStore) {\n    if (workUnitStore.type !== 'prerender-legacy' && workUnitStore.type !== 'cache' && workUnitStore.type !== 'unstable-cache') {\n        if (workUnitStore.type === 'request') {\n            return workUnitStore.renderResumeDataCache;\n        }\n        // We return the mutable resume data cache here as an immutable version of\n        // the cache as it can also be used for reading.\n        return workUnitStore.prerenderResumeDataCache;\n    }\n    return null;\n}\nfunction getHmrRefreshHash(workUnitStore) {\n    var _workUnitStore_cookies_get;\n    return workUnitStore.type === 'cache' ? workUnitStore.hmrRefreshHash : workUnitStore.type === 'request' ? (_workUnitStore_cookies_get = workUnitStore.cookies.get('__next_hmr_refresh_hash__')) == null ? void 0 : _workUnitStore_cookies_get.value : undefined;\n}\n\n//# sourceMappingURL=work-unit-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\n"));

/***/ })

}]);