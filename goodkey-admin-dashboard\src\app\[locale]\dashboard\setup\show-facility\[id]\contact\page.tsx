import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import ShowContactQuery from '@/services/queries/ShowContactQuery';
import ShowContactTable from '../components/show_contact_table'; // you need to implement this component

export default async function ShowContactPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();

      await client.prefetchQuery({
        queryKey: ['ShowContact', { locationId: Number(id) }],
        queryFn: () => ShowContactQuery.getByLocation(Number(id)),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          List of Contacts
        </h2>
        <HydrationBoundary state={dehydrate(client)}>
          <ShowContactTable locationId={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/show-facility/add');
  }
}
