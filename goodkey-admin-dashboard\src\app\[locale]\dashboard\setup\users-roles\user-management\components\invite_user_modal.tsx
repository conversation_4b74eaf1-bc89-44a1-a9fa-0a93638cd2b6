'use client';

import { useMutation } from '@tanstack/react-query';
import { AlertCircle, CheckCircle, Mail } from 'lucide-react';

import { Button } from '@/components/ui/button';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { getQueryClient } from '@/utils/query-client';
import UsersQuery from '@/services/queries/UsersQuery';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InviteUser } from '@/models/User';

interface InviteUserModalProps {
  close: () => void;
  userId: number;
  userName: string;
  userEmail: string;
}

export default function InviteUserModal({
  close,
  userId,
  userName,
  userEmail,
}: InviteUserModalProps) {
  const { mutate, isPending, isError, isSuccess } = useMutation({
    mutationFn: () => {
      const data: InviteUser = {
        userId: userId,
      };
      return UsersQuery.invite(data);
    },
    onSuccess: async () => {
      // Invalidate the users query to refresh the data
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });
    },
  });

  return (
    <ModalContainer
      className="gap-4 max-w-md"
      title="Send Invitation"
      description={`Send an invitation email to ${userName} (${userEmail})`}
      controls={
        <div className="flex flex-row gap-2">
          {!isSuccess && !isError && (
            <>
              <Button
                onClick={() => mutate()}
                variant="default"
                disabled={isPending}
              >
                {isPending ? 'Sending...' : 'Send Invitation'}
                {!isPending && <Mail className="ml-2 h-4 w-4" />}
              </Button>
              <Button onClick={close} variant="secondary">
                Cancel
              </Button>
            </>
          )}
          {(isSuccess || isError) && (
            <Button onClick={close} variant="secondary">
              Close
            </Button>
          )}
        </div>
      }
    >
      {isSuccess && (
        <Alert variant="default" className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            Invitation has been sent successfully.
          </AlertDescription>
        </Alert>
      )}

      {isError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error sending the invitation. Please try again.
          </AlertDescription>
        </Alert>
      )}
    </ModalContainer>
  );
}
