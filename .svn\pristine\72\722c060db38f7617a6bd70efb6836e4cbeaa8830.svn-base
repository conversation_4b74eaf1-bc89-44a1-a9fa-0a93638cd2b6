﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace goodkey_common.Models
{
    public partial class AuthUser
    {
        public AuthUser()
        {
            AuthGroupRole = new HashSet<AuthGroupRole>();
            CategoryCreatedBy = new HashSet<Category>();
            CategoryPropertyCreatedBy = new HashSet<CategoryProperty>();
            CategoryPropertyUpdatedBy = new HashSet<CategoryProperty>();
            CategoryUpdatedBy = new HashSet<Category>();
            CompanyCreatedBy = new HashSet<Company>();
            CompanyGroupCreatedBy = new HashSet<CompanyGroup>();
            CompanyGroupUpdatedBy = new HashSet<CompanyGroup>();
            CompanyUpdatedby = new HashSet<Company>();
            ContactArchivedBy = new HashSet<Contact>();
            ContactAuthuser = new HashSet<Contact>();
            ContactCreatedBy = new HashSet<Contact>();
            ContactUpdatedBy = new HashSet<Contact>();
            DepartmentCreatedBy = new HashSet<Department>();
            DepartmentUpdatedBy = new HashSet<Department>();
            DocumentFileTypesCreatedBy = new HashSet<DocumentFileTypes>();
            DocumentFileTypesUpdatedBy = new HashSet<DocumentFileTypes>();
            GroundConditions = new HashSet<GroundConditions>();
            GroundServices = new HashSet<GroundServices>();
            GroupCreatedBy = new HashSet<Group>();
            GroupUpdatedBy = new HashSet<Group>();
            InverseArchivedBy = new HashSet<AuthUser>();
            InverseCreatedBy = new HashSet<AuthUser>();
            InverseUpdatedBy = new HashSet<AuthUser>();
            OfferingCreatedBy = new HashSet<Offering>();
            OfferingPropertyCreatedBy = new HashSet<OfferingProperty>();
            OfferingPropertyUpdatedBy = new HashSet<OfferingProperty>();
            OfferingRateCreatedBy = new HashSet<OfferingRate>();
            OfferingRateUpdatedBy = new HashSet<OfferingRate>();
            OfferingTax = new HashSet<OfferingTax>();
            OfferingUpdatedBy = new HashSet<Offering>();
            PropertyCreatedBy = new HashSet<Property>();
            PropertyOptionCreatedBy = new HashSet<PropertyOption>();
            PropertyOptionUpdatedBy = new HashSet<PropertyOption>();
            PropertyUpdatedBy = new HashSet<Property>();
            SchedulesCreatedBy = new HashSet<Schedules>();
            SchedulesUpdatedBy = new HashSet<Schedules>();
            ShowDocsCreatedBy = new HashSet<ShowDocs>();
            ShowDocsUpdatedBy = new HashSet<ShowDocs>();
            ShowLocationHallsArchivedBy = new HashSet<ShowLocationHalls>();
            ShowLocationHallsCreatedBy = new HashSet<ShowLocationHalls>();
            ShowLocationHallsUpdatedBy = new HashSet<ShowLocationHalls>();
            ShowLocationsArchivedBy = new HashSet<ShowLocations>();
            ShowLocationsCreatedBy = new HashSet<ShowLocations>();
            ShowLocationsUpdatedBy = new HashSet<ShowLocations>();
            ShowSchedules = new HashSet<ShowSchedules>();
            Shows = new HashSet<Shows>();
            TaxProvinceCreatedBy = new HashSet<TaxProvince>();
            TaxProvinceUpdatedBy = new HashSet<TaxProvince>();
            TaxProvinceUpdatedIsActiveBy = new HashSet<TaxProvince>();
            WarehouseContactPerson = new HashSet<Warehouse>();
            WarehouseCreatedBy = new HashSet<Warehouse>();
            WarehouseLastUpdateActiveBy = new HashSet<Warehouse>();
            WarehouseUpdatedBy = new HashSet<Warehouse>();
        }

        public int UserId { get; set; }
        public int? RoleId { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public int? StatusId { get; set; }
        public int? SalutationId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int? DepartmentId { get; set; }
        public int? CompanyId { get; set; }
        public string WorkEmail { get; set; }
        public string WorkPhoneNumber { get; set; }
        public string MobileNumber { get; set; }
        public bool? IsVerified { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsArchived { get; set; }
        public string VerificationToken { get; set; }
        public DateTime? VerificationSentDate { get; set; }
        public string VerificationEmail { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? CreatedDate { get; set; }
        public int? UpdatedById { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int? ArchivedById { get; set; }
        public DateTime? ArchivedDate { get; set; }
        public int? AuthGroupId { get; set; }

        public virtual AuthUser ArchivedBy { get; set; }
        public virtual AuthGroup AuthGroup { get; set; }
        public virtual Company Company { get; set; }
        public virtual AuthUser CreatedBy { get; set; }
        public virtual Department Department { get; set; }
        public virtual AuthRole Role { get; set; }
        public virtual Salutation Salutation { get; set; }
        public virtual AuthStatus Status { get; set; }
        public virtual AuthUser UpdatedBy { get; set; }
        public virtual ICollection<AuthGroupRole> AuthGroupRole { get; set; }
        public virtual ICollection<Category> CategoryCreatedBy { get; set; }
        public virtual ICollection<CategoryProperty> CategoryPropertyCreatedBy { get; set; }
        public virtual ICollection<CategoryProperty> CategoryPropertyUpdatedBy { get; set; }
        public virtual ICollection<Category> CategoryUpdatedBy { get; set; }
        public virtual ICollection<Company> CompanyCreatedBy { get; set; }
        public virtual ICollection<CompanyGroup> CompanyGroupCreatedBy { get; set; }
        public virtual ICollection<CompanyGroup> CompanyGroupUpdatedBy { get; set; }
        public virtual ICollection<Company> CompanyUpdatedby { get; set; }
        public virtual ICollection<Contact> ContactArchivedBy { get; set; }
        public virtual ICollection<Contact> ContactAuthuser { get; set; }
        public virtual ICollection<Contact> ContactCreatedBy { get; set; }
        public virtual ICollection<Contact> ContactUpdatedBy { get; set; }
        public virtual ICollection<Department> DepartmentCreatedBy { get; set; }
        public virtual ICollection<Department> DepartmentUpdatedBy { get; set; }
        public virtual ICollection<DocumentFileTypes> DocumentFileTypesCreatedBy { get; set; }
        public virtual ICollection<DocumentFileTypes> DocumentFileTypesUpdatedBy { get; set; }
        public virtual ICollection<GroundConditions> GroundConditions { get; set; }
        public virtual ICollection<GroundServices> GroundServices { get; set; }
        public virtual ICollection<Group> GroupCreatedBy { get; set; }
        public virtual ICollection<Group> GroupUpdatedBy { get; set; }
        public virtual ICollection<AuthUser> InverseArchivedBy { get; set; }
        public virtual ICollection<AuthUser> InverseCreatedBy { get; set; }
        public virtual ICollection<AuthUser> InverseUpdatedBy { get; set; }
        public virtual ICollection<Offering> OfferingCreatedBy { get; set; }
        public virtual ICollection<OfferingProperty> OfferingPropertyCreatedBy { get; set; }
        public virtual ICollection<OfferingProperty> OfferingPropertyUpdatedBy { get; set; }
        public virtual ICollection<OfferingRate> OfferingRateCreatedBy { get; set; }
        public virtual ICollection<OfferingRate> OfferingRateUpdatedBy { get; set; }
        public virtual ICollection<OfferingTax> OfferingTax { get; set; }
        public virtual ICollection<Offering> OfferingUpdatedBy { get; set; }
        public virtual ICollection<Property> PropertyCreatedBy { get; set; }
        public virtual ICollection<PropertyOption> PropertyOptionCreatedBy { get; set; }
        public virtual ICollection<PropertyOption> PropertyOptionUpdatedBy { get; set; }
        public virtual ICollection<Property> PropertyUpdatedBy { get; set; }
        public virtual ICollection<Schedules> SchedulesCreatedBy { get; set; }
        public virtual ICollection<Schedules> SchedulesUpdatedBy { get; set; }
        public virtual ICollection<ShowDocs> ShowDocsCreatedBy { get; set; }
        public virtual ICollection<ShowDocs> ShowDocsUpdatedBy { get; set; }
        public virtual ICollection<ShowLocationHalls> ShowLocationHallsArchivedBy { get; set; }
        public virtual ICollection<ShowLocationHalls> ShowLocationHallsCreatedBy { get; set; }
        public virtual ICollection<ShowLocationHalls> ShowLocationHallsUpdatedBy { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsArchivedBy { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsCreatedBy { get; set; }
        public virtual ICollection<ShowLocations> ShowLocationsUpdatedBy { get; set; }
        public virtual ICollection<ShowSchedules> ShowSchedules { get; set; }
        public virtual ICollection<Shows> Shows { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceCreatedBy { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceUpdatedBy { get; set; }
        public virtual ICollection<TaxProvince> TaxProvinceUpdatedIsActiveBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseContactPerson { get; set; }
        public virtual ICollection<Warehouse> WarehouseCreatedBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseLastUpdateActiveBy { get; set; }
        public virtual ICollection<Warehouse> WarehouseUpdatedBy { get; set; }
    }
}