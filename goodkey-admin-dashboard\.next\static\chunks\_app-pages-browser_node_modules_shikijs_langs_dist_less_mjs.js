"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_less_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/less.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/less.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Less\\\",\\\"name\\\":\\\"less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#less-namespace-accessors\\\"},{\\\"include\\\":\\\"#less-extend\\\"},{\\\"include\\\":\\\"#at-rules\\\"},{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"include\\\":\\\"#property-list\\\"},{\\\"include\\\":\\\"#selector\\\"}],\\\"repository\\\":{\\\"angle-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE](?:[-+]?\\\\\\\\d+))*)|(?:[-+]?\\\\\\\\d+))(deg|grad|rad|turn))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"arbitrary-repetition\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:(,))\\\"},\\\"at-charset\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)charset\\\\\\\\b)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.charset.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*((?=;|$))\\\",\\\"name\\\":\\\"meta.at-rule.charset.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]},\\\"at-container\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@container)\\\",\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"((@)container)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.container.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.container.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.container.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=[{;])\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(not|and|or)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.comparison.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.at-rule.container-query.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(aspect-ratio|block-size|height|inline-size|orientation|width)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.size-feature.less\\\"},{\\\"match\\\":\\\"((<|>)=?)|=|\\\\\\\\/\\\",\\\"name\\\":\\\"keyword.operator.comparison.less\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},{\\\"match\\\":\\\"portrait|landscape\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"match\\\":\\\"\\\\\\\\/\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"include\\\":\\\"#style-function\\\"},{\\\"match\\\":\\\"--|(?:-?(?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R]))))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*\\\",\\\"name\\\":\\\"variable.parameter.container-name.css\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-counter-style\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)counter-style\\\\\\\\b)\\\\\\\\s+(?:(?i:\\\\\\\\b(decimal|none)\\\\\\\\b)|(-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*))\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.counter-style.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"invalid.illegal.counter-style-name.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.other.counter-style-name.css\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"name\\\":\\\"meta.at-rule.counter-style.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-custom-media\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@custom-media\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=;)\\\",\\\"name\\\":\\\"meta.at-rule.custom-media.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.property-list.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*;\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.custom-media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.custom-media.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((@)custom-media)(?=.*?)\\\"},{\\\"include\\\":\\\"#media-query-list\\\"}]},\\\"at-font-face\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)font-face)\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.font-face.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"name\\\":\\\"meta.at-rule.font-face.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-import\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)import\\\\\\\\b)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.import.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\;\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.at-rule.import.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"begin\\\":\\\"(?<=([\\\\\\\"'])|([\\\\\\\"']\\\\\\\\)))\\\\\\\\s*\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\;)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"reference|inline|less|css|once|multiple|optional\\\",\\\"name\\\":\\\"constant.language.import-directive.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]},{\\\"include\\\":\\\"#literal-string\\\"}]},\\\"at-keyframes\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)keyframes)(?=.*?\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.keyframe.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.constant.keyframe.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.keyframe-selector.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:(from|to)|((?:\\\\\\\\.[0-9]+|[0-9]+(?:\\\\\\\\.[0-9]*)?)(%)))\\\\\\\\s*,?\\\\\\\\s*\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.keyframe.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#keyframe-name\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]},\\\"at-media\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@media\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*((@)media)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.media.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.media.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query-list\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-namespace\\\":{\\\"begin\\\":\\\"\\\\\\\\s*((@)namespace)\\\\\\\\s+\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.namespace.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"end\\\":\\\"\\\\\\\\;\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.at-rule.namespace.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"match\\\":\\\"(-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.name.constant.namespace-prefix.less\\\"}]},\\\"at-page\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.page.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*((@)page)\\\\\\\\s*(?:(:)(first|left|right))?\\\\\\\\s*(?=\\\\\\\\{|$)\\\",\\\"name\\\":\\\"meta.at-rule.page.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#rule-list\\\"}]},\\\"at-rules\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#at-charset\\\"},{\\\"include\\\":\\\"#at-container\\\"},{\\\"include\\\":\\\"#at-counter-style\\\"},{\\\"include\\\":\\\"#at-custom-media\\\"},{\\\"include\\\":\\\"#at-font-face\\\"},{\\\"include\\\":\\\"#at-media\\\"},{\\\"include\\\":\\\"#at-import\\\"},{\\\"include\\\":\\\"#at-keyframes\\\"},{\\\"include\\\":\\\"#at-namespace\\\"},{\\\"include\\\":\\\"#at-page\\\"},{\\\"include\\\":\\\"#at-supports\\\"},{\\\"include\\\":\\\"#at-viewport\\\"}]},\\\"at-supports\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\s*@supports\\\\\\\\b)\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*)(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*((@)supports)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.at-rule.supports.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.constant.supports.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"name\\\":\\\"meta.at-rule.supports.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#at-supports-operators\\\"},{\\\"include\\\":\\\"#at-supports-parens\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"at-supports-operators\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:and|or|not)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.operator.logic.less\\\"},\\\"at-supports-parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#at-supports-operators\\\"},{\\\"include\\\":\\\"#at-supports-parens\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"}]},\\\"attr-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(attr)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#qualified-name\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"begin\\\":\\\"(-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"entity.other.attribute-name.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((?i:em|ex|ch|rem)|(?i:vw|vh|vmin|vmax)|(?i:cm|mm|q|in|pt|pc|px|fr)|(?i:deg|grad|rad|turn)|(?i:s|ms)|(?i:Hz|kHz)|(?i:dpi|dpcm|dppx))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"include\\\":\\\"#numeric-values\\\"}]},{\\\"include\\\":\\\"#color-values\\\"}]}]},\\\"builtin-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attr-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#counter-functions\\\"},{\\\"include\\\":\\\"#cross-fade-function\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#filter-function\\\"},{\\\"include\\\":\\\"#fit-content-function\\\"},{\\\"include\\\":\\\"#format-function\\\"},{\\\"include\\\":\\\"#gradient-functions\\\"},{\\\"include\\\":\\\"#grid-repeat-function\\\"},{\\\"include\\\":\\\"#image-function\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#local-function\\\"},{\\\"include\\\":\\\"#minmax-function\\\"},{\\\"include\\\":\\\"#regexp-function\\\"},{\\\"include\\\":\\\"#shape-functions\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"include\\\":\\\"#symbols-function\\\"},{\\\"include\\\":\\\"#transform-functions\\\"},{\\\"include\\\":\\\"#url-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]},\\\"calc-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(calc)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.calc.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#attr-function\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#relative-color\\\"}]}]},\\\"color-adjuster-operators\\\":{\\\"match\\\":\\\"[\\\\\\\\-\\\\\\\\+*](?=\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.operator.less\\\"},\\\"color-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(rgba?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"comment\\\":\\\"rgb(), rgba()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#value-separator\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hsla|hsl|hwb|oklab|oklch|lab|lch)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"comment\\\":\\\"hsla, hsl, hwb, oklab, oklch, lab, lch\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#value-separator\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(light-dark)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"comment\\\":\\\"light-dark()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"include\\\":\\\"#less-color-functions\\\"}]},\\\"color-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"match\\\":\\\"\\\\\\\\b(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-standard-color-name.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|turquoise|violet|wheat|whitesmoke|yellowgreen)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-extended-color-keywords.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i)currentColor|transparent)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.color.w3c-special-color-keyword.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.constant.less\\\"}},\\\"match\\\":\\\"(#)(\\\\\\\\h{3}|\\\\\\\\h{4}|\\\\\\\\h{6}|\\\\\\\\h{8})\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.color.rgb-value.less\\\"},{\\\"include\\\":\\\"#relative-color\\\"}]},\\\"comma-delimiter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(,)\\\\\\\\s*\\\"},\\\"comment-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"name\\\":\\\"comment.block.less\\\"},{\\\"include\\\":\\\"#comment-line\\\"}]},\\\"comment-line\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.less\\\"}},\\\"match\\\":\\\"(//).*$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.double-slash.less\\\"},\\\"counter-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(counter)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"match\\\":\\\"(?:--(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))+|-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.counter-name.less\\\"},{\\\"begin\\\":\\\"(?=,)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?xi:arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)|none)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.counter-style.less\\\"}]}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(counters)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.counter-name.less string.unquoted.less\\\"},{\\\"begin\\\":\\\"(?=,)\\\",\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?xi:arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)|none)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.counter-style.less\\\"}]}]}]}]},\\\"cross-fade-function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(cross-fade)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]}]},\\\"cubic-bezier-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(cubic-bezier)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.timing.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"contentName\\\":\\\"meta.group.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"}]},\\\"custom-property-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.custom-property.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.custom-property.name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(--)((?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))+)\\\",\\\"name\\\":\\\"support.type.custom-property.less\\\"},\\\"dimensions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#frequency-type\\\"},{\\\"include\\\":\\\"#time-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"}]},\\\"filter-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(filter)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#filter-functions\\\"}]}]},\\\"filter-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-functions\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(blur)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(brightness|contrast|grayscale|invert|opacity|saturate|sepia)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-functions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(drop-shadow)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hue-rotate)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.filter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"}]}]}]},\\\"fit-content-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(fit-content)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"}]}]},\\\"format-function\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(format)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.format.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]}]},\\\"frequency-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE](?:[-+]?\\\\\\\\d+))*)|(?:[-+]?\\\\\\\\d+))(Hz|kHz))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"global-property-values\\\":{\\\"match\\\":\\\"\\\\\\\\b(?:initial|inherit|unset|revert-layer|revert)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},\\\"gradient-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b((?:repeating-)?linear-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\bto\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:repeating-)?radial-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b(at|circle|ellipse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center|(farthest|closest)-(corner|side))\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]}]},\\\"grid-repeat-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(repeat)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#minmax-function\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"match\\\":\\\"\\\\\\\\b(auto-(fill|fit))\\\\\\\\b\\\",\\\"name\\\":\\\"support.keyword.repetitions.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(((max|min)-content)|auto)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},\\\"image-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(image)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#image-type\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"image-type\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#cross-fade-function\\\"},{\\\"include\\\":\\\"#gradient-functions\\\"},{\\\"include\\\":\\\"#image-function\\\"},{\\\"include\\\":\\\"#url-function\\\"}]},\\\"important\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"(\\\\\\\\!)\\\\\\\\s*important\\\",\\\"name\\\":\\\"keyword.other.important.less\\\"},\\\"integer-type\\\":{\\\"match\\\":\\\"(?:[-+]?\\\\\\\\d+)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"keyframe-name\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(-?(?:[_a-z]|[^\\\\\\\\x{00}-\\\\\\\\x{7F}]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\s\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))(?:[_a-z0-9-]|[^\\\\\\\\x{00}-\\\\\\\\x{7F}]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))*)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.constant.animation-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}}},\\\"length-type\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?:[-+]?)(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?(em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|m|q|in|pt|pc|px|fr|dpi|dpcm|dppx|x)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:[-+]?)0\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"}]},\\\"less-boolean-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(boolean)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.boolean.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-logical-comparisons\\\"}]}]},\\\"less-color-blend-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(multiply|screen|overlay|(soft|hard)light|difference|exclusion|negation|average)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-blend.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]}]},\\\"less-color-channel-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(hue|saturation|lightness|hsv(hue|saturation|value)|red|green|blue|alpha|luma|luminance)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-definition.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"}]}]}]},\\\"less-color-definition-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(argb)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-definition.less\\\"}},\\\"comment\\\":\\\"argb()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(hsva?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"comment\\\":\\\"hsva(), hsv()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#integer-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]}]},\\\"less-color-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-color-blend-functions\\\"},{\\\"include\\\":\\\"#less-color-channel-functions\\\"},{\\\"include\\\":\\\"#less-color-definition-functions\\\"},{\\\"include\\\":\\\"#less-color-operation-functions\\\"}]},\\\"less-color-operation-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(fade|shade|tint)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(spin)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(((de)?saturate)|((light|dark)en)|(fade(in|out)))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"match\\\":\\\"\\\\\\\\brelative\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.relative.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(contrast)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(greyscale)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(mix)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color-operation.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]}]},\\\"less-extend\\\":{\\\"begin\\\":\\\"(:)(extend)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.extend.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\ball\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.all.less\\\"},{\\\"include\\\":\\\"#selectors\\\"}]}]},\\\"less-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-boolean-function\\\"},{\\\"include\\\":\\\"#less-color-functions\\\"},{\\\"include\\\":\\\"#less-if-function\\\"},{\\\"include\\\":\\\"#less-list-functions\\\"},{\\\"include\\\":\\\"#less-math-functions\\\"},{\\\"include\\\":\\\"#less-misc-functions\\\"},{\\\"include\\\":\\\"#less-string-functions\\\"},{\\\"include\\\":\\\"#less-type-functions\\\"}]},\\\"less-if-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(if)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.if.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-mixin-guards\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},\\\"less-list-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(length)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.length.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(extract)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.extract.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(range)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.range.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"}]}]}]},\\\"less-logical-comparisons\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(=|((<|>)=?))\\\\\\\\s*\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-logical-comparisons\\\"}]},{\\\"match\\\":\\\"\\\\\\\\btrue|false\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.less\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]},\\\"less-math\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[-\\\\\\\\+\\\\\\\\*\\\\\\\\/]\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-math\\\"}]},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]},\\\"less-math-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(ceil|floor|percentage|round|sqrt|abs|a?(sin|cos|tan))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.math.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"}]}]},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"support.function.math.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"((pi)(\\\\\\\\()(\\\\\\\\)))\\\",\\\"name\\\":\\\"meta.function-call.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(pow|m(od|in|ax))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.math.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]}]},\\\"less-misc-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(color)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.color.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(image-(size|width|height))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.image.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(convert|unit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.convert.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"((c|m)?m|in|p(t|c|x)|m?s|g?rad|deg|turn|%|r?em|ex|ch)\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(data-uri)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.data-uri.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(?:(,))\\\"}]}]},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(default(\\\\\\\\()(\\\\\\\\)))\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.default.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(get-unit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.get-unit.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#dimensions\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(svg-gradient)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.svg-gradient.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"match\\\":\\\"\\\\\\\\bto\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(at|circle|ellipse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"}]}]}]},\\\"less-mixin-guards\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(and|not|or)?\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-comparison\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.group.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"match\\\":\\\"default((\\\\\\\\()(\\\\\\\\)))\\\",\\\"name\\\":\\\"support.function.default.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#less-logical-comparisons\\\"},{\\\"include\\\":\\\"$self\\\"}]}]}]},\\\"less-namespace-accessors\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=\\\\\\\\s*when\\\\\\\\b)\\\",\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"name\\\":\\\"meta.conditional.guarded-namespace.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.conditional.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.keyword.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(when)(?=.*?)\\\"},{\\\"include\\\":\\\"#less-mixin-guards\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.property-list.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\})\\\",\\\"name\\\":\\\"meta.block.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"}]},{\\\"include\\\":\\\"#selectors\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"match\\\":\\\"(;)|(?=[})])\\\"}]},\\\"less-string-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(e(scape)?)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.escape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\s*(%)(?=\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.format.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(replace)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.replace.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]}]},\\\"less-strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(~)('|\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"contentName\\\":\\\"markup.raw.inline.less\\\",\\\"end\\\":\\\"('|\\\\\\\")|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.other.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]}]},\\\"less-type-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(is(number|string|color|keyword|url|pixel|em|percentage|ruleset))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(isunit)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?i:em|ex|ch|rem)|(?i:vw|vh|vmin|vmax)|(?i:cm|mm|q|in|pt|pc|px|fr)|(?i:deg|grad|rad|turn)|(?i:s|ms)|(?i:Hz|kHz)|(?i:dpi|dpcm|dppx))\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.unit.less\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(isdefined)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.type.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"}]}]}]},\\\"less-variable-assignment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(@)(-?(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;|(\\\\\\\\.{3})|(?=\\\\\\\\)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.spread.less\\\"}},\\\"name\\\":\\\"meta.property-value.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#property-list\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"less-variable-comparison\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(@{1,2})([-]?([_a-z]|[^\\\\\\\\x{00}-\\\\\\\\x{7F}]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(=|((<|>)=?))\\\\\\\\s*\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.less\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"}]}]},\\\"less-variable-interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.expression.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.expression.less\\\"}},\\\"match\\\":\\\"(@)(\\\\\\\\{)([-\\\\\\\\w]+)(\\\\\\\\})\\\",\\\"name\\\":\\\"variable.other.readwrite.less\\\"},\\\"less-variables\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.variable.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.other.variable.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(@@?)([-\\\\\\\\w]+)\\\",\\\"name\\\":\\\"variable.other.readwrite.less\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},\\\"literal-string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"end\\\":\\\"(')|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.single.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.less\\\"}},\\\"end\\\":\\\"(\\\\\\\")|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.less\\\"}},\\\"name\\\":\\\"string.quoted.double.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-content\\\"}]},{\\\"include\\\":\\\"#less-strings\\\"}]},\\\"local-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(local)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.font-face.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},\\\"media-query\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(only|not)?\\\\\\\\s*(all|aural|braille|embossed|handheld|print|projection|screen|tty|tv)?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logic.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.constant.media.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(?:(,)|(?=[{;]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arbitrary-repetition.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*(and)?\\\\\\\\s*(\\\\\\\\()\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logic.media.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(--|(?:-?(?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R]))))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*)\\\\\\\\s*(?=[:)])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.media.less\\\"}},\\\"end\\\":\\\"(((\\\\\\\\+_?)?):)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}}},{\\\"match\\\":\\\"\\\\\\\\b(portrait|landscape|progressive|interlace)\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\d+)(/)(\\\\\\\\d+)\\\"},{\\\"include\\\":\\\"#less-math\\\"}]}]},\\\"media-query-list\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(?=[^{;])\\\",\\\"end\\\":\\\"\\\\\\\\s*(?=[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#media-query\\\"}]},\\\"minmax-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(minmax)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.grid.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"match\\\":\\\"\\\\\\\\b(max-content|min-content)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},\\\"number-type\\\":{\\\"match\\\":\\\"(?:[-+]?)(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"numeric-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#dimensions\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#number-type\\\"}]},\\\"percentage-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?:[-+]?)(?:\\\\\\\\d+\\\\\\\\.\\\\\\\\d+|\\\\\\\\.?\\\\\\\\d+)(?:[eE][-+]?\\\\\\\\d+)?(%)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"property-list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=(?=[^;]*)\\\\\\\\{)\\\",\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.end.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list\\\"}]}]},\\\"property-value-constants\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"align-content, align-items, align-self, justify-content, justify-items, justify-self\\\",\\\"match\\\":\\\"\\\\\\\\b(flex-start|flex-end|start|end|space-between|space-around|space-evenly|stretch|baseline|safe|unsafe|legacy|anchor-center|first|last|self-start|self-end)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"alignment-baseline\\\",\\\"match\\\":\\\"\\\\\\\\b(text-before-edge|before-edge|middle|central|text-after-edge|after-edge|ideographic|alphabetic|hanging|mathematical|top|center|bottom)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#global-property-values\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"comment\\\":\\\"animation-composition\\\",\\\"match\\\":\\\"\\\\\\\\b(?:replace|add|accumulate)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-direction\\\",\\\"match\\\":\\\"\\\\\\\\b(?:normal|alternate-reverse|alternate|reverse)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-fill-mode\\\",\\\"match\\\":\\\"\\\\\\\\b(?:forwards|backwards|both)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-iteration-count\\\",\\\"match\\\":\\\"\\\\\\\\b(?:infinite)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-play-state\\\",\\\"match\\\":\\\"\\\\\\\\b(?:running|paused)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-range, animation-range-start, animation-range-end\\\",\\\"match\\\":\\\"\\\\\\\\b(?:entry-crossing|exit-crossing|entry|exit)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"comment\\\":\\\"animation-timing-function\\\",\\\"match\\\":\\\"\\\\\\\\b(linear|ease-in-out|ease-in|ease-out|ease|step-start|step-end)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(absolute|active|add|all-petite-caps|all-small-caps|all-scroll|all|alphabetic|alpha|alternate-reverse|alternate|always|annotation|antialiased|at|autohiding-scrollbar|auto|avoid-column|avoid-page|avoid-region|avoid|background-color|background-image|background-position|background-size|background-repeat|background|backwards|balance|baseline|below|bevel|bicubic|bidi-override|blink|block-line-height|block-start|block-end|block|blur|bolder|bold|border-top-left-radius|border-top-right-radius|border-bottom-left-radius|border-bottom-right-radius|border-end-end-radius|border-end-start-radius|border-start-end-radius|border-start-start-radius|border-block-start-color|border-block-start-style|border-block-start-width|border-block-start|border-block-end-color|border-block-end-style|border-block-end-width|border-block-end|border-block-color|border-block-style|border-block-width|border-block|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-inline-start|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-end|border-inline-color|border-inline-style|border-inline-width|border-inline|border-top-color|border-top-style|border-top-width|border-top|border-right-color|border-right-style|border-right-width|border-right|border-bottom-color|border-bottom-style|border-bottom-width|border-bottom|border-left-color|border-left-style|border-left-width|border-left|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-image|border-color|border-style|border-width|border-radius|border-collapse|border-spacing|border|both|bottom|box-shadow|box|break-all|break-word|break-spaces|brightness|butt(on)?|capitalize|central|center|char(acter-variant)?|cjk-ideographic|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color-stop|color-burn|color-dodge|color|column-count|column-gap|column-reverse|column-rule-color|column-rule-width|column-rule|column-width|columns|column|common-ligatures|condensed|consider-shifts|contain|content-box|contents?|contextual|contrast|cover|crisp-edges|crispEdges|crop|crosshair|cross|darken|dashed|default|dense|device-width|diagonal-fractions|difference|disabled|discard|discretionary-ligatures|disregard-shifts|distribute-all-lines|distribute-letter|distribute-space|distribute|dotted|double|drop-shadow|[nsew]{1,4}-resize|ease-in-out|ease-in|ease-out|ease|element|ellipsis|embed|end|EndColorStr|evenodd|exclude-ruby|exclusion|expanded|extra-condensed|extra-expanded|farthest-corner|farthest-side|farthest|fill-box|fill-opacity|fill|filter|fit-content|fixed|flat|flex-basis|flex-end|flex-grow|flex-shrink|flex-start|flexbox|flex|flip|flood-color|font-size-adjust|font-size|font-stretch|font-weight|font|forwards|from-image|from|full-width|gap|geometricPrecision|glyphs|gradient|grayscale|grid-column-gap|grid-column|grid-row-gap|grid-row|grid-gap|grid-height|grid|groove|hand|hanging|hard-light|height|help|hidden|hide|historical-forms|historical-ligatures|horizontal-tb|horizontal|hue|ideographic|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|inactive|include-ruby|infinite|inherit|initial|inline-end|inline-size|inline-start|inline-table|inline-line-height|inline-flexbox|inline-flex|inline-box|inline-block|inline|inset|inside|inter-ideograph|inter-word|intersect|invert|isolate|isolation|italic|jis(04|78|83|90)|justify-all|justify|keep-all|larger|large|last|layout|left|letter-spacing|lighten|lighter|lighting-color|linear-gradient|linearRGB|linear|line-edge|line-height|line-through|line|lining-nums|list-item|local|loose|lowercase|lr-tb|ltr|luminosity|luminance|manual|manipulation|margin-bottom|margin-box|margin-left|margin-right|margin-top|margin|marker(-offset|s)?|match-parent|mathematical|max-(content|height|lines|size|width)|medium|middle|min-(content|height|width)|miter|mixed|move|multiply|newspaper|no-change|no-clip|no-close-quote|no-open-quote|no-common-ligatures|no-discretionary-ligatures|no-historical-ligatures|no-contextual|no-drop|no-repeat|none|nonzero|normal|not-allowed|nowrap|oblique|offset-after|offset-before|offset-end|offset-start|offset|oldstyle-nums|opacity|open-quote|optimize(Legibility|Precision|Quality|Speed)|order|ordinal|ornaments|outline-color|outline-offset|outline-width|outline|outset|outside|overline|over-edge|overlay|padding(-bottom|-box|-left|-right|-top|-box)?|page|paint(ed)?|paused|pan-(x|left|right|y|up|down)|perspective-origin|petite-caps|pixelated|pointer|pinch-zoom|pretty|pre(-line|-wrap)?|preserve-3d|preserve-breaks|preserve-spaces|preserve|progid:DXImageTransform\\\\\\\\.Microsoft\\\\\\\\.(Alpha|Blur|dropshadow|gradient|Shadow)|progress|proportional-nums|proportional-width|radial-gradient|recto|region|relative|repeating-linear-gradient|repeating-radial-gradient|repeat-x|repeat-y|repeat|replaced|reset-size|reverse|revert-layer|revert|ridge|right|round|row-gap|row-resize|row-reverse|row|rtl|ruby|running|saturate|saturation|screen|scrollbar|scroll-position|scroll|separate|sepia|scale-down|semi-condensed|semi-expanded|shape-image-threshold|shape-margin|shape-outside|show|sideways-lr|sideways-rl|sideways|simplified|size|slashed-zero|slice|small-caps|smaller|small|smooth|snap|solid|soft-light|space-around|space-between|space|span|sRGB|stable|stacked-fractions|stack|startColorStr|start|static|step-end|step-start|sticky|stop-color|stop-opacity|stretch|strict|stroke-box|stroke-dasharray|stroke-dashoffset|stroke-miterlimit|stroke-opacity|stroke-width|stroke|styleset|style|stylistic|subgrid|subpixel-antialiased|subtract|super|swash|table-caption|table-cell|table-column-group|table-footer-group|table-header-group|table-row-group|table-column|table-row|table|tabular-nums|tb-rl|text((-bottom|-(decoration|emphasis)-color|-indent|-(over|under)-edge|-shadow|-size(-adjust)?|-top)|field)?|thick|thin|titling-caps|titling-case|top|touch|to|traditional|transform-origin|transform-style|transform|ultra-condensed|ultra-expanded|under-edge|underline|unicase|unset|uppercase|upright|use-glyph-orientation|use-script|verso|vertical(-align|-ideographic|-lr|-rl|-text)?|view-box|viewport-fill-opacity|viewport-fill|visibility|visibleFill|visiblePainted|visibleStroke|visible|wait|wavy|weight|whitespace|width|word-spacing|wrap-reverse|wrap-reverse|wrap|xx?-(large|small)|z-index|zero|zoom-in|zoom-out|zoom|arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|decimal-leading-zero|decimal|devanagari|disclosure-closed|disclosure-open|disc|ethiopic-numeric|georgian|gujarati|gurmukhi|hebrew|hiragana-iroha|hiragana|japanese-formal|japanese-informal|kannada|katakana-iroha|katakana|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sans-serif|serif|monospace|fantasy|cursive)\\\\\\\\b(?=\\\\\\\\s*[;,\\\\\\\\n}])\\\",\\\"name\\\":\\\"support.constant.font-name.less\\\"}]},\\\"property-values\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#color-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#unicode-range\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#color-values\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#important\\\"}]},\\\"pseudo-selectors\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(:)(dir)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"ltr|rtl\\\",\\\"name\\\":\\\"variable.parameter.dir.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"(:)(lang)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"}]}]},{\\\"begin\\\":\\\"(:)(not)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"begin\\\":\\\"(:)(nth(-last)?-(child|of-type))(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"contentName\\\":\\\"meta.function-call.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(even|odd)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.pseudo-class.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.less\\\"}},\\\"match\\\":\\\"(?:([-+])?(?:\\\\\\\\d+)?(n)(\\\\\\\\s*([-+])\\\\\\\\s*\\\\\\\\d+)?|[-+]?\\\\\\\\s*\\\\\\\\d+)\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#less-strings\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]}]},{\\\"begin\\\":\\\"(:)(host-context|host|has|is|not|where)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.attribute-name.pseudo-class.less\\\"}},\\\"match\\\":\\\"(:)(active|any-link|autofill|blank|buffering|checked|current|default|defined|disabled|empty|enabled|first-child|first-of-type|first|focus-visible|focus-within|focus|fullscreen|future|host|hover|in-range|indeterminate|invalid|last-child|last-of-type|left|local-link|link|modal|muted|only-child|only-of-type|optional|out-of-range|past|paused|picture-in-picture|placeholder-shown|playing|popover-open|read-only|read-write|required|right|root|scope|seeking|stalled|target-within|target|user-invalid|user-valid|valid|visited|volume-locked)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.function-call.less\\\"},{\\\"begin\\\":\\\"(::?)(highlight|part|state)(?=\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"comment\\\":\\\"::highlight()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"--|(?:-?(?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R]))))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*\\\",\\\"name\\\":\\\"variable.parameter.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"}]}]},{\\\"begin\\\":\\\"(::?)slotted(?=\\\\\\\\s*(\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"comment\\\":\\\"::slotted()\\\",\\\"contentName\\\":\\\"meta.function-call.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.group.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#selectors\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"comment\\\":\\\"defined pseudo-elements\\\",\\\"match\\\":\\\"(::?)(after|backdrop|before|cue|file-selector-button|first-letter|first-line|grammar-error|marker|placeholder|selection|spelling-error|target-text|view-transition-group|view-transition-image-pair|view-transition-new|view-transition-old|view-transition)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.namespace.vendor-prefix.less\\\"}},\\\"comment\\\":\\\"other possible pseudo-elements\\\",\\\"match\\\":\\\"(::?)(-\\\\\\\\w+-)(--|(?:-?(?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R]))))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.other.attribute-name.pseudo-element.less\\\"}]},\\\"qualified-name\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.constant.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.namespace.wildcard.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.namespace.less\\\"}},\\\"match\\\":\\\"(?:(-?(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)|(\\\\\\\\*))?([|])(?!=)\\\"},\\\"regexp-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(regexp)(?=\\\\\\\\()\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"support.function.regexp.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#literal-string\\\"}]}]},\\\"relative-color\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"from\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b[hslawbch]\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"}]},\\\"rule-list\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.block.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\s*\\\\\\\\})\\\",\\\"name\\\":\\\"meta.property-list.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\"},{\\\"include\\\":\\\"#rule-list-body\\\"},{\\\"include\\\":\\\"#less-extend\\\"}]}]},\\\"rule-list-body\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#comment-line\\\"},{\\\"include\\\":\\\"#at-rules\\\"},{\\\"include\\\":\\\"#less-variable-assignment\\\"},{\\\"begin\\\":\\\"(?=[-\\\\\\\\w]*?@\\\\\\\\{.*\\\\\\\\}[-\\\\\\\\w]*?\\\\\\\\s*:[^;{(]*(?=[;})]))\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[^\\\\\\\\s:])\\\",\\\"end\\\":\\\"(?=(((\\\\\\\\+_?)?):)[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"name\\\":\\\"support.type.property-name.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"support.type.property-name.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"begin\\\":\\\"(?=[-a-z])\\\",\\\"end\\\":\\\"$|(?![-a-z])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"begin\\\":\\\"(-[\\\\\\\\w-]+?-)((?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"},\\\"1\\\":{\\\"name\\\":\\\"meta.namespace.vendor-prefix.less\\\"}},\\\"comment\\\":\\\"vendor-prefixed properties\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"},{\\\"match\\\":\\\"[\\\\\\\\w-]+\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"}]}]},{\\\"include\\\":\\\"#filter-function\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(border((-(bottom|top)-(left|right))|((-(start|end)){2}))?-radius|(border-image(?!-)))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"comment\\\":\\\"border-radius and border-image properties utilize a slash as a separator\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#value-separator\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.custom-property.prefix.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.custom-property.name.less\\\"}},\\\"match\\\":\\\"\\\\\\\\b(var-)(-?(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"invalid.deprecated.custom-property.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\bfont(-family)?(?!-)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"match\\\":\\\"-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*(\\\\\\\\s+-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)*\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.less\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\banimation-timeline\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"include\\\":\\\"#scroll-function\\\"},{\\\"include\\\":\\\"#view-function\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\banimation(?:-name)?(?=(?:\\\\\\\\+_?)?:)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#less-functions\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#numeric-values\\\"},{\\\"include\\\":\\\"#property-value-constants\\\"},{\\\"match\\\":\\\"-?(?:[_a-zA-Z]|[^\\\\\\\\x{00}-\\\\\\\\x{7F}]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\s\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))(?:[-_a-zA-Z0-9]|[^\\\\\\\\x{00}-\\\\\\\\x{7F}]|(?:(:?\\\\\\\\\\\\\\\\[0-9a-f]{1,6}(\\\\\\\\r\\\\\\\\n|[\\\\\\\\t\\\\\\\\r\\\\\\\\n\\\\\\\\f])?)|\\\\\\\\\\\\\\\\[^\\\\\\\\r\\\\\\\\n\\\\\\\\f0-9a-f]))*\\\",\\\"name\\\":\\\"variable.other.constant.animation-name.less string.unquoted.less\\\"},{\\\"include\\\":\\\"#less-math\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(transition(-(property|duration|delay|timing-function))?)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#time-type\\\"},{\\\"include\\\":\\\"#property-values\\\"},{\\\"include\\\":\\\"#cubic-bezier-function\\\"},{\\\"include\\\":\\\"#steps-function\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(?:backdrop-)?filter\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"\\\\\\\\b(inherit|initial|unset|none)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.property-value.less\\\"},{\\\"include\\\":\\\"#filter-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bwill-change\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"unset|initial|inherit|will-change|auto|scroll-position|contents\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"},{\\\"match\\\":\\\"-?(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bcounter-(increment|(re)?set)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.type.property-name.less\\\"}},\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"meta.property-name.less\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"match\\\":\\\"(((\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\"},{\\\"match\\\":\\\"-?(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{9f}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*\\\",\\\"name\\\":\\\"entity.name.constant.counter-name.less\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"match\\\":\\\"unset|initial|inherit|auto\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\bcontainer(?:-name)?(?=\\\\\\\\s*?:)\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"name\\\":\\\"support.type.property-name.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(((\\\\\\\\+_?)?):)(?=[\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"(?=\\\\\\\\s*(;)|(?=[})]))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bdefault\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.property-value.less\\\"},{\\\"include\\\":\\\"#global-property-values\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"contentName\\\":\\\"variable.other.constant.container-name.less\\\",\\\"match\\\":\\\"--|(?:-?(?:(?:[a-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R]))))(?:(?:[-\\\\\\\\da-zA-Z_]|[\\\\\\\\x{00B7}\\\\\\\\x{00C0}-\\\\\\\\x{00D6}\\\\\\\\x{00D8}-\\\\\\\\x{00F6}\\\\\\\\x{00F8}-\\\\\\\\x{037D}\\\\\\\\x{037F}-\\\\\\\\x{1FFF}\\\\\\\\x{200C}\\\\\\\\x{200D}\\\\\\\\x{203F}\\\\\\\\x{2040}\\\\\\\\x{2070}-\\\\\\\\x{218F}\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}\\\\\\\\x{3001}-\\\\\\\\x{D7FF}\\\\\\\\x{F900}-\\\\\\\\x{FDCF}\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}])|(?:\\\\\\\\\\\\\\\\(?:\\\\\\\\N|[[:^xdigit:]]|[[:xdigit:]]{1,6}[\\\\\\\\s\\\\\\\\R])))*\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},{\\\"match\\\":\\\"\\\\\\\\b(accent-height|align-content|align-items|align-self|alignment-baseline|all|animation-timing-function|animation-range-start|animation-range-end|animation-range|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation-composition|animation|appearance|ascent|aspect-ratio|azimuth|backface-visibility|background-size|background-repeat-y|background-repeat-x|background-repeat|background-position-y|background-position-x|background-position|background-origin|background-image|background-color|background-clip|background-blend-mode|background-attachment|background|baseline-shift|begin|bias|blend-mode|border-top-left-radius|border-top-right-radius|border-bottom-left-radius|border-bottom-right-radius|border-end-end-radius|border-end-start-radius|border-start-end-radius|border-start-start-radius|border-block-start-color|border-block-start-style|border-block-start-width|border-block-start|border-block-end-color|border-block-end-style|border-block-end-width|border-block-end|border-block-color|border-block-style|border-block-width|border-block|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-inline-start|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-end|border-inline-color|border-inline-style|border-inline-width|border-inline|border-top-color|border-top-style|border-top-width|border-top|border-right-color|border-right-style|border-right-width|border-right|border-bottom-color|border-bottom-style|border-bottom-width|border-bottom|border-left-color|border-left-style|border-left-width|border-left|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-image|border-color|border-style|border-width|border-radius|border-collapse|border-spacing|border|bottom|box-(align|decoration-break|direction|flex|ordinal-group|orient|pack|shadow|sizing)|break-(after|before|inside)|caption-side|clear|clip-path|clip-rule|clip|color(-(interpolation(-filters)?|profile|rendering))?|columns|column-(break-before|count|fill|gap|(rule(-(color|style|width))?)|span|width)|container-name|container-type|container|contain-intrinsic-block-size|contain-intrinsic-inline-size|contain-intrinsic-height|contain-intrinsic-size|contain-intrinsic-width|contain|content|counter-(increment|reset)|cursor|[cdf][xy]|direction|display|divisor|dominant-baseline|dur|elevation|empty-cells|enable-background|end|fallback|fill(-(opacity|rule))?|filter|flex(-(align|basis|direction|flow|grow|item-align|line-pack|negative|order|pack|positive|preferred-size|shrink|wrap))?|float|flood-(color|opacity)|font-display|font-family|font-feature-settings|font-kerning|font-language-override|font-size(-adjust)?|font-smoothing|font-stretch|font-style|font-synthesis|font-variant(-(alternates|caps|east-asian|ligatures|numeric|position))?|font-weight|font|fr|((column|row)-)?gap|glyph-orientation-(horizontal|vertical)|grid-(area|gap)|grid-auto-(columns|flow|rows)|grid-(column|row)(-(end|gap|start))?|grid-template(-(areas|columns|rows))?|grid|height|hyphens|image-(orientation|rendering|resolution)|inset(-(block|inline))?(-(start|end))?|isolation|justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-(box-contain|break|clamp|height)|list-style(-(image|position|type))?|(margin|padding)(-(bottom|left|right|top)|(-(block|inline)?(-(end|start))?))?|marker(-(end|mid|start))?|mask(-(clip||composite|image|origin|position|repeat|size|type))?|(max|min)-(height|width)|mix-blend-mode|nbsp-mode|negative|object-(fit|position)|opacity|operator|order|orphans|outline(-(color|offset|style|width))?|overflow(-((inline|block)|scrolling|wrap|x|y))?|overscroll-behavior(-block|-(inline|x|y))?|pad(ding(-(bottom|left|right|top))?)?|page(-break-(after|before|inside))?|paint-order|pause(-(after|before))?|perspective(-origin(-(x|y))?)?|pitch(-range)?|place-content|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|scale|scroll-behavior|shape-(image-threshold|margin|outside|rendering)|size|speak(-as)?|src|stop-(color|opacity)|stroke(-(dash(array|offset)|line(cap|join)|miterlimit|opacity|width))?|suffix|symbols|system|tab-size|table-layout|tap-highlight-color|text-align(-last)?|text-decoration(-(color|line|style))?|text-emphasis(-(color|position|style))?|text-(anchor|fill-color|height|indent|justify|orientation|overflow|rendering|size-adjust|shadow|transform|underline-position|wrap)|top|touch-action|transform(-origin(-(x|y))?)|transform(-style)?|transition(-(delay|duration|property|timing-function))?|translate|unicode-(bidi|range)|user-(drag|select)|vertical-align|visibility|white-space(-collapse)?|widows|width|will-change|word-(break|spacing|wrap)|writing-mode|z-index|zoom)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.property-name.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(((contain-intrinsic|max|min)-)?(block|inline)?-size)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.property-name.less\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b((?:(?:\\\\\\\\+_?)?):)([\\\\\\\\s\\\\\\\\t]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.less\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.property-value.less\\\"}},\\\"contentName\\\":\\\"meta.property-value.less\\\",\\\"end\\\":\\\"\\\\\\\\s*(;)|(?=[})])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.terminator.rule.less\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#property-values\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"scroll-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(scroll)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.scroll.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"root|nearest|self\\\",\\\"name\\\":\\\"support.constant.scroller.less\\\"},{\\\"match\\\":\\\"block|inline|x|y\\\",\\\"name\\\":\\\"support.constant.axis.less\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]},\\\"selector\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=[>~+/\\\\\\\\.*#a-zA-Z\\\\\\\\[&]|(\\\\\\\\:{1,2}[^\\\\\\\\s])|@\\\\\\\\{)\\\",\\\"contentName\\\":\\\"meta.selector.less\\\",\\\"end\\\":\\\"(?=@(?!\\\\\\\\{)|[{;])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-line\\\"},{\\\"include\\\":\\\"#selectors\\\"},{\\\"include\\\":\\\"#less-namespace-accessors\\\"},{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#important\\\"}]}]},\\\"selectors\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b([a-z](?:(?:[-_a-z0-9\\\\\\\\x{00B7}]|\\\\\\\\\\\\\\\\\\\\\\\\.|[[\\\\\\\\x{00C0}-\\\\\\\\x{00D6}][\\\\\\\\x{00D8}-\\\\\\\\x{00F6}][\\\\\\\\x{00F8}-\\\\\\\\x{02FF}][\\\\\\\\x{0300}-\\\\\\\\x{037D}][\\\\\\\\x{037F}-\\\\\\\\x{1FFF}][\\\\\\\\x{200C}-\\\\\\\\x{200D}][\\\\\\\\x{203F}-\\\\\\\\x{2040}][\\\\\\\\x{2070}-\\\\\\\\x{218F}][\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}][\\\\\\\\x{3001}-\\\\\\\\x{D7FF}][\\\\\\\\x{F900}-\\\\\\\\x{FDCF}][\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}][\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}]]))*-(?:(?:[-_a-z0-9\\\\\\\\x{00B7}]|\\\\\\\\\\\\\\\\\\\\\\\\.|[[\\\\\\\\x{00C0}-\\\\\\\\x{00D6}][\\\\\\\\x{00D8}-\\\\\\\\x{00F6}][\\\\\\\\x{00F8}-\\\\\\\\x{02FF}][\\\\\\\\x{0300}-\\\\\\\\x{037D}][\\\\\\\\x{037F}-\\\\\\\\x{1FFF}][\\\\\\\\x{200C}-\\\\\\\\x{200D}][\\\\\\\\x{203F}-\\\\\\\\x{2040}][\\\\\\\\x{2070}-\\\\\\\\x{218F}][\\\\\\\\x{2C00}-\\\\\\\\x{2FEF}][\\\\\\\\x{3001}-\\\\\\\\x{D7FF}][\\\\\\\\x{F900}-\\\\\\\\x{FDCF}][\\\\\\\\x{FDF0}-\\\\\\\\x{FFFD}][\\\\\\\\x{10000}-\\\\\\\\x{EFFFF}]]))*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.tag.custom.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|big|blockquote|body|br|button|canvas|caption|circle|cite|clipPath|code|col|colgroup|content|data|dataList|dd|defs|del|details|dfn|dialog|dir|div|dl|dt|element|ellipse|em|embed|eventsource|fieldset|figcaption|figure|filter|footer|foreignObject|form|frame|frameset|g|glyph|glyphRef|h1|h2|h3|h4|h5|h6|head|header|hgroup|hr|html|i|iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|line|linearGradient|link|main|map|mark|marker|mask|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|path|pattern|picture|polygon|polyline|pre|progress|q|radialGradient|rect|rp|ruby|rt|rtc|s|samp|script|section|select|shadow|small|source|span|stop|strike|strong|style|sub|summary|sup|svg|switch|symbol|table|tbody|td|template|textarea|textPath|tfoot|th|thead|time|title|tr|track|tref|tspan|tt|u|ul|use|var|video|wbr|xmp)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.tag.less\\\"},{\\\"begin\\\":\\\"(\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"(?![-\\\\\\\\w]|[^\\\\\\\\x{00}-\\\\\\\\x{9f}]|\\\\\\\\\\\\\\\\([A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9])|(\\\\\\\\@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.class.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"end\\\":\\\"(?![-\\\\\\\\w]|[^\\\\\\\\x{00}-\\\\\\\\x{9f}]|\\\\\\\\\\\\\\\\([A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9])|(\\\\\\\\@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.id.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"}]},{\\\"begin\\\":\\\"(&)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}},\\\"contentName\\\":\\\"entity.other.attribute-name.parent.less\\\",\\\"end\\\":\\\"(?![-\\\\\\\\w]|[^\\\\\\\\x{00}-\\\\\\\\x{9f}]|\\\\\\\\\\\\\\\\([A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9])|(\\\\\\\\@(?=\\\\\\\\{)))\\\",\\\"name\\\":\\\"entity.other.attribute-name.parent.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#selectors\\\"}]},{\\\"include\\\":\\\"#pseudo-selectors\\\"},{\\\"include\\\":\\\"#less-extend\\\"},{\\\"match\\\":\\\"(?!\\\\\\\\+_?:)(?:>{1,3}|[~+])(?![>~+;}])\\\",\\\"name\\\":\\\"punctuation.separator.combinator.less\\\"},{\\\"match\\\":\\\"((?:>{1,3}|[~+])){2,}\\\",\\\"name\\\":\\\"invalid.illegal.combinator.less\\\"},{\\\"match\\\":\\\"\\\\\\\\/deep\\\\\\\\/\\\",\\\"name\\\":\\\"invalid.illegal.combinator.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.less\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.less\\\"}},\\\"name\\\":\\\"meta.attribute-selector.less\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"include\\\":\\\"#qualified-name\\\"},{\\\"match\\\":\\\"(-?(?:[[_a-zA-Z][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))(?:[[-\\\\\\\\w][^\\\\\\\\x{00}-\\\\\\\\x{7F}]]|(?:\\\\\\\\\\\\\\\\\\\\\\\\h{1,6}[\\\\\\\\s\\\\\\\\t\\\\\\\\n\\\\\\\\f]?|\\\\\\\\\\\\\\\\[^\\\\\\\\n\\\\\\\\f\\\\\\\\h]))*)\\\",\\\"name\\\":\\\"entity.other.attribute-name.less\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*([~*|^$]?=)\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.attribute-selector.less\\\"}},\\\"end\\\":\\\"(?=(\\\\\\\\s|\\\\\\\\]))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"match\\\":\\\"[^\\\\\\\\s\\\\\\\\]\\\\\\\\['\\\\\\\"]\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.less\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\s+([iI]))?\\\"},{\\\"match\\\":\\\"\\\\\\\\]\\\",\\\"name\\\":\\\"punctuation.definition.entity.less\\\"}]}]},{\\\"include\\\":\\\"#arbitrary-repetition\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"entity.name.tag.wildcard.less\\\"}]},\\\"shape-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(rect)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bauto\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(inset)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bround\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(circle|ellipse)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bat\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.less\\\"},{\\\"match\\\":\\\"\\\\\\\\b(top|right|bottom|left|center|closest-side|farthest-side)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(polygon)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.shape.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(nonzero|evenodd)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#percentage-type\\\"}]}]}]},\\\"steps-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(steps)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.timing.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"contentName\\\":\\\"meta.group.less\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"jump-start|jump-end|jump-none|jump-both|start|end\\\",\\\"name\\\":\\\"support.constant.step-position.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#integer-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"}]},\\\"string-content\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variable-interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n\\\",\\\"name\\\":\\\"constant.character.escape.newline.less\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\h{1,6}|.)\\\",\\\"name\\\":\\\"constant.character.escape.less\\\"}]},\\\"style-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(style)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.style.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#rule-list-body\\\"}]}]},\\\"symbols-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(symbols)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.counter.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(cyclic|numeric|alphabetic|symbolic|fixed)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.symbol-type.less\\\"},{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#image-type\\\"}]}]},\\\"time-type\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.less\\\"}},\\\"match\\\":\\\"(?i:[-+]?(?:(?:\\\\\\\\d*\\\\\\\\.\\\\\\\\d+(?:[eE](?:[-+]?\\\\\\\\d+))*)|(?:[-+]?\\\\\\\\d+))(s|ms))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.less\\\"},\\\"transform-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(matrix3d|scale3d|matrix|scale)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translate(3d)?)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translate[XY])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(rotate[XYZ]?|skew[XY])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(skew)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(translateZ|perspective)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(rotate3d)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#angle-type\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},{\\\"begin\\\":\\\"\\\\\\\\b(scale[XYZ])(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.function.transform.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#number-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]}]},\\\"unicode-range\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.constant.unicode-range.prefix.less\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.codepoint-range.less\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.section.range.less\\\"}},\\\"match\\\":\\\"(?i)(u\\\\\\\\+)([0-9a-f?]{1,6}(?:(-)[0-9a-f]{1,6})?)\\\",\\\"name\\\":\\\"support.unicode-range.less\\\"},\\\"unquoted-string\\\":{\\\"match\\\":\\\"[^\\\\\\\\s'\\\\\\\"]\\\",\\\"name\\\":\\\"string.unquoted.less\\\"},\\\"url-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(url)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.url.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#literal-string\\\"},{\\\"include\\\":\\\"#unquoted-string\\\"},{\\\"include\\\":\\\"#var-function\\\"}]}]},\\\"value-separator\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.less\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(/)\\\\\\\\s*\\\"},\\\"var-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(var)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.var.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comma-delimiter\\\"},{\\\"include\\\":\\\"#custom-property-name\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#property-values\\\"}]}]},\\\"view-function\\\":{\\\"begin\\\":\\\"\\\\\\\\b(view)(?=\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.view.less\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.end.less\\\"}},\\\"name\\\":\\\"meta.function-call.less\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.group.begin.less\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"block|inline|x|y|auto\\\",\\\"name\\\":\\\"support.constant.property-value.less\\\"},{\\\"include\\\":\\\"#percentage-type\\\"},{\\\"include\\\":\\\"#length-type\\\"},{\\\"include\\\":\\\"#less-variables\\\"},{\\\"include\\\":\\\"#var-function\\\"},{\\\"include\\\":\\\"#calc-function\\\"},{\\\"include\\\":\\\"#arbitrary-repetition\\\"}]}]}},\\\"scopeName\\\":\\\"source.css.less\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/less.mjs\n"));

/***/ })

}]);