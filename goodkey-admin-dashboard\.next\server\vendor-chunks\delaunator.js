"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/delaunator";
exports.ids = ["vendor-chunks/delaunator"];
exports.modules = {

/***/ "(ssr)/./node_modules/delaunator/index.js":
/*!******************************************!*\
  !*** ./node_modules/delaunator/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunator)\n/* harmony export */ });\n/* harmony import */ var robust_predicates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! robust-predicates */ \"(ssr)/./node_modules/robust-predicates/index.js\");\n\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\n\n\nclass Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if ((0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/delaunator/index.js\n");

/***/ })

};
;