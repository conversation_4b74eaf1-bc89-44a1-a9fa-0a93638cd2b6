import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ServiceTableAccordion from './components/service_table_accordion';
import OfferingRateQuery from '@/services/queries/OfferingRateQuery';

export const metadata: Metadata = {
  title: 'Goodkey | Service Default Price',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  // await queryClient.prefetchQuery({
  //   queryKey: GroupTypeQuery.tags,
  //   queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(1),
  // });

  await queryClient.prefetchQuery({
    queryKey: ['service-price-all'],
    queryFn: () => OfferingRateQuery.getAll(2),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Inventory & Price Management',
          link: '/dashboard/setup',
        },
        {
          title: 'Service Default Price Management',
          link: '/dashboard/setup/products-services/inventory/service-price',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ServiceTableAccordion />
      </HydrationBoundary>
    </AppLayout>
  );
}
