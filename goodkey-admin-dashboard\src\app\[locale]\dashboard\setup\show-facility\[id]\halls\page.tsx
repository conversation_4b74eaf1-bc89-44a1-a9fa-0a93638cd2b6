import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import ShowHallQuery from '@/services/queries/ShowHallQuery';
import ShowHallTable from '../components/show_hall_table';

export default async function ShowHallPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['ShowHall', { locationId: Number(id) }],
        queryFn: () => ShowHallQuery.getByLocation(Number(id)),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          List of Halls
        </h2>
        <HydrationBoundary state={dehydrate(client)}>
          <ShowHallTable locationId={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/show-facility/add');
  }
}
