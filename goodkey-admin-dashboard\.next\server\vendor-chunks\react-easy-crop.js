"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-easy-crop";
exports.ids = ["vendor-chunks/react-easy-crop"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-easy-crop/index.module.js":
/*!******************************************************!*\
  !*** ./node_modules/react-easy-crop/index.module.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cropper),\n/* harmony export */   getInitialCropFromCroppedAreaPercentages: () => (/* binding */ getInitialCropFromCroppedAreaPercentages),\n/* harmony export */   getInitialCropFromCroppedAreaPixels: () => (/* binding */ getInitialCropFromCroppedAreaPixels)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var normalize_wheel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! normalize-wheel */ \"(ssr)/./node_modules/normalize-wheel/index.js\");\n/* harmony import */ var normalize_wheel__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(normalize_wheel__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\n/**\r\n * Compute the dimension of the crop area based on media size,\r\n * aspect ratio and optionally rotation\r\n */\nfunction getCropSize(mediaWidth, mediaHeight, containerWidth, containerHeight, aspect, rotation) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var _a = rotateSize(mediaWidth, mediaHeight, rotation),\n    width = _a.width,\n    height = _a.height;\n  var fittingWidth = Math.min(width, containerWidth);\n  var fittingHeight = Math.min(height, containerHeight);\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight\n    };\n  }\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect\n  };\n}\n/**\r\n * Compute media zoom.\r\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\r\n */\nfunction getMediaZoom(mediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height ? mediaSize.width / mediaSize.naturalWidth : mediaSize.height / mediaSize.naturalHeight;\n}\n/**\r\n * Ensure a new media position stays in the crop area.\r\n */\nfunction restrictPosition(position, mediaSize, cropSize, zoom, rotation) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var _a = rotateSize(mediaSize.width, mediaSize.height, rotation),\n    width = _a.width,\n    height = _a.height;\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom)\n  };\n}\nfunction restrictPositionCoord(position, mediaSize, cropSize, zoom) {\n  var maxPosition = mediaSize * zoom / 2 - cropSize / 2;\n  return clamp(position, -maxPosition, maxPosition);\n}\nfunction getDistanceBetweenPoints(pointA, pointB) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2));\n}\nfunction getRotationBetweenPoints(pointA, pointB) {\n  return Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180 / Math.PI;\n}\n/**\r\n * Compute the output cropped area of the media in percentages and pixels.\r\n * x/y are the top-left coordinates on the src media\r\n */\nfunction computeCroppedArea(crop, mediaSize, cropSize, aspect, zoom, rotation, restrictPosition) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  if (restrictPosition === void 0) {\n    restrictPosition = true;\n  }\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  var limitAreaFn = restrictPosition ? limitArea : noOp;\n  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n  // calculate the crop area in percentages\n  // in the rotated space\n  var croppedAreaPercentages = {\n    x: limitAreaFn(100, ((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width * 100),\n    y: limitAreaFn(100, ((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) / mediaBBoxSize.height * 100),\n    width: limitAreaFn(100, cropSize.width / mediaBBoxSize.width * 100 / zoom),\n    height: limitAreaFn(100, cropSize.height / mediaBBoxSize.height * 100 / zoom)\n  };\n  // we compute the pixels size naively\n  var widthInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.width, croppedAreaPercentages.width * mediaNaturalBBoxSize.width / 100));\n  var heightInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.height, croppedAreaPercentages.height * mediaNaturalBBoxSize.height / 100));\n  var isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect;\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  var sizePixels = isImgWiderThanHigh ? {\n    width: Math.round(heightInPixels * aspect),\n    height: heightInPixels\n  } : {\n    width: widthInPixels,\n    height: Math.round(widthInPixels / aspect)\n  };\n  var croppedAreaPixels = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, sizePixels), {\n    x: Math.round(limitAreaFn(mediaNaturalBBoxSize.width - sizePixels.width, croppedAreaPercentages.x * mediaNaturalBBoxSize.width / 100)),\n    y: Math.round(limitAreaFn(mediaNaturalBBoxSize.height - sizePixels.height, croppedAreaPercentages.y * mediaNaturalBBoxSize.height / 100))\n  });\n  return {\n    croppedAreaPercentages: croppedAreaPercentages,\n    croppedAreaPixels: croppedAreaPixels\n  };\n}\n/**\r\n * Ensure the returned value is between 0 and max\r\n */\nfunction limitArea(max, value) {\n  return Math.min(max, Math.max(0, value));\n}\nfunction noOp(_max, value) {\n  return value;\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPercentages.\r\n */\nfunction getInitialCropFromCroppedAreaPercentages(croppedAreaPercentages, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n  // This is the inverse process of computeCroppedArea\n  var zoom = clamp(cropSize.width / mediaBBoxSize.width * (100 / croppedAreaPercentages.width), minZoom, maxZoom);\n  var crop = {\n    x: zoom * mediaBBoxSize.width / 2 - cropSize.width / 2 - mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y: zoom * mediaBBoxSize.height / 2 - cropSize.height / 2 - mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100)\n  };\n  return {\n    crop: crop,\n    zoom: zoom\n  };\n}\n/**\r\n * Compute zoom from the croppedAreaPixels\r\n */\nfunction getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize) {\n  var mediaZoom = getMediaZoom(mediaSize);\n  return cropSize.height > cropSize.width ? cropSize.height / (croppedAreaPixels.height * mediaZoom) : cropSize.width / (croppedAreaPixels.width * mediaZoom);\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPixels\r\n */\nfunction getInitialCropFromCroppedAreaPixels(croppedAreaPixels, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n  var zoom = clamp(getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize), minZoom, maxZoom);\n  var cropZoom = cropSize.height > cropSize.width ? cropSize.height / croppedAreaPixels.height : cropSize.width / croppedAreaPixels.width;\n  var crop = {\n    x: ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y: ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) * cropZoom\n  };\n  return {\n    crop: crop,\n    zoom: zoom\n  };\n}\n/**\r\n * Return the point that is the center of point a and b\r\n */\nfunction getCenter(a, b) {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2\n  };\n}\nfunction getRadianAngle(degreeValue) {\n  return degreeValue * Math.PI / 180;\n}\n/**\r\n * Returns the new bounding area of a rotated rectangle.\r\n */\nfunction rotateSize(width, height, rotation) {\n  var rotRad = getRadianAngle(rotation);\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height)\n  };\n}\n/**\r\n * Clamp value between min and max\r\n */\nfunction clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\n/**\r\n * Combine multiple class names into a single string.\r\n */\nfunction classNames() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return args.filter(function (value) {\n    if (typeof value === 'string' && value.length > 0) {\n      return true;\n    }\n    return false;\n  }).join(' ').trim();\n}\n\nvar css_248z = \".reactEasyCrop_Container {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  overflow: hidden;\\n  user-select: none;\\n  touch-action: none;\\n  cursor: move;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.reactEasyCrop_Image,\\n.reactEasyCrop_Video {\\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\\n}\\n\\n.reactEasyCrop_Contain {\\n  max-width: 100%;\\n  max-height: 100%;\\n  margin: auto;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n.reactEasyCrop_Cover_Horizontal {\\n  width: 100%;\\n  height: auto;\\n}\\n.reactEasyCrop_Cover_Vertical {\\n  width: auto;\\n  height: 100%;\\n}\\n\\n.reactEasyCrop_CropArea {\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  box-sizing: border-box;\\n  box-shadow: 0 0 0 9999em;\\n  color: rgba(0, 0, 0, 0.5);\\n  overflow: hidden;\\n}\\n\\n.reactEasyCrop_CropAreaRound {\\n  border-radius: 50%;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::before {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 0;\\n  bottom: 0;\\n  left: 33.33%;\\n  right: 33.33%;\\n  border-top: 0;\\n  border-bottom: 0;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::after {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 33.33%;\\n  bottom: 33.33%;\\n  left: 0;\\n  right: 0;\\n  border-left: 0;\\n  border-right: 0;\\n}\\n\";\n\nvar MIN_ZOOM = 1;\nvar MAX_ZOOM = 3;\nvar KEYBOARD_STEP = 1;\nvar Cropper = /** @class */function (_super) {\n  (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__extends)(Cropper, _super);\n  function Cropper() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.cropperRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n    _this.imageRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n    _this.videoRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n    _this.containerPosition = {\n      x: 0,\n      y: 0\n    };\n    _this.containerRef = null;\n    _this.styleRef = null;\n    _this.containerRect = null;\n    _this.mediaSize = {\n      width: 0,\n      height: 0,\n      naturalWidth: 0,\n      naturalHeight: 0\n    };\n    _this.dragStartPosition = {\n      x: 0,\n      y: 0\n    };\n    _this.dragStartCrop = {\n      x: 0,\n      y: 0\n    };\n    _this.gestureZoomStart = 0;\n    _this.gestureRotationStart = 0;\n    _this.isTouching = false;\n    _this.lastPinchDistance = 0;\n    _this.lastPinchRotation = 0;\n    _this.rafDragTimeout = null;\n    _this.rafPinchTimeout = null;\n    _this.wheelTimer = null;\n    _this.currentDoc = typeof document !== 'undefined' ? document : null;\n    _this.currentWindow = typeof window !== 'undefined' ? window : null;\n    _this.resizeObserver = null;\n    _this.state = {\n      cropSize: null,\n      hasWheelJustStarted: false,\n      mediaObjectFit: undefined\n    };\n    _this.initResizeObserver = function () {\n      if (typeof window.ResizeObserver === 'undefined' || !_this.containerRef) {\n        return;\n      }\n      var isFirstResize = true;\n      _this.resizeObserver = new window.ResizeObserver(function (entries) {\n        if (isFirstResize) {\n          isFirstResize = false; // observe() is called on mount, we don't want to trigger a recompute on mount\n          return;\n        }\n        _this.computeSizes();\n      });\n      _this.resizeObserver.observe(_this.containerRef);\n    };\n    // this is to prevent Safari on iOS >= 10 to zoom the page\n    _this.preventZoomSafari = function (e) {\n      return e.preventDefault();\n    };\n    _this.cleanEvents = function () {\n      if (!_this.currentDoc) return;\n      _this.currentDoc.removeEventListener('mousemove', _this.onMouseMove);\n      _this.currentDoc.removeEventListener('mouseup', _this.onDragStopped);\n      _this.currentDoc.removeEventListener('touchmove', _this.onTouchMove);\n      _this.currentDoc.removeEventListener('touchend', _this.onDragStopped);\n      _this.currentDoc.removeEventListener('gesturemove', _this.onGestureMove);\n      _this.currentDoc.removeEventListener('gestureend', _this.onGestureEnd);\n      _this.currentDoc.removeEventListener('scroll', _this.onScroll);\n    };\n    _this.clearScrollEvent = function () {\n      if (_this.containerRef) _this.containerRef.removeEventListener('wheel', _this.onWheel);\n      if (_this.wheelTimer) {\n        clearTimeout(_this.wheelTimer);\n      }\n    };\n    _this.onMediaLoad = function () {\n      var cropSize = _this.computeSizes();\n      if (cropSize) {\n        _this.emitCropData();\n        _this.setInitialCrop(cropSize);\n      }\n      if (_this.props.onMediaLoaded) {\n        _this.props.onMediaLoaded(_this.mediaSize);\n      }\n    };\n    _this.setInitialCrop = function (cropSize) {\n      if (_this.props.initialCroppedAreaPercentages) {\n        var _a = getInitialCropFromCroppedAreaPercentages(_this.props.initialCroppedAreaPercentages, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),\n          crop = _a.crop,\n          zoom = _a.zoom;\n        _this.props.onCropChange(crop);\n        _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n      } else if (_this.props.initialCroppedAreaPixels) {\n        var _b = getInitialCropFromCroppedAreaPixels(_this.props.initialCroppedAreaPixels, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),\n          crop = _b.crop,\n          zoom = _b.zoom;\n        _this.props.onCropChange(crop);\n        _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n      }\n    };\n    _this.computeSizes = function () {\n      var _a, _b, _c, _d, _e, _f;\n      var mediaRef = _this.imageRef.current || _this.videoRef.current;\n      if (mediaRef && _this.containerRef) {\n        _this.containerRect = _this.containerRef.getBoundingClientRect();\n        _this.saveContainerPosition();\n        var containerAspect = _this.containerRect.width / _this.containerRect.height;\n        var naturalWidth = ((_a = _this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = _this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n        var naturalHeight = ((_c = _this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = _this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n        var isMediaScaledDown = mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight;\n        var mediaAspect = naturalWidth / naturalHeight;\n        // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n        // as the values they report are rounded. That will result in precision losses\n        // when calculating zoom. We use the fact that the media is positionned relative\n        // to the container. That allows us to use the container's dimensions\n        // and natural aspect ratio of the media to calculate accurate media size.\n        // However, for this to work, the container should not be rotated\n        var renderedMediaSize = void 0;\n        if (isMediaScaledDown) {\n          switch (_this.state.mediaObjectFit) {\n            default:\n            case 'contain':\n              renderedMediaSize = containerAspect > mediaAspect ? {\n                width: _this.containerRect.height * mediaAspect,\n                height: _this.containerRect.height\n              } : {\n                width: _this.containerRect.width,\n                height: _this.containerRect.width / mediaAspect\n              };\n              break;\n            case 'horizontal-cover':\n              renderedMediaSize = {\n                width: _this.containerRect.width,\n                height: _this.containerRect.width / mediaAspect\n              };\n              break;\n            case 'vertical-cover':\n              renderedMediaSize = {\n                width: _this.containerRect.height * mediaAspect,\n                height: _this.containerRect.height\n              };\n              break;\n          }\n        } else {\n          renderedMediaSize = {\n            width: mediaRef.offsetWidth,\n            height: mediaRef.offsetHeight\n          };\n        }\n        _this.mediaSize = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, renderedMediaSize), {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight\n        });\n        // set media size in the parent\n        if (_this.props.setMediaSize) {\n          _this.props.setMediaSize(_this.mediaSize);\n        }\n        var cropSize = _this.props.cropSize ? _this.props.cropSize : getCropSize(_this.mediaSize.width, _this.mediaSize.height, _this.containerRect.width, _this.containerRect.height, _this.props.aspect, _this.props.rotation);\n        if (((_e = _this.state.cropSize) === null || _e === void 0 ? void 0 : _e.height) !== cropSize.height || ((_f = _this.state.cropSize) === null || _f === void 0 ? void 0 : _f.width) !== cropSize.width) {\n          _this.props.onCropSizeChange && _this.props.onCropSizeChange(cropSize);\n        }\n        _this.setState({\n          cropSize: cropSize\n        }, _this.recomputeCropPosition);\n        // pass crop size to parent\n        if (_this.props.setCropSize) {\n          _this.props.setCropSize(cropSize);\n        }\n        return cropSize;\n      }\n    };\n    _this.saveContainerPosition = function () {\n      if (_this.containerRef) {\n        var bounds = _this.containerRef.getBoundingClientRect();\n        _this.containerPosition = {\n          x: bounds.left,\n          y: bounds.top\n        };\n      }\n    };\n    _this.onMouseDown = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.currentDoc.addEventListener('mousemove', _this.onMouseMove);\n      _this.currentDoc.addEventListener('mouseup', _this.onDragStopped);\n      _this.saveContainerPosition();\n      _this.onDragStart(Cropper.getMousePoint(e));\n    };\n    _this.onMouseMove = function (e) {\n      return _this.onDrag(Cropper.getMousePoint(e));\n    };\n    _this.onScroll = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.saveContainerPosition();\n    };\n    _this.onTouchStart = function (e) {\n      if (!_this.currentDoc) return;\n      _this.isTouching = true;\n      if (_this.props.onTouchRequest && !_this.props.onTouchRequest(e)) {\n        return;\n      }\n      _this.currentDoc.addEventListener('touchmove', _this.onTouchMove, {\n        passive: false\n      }); // iOS 11 now defaults to passive: true\n      _this.currentDoc.addEventListener('touchend', _this.onDragStopped);\n      _this.saveContainerPosition();\n      if (e.touches.length === 2) {\n        _this.onPinchStart(e);\n      } else if (e.touches.length === 1) {\n        _this.onDragStart(Cropper.getTouchPoint(e.touches[0]));\n      }\n    };\n    _this.onTouchMove = function (e) {\n      // Prevent whole page from scrolling on iOS.\n      e.preventDefault();\n      if (e.touches.length === 2) {\n        _this.onPinchMove(e);\n      } else if (e.touches.length === 1) {\n        _this.onDrag(Cropper.getTouchPoint(e.touches[0]));\n      }\n    };\n    _this.onGestureStart = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.currentDoc.addEventListener('gesturechange', _this.onGestureMove);\n      _this.currentDoc.addEventListener('gestureend', _this.onGestureEnd);\n      _this.gestureZoomStart = _this.props.zoom;\n      _this.gestureRotationStart = _this.props.rotation;\n    };\n    _this.onGestureMove = function (e) {\n      e.preventDefault();\n      if (_this.isTouching) {\n        // this is to avoid conflict between gesture and touch events\n        return;\n      }\n      var point = Cropper.getMousePoint(e);\n      var newZoom = _this.gestureZoomStart - 1 + e.scale;\n      _this.setNewZoom(newZoom, point, {\n        shouldUpdatePosition: true\n      });\n      if (_this.props.onRotationChange) {\n        var newRotation = _this.gestureRotationStart + e.rotation;\n        _this.props.onRotationChange(newRotation);\n      }\n    };\n    _this.onGestureEnd = function (e) {\n      _this.cleanEvents();\n    };\n    _this.onDragStart = function (_a) {\n      var _b, _c;\n      var x = _a.x,\n        y = _a.y;\n      _this.dragStartPosition = {\n        x: x,\n        y: y\n      };\n      _this.dragStartCrop = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, _this.props.crop);\n      (_c = (_b = _this.props).onInteractionStart) === null || _c === void 0 ? void 0 : _c.call(_b);\n    };\n    _this.onDrag = function (_a) {\n      var x = _a.x,\n        y = _a.y;\n      if (!_this.currentWindow) return;\n      if (_this.rafDragTimeout) _this.currentWindow.cancelAnimationFrame(_this.rafDragTimeout);\n      _this.rafDragTimeout = _this.currentWindow.requestAnimationFrame(function () {\n        if (!_this.state.cropSize) return;\n        if (x === undefined || y === undefined) return;\n        var offsetX = x - _this.dragStartPosition.x;\n        var offsetY = y - _this.dragStartPosition.y;\n        var requestedPosition = {\n          x: _this.dragStartCrop.x + offsetX,\n          y: _this.dragStartCrop.y + offsetY\n        };\n        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : requestedPosition;\n        _this.props.onCropChange(newPosition);\n      });\n    };\n    _this.onDragStopped = function () {\n      var _a, _b;\n      _this.isTouching = false;\n      _this.cleanEvents();\n      _this.emitCropData();\n      (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    _this.onWheel = function (e) {\n      if (!_this.currentWindow) return;\n      if (_this.props.onWheelRequest && !_this.props.onWheelRequest(e)) {\n        return;\n      }\n      e.preventDefault();\n      var point = Cropper.getMousePoint(e);\n      var pixelY = normalize_wheel__WEBPACK_IMPORTED_MODULE_1___default()(e).pixelY;\n      var newZoom = _this.props.zoom - pixelY * _this.props.zoomSpeed / 200;\n      _this.setNewZoom(newZoom, point, {\n        shouldUpdatePosition: true\n      });\n      if (!_this.state.hasWheelJustStarted) {\n        _this.setState({\n          hasWheelJustStarted: true\n        }, function () {\n          var _a, _b;\n          return (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n      }\n      if (_this.wheelTimer) {\n        clearTimeout(_this.wheelTimer);\n      }\n      _this.wheelTimer = _this.currentWindow.setTimeout(function () {\n        return _this.setState({\n          hasWheelJustStarted: false\n        }, function () {\n          var _a, _b;\n          return (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n      }, 250);\n    };\n    _this.getPointOnContainer = function (_a, containerTopLeft) {\n      var x = _a.x,\n        y = _a.y;\n      if (!_this.containerRect) {\n        throw new Error('The Cropper is not mounted');\n      }\n      return {\n        x: _this.containerRect.width / 2 - (x - containerTopLeft.x),\n        y: _this.containerRect.height / 2 - (y - containerTopLeft.y)\n      };\n    };\n    _this.getPointOnMedia = function (_a) {\n      var x = _a.x,\n        y = _a.y;\n      var _b = _this.props,\n        crop = _b.crop,\n        zoom = _b.zoom;\n      return {\n        x: (x + crop.x) / zoom,\n        y: (y + crop.y) / zoom\n      };\n    };\n    _this.setNewZoom = function (zoom, point, _a) {\n      var _b = _a === void 0 ? {} : _a,\n        _c = _b.shouldUpdatePosition,\n        shouldUpdatePosition = _c === void 0 ? true : _c;\n      if (!_this.state.cropSize || !_this.props.onZoomChange) return;\n      var newZoom = clamp(zoom, _this.props.minZoom, _this.props.maxZoom);\n      if (shouldUpdatePosition) {\n        var zoomPoint = _this.getPointOnContainer(point, _this.containerPosition);\n        var zoomTarget = _this.getPointOnMedia(zoomPoint);\n        var requestedPosition = {\n          x: zoomTarget.x * newZoom - zoomPoint.x,\n          y: zoomTarget.y * newZoom - zoomPoint.y\n        };\n        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, newZoom, _this.props.rotation) : requestedPosition;\n        _this.props.onCropChange(newPosition);\n      }\n      _this.props.onZoomChange(newZoom);\n    };\n    _this.getCropData = function () {\n      if (!_this.state.cropSize) {\n        return null;\n      }\n      // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n      var restrictedPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n      return computeCroppedArea(restrictedPosition, _this.mediaSize, _this.state.cropSize, _this.getAspect(), _this.props.zoom, _this.props.rotation, _this.props.restrictPosition);\n    };\n    _this.emitCropData = function () {\n      var cropData = _this.getCropData();\n      if (!cropData) return;\n      var croppedAreaPercentages = cropData.croppedAreaPercentages,\n        croppedAreaPixels = cropData.croppedAreaPixels;\n      if (_this.props.onCropComplete) {\n        _this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels);\n      }\n      if (_this.props.onCropAreaChange) {\n        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n      }\n    };\n    _this.emitCropAreaChange = function () {\n      var cropData = _this.getCropData();\n      if (!cropData) return;\n      var croppedAreaPercentages = cropData.croppedAreaPercentages,\n        croppedAreaPixels = cropData.croppedAreaPixels;\n      if (_this.props.onCropAreaChange) {\n        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n      }\n    };\n    _this.recomputeCropPosition = function () {\n      if (!_this.state.cropSize) return;\n      var newPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n      _this.props.onCropChange(newPosition);\n      _this.emitCropData();\n    };\n    _this.onKeyDown = function (event) {\n      var _a, _b;\n      var _c = _this.props,\n        crop = _c.crop,\n        onCropChange = _c.onCropChange,\n        keyboardStep = _c.keyboardStep,\n        zoom = _c.zoom,\n        rotation = _c.rotation;\n      var step = keyboardStep;\n      if (!_this.state.cropSize) return;\n      // if the shift key is pressed, reduce the step to allow finer control\n      if (event.shiftKey) {\n        step *= 0.2;\n      }\n      var newCrop = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, crop);\n      switch (event.key) {\n        case 'ArrowUp':\n          newCrop.y -= step;\n          event.preventDefault();\n          break;\n        case 'ArrowDown':\n          newCrop.y += step;\n          event.preventDefault();\n          break;\n        case 'ArrowLeft':\n          newCrop.x -= step;\n          event.preventDefault();\n          break;\n        case 'ArrowRight':\n          newCrop.x += step;\n          event.preventDefault();\n          break;\n        default:\n          return;\n      }\n      if (_this.props.restrictPosition) {\n        newCrop = restrictPosition(newCrop, _this.mediaSize, _this.state.cropSize, zoom, rotation);\n      }\n      if (!event.repeat) {\n        (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n      }\n      onCropChange(newCrop);\n    };\n    _this.onKeyUp = function (event) {\n      var _a, _b;\n      switch (event.key) {\n        case 'ArrowUp':\n        case 'ArrowDown':\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          event.preventDefault();\n          break;\n        default:\n          return;\n      }\n      _this.emitCropData();\n      (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    return _this;\n  }\n  Cropper.prototype.componentDidMount = function () {\n    if (!this.currentDoc || !this.currentWindow) return;\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument;\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView;\n      }\n      this.initResizeObserver();\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes);\n      }\n      this.props.zoomWithScroll && this.containerRef.addEventListener('wheel', this.onWheel, {\n        passive: false\n      });\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart);\n    }\n    this.currentDoc.addEventListener('scroll', this.onScroll);\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style');\n      this.styleRef.setAttribute('type', 'text/css');\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce);\n      }\n      this.styleRef.innerHTML = css_248z;\n      this.currentDoc.head.appendChild(this.styleRef);\n    }\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad();\n    }\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef);\n    }\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef);\n    }\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef);\n    }\n  };\n  Cropper.prototype.componentWillUnmount = function () {\n    var _a, _b;\n    if (!this.currentDoc || !this.currentWindow) return;\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes);\n    }\n    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari);\n    }\n    if (this.styleRef) {\n      (_b = this.styleRef.parentNode) === null || _b === void 0 ? void 0 : _b.removeChild(this.styleRef);\n    }\n    this.cleanEvents();\n    this.props.zoomWithScroll && this.clearScrollEvent();\n  };\n  Cropper.prototype.componentDidUpdate = function (prevProps) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes();\n      this.recomputeCropPosition();\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes();\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes();\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition();\n    } else if (((_a = prevProps.cropSize) === null || _a === void 0 ? void 0 : _a.height) !== ((_b = this.props.cropSize) === null || _b === void 0 ? void 0 : _b.height) || ((_c = prevProps.cropSize) === null || _c === void 0 ? void 0 : _c.width) !== ((_d = this.props.cropSize) === null || _d === void 0 ? void 0 : _d.width)) {\n      this.computeSizes();\n    } else if (((_e = prevProps.crop) === null || _e === void 0 ? void 0 : _e.x) !== ((_f = this.props.crop) === null || _f === void 0 ? void 0 : _f.x) || ((_g = prevProps.crop) === null || _g === void 0 ? void 0 : _g.y) !== ((_h = this.props.crop) === null || _h === void 0 ? void 0 : _h.y)) {\n      this.emitCropAreaChange();\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll ? this.containerRef.addEventListener('wheel', this.onWheel, {\n        passive: false\n      }) : this.clearScrollEvent();\n    }\n    if (prevProps.video !== this.props.video) {\n      (_j = this.videoRef.current) === null || _j === void 0 ? void 0 : _j.load();\n    }\n    var objectFit = this.getObjectFit();\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({\n        mediaObjectFit: objectFit\n      }, this.computeSizes);\n    }\n  };\n  Cropper.prototype.getAspect = function () {\n    var _a = this.props,\n      cropSize = _a.cropSize,\n      aspect = _a.aspect;\n    if (cropSize) {\n      return cropSize.width / cropSize.height;\n    }\n    return aspect;\n  };\n  Cropper.prototype.getObjectFit = function () {\n    var _a, _b, _c, _d;\n    if (this.props.objectFit === 'cover') {\n      var mediaRef = this.imageRef.current || this.videoRef.current;\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect();\n        var containerAspect = this.containerRect.width / this.containerRect.height;\n        var naturalWidth = ((_a = this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n        var naturalHeight = ((_c = this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n        var mediaAspect = naturalWidth / naturalHeight;\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover';\n      }\n      return 'horizontal-cover';\n    }\n    return this.props.objectFit;\n  };\n  Cropper.prototype.onPinchStart = function (e) {\n    var pointA = Cropper.getTouchPoint(e.touches[0]);\n    var pointB = Cropper.getTouchPoint(e.touches[1]);\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB);\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB);\n    this.onDragStart(getCenter(pointA, pointB));\n  };\n  Cropper.prototype.onPinchMove = function (e) {\n    var _this = this;\n    if (!this.currentDoc || !this.currentWindow) return;\n    var pointA = Cropper.getTouchPoint(e.touches[0]);\n    var pointB = Cropper.getTouchPoint(e.touches[1]);\n    var center = getCenter(pointA, pointB);\n    this.onDrag(center);\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout);\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(function () {\n      var distance = getDistanceBetweenPoints(pointA, pointB);\n      var newZoom = _this.props.zoom * (distance / _this.lastPinchDistance);\n      _this.setNewZoom(newZoom, center, {\n        shouldUpdatePosition: false\n      });\n      _this.lastPinchDistance = distance;\n      var rotation = getRotationBetweenPoints(pointA, pointB);\n      var newRotation = _this.props.rotation + (rotation - _this.lastPinchRotation);\n      _this.props.onRotationChange && _this.props.onRotationChange(newRotation);\n      _this.lastPinchRotation = rotation;\n    });\n  };\n  Cropper.prototype.render = function () {\n    var _this = this;\n    var _a;\n    var _b = this.props,\n      image = _b.image,\n      video = _b.video,\n      mediaProps = _b.mediaProps,\n      cropperProps = _b.cropperProps,\n      transform = _b.transform,\n      _c = _b.crop,\n      x = _c.x,\n      y = _c.y,\n      rotation = _b.rotation,\n      zoom = _b.zoom,\n      cropShape = _b.cropShape,\n      showGrid = _b.showGrid,\n      _d = _b.style,\n      containerStyle = _d.containerStyle,\n      cropAreaStyle = _d.cropAreaStyle,\n      mediaStyle = _d.mediaStyle,\n      _e = _b.classes,\n      containerClassName = _e.containerClassName,\n      cropAreaClassName = _e.cropAreaClassName,\n      mediaClassName = _e.mediaClassName;\n    var objectFit = (_a = this.state.mediaObjectFit) !== null && _a !== void 0 ? _a : this.getObjectFit();\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      onMouseDown: this.onMouseDown,\n      onTouchStart: this.onTouchStart,\n      ref: function ref(el) {\n        return _this.containerRef = el;\n      },\n      \"data-testid\": \"container\",\n      style: containerStyle,\n      className: classNames('reactEasyCrop_Container', containerClassName)\n    }, image ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n      alt: \"\",\n      className: classNames('reactEasyCrop_Image', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)\n    }, mediaProps, {\n      src: image,\n      ref: this.imageRef,\n      style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, mediaStyle), {\n        transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n      }),\n      onLoad: this.onMediaLoad\n    })) : video && react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"video\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n      autoPlay: true,\n      playsInline: true,\n      loop: true,\n      muted: true,\n      className: classNames('reactEasyCrop_Video', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)\n    }, mediaProps, {\n      ref: this.videoRef,\n      onLoadedMetadata: this.onMediaLoad,\n      style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, mediaStyle), {\n        transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n      }),\n      controls: false\n    }), (Array.isArray(video) ? video : [{\n      src: video\n    }]).map(function (item) {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"source\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n        key: item.src\n      }, item));\n    })), this.state.cropSize && react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({\n      ref: this.cropperRef,\n      style: (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, cropAreaStyle), {\n        width: this.state.cropSize.width,\n        height: this.state.cropSize.height\n      }),\n      tabIndex: 0,\n      onKeyDown: this.onKeyDown,\n      onKeyUp: this.onKeyUp,\n      \"data-testid\": \"cropper\",\n      className: classNames('reactEasyCrop_CropArea', cropShape === 'round' && 'reactEasyCrop_CropAreaRound', showGrid && 'reactEasyCrop_CropAreaGrid', cropAreaClassName)\n    }, cropperProps)));\n  };\n  Cropper.defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect',\n    objectFit: 'contain',\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP\n  };\n  Cropper.getMousePoint = function (e) {\n    return {\n      x: Number(e.clientX),\n      y: Number(e.clientY)\n    };\n  };\n  Cropper.getTouchPoint = function (touch) {\n    return {\n      x: Number(touch.clientX),\n      y: Number(touch.clientY)\n    };\n  };\n  return Cropper;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\n\n//# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-easy-crop/index.module.js\n");

/***/ })

};
;