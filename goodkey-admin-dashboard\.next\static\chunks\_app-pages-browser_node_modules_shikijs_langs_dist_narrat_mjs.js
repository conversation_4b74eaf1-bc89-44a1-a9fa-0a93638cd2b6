"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_narrat_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/narrat.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/narrat.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Narrat Language\\\",\\\"name\\\":\\\"narrat\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expression\\\"}],\\\"repository\\\":{\\\"commands\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(set|var)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.variables.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(talk|think)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.text.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(jump|run|wait|return|save|save_prompt)\\\",\\\"name\\\":\\\"keyword.commands.flow.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(log|clear_dialog)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.helpers.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(set_screen|empty_layer|set_button)\\\",\\\"name\\\":\\\"keyword.commands.screens.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(play|pause|stop)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.audio.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(notify|enable_notifications|disable_notifications)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.notifications.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(set_stat|get_stat_value|add_stat)\\\",\\\"name\\\":\\\"keyword.commands.stats.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(neg|abs|random|random_float|random_from_args|min|max|clamp|floor|round|ceil|sqrt|^)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.math.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(concat|join)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.string.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(text_field)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.text_field.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(add_level|set_level|add_xp|roll|get_level|get_xp)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.commands.skills.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(add_item|remove_item|enable_interaction|disable_interaction|has_item?|item_amount?)\\\",\\\"name\\\":\\\"keyword.commands.inventory.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\b(start_quest|start_objective|complete_objective|complete_quest|quest_started?|objective_started?|quest_completed?|objective_completed?)\\\",\\\"name\\\":\\\"keyword.commands.quests.narrat\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\/\\\\\\\\/.*$\\\",\\\"name\\\":\\\"comment.line.narrat\\\"}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#commands\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#primitives\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#paren-expression\\\"}]},\\\"interpolation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\w|\\\\\\\\.)+\\\",\\\"name\\\":\\\"variable.interpolation.narrat\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(if|else|choice)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\$[\\\\\\\\w|\\\\\\\\.]+\\\\\\\\b\\\",\\\"name\\\":\\\"variable.value.narrat\\\"},{\\\"match\\\":\\\"^\\\\\\\\w+(?=(\\\\\\\\s|\\\\\\\\w)*:)\\\",\\\"name\\\":\\\"entity.name.function.narrat\\\"},{\\\"match\\\":\\\"^\\\\\\\\w+(?!(\\\\\\\\s|\\\\\\\\w)*:)\\\",\\\"name\\\":\\\"invalid.label.narrat\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\w)[^^](\\\\\\\\b\\\\\\\\w+\\\\\\\\b)(?=(\\\\\\\\s|\\\\\\\\w)*:)\\\",\\\"name\\\":\\\"entity.other.attribute-name\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(&&|\\\\\\\\|\\\\\\\\||!=|==|>=|<=|<|>|!|\\\\\\\\?)\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.logic.narrat\\\"},{\\\"match\\\":\\\"(\\\\\\\\+|-|\\\\\\\\*|\\\\\\\\/)\\\\\\\\s\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.narrat\\\"}]},\\\"paren-expression\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.open\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.paren.close\\\"}},\\\"name\\\":\\\"expression.group\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"primitives\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.true.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bfalse\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.false.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bnull\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.null.narrat\\\"},{\\\"match\\\":\\\"\\\\\\\\bundefined\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.undefined.narrat\\\"}]},\\\"strings\\\":{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.narrat\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.narrat\\\"},{\\\"begin\\\":\\\"%{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.template.open\\\"}},\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.template.close.narrat\\\"}},\\\"name\\\":\\\"expression.template\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"include\\\":\\\"#interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.narrat\\\",\\\"aliases\\\":[\\\"nar\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/narrat.mjs\n"));

/***/ })

}]);