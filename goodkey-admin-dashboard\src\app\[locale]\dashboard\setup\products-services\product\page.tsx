import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ProductTableAccordion from './components/product_table_accordion';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';

export const metadata: Metadata = {
  title: 'Goodkey | Product',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: GroupTypeQuery.tags,
    queryFn: () => GroupTypeQuery.getAllByGroupIdHierarchical(1),
  });
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Product',
          link: '/dashboard/setup/products-services/product',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        {/* <ProductTable /> */}
        <ProductTableAccordion />
      </HydrationBoundary>
    </AppLayout>
  );
}
