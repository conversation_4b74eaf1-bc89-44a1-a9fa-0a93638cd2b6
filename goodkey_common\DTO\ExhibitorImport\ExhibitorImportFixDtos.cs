using System.ComponentModel.DataAnnotations;

namespace goodkey_common.DTO.ExhibitorImport
{
    // =====================================================
    // FIELD EDITING AND ERROR FIXING DTOs
    // =====================================================

    public class ExhibitorImportFieldEditRequestDto
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        public List<FieldEditDto> FieldEdits { get; set; } = new List<FieldEditDto>();
    }

    public class FieldEditDto
    {
        [Required]
        public int RowNumber { get; set; }

        [Required]
        public string FieldName { get; set; } = string.Empty;

        [Required]
        public string NewValue { get; set; } = string.Empty;

        public string? OriginalValue { get; set; }

        public string EditReason { get; set; } = "UserEdit"; // "UserEdit", "ErrorFix", "ValidationFix"
    }

    public class ExhibitorImportFieldEditResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<FieldEditResultDto> Results { get; set; } = new List<FieldEditResultDto>();
        public ExhibitorImportValidationSummaryDto UpdatedSummary { get; set; } = new ExhibitorImportValidationSummaryDto();
    }

    public class FieldEditResultDto
    {
        public int RowNumber { get; set; }
        public string FieldName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public bool ValidationFixed { get; set; }
        public List<string> RemainingErrors { get; set; } = new List<string>();
    }

    // =====================================================
    // BULK ERROR FIXING DTOs
    // =====================================================

    public class ExhibitorImportBulkFixRequestDto
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        public List<ValidationErrorFixDto> ErrorFixes { get; set; } = new List<ValidationErrorFixDto>();
        public List<DuplicateFixDto> DuplicateFixes { get; set; } = new List<DuplicateFixDto>();
        public bool RevalidateAfterFix { get; set; } = true;
    }

    public class ValidationErrorFixDto
    {
        [Required]
        public int RowNumber { get; set; }

        [Required]
        public string FieldName { get; set; } = string.Empty;

        [Required]
        public string FixedValue { get; set; } = string.Empty;

        public string? OriginalValue { get; set; }
        public string? ValidationRule { get; set; }
        public string? MessageCode { get; set; }
    }

    public class DuplicateFixDto
    {
        [Required]
        public int DuplicateId { get; set; }

        [Required]
        public string Resolution { get; set; } = string.Empty; // "UseExcel", "UseDatabase", "Merge", "Custom"

        public List<FieldResolutionDto> FieldResolutions { get; set; } = new List<FieldResolutionDto>();
    }

    public class FieldResolutionDto
    {
        [Required]
        public string FieldName { get; set; } = string.Empty;

        [Required]
        public string SelectedValue { get; set; } = string.Empty;

        public string? ExcelValue { get; set; }
        public string? DatabaseValue { get; set; }
        public string? CustomValue { get; set; }
        public string Source { get; set; } = "Custom"; // "Excel", "Database", "Custom"
    }

    public class ExhibitorImportBulkFixResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int FixedErrorsCount { get; set; }
        public int FixedDuplicatesCount { get; set; }
        public int RemainingErrorsCount { get; set; }
        public int RemainingDuplicatesCount { get; set; }
        public List<FieldEditResultDto> Results { get; set; } = new List<FieldEditResultDto>();
        public ExhibitorImportValidationSummaryDto UpdatedSummary { get; set; } = new ExhibitorImportValidationSummaryDto();
    }

    // =====================================================
    // ROW COMPARISON DTOs
    // =====================================================

    public class ExhibitorImportRowComparisonRequestDto
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        public int RowNumber { get; set; }

        public int? CompareWithContactId { get; set; }
        public int? CompareWithCompanyId { get; set; }
    }

    public class ExhibitorImportRowComparisonResponseDto
    {
        public ExhibitorImportRowComparisonDto ExcelData { get; set; } = new ExhibitorImportRowComparisonDto();
        public ExhibitorImportRowComparisonDto? DatabaseData { get; set; }
        public List<FieldComparisonDto> FieldComparisons { get; set; } = new List<FieldComparisonDto>();
        public List<ExhibitorImportValidationMessageDto> ValidationMessages { get; set; } = new List<ExhibitorImportValidationMessageDto>();
    }

    public class ExhibitorImportRowComparisonDto
    {
        public string Source { get; set; } = string.Empty; // "Excel", "Database"
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyEmail { get; set; } = string.Empty;
        public string CompanyPhone { get; set; } = string.Empty;
        public string CompanyAddress { get; set; } = string.Empty;
        public string ContactFirstName { get; set; } = string.Empty;
        public string ContactLastName { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string ContactPhone { get; set; } = string.Empty;
        public string ContactMobile { get; set; } = string.Empty;
        public string BoothNumbers { get; set; } = string.Empty;
        public DateTime? LastUpdated { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class FieldComparisonDto
    {
        public string FieldName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string ExcelValue { get; set; } = string.Empty;
        public string? DatabaseValue { get; set; }
        public bool HasConflict { get; set; }
        public bool HasValidationError { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
        public string? SuggestedValue { get; set; }
        public string? SuggestionReason { get; set; }
    }

    // =====================================================
    // VALIDATION RE-RUN DTOs
    // =====================================================

    public class ExhibitorImportRevalidateRequestDto
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        public List<int>? RowNumbers { get; set; } // If null, revalidate all rows
        public bool ClearExistingMessages { get; set; } = true;
        public bool CheckDuplicates { get; set; } = true;
    }

    public class ExhibitorImportRevalidateResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public ExhibitorImportValidationSummaryDto UpdatedSummary { get; set; } = new ExhibitorImportValidationSummaryDto();
        public List<ExhibitorImportValidationMessageDto> NewValidationMessages { get; set; } = new List<ExhibitorImportValidationMessageDto>();
        public List<ExhibitorImportDuplicateDto> NewDuplicates { get; set; } = new List<ExhibitorImportDuplicateDto>();
    }

    // =====================================================
    // VALIDATION SUMMARY DTO
    // =====================================================

    public class ExhibitorImportValidationSummaryDto
    {
        public int TotalRows { get; set; }
        public int ValidRows { get; set; }
        public int ErrorRows { get; set; }
        public int WarningRows { get; set; }
        public bool HasErrors { get; set; }
        public bool HasWarnings { get; set; }
        public bool HasDuplicates { get; set; }
        public int UnresolvedDuplicates { get; set; }
    }

    // =====================================================
    // FIELD SUGGESTIONS DTOs
    // =====================================================

    public class ExhibitorImportFieldSuggestionsRequestDto
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        public int RowNumber { get; set; }

        [Required]
        public string FieldName { get; set; } = string.Empty;

        public string? CurrentValue { get; set; }
    }

    public class ExhibitorImportFieldSuggestionsResponseDto
    {
        public List<FieldSuggestionDto> Suggestions { get; set; } = new List<FieldSuggestionDto>();
    }

    public class FieldSuggestionDto
    {
        public string SuggestedValue { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public float Confidence { get; set; }
        public string Source { get; set; } = string.Empty; // "Database", "AutoCorrect", "Pattern"
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
