import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import CategoryQuery from '@/services/queries/CategoryQuery';
import CategoryGeneralInfo from './components/category_general_info';

export default async function Page({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['Category', { id: Number(id) }],
        queryFn: () => CategoryQuery.getById(Number(id)),
      });
    }

    return (
      <HydrationBoundary state={dehydrate(client)}>
        <CategoryGeneralInfo
          id={Number.isNaN(Number(id)) ? undefined : Number(id)}
        />
      </HydrationBoundary>
    );
  } catch (error) {
    redirect('/dashboard/setup/products-services/category/add');
  }
}
