const nighty = {
  cold_red: '#ff4b59',
  background: '#23315e',
  lighter_background: '#2c3964',
  cold_blue: '#01b9ca',
  hot_red: '#f44336',
  good_green: '#0acf83',
  secondary_color: '#344175',
  darkersec_color: '#29336a',
  border_color: '#3c4b7e',
  hot_purple: '#626ED4',
  silver_gray: '#646d8d',
  white: '#fff',
  text_gray: '#bdbdbd',
  good_black: '#18191c',
  warm_orange: '#f2994a',
  darker: ' #00000020',
  light: '#ffffff20',
  coldBlack: '#374151',
};

export const themeColors = {
  border: 'hsl(var(--border))',
  input: 'hsl(var(--input))',
  ring: 'hsl(var(--ring))',
  warm_orange: '#FFA000',
  cold_blue: '#82BCF4',
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  primary: {
    DEFAULT: 'hsl(var(--primary))',
    foreground: 'hsl(var(--primary-foreground))',
  },
  secondary: {
    DEFAULT: 'hsl(var(--secondary))',
    foreground: 'hsl(var(--secondary-foreground))',
  },
  destructive: {
    DEFAULT: 'hsl(var(--destructive))',
    foreground: 'hsl(var(--destructive-foreground))',
  },
  success: {
    DEFAULT: 'hsl(var(--success))',
    foreground: 'hsl(var(--success-foreground))',
  },
  warning: {
    DEFAULT: 'hsl(var(--warning))',
    foreground: 'hsl(var(--warning-foreground))',
  },
  gray: {
    DEFAULT: 'hsl(var(--gray))',
    foreground: 'hsl(var(--gray-foreground))',
  },
  muted: {
    DEFAULT: 'hsl(var(--muted))',
    foreground: 'hsl(var(--muted-foreground))',
  },
  accent: {
    DEFAULT: 'hsl(var(--accent))',
    foreground: 'hsl(var(--accent-foreground))',
  },
  popover: {
    DEFAULT: 'hsl(var(--popover))',
    foreground: 'hsl(var(--popover-foreground))',
  },
  card: {
    DEFAULT: 'hsl(var(--card))',
    foreground: 'hsl(var(--card-foreground))',
  },
};

export { nighty as staticColors };
