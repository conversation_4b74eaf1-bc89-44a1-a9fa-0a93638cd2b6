'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import FloatingCartButton from '@/components/floating-cart-button';
import { ArrowLeft, ShoppingCart, Search, Info, Check } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { toast } from '@/components/ui/use-toast';
import WelcomeBanner from '@/components/ui/welcome-banner';

// Define the product type
interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  image: string;
  colors: string[];
}

// Define the color options with their display names and hex values
const colorOptions = {
  blue: { name: 'Blue', hex: '#1e40af' },
  green: { name: 'Green', hex: '#15803d' },
  teal: { name: 'Teal', hex: '#0d9488' },
  purple: { name: 'Purple', hex: '#7e22ce' },
  red: { name: 'Red', hex: '#b91c1c' },
  black: { name: 'Black', hex: '#171717' },
  gray: { name: 'Gray', hex: '#6b7280' },
  white: { name: 'White', hex: '#f8fafc' },
};

// Mock product data for the banjo skirts
const productData: Product = {
  id: 'banjo-skirts-counter',
  name: 'Banjo skirts for counter height tables - 40" h',
  description:
    'High-quality banjo skirts designed for counter height tables. These skirts provide an elegant finish to your event tables and are available in multiple colors to match your event theme.',
  price: 45.99,
  image:
    'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-lxw4vgmyexI38Z7GalFAqwVGOLyROS.png',
  colors: ['blue', 'green', 'teal', 'purple', 'red', 'black', 'gray', 'white'],
};

export default function ProductDetailClient({
  params,
}: {
  params: { id: string; productId: string };
}) {
  const router = useRouter();
  const eventId = params.id;
  const productId = params.productId;

  const [selectedColor, setSelectedColor] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [hoveredColor, setHoveredColor] = useState<string | null>(null);
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [cartItemCount, setCartItemCount] = useState(4);
  const [addedToCart, setAddedToCart] = useState(false);

  // Calculate total amount when quantity or color changes
  useEffect(() => {
    setTotalAmount(quantity * productData.price);
  }, [quantity, selectedColor]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number.parseInt(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setQuantity(value);
    } else {
      setQuantity(0);
    }
  };

  const handleAddToCart = () => {
    if (quantity <= 0) {
      toast({
        title: 'Error',
        description: 'Please select a quantity greater than 0',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedColor) {
      toast({
        title: 'Error',
        description: 'Please select a color',
        variant: 'destructive',
      });
      return;
    }

    // In a real app, this would add the item to the cart
    setCartItemCount(cartItemCount + quantity);
    setAddedToCart(true);

    toast({
      title: 'Added to cart',
      description: `${quantity} ${colorOptions[selectedColor as keyof typeof colorOptions].name} banjo skirt(s) added to your cart`,
      action: (
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push(`/event/${eventId}/cart`)}
        >
          View Cart
        </Button>
      ),
      variant: 'success',
    });

    // Reset after a delay
    setTimeout(() => {
      setAddedToCart(false);
    }, 2000);
  };

  const handleViewCart = () => {
    router.push(`/event/${eventId}/cart`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <WelcomeBanner userName="Harry Hekimian" showInstructions={false} />
      {/* <HorizontalMenu activeItem="MANAGEMENT" /> */}

      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() =>
              router.push(`/event/${eventId}/products/tables-and-covers`)
            }
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Tables and Covers
          </Button>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/event/${eventId}`)}
        >
          Back to Event
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Image */}
        <div className="relative">
          <Card className="overflow-hidden max-w-md mx-auto">
            <CardContent className="p-0">
              <div className="relative aspect-square max-h-80">
                <img
                  src={productData.image || '/placeholder.svg'}
                  alt={productData.name}
                  className="w-full h-full object-contain"
                />
                <Dialog
                  open={imageDialogOpen}
                  onOpenChange={setImageDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute bottom-4 right-4 bg-white/80 hover:bg-white"
                    >
                      <Search className="h-4 w-4" />
                      <span className="sr-only">Click to enlarge</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <DialogHeader>
                      <DialogTitle>{productData.name}</DialogTitle>
                      <DialogDescription>
                        Available in multiple colors
                      </DialogDescription>
                    </DialogHeader>
                    <div className="aspect-square w-full">
                      <img
                        src={productData.image || '/placeholder.svg'}
                        alt={productData.name}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
          <div className="text-sm text-slate-500 mt-2 flex items-center justify-center">
            <Search className="h-3 w-3 mr-1" />
            Click to enlarge (magnifying glass icon)
          </div>
        </div>

        {/* Product Details */}
        <div>
          <h1 className="text-2xl font-bold text-slate-800 mb-2">
            {productData.name}
          </h1>
          <p className="text-slate-600 mb-6">{productData.description}</p>

          <div className="space-y-6">
            {/* Color Selection */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Select a colour:
              </label>
              <Select value={selectedColor} onValueChange={setSelectedColor}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="- None Selected -" />
                </SelectTrigger>
                <SelectContent>
                  {productData.colors.map((color) => (
                    <SelectItem key={color} value={color}>
                      <div className="flex items-center">
                        <div
                          className="w-4 h-4 rounded-full mr-2"
                          style={{
                            backgroundColor:
                              colorOptions[color as keyof typeof colorOptions]
                                .hex,
                          }}
                        />
                        {colorOptions[color as keyof typeof colorOptions].name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Quantity:
              </label>
              <Input
                type="number"
                min="1"
                value={quantity}
                onChange={handleQuantityChange}
                className="w-full"
              />
            </div>

            {/* Pricing */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-slate-800">Pricing:</h3>
              <div className="flex justify-between">
                <span className="text-slate-600">Unit Price:</span>
                <span className="font-medium">
                  ${productData.price.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600">Total Amount:</span>
                <span className="font-medium">${totalAmount.toFixed(2)}</span>
              </div>
            </div>

            {/* Color Swatches */}
            <div>
              <div className="flex items-center mb-2">
                <h3 className="text-lg font-medium text-slate-800 mr-2">
                  Colours available:
                </h3>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-slate-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Point at colours to view</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="flex flex-wrap gap-2">
                {productData.colors.map((color) => (
                  <TooltipProvider key={color}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={`w-8 h-8 rounded-full cursor-pointer border ${
                            selectedColor === color
                              ? 'border-[#00646C] ring-2 ring-[#00646C]/30'
                              : 'border-slate-200'
                          }`}
                          style={{
                            backgroundColor:
                              colorOptions[color as keyof typeof colorOptions]
                                .hex,
                          }}
                          onMouseEnter={() => setHoveredColor(color)}
                          onMouseLeave={() => setHoveredColor(null)}
                          onClick={() => setSelectedColor(color)}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {
                            colorOptions[color as keyof typeof colorOptions]
                              .name
                          }
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
              </div>
              <p className="text-xs text-slate-500 mt-2">
                Point at colours to view
              </p>
            </div>

            {/* Add to Cart Button */}
            <div className="flex gap-2">
              <Button
                className="flex-1 bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={handleAddToCart}
                disabled={addedToCart}
              >
                {addedToCart ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Added to cart
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to cart
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                className="border-[#00646C] text-[#00646C]"
                onClick={handleViewCart}
              >
                View Cart
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Cart Button */}
      <FloatingCartButton eventId={eventId} itemCount={cartItemCount} />
    </div>
  );
}
