"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_splunk_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/splunk.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/splunk.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Splunk Query Language\\\",\\\"fileTypes\\\":[\\\"splunk\\\",\\\"spl\\\"],\\\"name\\\":\\\"splunk\\\",\\\"patterns\\\":[{\\\"comment\\\":\\\"Splunk Built-in functions\\\",\\\"match\\\":\\\"(?<=(\\\\\\\\||\\\\\\\\[))([\\\\\\\\s]*)\\\\\\\\b(abstract|accum|addcoltotals|addinfo|addtotals|analyzefields|anomalies|anomalousvalue|append|appendcols|appendpipe|arules|associate|audit|autoregress|bucket|bucketdir|chart|cluster|collect|concurrency|contingency|convert|correlate|crawl|datamodel|dbinspect|dbxquery|dbxlookup|dedup|delete|delta|diff|dispatch|erex|eval|eventcount|eventstats|extract|fieldformat|fields|fieldsummary|file|filldown|fillnull|findtypes|folderize|foreach|format|from|gauge|gentimes|geostats|head|highlight|history|input|inputcsv|inputlookup|iplocation|join|kmeans|kvform|loadjob|localize|localop|lookup|makecontinuous|makemv|makeresults|map|metadata|metasearch|multikv|multisearch|mvcombine|mvexpand|nomv|outlier|outputcsv|outputlookup|outputtext|overlap|pivot|predict|rangemap|rare|regex|relevancy|reltime|rename|replace|rest|return|reverse|rex|rtorder|run|savedsearch|script|scrub|search|searchtxn|selfjoin|sendemail|set|setfields|sichart|sirare|sistats|sitimechart|sitop|sort|spath|stats|strcat|streamstats|table|tags|tail|timechart|top|transaction|transpose|trendline|tscollect|tstats|typeahead|typelearner|typer|uniq|untable|where|x11|xmlkv|xmlunescape|xpath|xyseries)\\\\\\\\b(?=[\\\\\\\\s])\\\",\\\"name\\\":\\\"support.class.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Eval functions\\\",\\\"match\\\":\\\"\\\\\\\\b(abs|acos|acosh|asin|asinh|atan|atan2|atanh|case|cidrmatch|ceiling|coalesce|commands|cos|cosh|exact|exp|floor|hypot|if|in|isbool|isint|isnotnull|isnull|isnum|isstr|len|like|ln|log|lower|ltrim|match|max|md5|min|mvappend|mvcount|mvdedup|mvfilter|mvfind|mvindex|mvjoin|mvrange|mvsort|mvzip|now|null|nullif|pi|pow|printf|random|relative_time|replace|round|rtrim|searchmatch|sha1|sha256|sha512|sigfig|sin|sinh|spath|split|sqrt|strftime|strptime|substr|tan|tanh|time|tonumber|tostring|trim|typeof|upper|urldecode|validate)(?=\\\\\\\\()\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Transforming functions\\\",\\\"match\\\":\\\"\\\\\\\\b(avg|count|distinct_count|estdc|estdc_error|eval|max|mean|median|min|mode|percentile|range|stdev|stdevp|sum|sumsq|var|varp|first|last|list|values|earliest|earliest_time|latest|latest_time|per_day|per_hour|per_minute|per_second|rate)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Macro Names\\\",\\\"match\\\":\\\"(?<=\\\\\\\\`)[\\\\\\\\w]+(?=\\\\\\\\(|\\\\\\\\`)\\\",\\\"name\\\":\\\"entity.name.function.splunk_search\\\"},{\\\"comment\\\":\\\"Digits\\\",\\\"match\\\":\\\"\\\\\\\\b(\\\\\\\\d+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.splunk_search\\\"},{\\\"comment\\\":\\\"Escape Characters\\\",\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\|\\\\\\\\\\\\\\\\\\\\\\\\||\\\\\\\\\\\\\\\\\\\\\\\\*|\\\\\\\\\\\\\\\\\\\\\\\\=)\\\",\\\"name\\\":\\\"contant.character.escape.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Operators\\\",\\\"match\\\":\\\"(\\\\\\\\|,)\\\",\\\"name\\\":\\\"keyword.operator.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Language Constants\\\",\\\"match\\\":\\\"(?i)\\\\\\\\b(as|by|or|and|over|where|output|outputnew)\\\\\\\\b|(?-i)\\\\\\\\b(NOT|true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Macro Parameters\\\",\\\"match\\\":\\\"(?<=\\\\\\\\(|,|[^=]\\\\\\\\s{300})([^\\\\\\\\(\\\\\\\\)\\\\\\\\\\\\\\\",=]+)(?=\\\\\\\\)|,)\\\",\\\"name\\\":\\\"variable.parameter.splunk_search\\\"},{\\\"comment\\\":\\\"Splunk Variables\\\",\\\"match\\\":\\\"([\\\\\\\\w\\\\\\\\.]+)(\\\\\\\\[\\\\\\\\]|\\\\\\\\{\\\\\\\\})?([\\\\\\\\s]*)(?=\\\\\\\\=)\\\",\\\"name\\\":\\\"variable.splunk_search\\\"},{\\\"comment\\\":\\\"Comparison or assignment\\\",\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.splunk_search\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.splunk_search\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)'\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)'\\\",\\\"name\\\":\\\"string.quoted.single.splunk_search\\\"},{\\\"begin\\\":\\\"query=\\\\\\\\\\\\\\\"\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)\\\\\\\"\\\",\\\"name\\\":\\\"meta.embedded.block.sql\\\"},{\\\"begin\\\":\\\"(?<!\\\\\\\\\\\\\\\\)```\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)```\\\",\\\"name\\\":\\\"comment.block.splunk_search\\\"},{\\\"begin\\\":\\\"`comment\\\\\\\\(\\\",\\\"end\\\":\\\"\\\\\\\\)`\\\",\\\"name\\\":\\\"comment.block.splunk_search\\\"}],\\\"scopeName\\\":\\\"source.splunk_search\\\",\\\"aliases\\\":[\\\"spl\\\"]}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2hpa2lqcy9sYW5ncy9kaXN0L3NwbHVuay5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHdDQUF3QyxnSEFBZ0gsaXhDQUFpeEMsRUFBRSx3bkJBQXduQixFQUFFLDJWQUEyVixFQUFFLG1JQUFtSSxFQUFFLG9HQUFvRyxFQUFFLGlLQUFpSyxFQUFFLG9HQUFvRyxFQUFFLDhMQUE4TCxFQUFFLDBFQUEwRSxJQUFJLHVGQUF1RixFQUFFLDZFQUE2RSxLQUFLLDREQUE0RCxFQUFFLHFHQUFxRyxFQUFFLDhHQUE4RyxFQUFFLHdHQUF3RyxFQUFFLGdHQUFnRyxFQUFFLHFHQUFxRyxFQUFFLHdGQUF3RiwrREFBK0Q7O0FBRTlnSSxpRUFBZTtBQUNmO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkZXZlbG9wZXIyXFxzb3VyY2VcXHJlcG9zXFxQcm9qZWN0XFxnb29ka2V5LWFkbWluLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxAc2hpa2lqc1xcbGFuZ3NcXGRpc3RcXHNwbHVuay5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbGFuZyA9IE9iamVjdC5mcmVlemUoSlNPTi5wYXJzZShcIntcXFwiZGlzcGxheU5hbWVcXFwiOlxcXCJTcGx1bmsgUXVlcnkgTGFuZ3VhZ2VcXFwiLFxcXCJmaWxlVHlwZXNcXFwiOltcXFwic3BsdW5rXFxcIixcXFwic3BsXFxcIl0sXFxcIm5hbWVcXFwiOlxcXCJzcGx1bmtcXFwiLFxcXCJwYXR0ZXJuc1xcXCI6W3tcXFwiY29tbWVudFxcXCI6XFxcIlNwbHVuayBCdWlsdC1pbiBmdW5jdGlvbnNcXFwiLFxcXCJtYXRjaFxcXCI6XFxcIig/PD0oXFxcXFxcXFx8fFxcXFxcXFxcWykpKFtcXFxcXFxcXHNdKilcXFxcXFxcXGIoYWJzdHJhY3R8YWNjdW18YWRkY29sdG90YWxzfGFkZGluZm98YWRkdG90YWxzfGFuYWx5emVmaWVsZHN8YW5vbWFsaWVzfGFub21hbG91c3ZhbHVlfGFwcGVuZHxhcHBlbmRjb2xzfGFwcGVuZHBpcGV8YXJ1bGVzfGFzc29jaWF0ZXxhdWRpdHxhdXRvcmVncmVzc3xidWNrZXR8YnVja2V0ZGlyfGNoYXJ0fGNsdXN0ZXJ8Y29sbGVjdHxjb25jdXJyZW5jeXxjb250aW5nZW5jeXxjb252ZXJ0fGNvcnJlbGF0ZXxjcmF3bHxkYXRhbW9kZWx8ZGJpbnNwZWN0fGRieHF1ZXJ5fGRieGxvb2t1cHxkZWR1cHxkZWxldGV8ZGVsdGF8ZGlmZnxkaXNwYXRjaHxlcmV4fGV2YWx8ZXZlbnRjb3VudHxldmVudHN0YXRzfGV4dHJhY3R8ZmllbGRmb3JtYXR8ZmllbGRzfGZpZWxkc3VtbWFyeXxmaWxlfGZpbGxkb3dufGZpbGxudWxsfGZpbmR0eXBlc3xmb2xkZXJpemV8Zm9yZWFjaHxmb3JtYXR8ZnJvbXxnYXVnZXxnZW50aW1lc3xnZW9zdGF0c3xoZWFkfGhpZ2hsaWdodHxoaXN0b3J5fGlucHV0fGlucHV0Y3N2fGlucHV0bG9va3VwfGlwbG9jYXRpb258am9pbnxrbWVhbnN8a3Zmb3JtfGxvYWRqb2J8bG9jYWxpemV8bG9jYWxvcHxsb29rdXB8bWFrZWNvbnRpbnVvdXN8bWFrZW12fG1ha2VyZXN1bHRzfG1hcHxtZXRhZGF0YXxtZXRhc2VhcmNofG11bHRpa3Z8bXVsdGlzZWFyY2h8bXZjb21iaW5lfG12ZXhwYW5kfG5vbXZ8b3V0bGllcnxvdXRwdXRjc3Z8b3V0cHV0bG9va3VwfG91dHB1dHRleHR8b3ZlcmxhcHxwaXZvdHxwcmVkaWN0fHJhbmdlbWFwfHJhcmV8cmVnZXh8cmVsZXZhbmN5fHJlbHRpbWV8cmVuYW1lfHJlcGxhY2V8cmVzdHxyZXR1cm58cmV2ZXJzZXxyZXh8cnRvcmRlcnxydW58c2F2ZWRzZWFyY2h8c2NyaXB0fHNjcnVifHNlYXJjaHxzZWFyY2h0eG58c2VsZmpvaW58c2VuZGVtYWlsfHNldHxzZXRmaWVsZHN8c2ljaGFydHxzaXJhcmV8c2lzdGF0c3xzaXRpbWVjaGFydHxzaXRvcHxzb3J0fHNwYXRofHN0YXRzfHN0cmNhdHxzdHJlYW1zdGF0c3x0YWJsZXx0YWdzfHRhaWx8dGltZWNoYXJ0fHRvcHx0cmFuc2FjdGlvbnx0cmFuc3Bvc2V8dHJlbmRsaW5lfHRzY29sbGVjdHx0c3RhdHN8dHlwZWFoZWFkfHR5cGVsZWFybmVyfHR5cGVyfHVuaXF8dW50YWJsZXx3aGVyZXx4MTF8eG1sa3Z8eG1sdW5lc2NhcGV8eHBhdGh8eHlzZXJpZXMpXFxcXFxcXFxiKD89W1xcXFxcXFxcc10pXFxcIixcXFwibmFtZVxcXCI6XFxcInN1cHBvcnQuY2xhc3Muc3BsdW5rX3NlYXJjaFxcXCJ9LHtcXFwiY29tbWVudFxcXCI6XFxcIlNwbHVuayBFdmFsIGZ1bmN0aW9uc1xcXCIsXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKGFic3xhY29zfGFjb3NofGFzaW58YXNpbmh8YXRhbnxhdGFuMnxhdGFuaHxjYXNlfGNpZHJtYXRjaHxjZWlsaW5nfGNvYWxlc2NlfGNvbW1hbmRzfGNvc3xjb3NofGV4YWN0fGV4cHxmbG9vcnxoeXBvdHxpZnxpbnxpc2Jvb2x8aXNpbnR8aXNub3RudWxsfGlzbnVsbHxpc251bXxpc3N0cnxsZW58bGlrZXxsbnxsb2d8bG93ZXJ8bHRyaW18bWF0Y2h8bWF4fG1kNXxtaW58bXZhcHBlbmR8bXZjb3VudHxtdmRlZHVwfG12ZmlsdGVyfG12ZmluZHxtdmluZGV4fG12am9pbnxtdnJhbmdlfG12c29ydHxtdnppcHxub3d8bnVsbHxudWxsaWZ8cGl8cG93fHByaW50ZnxyYW5kb218cmVsYXRpdmVfdGltZXxyZXBsYWNlfHJvdW5kfHJ0cmltfHNlYXJjaG1hdGNofHNoYTF8c2hhMjU2fHNoYTUxMnxzaWdmaWd8c2lufHNpbmh8c3BhdGh8c3BsaXR8c3FydHxzdHJmdGltZXxzdHJwdGltZXxzdWJzdHJ8dGFufHRhbmh8dGltZXx0b251bWJlcnx0b3N0cmluZ3x0cmltfHR5cGVvZnx1cHBlcnx1cmxkZWNvZGV8dmFsaWRhdGUpKD89XFxcXFxcXFwoKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImNvbW1lbnRcXFwiOlxcXCJTcGx1bmsgVHJhbnNmb3JtaW5nIGZ1bmN0aW9uc1xcXCIsXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKGF2Z3xjb3VudHxkaXN0aW5jdF9jb3VudHxlc3RkY3xlc3RkY19lcnJvcnxldmFsfG1heHxtZWFufG1lZGlhbnxtaW58bW9kZXxwZXJjZW50aWxlfHJhbmdlfHN0ZGV2fHN0ZGV2cHxzdW18c3Vtc3F8dmFyfHZhcnB8Zmlyc3R8bGFzdHxsaXN0fHZhbHVlc3xlYXJsaWVzdHxlYXJsaWVzdF90aW1lfGxhdGVzdHxsYXRlc3RfdGltZXxwZXJfZGF5fHBlcl9ob3VyfHBlcl9taW51dGV8cGVyX3NlY29uZHxyYXRlKVxcXFxcXFxcYlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdXBwb3J0LmZ1bmN0aW9uLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImNvbW1lbnRcXFwiOlxcXCJTcGx1bmsgTWFjcm8gTmFtZXNcXFwiLFxcXCJtYXRjaFxcXCI6XFxcIig/PD1cXFxcXFxcXGApW1xcXFxcXFxcd10rKD89XFxcXFxcXFwofFxcXFxcXFxcYClcXFwiLFxcXCJuYW1lXFxcIjpcXFwiZW50aXR5Lm5hbWUuZnVuY3Rpb24uc3BsdW5rX3NlYXJjaFxcXCJ9LHtcXFwiY29tbWVudFxcXCI6XFxcIkRpZ2l0c1xcXCIsXFxcIm1hdGNoXFxcIjpcXFwiXFxcXFxcXFxiKFxcXFxcXFxcZCspXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lm51bWVyaWMuc3BsdW5rX3NlYXJjaFxcXCJ9LHtcXFwiY29tbWVudFxcXCI6XFxcIkVzY2FwZSBDaGFyYWN0ZXJzXFxcIixcXFwibWF0Y2hcXFwiOlxcXCIoXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFx8XFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcfHxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXFwqfFxcXFxcXFxcXFxcXFxcXFxcXFxcXFxcXD0pXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnRhbnQuY2hhcmFjdGVyLmVzY2FwZS5zcGx1bmtfc2VhcmNoXFxcIn0se1xcXCJjb21tZW50XFxcIjpcXFwiU3BsdW5rIE9wZXJhdG9yc1xcXCIsXFxcIm1hdGNoXFxcIjpcXFwiKFxcXFxcXFxcfCwpXFxcIixcXFwibmFtZVxcXCI6XFxcImtleXdvcmQub3BlcmF0b3Iuc3BsdW5rX3NlYXJjaFxcXCJ9LHtcXFwiY29tbWVudFxcXCI6XFxcIlNwbHVuayBMYW5ndWFnZSBDb25zdGFudHNcXFwiLFxcXCJtYXRjaFxcXCI6XFxcIig/aSlcXFxcXFxcXGIoYXN8Ynl8b3J8YW5kfG92ZXJ8d2hlcmV8b3V0cHV0fG91dHB1dG5ldylcXFxcXFxcXGJ8KD8taSlcXFxcXFxcXGIoTk9UfHRydWV8ZmFsc2UpXFxcXFxcXFxiXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbnN0YW50Lmxhbmd1YWdlLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImNvbW1lbnRcXFwiOlxcXCJTcGx1bmsgTWFjcm8gUGFyYW1ldGVyc1xcXCIsXFxcIm1hdGNoXFxcIjpcXFwiKD88PVxcXFxcXFxcKHwsfFtePV1cXFxcXFxcXHN7MzAwfSkoW15cXFxcXFxcXChcXFxcXFxcXClcXFxcXFxcXFxcXFxcXFwiLD1dKykoPz1cXFxcXFxcXCl8LClcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUucGFyYW1ldGVyLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImNvbW1lbnRcXFwiOlxcXCJTcGx1bmsgVmFyaWFibGVzXFxcIixcXFwibWF0Y2hcXFwiOlxcXCIoW1xcXFxcXFxcd1xcXFxcXFxcLl0rKShcXFxcXFxcXFtcXFxcXFxcXF18XFxcXFxcXFx7XFxcXFxcXFx9KT8oW1xcXFxcXFxcc10qKSg/PVxcXFxcXFxcPSlcXFwiLFxcXCJuYW1lXFxcIjpcXFwidmFyaWFibGUuc3BsdW5rX3NlYXJjaFxcXCJ9LHtcXFwiY29tbWVudFxcXCI6XFxcIkNvbXBhcmlzb24gb3IgYXNzaWdubWVudFxcXCIsXFxcIm1hdGNoXFxcIjpcXFwiPVxcXCIsXFxcIm5hbWVcXFwiOlxcXCJrZXl3b3JkLm9wZXJhdG9yLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiKD88IVxcXFxcXFxcXFxcXFxcXFwpXFxcXFxcXCJcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhXFxcXFxcXFxcXFxcXFxcXClcXFxcXFxcIlxcXCIsXFxcIm5hbWVcXFwiOlxcXCJzdHJpbmcucXVvdGVkLmRvdWJsZS5zcGx1bmtfc2VhcmNoXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKSdcXFwiLFxcXCJlbmRcXFwiOlxcXCIoPzwhXFxcXFxcXFxcXFxcXFxcXCknXFxcIixcXFwibmFtZVxcXCI6XFxcInN0cmluZy5xdW90ZWQuc2luZ2xlLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwicXVlcnk9XFxcXFxcXFxcXFxcXFxcIlxcXCIsXFxcImVuZFxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKVxcXFxcXFwiXFxcIixcXFwibmFtZVxcXCI6XFxcIm1ldGEuZW1iZWRkZWQuYmxvY2suc3FsXFxcIn0se1xcXCJiZWdpblxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKWBgYFxcXCIsXFxcImVuZFxcXCI6XFxcIig/PCFcXFxcXFxcXFxcXFxcXFxcKWBgYFxcXCIsXFxcIm5hbWVcXFwiOlxcXCJjb21tZW50LmJsb2NrLnNwbHVua19zZWFyY2hcXFwifSx7XFxcImJlZ2luXFxcIjpcXFwiYGNvbW1lbnRcXFxcXFxcXChcXFwiLFxcXCJlbmRcXFwiOlxcXCJcXFxcXFxcXClgXFxcIixcXFwibmFtZVxcXCI6XFxcImNvbW1lbnQuYmxvY2suc3BsdW5rX3NlYXJjaFxcXCJ9XSxcXFwic2NvcGVOYW1lXFxcIjpcXFwic291cmNlLnNwbHVua19zZWFyY2hcXFwiLFxcXCJhbGlhc2VzXFxcIjpbXFxcInNwbFxcXCJdfVwiKSlcblxuZXhwb3J0IGRlZmF1bHQgW1xubGFuZ1xuXVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/splunk.mjs\n"));

/***/ })

}]);