"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_shikijs_langs_dist_zig_mjs"],{

/***/ "(app-pages-browser)/./node_modules/@shikijs/langs/dist/zig.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@shikijs/langs/dist/zig.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Zig\\\",\\\"fileTypes\\\":[\\\"zig\\\",\\\"zon\\\"],\\\"name\\\":\\\"zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#punctuation\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#support\\\"},{\\\"include\\\":\\\"#variables\\\"}],\\\"repository\\\":{\\\"commentContents\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(TODO|FIXME|XXX|NOTE)\\\\\\\\b:?\\\",\\\"name\\\":\\\"keyword.todo.zig\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"//[!/](?=[^/])\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.documentation.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commentContents\\\"}]},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#commentContents\\\"}]}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\binline\\\\\\\\b(?!\\\\\\\\s*\\\\\\\\bfn\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.control.repeat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(while|for)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.repeat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(extern|packed|export|pub|noalias|inline|comptime|volatile|align|linksection|threadlocal|allowzero|noinline|callconv)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.storage.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(struct|enum|union|opaque)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.structure.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(asm|unreachable)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.statement.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|return|continue|defer|errdefer)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(await|resume|suspend|async|nosuspend)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.async.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(try|catch)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.trycatch.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(if|else|switch|orelse)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.conditional.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(null|undefined)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.constant.default.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(true|false)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.constant.bool.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(usingnamespace|test|and|or)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.default.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(bool|void|noreturn|type|error|anyerror|anyframe|anytype|anyopaque)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(f16|f32|f64|f80|f128|u\\\\\\\\d+|i\\\\\\\\d+|isize|usize|comptime_int|comptime_float)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.integer.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b(c_char|c_short|c_ushort|c_int|c_uint|c_long|c_ulong|c_longlong|c_ulonglong|c_longdouble)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.type.c.zig\\\"}]},\\\"numbers\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b0x[0-9a-fA-F][0-9a-fA-F_]*(\\\\\\\\.[0-9a-fA-F][0-9a-fA-F_]*)?([pP][+-]?[0-9a-fA-F_]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexfloat.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9][0-9_]*(\\\\\\\\.[0-9][0-9_]*)?([eE][+-]?[0-9_]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9][0-9_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0x[a-fA-F0-9_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hexadecimal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0o[0-7_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.octal.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b0b[01_]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.binary.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9](([eEpP][+-])|[0-9a-zA-Z_])*(\\\\\\\\.(([eEpP][+-])|[0-9a-zA-Z_])*)?([eEpP][+-])?[0-9a-zA-Z_]*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.invalid.zig\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=\\\\\\\\[)\\\\\\\\*c(?=\\\\\\\\])\\\",\\\"name\\\":\\\"keyword.operator.c-pointer.zig\\\"},{\\\"match\\\":\\\"(\\\\\\\\b(and|or)\\\\\\\\b)|(==|!=|<=|>=|<|>)\\\",\\\"name\\\":\\\"keyword.operator.comparison.zig\\\"},{\\\"match\\\":\\\"(-%?|\\\\\\\\+%?|\\\\\\\\*%?|/|%)=?\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.zig\\\"},{\\\"match\\\":\\\"(<<%?|>>|!|~|&|\\\\\\\\^|\\\\\\\\|)=?\\\",\\\"name\\\":\\\"keyword.operator.bitwise.zig\\\"},{\\\"match\\\":\\\"(==|\\\\\\\\+\\\\\\\\+|\\\\\\\\*\\\\\\\\*|->)\\\",\\\"name\\\":\\\"keyword.operator.special.zig\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\?\\\",\\\"name\\\":\\\"keyword.operator.question.zig\\\"}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.accessor.zig\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.comma.zig\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.key-value.zig\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.zig\\\"}]},\\\"stringcontent\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([nrt'\\\\\\\"\\\\\\\\\\\\\\\\]|(x[0-9a-fA-F]{2})|(u\\\\\\\\{[0-9a-fA-F]+\\\\\\\\}))\\\",\\\"name\\\":\\\"constant.character.escape.zig\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unrecognized-string-escape.zig\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"string.multiline.zig\\\"},{\\\"match\\\":\\\"'([^'\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\(x\\\\\\\\h{2}|[0-2][0-7]{,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.))'\\\",\\\"name\\\":\\\"string.quoted.single.zig\\\"}]},\\\"support\\\":{\\\"patterns\\\":[{\\\"comment\\\":\\\"Built-in functions\\\",\\\"match\\\":\\\"@[_a-zA-Z][_a-zA-Z0-9]*\\\",\\\"name\\\":\\\"support.function.builtin.zig\\\"}]},\\\"variables\\\":{\\\"patterns\\\":[{\\\"name\\\":\\\"meta.function.declaration.zig\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.type.zig\\\"}},\\\"match\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([A-Z][a-zA-Z0-9]*)\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.function.zig\\\"}},\\\"match\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+([_a-zA-Z][_a-zA-Z0-9]*)\\\\\\\\b\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(fn)\\\\\\\\s+@\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.zig\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"entity.name.function.string.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]},{\\\"match\\\":\\\"\\\\\\\\b(const|var|fn)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.default.zig\\\"}]},{\\\"name\\\":\\\"meta.function.call.zig\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"([A-Z][a-zA-Z0-9]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.type.zig\\\"},{\\\"match\\\":\\\"([_a-zA-Z][_a-zA-Z0-9]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"entity.name.function.zig\\\"}]},{\\\"name\\\":\\\"meta.variable.zig\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b[_a-zA-Z][_a-zA-Z0-9]*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.zig\\\"},{\\\"begin\\\":\\\"@\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"variable.string.zig\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#stringcontent\\\"}]}]}]}},\\\"scopeName\\\":\\\"source.zig\\\"}\"))\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\nlang\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@shikijs/langs/dist/zig.mjs\n"));

/***/ })

}]);